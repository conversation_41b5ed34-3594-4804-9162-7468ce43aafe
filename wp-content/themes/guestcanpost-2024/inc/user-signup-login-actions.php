<?php

/** Ajax related To User registration and login */

function user_sign_up()
{
    try {

        $form_data = $_POST;
        // parse_str($_POST['formData'], $form_data);

        if (!isset($form_data['first_name']) || !isset($form_data['last_name']) ||  !isset($form_data['username']) || !isset($form_data['email']) || !isset($form_data['password']) || !isset($form_data['confirm_password'])) {
            throw new Exception('All Fields are required !');
        } else {
            $firstName = $form_data['first_name'];
            $lastName = $form_data['last_name'];
            $userName = $form_data['username'];
            $email = $form_data['email'];
            $password = $form_data['password'];
            $confirm_password = $form_data['confirm_password'];
            $designation = $form_data['designation'];
            $long_description = $form_data['long_description'];

            //check email 
            if (!is_email($email)) {
                throw new Exception("Please ENter valid email");
            } elseif (email_exists($email)) {
                throw new Exception("Email Already Exists");
            }

            // Check if passwords match
            if ($password !== $confirm_password) {
                throw new Exception('Passwords do not match!');
            }

            // Check password strength (example: at least 8 characters and one special character)
            if (strlen($password) < 8 || !preg_match('/[^a-zA-Z\d]/', $password)) {
                throw new Exception('Password must be at least 8 characters long and contain at least one special character!');
            }

            // Register user
            //$userName = $email;
            $userData = array(
                'user_pass'        => $confirm_password,
                'user_login'       => $userName,
                'user_email'       => $email,
                'first_name'       => $firstName,
                'last_name'        => $lastName,
                'user_registered'     => date("Y-m-d H:i:s"),
                'show_admin_bar_front'   => 'false',
                'role'           => 'um_member',
                'user_status' => 1,
            );
            $userId =  wp_insert_user($userData);
            if (is_wp_error($userId)) {
                wp_send_json(array(
                    'status' => 'error',
                    'data' => array(
                        'message' => $userId->get_error_message(),
                        'url' => ''
                    )
                ));
            } else {
                //Update additional user meta fields
                add_user_meta($userId, 'designation', $designation);
                add_user_meta($userId, 'long_description', $long_description);

                if (isset($_FILES['profile_image']) && !empty($_FILES['profile_image']['name'])) {
                    $uploaded_file = $_FILES['profile_image'];

                    try {
                        // Validate file upload
                        if ($uploaded_file['error'] !== UPLOAD_ERR_OK) {
                            throw new Exception('File upload error: ' . $uploaded_file['error']);
                        }

                        // Upload image path
                        $upload_dir = wp_upload_dir();
                        $upload_path = $upload_dir['basedir'] . '/avatars/';

                        if (!file_exists($upload_path)) {
                            wp_mkdir_p($upload_path);
                        }

                        $file_name = wp_unique_filename($upload_path, $uploaded_file['name']);
                        $file_path = $upload_path . $file_name;

                        if (move_uploaded_file($uploaded_file['tmp_name'], $file_path)) {
                            $file_url =  '/avatars/' . $file_name;

                            if ($userId) {
                                if (add_user_meta($userId, 'profile_image', $file_url)) {
                                    $message = 'Profile image updated successfully.';
                                } else {
                                    throw new Exception('Failed to update user meta.');
                                }
                            } else {
                                throw new Exception('User ID is not set.');
                            }
                        } else {
                            throw new Exception('Failed to upload profile image.');
                        }
                    } catch (Exception $e) {
                        wp_send_json(array(
                            'status' => 'error',
                            'message' => $e->getMessage(),
                        ));
                        exit;
                    }
                }
                /****************** User Activation Key Start **********************/

                $activation_key = wp_generate_password(32, false);
                add_user_meta($userId, 'activation_key', $activation_key);
                add_user_meta($userId, 'is_activated', 0);

                // Send Activation Email
                $activation_url = home_url('/user-activation/');
                $message = "Thank you for registering!\n\n";
                $message .= "To complete the activation of your account, please click the following link and then click the 'Activate' button:\n";
                $message .= $activation_url . "\n\n";
                $message .= "If the 'Activation Key' field is empty, copy and paste the following key into the field:\n";
                $message .= $activation_key;

                wp_mail($email, "Welcome to GuestCanPost!", $message);
                /****************** User Activation Key End **********************/
                wp_send_json(array(
                    'status'  => 'success',
                    'message' => ' User Registered Successfully !',
                    'url' => home_url('/user-activation/'),
                ));
            }
        }
    } catch (\Exception $e) {
        $response = array(
            'status' => 'error',
            'message' => $e->getMessage()
        );
        wp_send_json($response);
    }
}
add_action('wp_ajax_user_sign_up', 'user_sign_up');
add_action('wp_ajax_nopriv_user_sign_up', 'user_sign_up');

function user_login()
{
    try {
        $form_data = $_POST;

        if ((!isset($form_data['username']) && !isset($form_data['email'])) || !isset($form_data['password'])) {
            throw new Exception("Password is required and either username or email must be provided!");
        }

        // $username = $form_data['username'];
        // $email    = $form_data['email'];
        // $password = $form_data['password'];

        //added by Jalpa K. july-24,2025
        $username = isset($form_data['username']) ? sanitize_text_field($form_data['username']) : '';
        $email    = isset($form_data['email']) ? sanitize_email($form_data['email']) : '';
        $password = $form_data['password'];

        $loginName = !empty($username) ? $username : $email;

        // Attempt to get user by login or email
        $user = get_user_by('login', $loginName);
        if (!$user && is_email($loginName)) {
            $user = get_user_by('email', $loginName);
        }

        if (!$user) {
            throw new Exception("User not found.");
        }

        // Check activation status
        $is_activated = get_user_meta($user->ID, 'is_activated', true);
        if ($is_activated != 1) {
            throw new Exception("Account not activated. Please check your email to activate your account.");
        }

        // Proceed to authenticate
        // $creds = array(
        //     'user_login'    => $loginName,
        //     'user_password' => $password,
        //     'remember'      => true,
        // );

        $creds = array(
            'user_login'    => $user->user_login,
            'user_password' => $password,
            'remember'      => true,
        );

        $signon = wp_signon($creds, false);

        if (is_wp_error($signon)) {
            throw new Exception("Invalid username or password.");
        }

        // wp_signon() already handles authentication cookies properly
        // No need to clear and reset cookies manually
        wp_set_current_user($signon->ID);

        $url = home_url('/members/' . sanitize_title($signon->display_name));

        wp_send_json(array(
            'status' => 'success',
            'data' => array(
                'message' => '',
                'url' => $url
            )
        ));
    } catch (Exception $e) {
        wp_send_json(array(
            'status'  => 'error',
            'message' => $e->getMessage()
        ));
    }
}

add_action('wp_ajax_user_login', 'user_login');
add_action('wp_ajax_nopriv_user_login', 'user_login');

function check_if_username_or_email_exists()
{
    try {
        $messages = array();
        $types = array();
        $status = "success";

        if (isset($_POST['type'])) {
            if ($_POST['type'] == 'signup') {
                //check Email Exists
                if (isset($_POST['email']) && $_POST) {
                    $email = $_POST['email'];
                    if (!is_email($email)) {
                        $messages['email'] = "Enter a valid email";
                    }

                    $user = get_user_by('email', $email);
                    if ($user) {
                        $messages['email'] = "Email Address Already exists";
                        $types[] = 'email';
                        $status = "error";
                    }
                } else {
                    $messages['email'] = 'Email is Required !';
                }

                //check Username Exists
                if (isset($_POST['username']) && $_POST['username']) {
                    $username = $_POST['username'];
                    $user = get_user_by('login', $username);
                    if ($user) {
                        $messages['username'] = "Username Already exists";
                        $types[] = 'username';
                        $status = "error";
                    }
                } else {
                    $messages['username'] = 'Username is Required !';
                }
            }
        } else {
            throw new Exception("Invalid Request type");
        }
        $response = array(
            'status' => $status,
            'data' => array(
                'messages' => $messages,
                'types' => $types,
            )
        );
        wp_send_json($response);
    } catch (\Throwable $e) {
        $response = array(
            'status'  => 'error',
            'message' =>  $e->getMessage()
        );
        wp_send_json($response);
    }
}
add_action('wp_ajax_check_if_username_or_email_exists', 'check_if_username_or_email_exists');
add_action('wp_ajax_nopriv_check_if_username_or_email_exists', 'check_if_username_or_email_exists');

// function send_password_reset_request()
// {
//     try {

//         $status = "success";
//         if (!isset($_POST['email']) && !empty($_POST['email'])) {
//             throw new Exception("Please Enter Email it is Required !");
//         } else {
//         }
//     } catch (\Throwable $e) {
//         $response = array(
//             'status'  => 'error',
//             'message' => $e->getMessage()
//         );
//         wp_send_json($response);
//     }
// }
// add_action('wp_ajax_send_password_reset_request', 'send_password_reset_request');
// add_action('wp_ajax_nopriv_send_password_reset_request');

//method-2

// function send_password_reset_request()
// {
//     try {
//         $message = "";
//         $status = "success";
//         // Check if the email is set and not empty
//         if (isset($_POST['email']) && !empty($_POST['email'])) {
//             $email = $_POST['email'];

//             // Validate the email address
//             if (is_email($email)) {
//                 $user = get_user_by('email', $email);

//                 if ($user) {
//                     // Initiate the password reset process
//                     $reset_key = get_password_reset_key($user);
//                     if (is_wp_error($reset_key)) {
//                         //throw new Exception('Error generating the reset link.');
//                         $message = "Error generating the reset link ! ";
//                         $status = "error";
//                     } else {
//                         $reset_link = site_url('wp-login.php?action=rp&key=' . $reset_key . '&login=' . rawurlencode($user->user_login));
//                         $message = 'Click the following link to reset your password: ' . $reset_link;

//                         // Send email to the user
//                         wp_mail($user->user_email, 'Password Reset Request', $message);

//                         // Set success response
//                         //$response['status'] = 'success';
//                         $message = 'Please check your email for the password reset link.';
//                         $status = "success";
//                     }
//                 } else {
//                     $message = 'No user found with this email address.';
//                     $status = "error";
//                 }
//             } else {
//                 $message = 'The email address you entered is invalid.';
//                 $status = "error";
//             }
//         } else {
//             $message = 'Please enter your email address.';
//             $status = "error";
//         }
//         $response = array(
//             'status' => $status,
//             'message' => $message,
//         );
//         wp_send_json($response);
//     } catch (\Throwable $e) {
//         $response = array(
//             'status'  => 'error',
//             'message' => $e->getMessage()
//         );
//         wp_send_json($response);
//     }
// }
// add_action('wp_ajax_send_password_reset_request', 'send_password_reset_request');
// add_action('wp_ajax_nopriv_send_password_reset_request', 'send_password_reset_request');


function send_password_reset_request()
{
    try {
        $message = "";
        $status = "success";
        // Check if the email is set and not empty
        if (isset($_POST['email']) && !empty($_POST['email'])) {
            $email = $_POST['email'];

            // Validate the email address
            if (is_email($email)) {
                $user = get_user_by('email', $email);

                if ($user) {
                    // Initiate the password reset process
                    $reset_key = get_password_reset_key($user);
                    if (is_wp_error($reset_key)) {
                        // Error generating reset link
                        $message = "Error generating the reset link!";
                        $status = "error";
                    } else {
                        // Create a custom reset link to your custom password reset page
                        $reset_link = site_url('change-password/?key=' . $reset_key . '&login=' . rawurlencode($user->user_login));
                        $message = 'Click the following link to reset your password: ' . $reset_link;

                        // Send email to the user
                        wp_mail($user->user_email, 'Password Reset Request', $message);

                        // Success response
                        $message = 'Please check your email for the password reset link.';
                        $status = "success";
                    }
                } else {
                    $message = 'No user found with this email address.';
                    $status = "error";
                }
            } else {
                $message = 'The email address you entered is invalid.';
                $status = "error";
            }
        } else {
            $message = 'Please enter your email address.';
            $status = "error";
        }

        $response = array(
            'status' => $status,
            'message' => $message,
        );
        wp_send_json($response);
    } catch (\Throwable $e) {
        $response = array(
            'status'  => 'error',
            'message' => $e->getMessage()
        );
        wp_send_json($response);
    }
}

add_action('wp_ajax_send_password_reset_request', 'send_password_reset_request');
add_action('wp_ajax_nopriv_send_password_reset_request', 'send_password_reset_request');
