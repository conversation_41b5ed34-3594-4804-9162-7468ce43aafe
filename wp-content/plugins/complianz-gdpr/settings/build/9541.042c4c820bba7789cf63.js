"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[9541],{19541:(s,e,a)=>{a.r(e),a.d(e,{default:()=>i});var c=a(10790);const i=({type:s,className:e="",error:a="",...i})=>{if(s)return"email"===s?(0,c.jsxs)("div",{className:`cmplz-websitescan-input-wrapper ${s}`,children:[(0,c.jsx)("input",{className:`${e} cmplz-websitescan-input`,type:"email",...i}),a&&(0,c.jsx)("span",{className:"cmplz-websitescan-input-invalid",children:a})]}):"checkbox"===s?(0,c.jsx)("div",{className:`cmplz-websitescan-input-wrapper ${s}`,children:(0,c.jsx)("input",{className:`${e} cmplz-websitescan-input`,type:"checkbox",...i})}):null}}}]);