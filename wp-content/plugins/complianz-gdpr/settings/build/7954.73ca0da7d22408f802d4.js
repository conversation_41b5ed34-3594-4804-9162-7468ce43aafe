"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[7954],{27954:(e,n,u)=>{u.r(n),u.d(n,{default:()=>r});var a=u(86087),t=u(10790);const r=(0,a.memo)(({value:e,onChange:n,onError:u,required:r,disabled:i,id:l,name:p})=>{const s=l||p,[o,c]=(0,a.useState)("");return(0,a.useEffect)(()=>{e||(e=""),c(e)},[]),(0,a.useEffect)(()=>{if(o===e)return;const a=setTimeout(()=>{n(o),null===o.match(/^\+?[\d\-\(\)\.\s]*$/)&&u("invalid_phone")},500);return()=>{clearTimeout(a)}},[o]),(0,t.jsx)("div",{className:"cmplz-input-group cmplz-phone-input-group",children:(0,t.jsx)("input",{type:"tel",id:s,name:p,value:o,onChange:e=>(e=>{c(e)})(e.target.value),required:r,disabled:i,className:"cmplz-phone-input-group__input"})})})}}]);