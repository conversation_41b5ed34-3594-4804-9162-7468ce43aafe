"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[5207],{5207:(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});var o=i(81621),n=i(16535),d=i(9588),c=i(73710);const a=(0,o.vt)((e,t)=>({documentsLoaded:!1,savedDocument:{},conclusions:[],region:"",fileName:"",fetching:!1,updating:!1,loadingFields:!1,documents:[],regions:[],fields:[],editDocumentId:!1,resetEditDocumentId:t=>{e({editDocumentId:!1,region:""})},editDocument:async t=>{e({updating:!0}),await d.doAction("load_databreach_report",{id:t}).then(t=>{e({fields:t.fields,region:t.region,updating:!1,fileName:t.file_name})}).catch(e=>{console.error(e)}),e({editDocumentId:t})},setRegion:t=>{e({region:t})},updateField:(i,o)=>{let d=!1,a=!1;e((0,n.Ay)(e=>{e.fields.forEach(function(e,t){e.id===i&&(a=t,d=!0)}),!1!==a&&(e.fields[a].value=o)}));let s=(0,c.updateFieldsListWithConditions)(t().fields);e({fields:s})},save:async i=>{e({updating:!0});let o=t().editDocumentId,n=0;await d.doAction("save_databreach_report",{fields:t().fields,region:i,post_id:o}).then(t=>(n=t.post_id,e({updating:!1,conclusions:t.conclusions}),t)).catch(e=>{console.error(e)}),await t().fetchData();let c=t().documents.filter(e=>e.id===n);c.length>0&&e({savedDocument:c[0]})},deleteDocuments:async i=>{let o=t().documents.filter(e=>i.includes(e.id));e(e=>({documents:e.documents.filter(e=>!i.includes(e.id))}));let n={};n.documents=o,await d.doAction("delete_databreach_report",n).then(e=>e).catch(e=>{console.error(e)})},fetchData:async()=>{if(t().fetching)return;e({fetching:!0});const{documents:i,regions:o}=await d.doAction("get_databreach_reports",{}).then(e=>e).catch(e=>{console.error(e)});e(e=>({documentsLoaded:!0,documents:i,regions:o,fetching:!1}))},fetchFields:async t=>{let i={region:t};e({loadingFields:!0});const{fields:o}=await d.doAction("get_databreach_report_fields",i).then(e=>e).catch(e=>{console.error(e)});let n=(0,c.updateFieldsListWithConditions)(o);e(e=>({fields:n,loadingFields:!1}))}}))}}]);