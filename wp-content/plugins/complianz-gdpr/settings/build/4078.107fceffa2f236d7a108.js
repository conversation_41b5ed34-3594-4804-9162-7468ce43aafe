"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[2489,2921,4078,4759],{32489:(t,e,s)=>{s.r(e),s.d(e,{default:()=>c});var r=s(51609),i=s(10790);const c=(0,r.memo)(({value:t,onChange:e,required:s,disabled:c,id:n,name:l,placeholder:a})=>{const o=n||l,[d,p]=(0,r.useState)("");return(0,r.useEffect)(()=>{p(t||"")},[t]),(0,r.useEffect)(()=>{if(t===d)return;const s=setTimeout(()=>{e(d)},400);return()=>{clearTimeout(s)}},[d]),(0,i.jsx)("div",{className:"cmplz-input-group cmplz-text-input-group",children:(0,i.jsx)("input",{type:"text",id:o,name:l,value:d,onChange:t=>(t=>{p(t)})(t.target.value),required:s,disabled:c,className:"cmplz-text-input-group__input",placeholder:a})})})},32921:(t,e,s)=>{s.r(e),s.d(e,{default:()=>n});var r=s(27723),i=s(44124),c=s(10790);const n=t=>(0,c.jsxs)(c.Fragment,{children:[" ",(0,c.jsx)(i.default,{url:t,target:"_blank",rel:"noopener noreferrer",text:(0,r.__)("For more information, please read this %sarticle%s.","complianz-gdpr")})," "]})},34759:(t,e,s)=>{s.r(e),s.d(e,{default:()=>n});var r=s(81621),i=s(16535),c=s(9588);const n=(0,r.vt)((t,e)=>({integrationsLoaded:!1,fetching:!1,services:[],plugins:[],scripts:[],placeholders:[],blockedScripts:[],setScript:(e,s)=>{t((0,i.Ay)(t=>{if("block_script"===s){let s=t.blockedScripts;if(e.urls){for(const[t,r]of Object.entries(e.urls)){if(!r||0===r.length)continue;let t=!1;for(const[e,i]of Object.entries(s))r===e&&(t=!0);t||(s[r]=r)}t.blockedScripts=s}}const r=t.scripts[s].findIndex(t=>t.id===e.id);-1!==r&&(t.scripts[s][r]=e)}))},fetchIntegrationsData:async()=>{if(e().fetching)return;t({fetching:!0});const{services:s,plugins:r,scripts:i,placeholders:c,blocked_scripts:n}=await l();let a=i;a.block_script&&a.block_script.length>0&&a.block_script.forEach((t,e)=>{t.id=e}),a.add_script&&a.add_script.length>0&&a.add_script.forEach((t,e)=>{t.id=e}),a.whitelist_script&&a.whitelist_script.length>0&&a.whitelist_script.forEach((t,e)=>{t.id=e}),t(()=>({integrationsLoaded:!0,services:s,plugins:r,scripts:a,fetching:!1,placeholders:c,blockedScripts:n}))},addScript:s=>{t({fetching:!0}),e().scripts[s]&&Array.isArray(e().scripts[s])||t((0,i.Ay)(t=>{t.scripts[s]=[]})),t((0,i.Ay)(t=>{t.scripts[s].push({name:"general",id:t.scripts[s].length,enable:!0})}));let r=e().scripts;return c.doAction("update_scripts",{scripts:r}).then(e=>(t({fetching:!1}),e)).catch(t=>{console.error(t)})},saveScript:(s,r)=>{t({fetching:!0}),e().scripts[r]&&Array.isArray(e().scripts[r])||t((0,i.Ay)(t=>{t.scripts[r]=[]})),t((0,i.Ay)(t=>{const e=t.scripts[r].findIndex(t=>t.id===s.id);-1!==e&&(t.scripts[r][e]=s)}));let n=e().scripts;return c.doAction("update_scripts",{scripts:n}).then(e=>(t({fetching:!1}),e)).catch(t=>{console.error(t)})},deleteScript:(s,r)=>{t({fetching:!0}),e().scripts[r]&&Array.isArray(e().scripts[r])||t((0,i.Ay)(t=>{t.scripts[r]=[]})),t((0,i.Ay)(t=>{const e=t.scripts[r].findIndex(t=>t.id===s.id);-1!==e&&t.scripts[r].splice(e,1)}));let n=e().scripts;return c.doAction("update_scripts",{scripts:n}).then(e=>(t({fetching:!1}),e)).catch(t=>{console.error(t)})},updatePluginStatus:async(e,s)=>{t({fetching:!0}),t((0,i.Ay)(t=>{const r=t.plugins.findIndex(t=>t.id===e);-1!==r&&(t.plugins[r].enabled=s)}));const r=await c.doAction("update_plugin_status",{plugin:e,enabled:s}).then(t=>t).catch(t=>{console.error(t)});return t({fetching:!1}),r},updatePlaceholderStatus:async(e,s,r)=>{t({fetching:!0}),r&&t((0,i.Ay)(t=>{const r=t.plugins.findIndex(t=>t.id===e);-1!==r&&(t.plugins[r].placeholder=s?"enabled":"disabled")}));const n=await c.doAction("update_placeholder_status",{id:e,enabled:s}).then(t=>t).catch(t=>{console.error(t)});return t({fetching:!1}),n}})),l=()=>c.doAction("get_integrations_data",{}).then(t=>t).catch(t=>{console.error(t)})},44078:(t,e,s)=>{s.r(e),s.d(e,{default:()=>d});var r=s(27723),i=s(32921),c=s(32489),n=s(45111),l=s(34759),a=s(51609),o=s(10790);const d=t=>{const{setScript:e,fetching:s}=(0,l.default)(),[d,p]=(0,a.useState)(!1),u=t.script,h=t.type;(0,a.useEffect)(()=>{(u.hasOwnProperty("urls")?Object.values(u.urls):[""]).includes("")?p(!0):p(!1)},[u]);let g=u.hasOwnProperty("urls")?Object.entries(u.urls):[""];return(0,o.jsxs)("div",{className:"cmplz-details-row",children:[(0,o.jsxs)("label",{children:["block_script"===h&&(0,r.__)("URLs that should be blocked before consent.","complianz-gdpr"),"whitelist_script"===h&&(0,o.jsxs)(o.Fragment,{children:[(0,r.__)("URLs that should be whitelisted.","complianz-gdpr"),(0,i.default)("https://complianz.io/whitelisting-inline-script/")]})]}),g.map(([r,i],l)=>(0,o.jsxs)("div",{className:"cmplz-scriptcenter-url",children:[(0,o.jsx)(c.default,{disabled:s,value:i||"",onChange:s=>((s,r)=>{let i={...u},c={...i.urls};c[s]=r,i.urls=c,e(i,t.type)})(r,s),id:l+"_url",name:"url"}),0===l&&!d&&(0,o.jsxs)("button",{className:"button button-default",onClick:()=>(()=>{let s={...u},r=s.hasOwnProperty("urls")?{...s.urls}:[""];r[Object.keys(r).length+1]="",s.urls=r,e(s,t.type)})(),children:[" ",(0,o.jsx)(n.default,{name:"plus",size:14})]}),0!==l&&(0,o.jsxs)("button",{className:"button button-default",onClick:()=>(s=>{let r={...u},i={...r.urls};delete i[s],r.urls=i,e(r,t.type)})(r),children:[" ",(0,o.jsx)(n.default,{name:"minus",size:14})]})]},l))]})}}}]);