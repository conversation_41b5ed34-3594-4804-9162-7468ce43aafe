"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[1356,2010,2489,2921,4078,4759,5228,7320,9091,9713,9736,9758],{7320:(e,t,s)=>{s.r(t),s.d(t,{default:()=>n});var a=s(29243),r=s(86087),l=s(10790);const n=(0,r.memo)(({label:e,id:t,value:s,onChange:r,required:n,defaultValue:c,disabled:i,options:d={}})=>(0,l.jsx)(a.bL,{disabled:i&&!Array.isArray(i),className:"cmplz-input-group cmplz-radio-group",value:s,"aria-label":e,onValueChange:r,required:n,default:c,children:Object.entries(d).map(([e,s])=>(0,l.jsxs)("div",{className:"cmplz-radio-group__item",children:[(0,l.jsx)(a.q7,{disabled:Array.isArray(i)&&i.includes(e),value:e,id:t+"_"+e,children:(0,l.jsx)(a.C1,{className:"cmplz-radio-group__indicator"})}),(0,l.jsx)("label",{className:"cmplz-radio-label",htmlFor:t+"_"+e,children:s})]},e))}))},10800:(e,t,s)=>{s.r(t),s.d(t,{default:()=>w});var a=s(86087),r=s(51609),l=s(9957),n=s(91071),c=s(62133),i=s(81351),d=s(85357),o=s(54150),p=s(12579),u=s(10790),h="Switch",[m,f]=(0,c.A)(h),[b,g]=m(h),_=r.forwardRef((e,t)=>{const{__scopeSwitch:s,name:a,checked:c,defaultChecked:d,required:o,disabled:m,value:f="on",onCheckedChange:g,form:_,...x}=e,[j,k]=r.useState(null),z=(0,n.s)(t,e=>k(e)),C=r.useRef(!1),w=!j||_||!!j.closest("form"),[N,S]=(0,i.i)({prop:c,defaultProp:d??!1,onChange:g,caller:h});return(0,u.jsxs)(b,{scope:s,checked:N,disabled:m,children:[(0,u.jsx)(p.sG.button,{type:"button",role:"switch","aria-checked":N,"aria-required":o,"data-state":v(N),"data-disabled":m?"":void 0,disabled:m,value:f,...x,ref:z,onClick:(0,l.m)(e.onClick,e=>{S(e=>!e),w&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),w&&(0,u.jsx)(y,{control:j,bubbles:!C.current,name:a,value:f,checked:N,required:o,disabled:m,form:_,style:{transform:"translateX(-100%)"}})]})});_.displayName=h;var x="SwitchThumb",j=r.forwardRef((e,t)=>{const{__scopeSwitch:s,...a}=e,r=g(x,s);return(0,u.jsx)(p.sG.span,{"data-state":v(r.checked),"data-disabled":r.disabled?"":void 0,...a,ref:t})});j.displayName=x;var y=r.forwardRef(({__scopeSwitch:e,control:t,checked:s,bubbles:a=!0,...l},c)=>{const i=r.useRef(null),p=(0,n.s)(i,c),h=(0,d.Z)(s),m=(0,o.X)(t);return r.useEffect(()=>{const e=i.current;if(!e)return;const t=window.HTMLInputElement.prototype,r=Object.getOwnPropertyDescriptor(t,"checked").set;if(h!==s&&r){const t=new Event("click",{bubbles:a});r.call(e,s),e.dispatchEvent(t)}},[h,s,a]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...l,tabIndex:-1,ref:p,style:{...l.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function v(e){return e?"checked":"unchecked"}y.displayName="SwitchBubbleInput";var k=_,z=j,C=s(4219);const w=(0,a.memo)(({value:e,onChange:t,required:s,disabled:a,className:r,label:l,id:n})=>{const{getField:c}=(0,C.default)();let i=e;return"0"!==e&&"1"!==e||(i="1"===e),(0,u.jsx)("div",{className:"cmplz-input-group cmplz-switch-group",children:(0,u.jsx)(k,{className:"cmplz-switch-root "+r,checked:i,onCheckedChange:e=>{"banner"===c(n).data_target&&(e=e?"1":"0"),t(e)},disabled:a,required:s,children:(0,u.jsx)(z,{className:"cmplz-switch-thumb"})})})})},25228:(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});var a=s(86087),r=s(21366),l=s(45111),n=s(27723),c=s(10790);const i=(0,a.memo)(({value:e=!1,onChange:t,required:s,defaultValue:a,disabled:i,options:d={},canBeEmpty:o=!0,label:p})=>{if(Array.isArray(d)){let e={};d.map(t=>{e[t.value]=t.label}),d=e}return o?(""===e||!1===e||0===e)&&(e="0",d={0:(0,n.__)("Select an option","complianz-gdpr"),...d}):e||(e=Object.keys(d)[0]),(0,c.jsx)("div",{className:"cmplz-input-group cmplz-select-group",children:(0,c.jsxs)(r.bL,{value:e,defaultValue:a,onValueChange:t,required:s,disabled:i&&!Array.isArray(i),children:[(0,c.jsxs)(r.l9,{className:"cmplz-select-group__trigger",children:[(0,c.jsx)(r.WT,{}),(0,c.jsx)(l.default,{name:"chevron-down"})]}),(0,c.jsxs)(r.UC,{className:"cmplz-select-group__content",position:"popper",children:[(0,c.jsx)(r.PP,{className:"cmplz-select-group__scroll-button",children:(0,c.jsx)(l.default,{name:"chevron-up"})}),(0,c.jsx)(r.LM,{className:"cmplz-select-group__viewport",children:(0,c.jsx)(r.YJ,{children:Object.entries(d).map(([e,t])=>(0,c.jsx)(r.q7,{disabled:Array.isArray(i)&&i.includes(e),className:"cmplz-select-group__item",value:e,children:(0,c.jsx)(r.p4,{children:t})},e))})}),(0,c.jsx)(r.wn,{className:"cmplz-select-group__scroll-button",children:(0,c.jsx)(l.default,{name:"chevron-down"})})]})]})},p)})},31356:(e,t,s)=>{s.r(t),s.d(t,{default:()=>c});var a=s(7320),r=s(27723),l=s(34759),n=s(10790);const c=e=>{const{setScript:t,fetching:s}=(0,l.default)(),c=e.script,i={statistics:(0,r.__)("Statistics","complianz-gdpr"),marketing:(0,r.__)("Marketing","complianz-gdpr")};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("label",{children:(0,r.__)("Category","complianz-gdpr")}),(0,n.jsx)(a.default,{disabled:s,label:(0,r.__)("Marketing","complianz-gdpr"),id:"category",value:c.category,onChange:s=>(s=>{let a={...c};a.category=s,t(a,e.type)})(s),defaultValue:"marketing",options:i})]})}},32489:(e,t,s)=>{s.r(t),s.d(t,{default:()=>l});var a=s(51609),r=s(10790);const l=(0,a.memo)(({value:e,onChange:t,required:s,disabled:l,id:n,name:c,placeholder:i})=>{const d=n||c,[o,p]=(0,a.useState)("");return(0,a.useEffect)(()=>{p(e||"")},[e]),(0,a.useEffect)(()=>{if(e===o)return;const s=setTimeout(()=>{t(o)},400);return()=>{clearTimeout(s)}},[o]),(0,r.jsx)("div",{className:"cmplz-input-group cmplz-text-input-group",children:(0,r.jsx)("input",{type:"text",id:d,name:c,value:o,onChange:e=>(e=>{p(e)})(e.target.value),required:s,disabled:l,className:"cmplz-text-input-group__input",placeholder:i})})})},32921:(e,t,s)=>{s.r(t),s.d(t,{default:()=>n});var a=s(27723),r=s(44124),l=s(10790);const n=e=>(0,l.jsxs)(l.Fragment,{children:[" ",(0,l.jsx)(r.default,{url:e,target:"_blank",rel:"noopener noreferrer",text:(0,a.__)("For more information, please read this %sarticle%s.","complianz-gdpr")})," "]})},34759:(e,t,s)=>{s.r(t),s.d(t,{default:()=>n});var a=s(81621),r=s(16535),l=s(9588);const n=(0,a.vt)((e,t)=>({integrationsLoaded:!1,fetching:!1,services:[],plugins:[],scripts:[],placeholders:[],blockedScripts:[],setScript:(t,s)=>{e((0,r.Ay)(e=>{if("block_script"===s){let s=e.blockedScripts;if(t.urls){for(const[e,a]of Object.entries(t.urls)){if(!a||0===a.length)continue;let e=!1;for(const[t,r]of Object.entries(s))a===t&&(e=!0);e||(s[a]=a)}e.blockedScripts=s}}const a=e.scripts[s].findIndex(e=>e.id===t.id);-1!==a&&(e.scripts[s][a]=t)}))},fetchIntegrationsData:async()=>{if(t().fetching)return;e({fetching:!0});const{services:s,plugins:a,scripts:r,placeholders:l,blocked_scripts:n}=await c();let i=r;i.block_script&&i.block_script.length>0&&i.block_script.forEach((e,t)=>{e.id=t}),i.add_script&&i.add_script.length>0&&i.add_script.forEach((e,t)=>{e.id=t}),i.whitelist_script&&i.whitelist_script.length>0&&i.whitelist_script.forEach((e,t)=>{e.id=t}),e(()=>({integrationsLoaded:!0,services:s,plugins:a,scripts:i,fetching:!1,placeholders:l,blockedScripts:n}))},addScript:s=>{e({fetching:!0}),t().scripts[s]&&Array.isArray(t().scripts[s])||e((0,r.Ay)(e=>{e.scripts[s]=[]})),e((0,r.Ay)(e=>{e.scripts[s].push({name:"general",id:e.scripts[s].length,enable:!0})}));let a=t().scripts;return l.doAction("update_scripts",{scripts:a}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},saveScript:(s,a)=>{e({fetching:!0}),t().scripts[a]&&Array.isArray(t().scripts[a])||e((0,r.Ay)(e=>{e.scripts[a]=[]})),e((0,r.Ay)(e=>{const t=e.scripts[a].findIndex(e=>e.id===s.id);-1!==t&&(e.scripts[a][t]=s)}));let n=t().scripts;return l.doAction("update_scripts",{scripts:n}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},deleteScript:(s,a)=>{e({fetching:!0}),t().scripts[a]&&Array.isArray(t().scripts[a])||e((0,r.Ay)(e=>{e.scripts[a]=[]})),e((0,r.Ay)(e=>{const t=e.scripts[a].findIndex(e=>e.id===s.id);-1!==t&&e.scripts[a].splice(t,1)}));let n=t().scripts;return l.doAction("update_scripts",{scripts:n}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},updatePluginStatus:async(t,s)=>{e({fetching:!0}),e((0,r.Ay)(e=>{const a=e.plugins.findIndex(e=>e.id===t);-1!==a&&(e.plugins[a].enabled=s)}));const a=await l.doAction("update_plugin_status",{plugin:t,enabled:s}).then(e=>e).catch(e=>{console.error(e)});return e({fetching:!1}),a},updatePlaceholderStatus:async(t,s,a)=>{e({fetching:!0}),a&&e((0,r.Ay)(e=>{const a=e.plugins.findIndex(e=>e.id===t);-1!==a&&(e.plugins[a].placeholder=s?"enabled":"disabled")}));const n=await l.doAction("update_placeholder_status",{id:t,enabled:s}).then(e=>e).catch(e=>{console.error(e)});return e({fetching:!1}),n}})),c=()=>l.doAction("get_integrations_data",{}).then(e=>e).catch(e=>{console.error(e)})},44078:(e,t,s)=>{s.r(t),s.d(t,{default:()=>o});var a=s(27723),r=s(32921),l=s(32489),n=s(45111),c=s(34759),i=s(51609),d=s(10790);const o=e=>{const{setScript:t,fetching:s}=(0,c.default)(),[o,p]=(0,i.useState)(!1),u=e.script,h=e.type;(0,i.useEffect)(()=>{(u.hasOwnProperty("urls")?Object.values(u.urls):[""]).includes("")?p(!0):p(!1)},[u]);let m=u.hasOwnProperty("urls")?Object.entries(u.urls):[""];return(0,d.jsxs)("div",{className:"cmplz-details-row",children:[(0,d.jsxs)("label",{children:["block_script"===h&&(0,a.__)("URLs that should be blocked before consent.","complianz-gdpr"),"whitelist_script"===h&&(0,d.jsxs)(d.Fragment,{children:[(0,a.__)("URLs that should be whitelisted.","complianz-gdpr"),(0,r.default)("https://complianz.io/whitelisting-inline-script/")]})]}),m.map(([a,r],c)=>(0,d.jsxs)("div",{className:"cmplz-scriptcenter-url",children:[(0,d.jsx)(l.default,{disabled:s,value:r||"",onChange:s=>((s,a)=>{let r={...u},l={...r.urls};l[s]=a,r.urls=l,t(r,e.type)})(a,s),id:c+"_url",name:"url"}),0===c&&!o&&(0,d.jsxs)("button",{className:"button button-default",onClick:()=>(()=>{let s={...u},a=s.hasOwnProperty("urls")?{...s.urls}:[""];a[Object.keys(a).length+1]="",s.urls=a,t(s,e.type)})(),children:[" ",(0,d.jsx)(n.default,{name:"plus",size:14})]}),0!==c&&(0,d.jsxs)("button",{className:"button button-default",onClick:()=>(s=>{let a={...u},r={...a.urls};delete r[s],a.urls=r,t(a,e.type)})(a),children:[" ",(0,d.jsx)(n.default,{name:"minus",size:14})]})]},c))]})}},48033:(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});var a=s(27723),r=(s(10800),s(25228)),l=s(34759),n=s(81366),c=s(10790);const i=e=>{const{setScript:t,blockedScripts:s,fetching:i}=(0,l.default)(),d=s,o=e.script,p=e=>{if(!o.dependency||0===o.dependency.length)return"";let t=Object.entries(o.dependency);for(const[s,a]of t)if(s===e)return a;return""},u=(e,t)=>{let s={...e};for(const[e,a]of Object.entries(s))if(a===t){delete s[e];break}return s};let h=o.hasOwnProperty("urls")?Object.entries(o.urls):[""];return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,c.jsx)(n.default,{id:o.id+"dependency",disabled:i,value:o.enable_dependency,onChange:s=>(s=>{let a={...o};a.enable_dependency=s,t(a,e.type)})(s),options:{true:(0,a.__)("Enable dependency","complianz-gdpr")}})}),!!o.enable_dependency&&(0,c.jsxs)("div",{className:"cmplz-details-row cmplz-details-row",children:[h.length>1&&h.map(([s,l],n)=>(0,c.jsxs)("div",{className:"cmplz-scriptcenter-dependencies",children:[(0,c.jsx)(r.default,{disabled:i,value:p(l),options:u(d,l),onChange:s=>((s,a)=>{let r={...o},l={...r.dependency};l[a]=s,r.dependency=l,t(r,e.type)})(s,l)}),(0,c.jsxs)("div",{children:[(0,a.__)("waits for: ","complianz-gdpr"),l||(0,a.__)("Empty URL","complianz-gdpr")]})]},n)),h.length<=1&&(0,c.jsx)(c.Fragment,{children:(0,a.__)("Add a URL to create a dependency between two URLs","complianz-gdpr")})]})]})}},52010:(e,t,s)=>{s.r(t),s.d(t,{default:()=>n});var a=s(45111),r=s(86087),l=s(10790);const n=e=>{const[t,s]=(0,r.useState)(!1);return(0,l.jsx)("div",{className:"cmplz-panel__list__item",style:e.style?e.style:{},children:(0,l.jsxs)("details",{open:t,children:[(0,l.jsxs)("summary",{onClick:e=>(e=>{e.preventDefault(),s(!t)})(e),children:[e.icon&&(0,l.jsx)(a.default,{name:e.icon}),(0,l.jsx)("h5",{className:"cmplz-panel__list__item__title",children:e.summary}),(0,l.jsx)("div",{className:"cmplz-panel__list__item__comment",children:e.comment}),(0,l.jsx)("div",{className:"cmplz-panel__list__item__icons",children:e.icons}),(0,l.jsx)(a.default,{name:"chevron-down",size:18})]}),(0,l.jsx)("div",{className:"cmplz-panel__list__item__details",children:t&&e.details})]})})}},59736:(e,t,s)=>{s.r(t),s.d(t,{default:()=>f});var a=s(52010),r=s(27723),l=s(34759),n=s(89713),c=s(10800),i=s(86087),d=s(31356),o=s(60847),p=s(48033),u=s(44078),h=s(81366),m=s(10790);const f=(0,i.memo)(e=>{const{setScript:t,fetching:s,saveScript:i,deleteScript:f}=(0,l.default)(),b=e.script,g=(s,a)=>{let r={...b};r[a]=s,t(r,e.type),i(r,e.type)},_=(s,a)=>{let r={...b};r[a]=s,t(r,e.type)},x=()=>{i(b,e.type)},j=()=>{f(b,e.type)};return b?(0,m.jsx)(m.Fragment,{children:(0,m.jsx)(a.default,{summary:b.name,icons:(e=>(0,m.jsx)(m.Fragment,{children:(0,m.jsx)(c.default,{className:"cmplz-switch-input-tiny",onChange:e=>g(e,"enable"),value:e.enable})}))(b),details:((e,t)=>{const{fetching:s}=(0,l.default)();return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsxs)("div",{className:"cmplz-details-row",children:[(0,m.jsx)("label",{children:(0,r.__)("Name","complianz-gdpr")}),(0,m.jsx)("input",{disabled:s,onChange:e=>_(e.target.value,"name"),type:"text",placeholder:(0,r.__)("Name","complianz-gdpr"),value:e.name})]}),"add_script"===t&&(0,m.jsx)("div",{className:"cmplz-details-row",children:(0,m.jsx)(n.default,{disabled:s,onChange:e=>(e=>{_(e,"editor")})(e),placeholder:"Enter your script here",value:e.editor?e.editor:console.log("marketing enabled")})}),("block_script"===t||"whitelist_script"===t)&&(0,m.jsx)(u.default,{script:e,type:t}),"whitelist_script"!==t&&(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,m.jsx)(h.default,{id:e.id,disabled:s,value:e.async,onChange:e=>_(e,"async"),options:{true:(0,r.__)("This script contains an async attribute.","complianz-gdpr")}})}),(0,m.jsx)("div",{className:"cmplz-details-row",children:(0,m.jsx)(d.default,{script:e,type:t})}),(0,m.jsx)(o.default,{script:e,type:t})]}),"block_script"===t&&(0,m.jsxs)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:[(0,m.jsx)("label",{children:(0,r.__)("Dependency","complianz-gdpr")}),(0,m.jsx)(p.default,{script:e,type:t})]}),(0,m.jsxs)("div",{className:"cmplz-details-row cmplz-details-row__buttons",children:[(0,m.jsx)("button",{disabled:s,onClick:e=>x(),className:"button button-default",children:(0,r.__)("Save","complianz-gdpr")}),(0,m.jsx)("button",{disabled:s,className:"button button-default cmplz-reset-button",onClick:e=>j(),children:(0,r.__)("Delete","complianz-gdpr")})]})]})})(b,e.type)})}):(0,m.jsx)(a.default,{summary:"..."})})},60847:(e,t,s)=>{s.r(t),s.d(t,{default:()=>o}),s(10800);var a=s(32921),r=s(32489),l=s(25228),n=s(27723),c=s(34759),i=s(81366),d=s(10790);const o=e=>{const{setScript:t,fetching:s,placeholders:o}=(0,c.default)(),p=e.script,u=e.type,h=(s,a)=>{let r={...p};r[a]=s,t(r,e.type)};return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:[(0,d.jsx)("label",{children:(0,n.__)("Placeholder","complianz-gdpr")}),(0,d.jsx)(i.default,{id:p.id+"placeholder",disabled:s,value:p.enable_placeholder,onChange:e=>h(e,"enable_placeholder"),options:{true:(0,n.__)("Enable placeholder","complianz-gdpr")}})]}),!!p.enable_placeholder&&(0,d.jsxs)(d.Fragment,{children:["block_script"===u&&(0,d.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,d.jsx)(i.default,{id:p.id+"iframe",disabled:s,value:p.iframe||"",onChange:e=>h(e||"","iframe"),options:{true:(0,n.__)("The blocked content is an iframe","complianz-gdpr")}})}),!p.iframe&&(0,d.jsxs)("div",{className:"cmplz-details-row cmplz-details-row",children:[(0,d.jsxs)("p",{children:[(0,n.__)("Enter the div class or ID that should be targeted.","complianz-gdpr"),(0,a.default)("https://complianz.io/integrating-plugins/#placeholder/")]}),(0,d.jsx)(r.default,{disabled:s,value:p.placeholder_class||"",onChange:e=>h(e||"","placeholder_class"),name:"placeholder_class",placeholder:(0,n.__)("Your CSS class","complianz-gdpr")})]}),(0,d.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,d.jsx)(l.default,{disabled:s,value:p.placeholder?p.placeholder:"default",options:o,onChange:e=>h(e||"default","placeholder")})})]})]})}},79758:(e,t,s)=>{s.r(t),s.d(t,{default:()=>p});var a=s(86087),r=s(9588),l=s(4219),n=s(52043),c=s(56427),i=s(99091),d=s(32828),o=s(10790);const p=(0,a.memo)(({type:e="action",style:t="tertiary",label:s,onClick:p,href:u="",target:h="",disabled:m,action:f,field:b,children:g})=>{if(!s&&!g)return null;const _=(b&&b.button_text?b.button_text:s)||g,{fetchFieldsData:x,showSavedSettingsNotice:j}=(0,l.default)(),{setInitialLoadCompleted:y,setProgress:v}=(0,i.UseCookieScanData)(),{setProgressLoaded:k}=(0,d.default)(),{selectedSubMenuItem:z}=(0,n.default)(),[C,w]=(0,a.useState)(!1),N=`button cmplz-button button--${t} button-${e}`,S=async e=>{await r.doAction(b.action,{}).then(e=>{e.success&&(x(z),"reset_settings"===e.id&&(y(!1),v(0),k(!1)),j(e.message))})},A=b&&b.warn?b.warn:"";return"action"===e?(0,o.jsxs)(o.Fragment,{children:[c.__experimentalConfirmDialog&&(0,o.jsx)(c.__experimentalConfirmDialog,{isOpen:C,onConfirm:async()=>{w(!1),await S()},onCancel:()=>{w(!1)},children:A}),(0,o.jsx)("button",{className:N,onClick:async t=>{if("action"!==e||!p)return"action"===e&&f?c.__experimentalConfirmDialog?void(b&&b.warn?w(!0):await S()):void await S():void(window.location.href=b.url);p(t)},disabled:m,children:_})]}):"link"===e?(0,o.jsx)("a",{className:N,href:u,target:h,children:_}):void 0})},81366:(e,t,s)=>{s.r(t),s.d(t,{default:()=>O});var a=s(51609),r=s(91071),l=s(62133),n=s(9957),c=s(81351),i=s(85357),d=s(54150),o=s(7971),p=s(12579),u=s(10790),h="Checkbox",[m,f]=(0,l.A)(h),[b,g]=m(h);function _(e){const{__scopeCheckbox:t,checked:s,children:r,defaultChecked:l,disabled:n,form:i,name:d,onCheckedChange:o,required:p,value:m="on",internal_do_not_use_render:f}=e,[g,_]=(0,c.i)({prop:s,defaultProp:l??!1,onChange:o,caller:h}),[x,j]=a.useState(null),[y,v]=a.useState(null),k=a.useRef(!1),z=!x||!!i||!!x.closest("form"),C={checked:g,disabled:n,setChecked:_,control:x,setControl:j,name:d,form:i,value:m,hasConsumerStoppedPropagationRef:k,required:p,defaultChecked:!N(l)&&l,isFormControl:z,bubbleInput:y,setBubbleInput:v};return(0,u.jsx)(b,{scope:t,...C,children:w(f)?f(C):r})}var x="CheckboxTrigger",j=a.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:s,...l},c)=>{const{control:i,value:d,disabled:o,checked:h,required:m,setControl:f,setChecked:b,hasConsumerStoppedPropagationRef:_,isFormControl:j,bubbleInput:y}=g(x,e),v=(0,r.s)(c,f),k=a.useRef(h);return a.useEffect(()=>{const e=i?.form;if(e){const t=()=>b(k.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[i,b]),(0,u.jsx)(p.sG.button,{type:"button",role:"checkbox","aria-checked":N(h)?"mixed":h,"aria-required":m,"data-state":S(h),"data-disabled":o?"":void 0,disabled:o,value:d,...l,ref:v,onKeyDown:(0,n.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,n.m)(s,e=>{b(e=>!!N(e)||!e),y&&j&&(_.current=e.isPropagationStopped(),_.current||e.stopPropagation())})})});j.displayName=x;var y=a.forwardRef((e,t)=>{const{__scopeCheckbox:s,name:a,checked:r,defaultChecked:l,required:n,disabled:c,value:i,onCheckedChange:d,form:o,...p}=e;return(0,u.jsx)(_,{__scopeCheckbox:s,checked:r,defaultChecked:l,disabled:c,required:n,onCheckedChange:d,name:a,form:o,value:i,internal_do_not_use_render:({isFormControl:e})=>(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(j,{...p,ref:t,__scopeCheckbox:s}),e&&(0,u.jsx)(C,{__scopeCheckbox:s})]})})});y.displayName=h;var v="CheckboxIndicator",k=a.forwardRef((e,t)=>{const{__scopeCheckbox:s,forceMount:a,...r}=e,l=g(v,s);return(0,u.jsx)(o.C,{present:a||N(l.checked)||!0===l.checked,children:(0,u.jsx)(p.sG.span,{"data-state":S(l.checked),"data-disabled":l.disabled?"":void 0,...r,ref:t,style:{pointerEvents:"none",...e.style}})})});k.displayName=v;var z="CheckboxBubbleInput",C=a.forwardRef(({__scopeCheckbox:e,...t},s)=>{const{control:l,hasConsumerStoppedPropagationRef:n,checked:c,defaultChecked:o,required:h,disabled:m,name:f,value:b,form:_,bubbleInput:x,setBubbleInput:j}=g(z,e),y=(0,r.s)(s,j),v=(0,i.Z)(c),k=(0,d.X)(l);a.useEffect(()=>{const e=x;if(!e)return;const t=window.HTMLInputElement.prototype,s=Object.getOwnPropertyDescriptor(t,"checked").set,a=!n.current;if(v!==c&&s){const t=new Event("click",{bubbles:a});e.indeterminate=N(c),s.call(e,!N(c)&&c),e.dispatchEvent(t)}},[x,v,c,n]);const C=a.useRef(!N(c)&&c);return(0,u.jsx)(p.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:o??C.current,required:h,disabled:m,name:f,value:b,form:_,...t,tabIndex:-1,ref:y,style:{...t.style,...k,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function w(e){return"function"==typeof e}function N(e){return"indeterminate"===e}function S(e){return N(e)?"indeterminate":e?"checked":"unchecked"}C.displayName=z;var A=s(27723),L=s(45111),E=s(86087),I=s(79758);const O=(0,E.memo)(({indeterminate:e,label:t,value:s,id:a,onChange:r,required:l,disabled:n,options:c={}})=>{const[i,d]=(0,E.useState)(!1),[o,p]=(0,E.useState)(!1);let h=s;Array.isArray(h)||(h=""===h?[]:[h]),(0,E.useEffect)(()=>{let e=1===Object.keys(c).length&&"true"===Object.keys(c)[0];d(e)},[]),e&&(s=!0);const m=h;let f=!1;Object.keys(c).length>10&&(f=!0);const b=e=>i?s:m.includes(""+e)||m.includes(parseInt(e)),g=()=>{p(!o)};let _=n&&!Array.isArray(n);return 0===Object.keys(c).length?(0,u.jsx)(u.Fragment,{children:(0,A.__)("No options found","complianz-gdpr")}):(0,u.jsxs)("div",{className:"cmplz-checkbox-group",children:[Object.entries(c).map(([c,d],p)=>(0,u.jsxs)("div",{className:"cmplz-checkbox-group__item"+(!o&&p>9?" cmplz-hidden":""),children:[(0,u.jsx)(y,{className:"cmplz-checkbox-group__checkbox",id:a+"_"+c,checked:b(c),"aria-label":t,disabled:_||Array.isArray(n)&&n.includes(c),required:l,onCheckedChange:e=>((e,t)=>{if(i)r(!s);else{const e=m.includes(""+t)||m.includes(parseInt(t))?m.filter(e=>e!==""+t&&e!==parseInt(t)):[...m,t];r(e)}})(0,c),children:(0,u.jsx)(k,{className:"cmplz-checkbox-group__indicator",children:(0,u.jsx)(L.default,{name:e?"indeterminate":"check",size:14,color:"dark-blue"})})}),(0,u.jsx)("label",{className:"cmplz-checkbox-group__label",htmlFor:a+"_"+c,children:d})]},c)),!o&&f&&(0,u.jsx)(I.default,{onClick:()=>g(),children:(0,A.__)("Show more","complianz-gdpr")}),o&&f&&(0,u.jsx)(I.default,{onClick:()=>g(),children:(0,A.__)("Show less","complianz-gdpr")})]})})},89713:(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});var a=s(86087),r=s(70470),l=(s(43158),s(51289),s(20047),s(27723)),n=s(45111),c=s(10790);const i=(0,a.memo)(e=>{let t=e.mode?e.mode:"css",s=e.height?e.height:"200px",i=e.field&&e.field.default?e.field.default:e.placeholder;const[d,o]=(0,a.useState)(e.value),[p,u]=(0,a.useState)(!1);(0,a.useEffect)(()=>{if(d===e.value)return;const t=setTimeout(()=>{e.onChange(d)},500);return()=>{clearTimeout(t)}},[d]);let h=e.disabled?"cmplz-editor-disabled":"";return(0,c.jsxs)("div",{className:h,children:[p&&(0,c.jsxs)("div",{className:"cmplz-error-text",children:[(0,c.jsx)(n.default,{name:"error",size:13,color:"red"}),(0,c.jsx)("p",{children:(0,l.__)("Write your JavaScript without wrapping it in script tags.","complianz-gdpr")})]}),(0,c.jsx)(r.Ay,{readOnly:e.disabled,placeholder:"//"+i,mode:t,theme:"monokai",width:"100%",height:s,onChange:e=>(e=>{(e.includes("<script>")||e.includes("<\/script>"))&&u(!0),e=(e=e.replace(/<script>/gi,"")).replace(/<\/script>/gi,""),o(e)})(e),fontSize:12,showPrintMargin:!0,showGutter:!0,highlightActiveLine:!0,value:d,setOptions:{enableBasicAutocompletion:!1,enableLiveAutocompletion:!1,enableSnippets:!1,showLineNumbers:!0,tabSize:2,useWorker:!1}})]})})},99091:(e,t,s)=>{s.r(t),s.d(t,{UseCookieScanData:()=>l});var a=s(81621),r=s(9588);const l=(0,a.vt)((e,t)=>({initialLoadCompleted:!1,setInitialLoadCompleted:t=>e({initialLoadCompleted:t}),iframeLoaded:!1,loading:!1,nextPage:!1,progress:0,cookies:[],lastLoadedIframe:"",setIframeLoaded:t=>e({iframeLoaded:t}),setLastLoadedIframe:t=>e(e=>({lastLoadedIframe:t})),setProgress:t=>e({progress:t}),fetchProgress:()=>(e({loading:!0}),r.doAction("get_scan_progress",{}).then(t=>(e({initialLoadCompleted:!0,loading:!1,nextPage:t.next_page,progress:t.progress,cookies:t.cookies}),t)))}))}}]);