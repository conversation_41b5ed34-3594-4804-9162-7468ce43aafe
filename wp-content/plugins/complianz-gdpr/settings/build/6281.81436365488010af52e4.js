"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[1629,2489,4101,5228,6281],{25228:(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});var n=a(86087),l=a(21366),i=a(45111),s=a(27723),c=a(10790);const o=(0,n.memo)(({value:e=!1,onChange:t,required:a,defaultValue:n,disabled:o,options:r={},canBeEmpty:d=!0,label:p})=>{if(Array.isArray(r)){let e={};r.map(t=>{e[t.value]=t.label}),r=e}return d?(""===e||!1===e||0===e)&&(e="0",r={0:(0,s.__)("Select an option","complianz-gdpr"),...r}):e||(e=Object.keys(r)[0]),(0,c.jsx)("div",{className:"cmplz-input-group cmplz-select-group",children:(0,c.jsxs)(l.bL,{value:e,defaultValue:n,onValueChange:t,required:a,disabled:o&&!Array.isArray(o),children:[(0,c.jsxs)(l.l9,{className:"cmplz-select-group__trigger",children:[(0,c.jsx)(l.WT,{}),(0,c.jsx)(i.default,{name:"chevron-down"})]}),(0,c.jsxs)(l.UC,{className:"cmplz-select-group__content",position:"popper",children:[(0,c.jsx)(l.PP,{className:"cmplz-select-group__scroll-button",children:(0,c.jsx)(i.default,{name:"chevron-up"})}),(0,c.jsx)(l.LM,{className:"cmplz-select-group__viewport",children:(0,c.jsx)(l.YJ,{children:Object.entries(r).map(([e,t])=>(0,c.jsx)(l.q7,{disabled:Array.isArray(o)&&o.includes(e),className:"cmplz-select-group__item",value:e,children:(0,c.jsx)(l.p4,{children:t})},e))})}),(0,c.jsx)(l.wn,{className:"cmplz-select-group__scroll-button",children:(0,c.jsx)(i.default,{name:"chevron-down"})})]})]})},p)})},32489:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});var n=a(51609),l=a(10790);const i=(0,n.memo)(({value:e,onChange:t,required:a,disabled:i,id:s,name:c,placeholder:o})=>{const r=s||c,[d,p]=(0,n.useState)("");return(0,n.useEffect)(()=>{p(e||"")},[e]),(0,n.useEffect)(()=>{if(e===d)return;const a=setTimeout(()=>{t(d)},400);return()=>{clearTimeout(a)}},[d]),(0,l.jsx)("div",{className:"cmplz-input-group cmplz-text-input-group",children:(0,l.jsx)("input",{type:"text",id:r,name:c,value:d,onChange:e=>(e=>{p(e)})(e.target.value),required:a,disabled:i,className:"cmplz-text-input-group__input",placeholder:o})})})},74101:(e,t,a)=>{a.r(t),a.d(t,{upload:()=>l});var n=a(71083);const l=(e,t,a)=>{let l=new FormData;return l.append("data",t),void 0!==a&&l.append("details",JSON.stringify(a)),n.A.post(cmplz_settings.admin_url+"?page=complianz&cmplz_upload_file=1&action="+e,l,{headers:{"Content-Type":"multipart/form-data","X-WP-Nonce":cmplz_settings.nonce}})}},81629:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var n=a(81621),l=a(16535),i=a(9588),s=a(73710);const c=(0,n.vt)((e,t)=>({documentsLoaded:!1,region:"",fileName:"",serviceName:"",fetching:!1,updating:!1,loadingFields:!1,documents:[],regions:[],fields:[],editDocumentId:!1,resetEditDocumentId:t=>{e({editDocumentId:!1,region:"",serviceName:""})},editDocument:async t=>{e({updating:!0}),await i.doAction("load_processing_agreement",{id:t}).then(t=>{e({fields:t.fields,region:t.region,serviceName:t.serviceName,updating:!1,fileName:t.file_name})}).catch(e=>{console.error(e)}),e({editDocumentId:t})},setRegion:t=>{e({region:t})},setServiceName:t=>{e({serviceName:t})},updateField:(a,n)=>{let i=!1,c=!1;e((0,l.Ay)(e=>{e.fields.forEach(function(e,t){e.id===a&&(c=t,i=!0)}),!1!==c&&(e.fields[c].value=n)}));let o=(0,s.updateFieldsListWithConditions)(t().fields);e({fields:o})},save:async(a,n)=>{e({updating:!0});let l=t().editDocumentId;await i.doAction("save_processing_agreement",{fields:t().fields,region:a,serviceName:n,post_id:l}).then(t=>(e({updating:!1}),t)).catch(e=>{console.error(e)}),t().fetchData()},deleteDocuments:async a=>{let n=t().documents.filter(e=>a.includes(e.id));e(e=>({documents:e.documents.filter(e=>!a.includes(e.id))}));let l={};l.documents=n,await i.doAction("delete_processing_agreement",l).then(e=>e).catch(e=>{console.error(e)})},fetchData:async()=>{if(t().fetching)return;e({fetching:!0});const{documents:a,regions:n}=await i.doAction("get_processing_agreements",{}).then(e=>e).catch(e=>{console.error(e)});e(()=>({documentsLoaded:!0,documents:a,regions:n,fetching:!1}))},fetchFields:async t=>{let a={region:t};e({loadingFields:!0});const{fields:n}=await i.doAction("get_processing_agreement_fields",a).then(e=>e).catch(e=>{console.error(e)});let l=(0,s.updateFieldsListWithConditions)(n);e(e=>({fields:l,loadingFields:!1}))}}))},86281:(e,t,a)=>{a.r(t),a.d(t,{default:()=>h});var n=a(81629),l=a(86087),i=a(27723),s=a(56427),c=a(32636),o=a(4219),r=a(45111),d=a(74101),p=a(25228),u=a(32489),m=a(59387),g=a(10790);const h=(0,l.memo)(()=>{const{fields:e,fileName:t,fetching:a,loadingFields:h,updating:f,regions:_,resetEditDocumentId:b,fetchData:x,fetchFields:j,updateField:v,save:z,editDocumentId:N,region:y,setRegion:C,serviceName:F,setServiceName:w}=(0,n.default)(),[k,S]=(0,l.useState)(!0),[A,D]=(0,l.useState)(0),{allRequiredFieldsCompleted:I,fetchAllFieldsCompleted:q,fieldsLoaded:E,addHelpNotice:T,showSavedSettingsNotice:L,removeHelpNotice:R}=(0,o.default)();let P=React.createRef();const[V,W]=(0,l.useState)(!1),[H,O]=(0,l.useState)(!1),[U,Y]=(0,l.useState)(!0);(0,l.useEffect)(()=>{N&&P.current&&P.current.scrollIntoView({behavior:"smooth",block:"start"})},[N]),(0,l.useEffect)(()=>{q()},[E]),(0,l.useEffect)(()=>{S(""===y||""===F)},[y,F,a,N]),(0,l.useEffect)(()=>{(async()=>{V&&("application/pdf"!==V.type&&"application/doc"!==V.type&&"application/docx"!==V.type?(Y(!0),T("create-processing-agreements","warning",(0,i.__)("You can only upload .pdf, .doc or .docs files","complianz-gdpr"),(0,i.__)("Incorrect extension","complianz-gdpr"),!1)):(Y(!1),R("create-processing-agreements")),V&&S(!0))})()},[V]);const J=async()=>{await z(y,F),L()};(0,l.useEffect)(()=>{""===y||""===F||a||S(!1)},[y,F,a]);let M=e.filter(e=>e=>void 0===e.conditionallyDisabled||!1===e.conditionallyDisabled),B=Math.ceil(M.length/5),X=(e=>{const t=5*(A-1),a=t+5;return e.slice(t,a)})(e);return(0,g.jsxs)(g.Fragment,{children:[!I&&(0,g.jsx)("div",{className:"cmplz-locked",children:(0,g.jsxs)("div",{className:"cmplz-locked-overlay",children:[(0,g.jsx)("span",{className:"cmplz-task-status cmplz-warning",children:(0,i.__)("Incomplete","complianz-gdpr")}),(0,g.jsx)("span",{children:(0,i.__)("The wizard has not been completed yet, but this field requires information from the wizard. Please complete the wizard first.","complianz-gdpr")})]})}),0===A&&(0,g.jsxs)(g.Fragment,{children:[N&&(0,g.jsx)("div",{className:"cmplz-selected-document",children:t}),(0,g.jsx)(m.default,{id:"region_for_processing_agreement",label:(0,i.__)("Region","complianz-gdpr"),required:!0,type:"select"}),(0,g.jsx)(p.default,{innerRef:P,disabled:f,onChange:e=>C(e),options:_,value:y,required:!0}),(0,g.jsx)(m.default,{id:"servicename_for_processing_agreement",label:(0,i.__)("Service name","complianz-gdpr"),required:!0,type:"text"}),(0,g.jsx)(u.default,{placeholder:(0,i.__)("e.g. Alphabet Inc","complianz-gdpr"),onChange:e=>w(e),value:F||"",disabled:f,required:!0}),(0,g.jsx)("div",{className:"cmplz-table-header",children:(0,g.jsxs)("div",{className:"cmplz-table-header-controls",children:[N&&(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("button",{disabled:f,className:"button button-default",onClick:()=>{b(),D(0)},children:(0,i.__)("Cancel","complianz-gdpr")}),(0,g.jsx)("button",{disabled:f,className:"button button-primary",onClick:()=>D(A+1),children:(0,i.__)("Next","complianz-gdpr")}),(0,g.jsx)("button",{disabled:f,className:"button button-primary",onClick:()=>J(),children:(0,i.__)("Save","complianz-gdpr")})]}),!N&&(0,g.jsxs)(g.Fragment,{children:[V&&V.name,(0,g.jsx)(s.FormFileUpload,{accept:"",icon:(0,g.jsx)(r.default,{name:"upload",color:"black"}),onChange:e=>W(e.currentTarget.files[0]),children:(0,i.__)("Select file","complianz-gdpr")}),(0,g.jsxs)("button",{disabled:U,className:"button button-default",onClick:e=>(Y(!0),O(!0),void(0,d.upload)("upload_processing_agreement",V,{region:y,serviceName:F}).then(e=>(e.data.upload_success?L((0,i.__)("Settings imported","complianz-gdpr")):T("import_settings","warning",(0,i.__)("You can only upload .json files","complianz-gdpr"),(0,i.__)("Incorrect extension","complianz-gdpr"),!1),O(!1),W(!1),b(),x(),!0)).catch(e=>{console.error(e)})),children:[(0,i.__)("Upload","complianz-gdpr"),H&&(0,g.jsx)(r.default,{name:"loading",color:"grey"})]}),(0,g.jsxs)("button",{disabled:k||h,className:"button cmplz-button button-primary",onClick:()=>(async()=>{await j(y),D(1)})(),children:[(0,i.__)("Create","complianz-gdpr"),h&&(0,g.jsx)(r.default,{name:"loading",color:"grey"})]})]})]})})]}),A>0&&(0,g.jsxs)(g.Fragment,{children:[A<=B&&X.map((e,t)=>(0,g.jsx)(c.default,{index:t,field:e,isCustomField:!0,customChangeHandler:(e,t)=>((e,t)=>{v(e,t)})(e,t)},t)),(0,g.jsx)("div",{className:"cmplz-table-header",children:(0,g.jsxs)("div",{className:"cmplz-table-header-controls",children:[(0,g.jsx)("button",{disabled:f,className:"button button-default",onClick:()=>{b(),D(0)},children:(0,i.__)("Cancel","complianz-gdpr")}),(0,g.jsx)("button",{className:"button button-default",onClick:()=>D(A-1),children:(0,i.__)("Previous","complianz-gdpr")}),A<B&&(0,g.jsx)(g.Fragment,{children:(0,g.jsx)("button",{className:"button button-primary",onClick:()=>D(A+1),children:(0,i.__)("Next","complianz-gdpr")})}),A===B&&(0,g.jsx)(g.Fragment,{children:(0,g.jsxs)("button",{className:"button button-primary",onClick:()=>(async()=>{await z(y,F),D(0),L(),b()})(),children:[(0,i.__)("Finish","complianz-gdpr"),f&&(0,g.jsx)(r.default,{name:"loading",color:"grey"})]})}),N&&A<B&&(0,g.jsx)(g.Fragment,{children:(0,g.jsx)("button",{disabled:f,className:"button button-primary",onClick:()=>J(),children:(0,i.__)("Save","complianz-gdpr")})})]})})]})]})})}}]);