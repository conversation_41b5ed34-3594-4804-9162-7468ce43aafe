"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[4759],{34759:(t,s,i)=>{i.r(s),i.d(s,{default:()=>n});var c=i(81621),e=i(16535),r=i(9588);const n=(0,c.vt)((t,s)=>({integrationsLoaded:!1,fetching:!1,services:[],plugins:[],scripts:[],placeholders:[],blockedScripts:[],setScript:(s,i)=>{t((0,e.Ay)(t=>{if("block_script"===i){let i=t.blockedScripts;if(s.urls){for(const[t,c]of Object.entries(s.urls)){if(!c||0===c.length)continue;let t=!1;for(const[s,e]of Object.entries(i))c===s&&(t=!0);t||(i[c]=c)}t.blockedScripts=i}}const c=t.scripts[i].findIndex(t=>t.id===s.id);-1!==c&&(t.scripts[i][c]=s)}))},fetchIntegrationsData:async()=>{if(s().fetching)return;t({fetching:!0});const{services:i,plugins:c,scripts:e,placeholders:r,blocked_scripts:n}=await p();let o=e;o.block_script&&o.block_script.length>0&&o.block_script.forEach((t,s)=>{t.id=s}),o.add_script&&o.add_script.length>0&&o.add_script.forEach((t,s)=>{t.id=s}),o.whitelist_script&&o.whitelist_script.length>0&&o.whitelist_script.forEach((t,s)=>{t.id=s}),t(()=>({integrationsLoaded:!0,services:i,plugins:c,scripts:o,fetching:!1,placeholders:r,blockedScripts:n}))},addScript:i=>{t({fetching:!0}),s().scripts[i]&&Array.isArray(s().scripts[i])||t((0,e.Ay)(t=>{t.scripts[i]=[]})),t((0,e.Ay)(t=>{t.scripts[i].push({name:"general",id:t.scripts[i].length,enable:!0})}));let c=s().scripts;return r.doAction("update_scripts",{scripts:c}).then(s=>(t({fetching:!1}),s)).catch(t=>{console.error(t)})},saveScript:(i,c)=>{t({fetching:!0}),s().scripts[c]&&Array.isArray(s().scripts[c])||t((0,e.Ay)(t=>{t.scripts[c]=[]})),t((0,e.Ay)(t=>{const s=t.scripts[c].findIndex(t=>t.id===i.id);-1!==s&&(t.scripts[c][s]=i)}));let n=s().scripts;return r.doAction("update_scripts",{scripts:n}).then(s=>(t({fetching:!1}),s)).catch(t=>{console.error(t)})},deleteScript:(i,c)=>{t({fetching:!0}),s().scripts[c]&&Array.isArray(s().scripts[c])||t((0,e.Ay)(t=>{t.scripts[c]=[]})),t((0,e.Ay)(t=>{const s=t.scripts[c].findIndex(t=>t.id===i.id);-1!==s&&t.scripts[c].splice(s,1)}));let n=s().scripts;return r.doAction("update_scripts",{scripts:n}).then(s=>(t({fetching:!1}),s)).catch(t=>{console.error(t)})},updatePluginStatus:async(s,i)=>{t({fetching:!0}),t((0,e.Ay)(t=>{const c=t.plugins.findIndex(t=>t.id===s);-1!==c&&(t.plugins[c].enabled=i)}));const c=await r.doAction("update_plugin_status",{plugin:s,enabled:i}).then(t=>t).catch(t=>{console.error(t)});return t({fetching:!1}),c},updatePlaceholderStatus:async(s,i,c)=>{t({fetching:!0}),c&&t((0,e.Ay)(t=>{const c=t.plugins.findIndex(t=>t.id===s);-1!==c&&(t.plugins[c].placeholder=i?"enabled":"disabled")}));const n=await r.doAction("update_placeholder_status",{id:s,enabled:i}).then(t=>t).catch(t=>{console.error(t)});return t({fetching:!1}),n}})),p=()=>r.doAction("get_integrations_data",{}).then(t=>t).catch(t=>{console.error(t)})}}]);