"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[7511,9487],{7511:(a,t,e)=>{e.r(t),e.d(t,{default:()=>l});var s=e(81621),i=e(9588);const l=(0,s.vt)((a,t)=>({apiRequestActive:!1,pluginAction:"status",wordPressUrl:"#",upgradeUrl:"#",rating:[],statusLoaded:!1,setStatusLoaded:t=>{a({statusLoaded:t})},startPluginAction:(e,s)=>{let l={};a({apiRequestActive:!0}),l.pluginAction=void 0!==s?s:t().pluginAction,l.slug=e;let n=!1;"download"===l.pluginAction&&(n="activate"),i.doAction("plugin_actions",l).then(s=>{a({pluginAction:s.pluginAction,wordPressUrl:s.wordpress_url,upgradeUrl:s.upgrade_url});let i=Math.round(s.star_rating.rating/10,0)/2;a({rating:i,ratingCount:s.star_rating.rating_count,apiRequestActive:!1,statusLoaded:!0}),"activate"===n&&"installed"!==s.pluginAction&&t().startPluginAction(e,s.pluginAction)})}}))},29487:(a,t,e)=>{e.r(t),e.d(t,{default:()=>d});var s=e(86087),i=e(45111),l=e(27723),n=e(7511),r=e(10790);const d=(0,s.memo)(({field:a})=>{const{statusLoaded:t,startPluginAction:e,apiRequestActive:d,pluginAction:u,rating:g,upgradeUrl:c,setStatusLoaded:p}=(0,n.default)(),o=a.plugin_data.title,m=a.plugin_data.summary,_=a.plugin_data.description,h=a.plugin_data.image;(0,s.useEffect)(()=>{t||e(a.plugin_data.slug,"status")},[t]),(0,s.useEffect)(()=>{p(!1)},[a.plugin_data.slug]);const v=a=>{let t=[];for(let e=1;e<=a;e++)t.push(e);return t};let z,A=d;switch(u){case"upgrade-to-premium":z=(0,l.__)("Upgrade","complianz-gdpr");break;case"activate":z=d?(0,l.__)("Activating","complianz-gdpr"):(0,l.__)("Activate","complianz-gdpr");break;case"download":z=d?(0,l.__)("Installing","complianz-gdpr"):(0,l.__)("Install","complianz-gdpr");break;default:A=!0,z=t?(0,l.__)("Installed","complianz-gdpr"):(0,l.__)("Checking status","complianz-gdpr")}return(0,r.jsxs)("div",{className:"cmplz-suggested-plugin",children:[(0,r.jsx)("img",{className:"cmplz-suggested-plugin-img",src:cmplz_settings.plugin_url+"/upgrade/img/"+h}),(0,r.jsxs)("div",{className:"cmplz-suggested-plugin-group",children:[(0,r.jsx)("div",{className:"cmplz-suggested-plugin-group-title",children:o}),(0,r.jsx)("div",{className:"cmplz-suggested-plugin-group-desc",children:m}),(0,r.jsx)("div",{className:"cmplz-suggested-plugin-group-rating",children:(()=>{let a=Math.floor(g),t=Math.ceil(g-a),e=5-a-t;return a=v(a),t=v(t),e=v(e),(0,r.jsxs)("div",{className:"star-rating",children:[(0,r.jsx)("span",{className:"screen-reader-text",children:(0,l.__)("%s rating based on %d ratings","complianz-gdpr").replace("%s","5").replace("%d","84")}),a.map((a,t)=>(0,r.jsx)("div",{className:"star star-full","aria-hidden":"true"},t)),t.map((a,t)=>(0,r.jsx)("div",{className:"star star-half","aria-hidden":"true"},t)),e.map((a,t)=>(0,r.jsx)("div",{className:"star star-empty","aria-hidden":"true"},t))]})})()})]}),(0,r.jsx)("div",{className:"cmplz-suggested-plugin-desc-long",children:_}),(0,r.jsxs)("div",{children:["upgrade-to-premium"!==u&&(0,r.jsxs)("button",{type:"button",disabled:A,onClick:t=>{e(a.plugin_data.slug)},className:"button-secondary cmplz-install-plugin",children:[z,d&&(0,r.jsx)(i.default,{name:"loading",color:"grey"})]}),"upgrade-to-premium"===u&&(0,r.jsx)("a",{target:"_blank",rel:"noopener noreferrer",href:c,type:"button",className:"button-secondary cmplz-install-plugin",children:z})]})]})})}}]);