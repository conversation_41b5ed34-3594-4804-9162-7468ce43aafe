"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[9179],{99179:(e,s,l)=>{l.r(s),l.d(s,{default:()=>o});var n=l(86087),t=l(10790);class a extends n.Component{constructor(){super(...arguments)}onChangeHandler(e){let s=this.props.fields,l=this.props.field;s[this.props.index].value=e,setChangedField(l.id,e),this.setState({fields:s})}render(){let e=this.props.field,s=e.value;return this.props.fields,(0,t.jsx)("div",{className:"components-base-control",children:(0,t.jsxs)("div",{className:"components-base-control__field",children:[(0,t.jsx)("label",{className:"components-base-control__label",htmlFor:e.id,children:e.label}),(0,t.jsx)("input",{className:"components-text-control__input",type:"password",id:e.id,value:s,onChange:e=>this.onChangeHandler(e.target.value)})]})})}}const o=a}}]);