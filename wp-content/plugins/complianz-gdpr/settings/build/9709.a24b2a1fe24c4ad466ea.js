"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[7771,9709],{17771:(e,n,t)=>{t.r(n),t.d(n,{UseMenuData:()=>i});var c=t(81621),u=t(9588),a=t(16535),o=t(55446),d=t(27723);const i=(0,c.vt)((e,n)=>({menuDataLoaded:!1,saving:!1,menu:[],menuChanged:!1,changedMenuType:"per_document",emptyMenuLink:"#",requiredDocuments:[],createdDocuments:[],genericDocuments:[],documentsNotInMenu:[],pageTypes:[],regions:[],fetchMenuData:async()=>{const n=await s(!1);let t=n.required_documents.filter(e=>e.page_id);e({menuDataLoaded:!0,emptyMenuLink:n.empty_menu_link,menu:n.menu,requiredDocuments:n.required_documents,genericDocuments:n.generic_documents_list,createdDocuments:t,pageTypes:n.page_types,documentsNotInMenu:n.documents_not_in_menu,regions:n.regions})},updateMenu:(n,t)=>{let c=isNaN(n)?"per_type":"per_document";e({menuType:c}),e("per_type"===c?(0,a.Ay)(e=>{let c=e.genericDocuments.findIndex(function(e,t){return e.page_id===n||e.type===n}),u=e.createdDocuments.findIndex(function(e,t){return e.page_id===n||e.type===n});-1!==c&&(e.genericDocuments[c].menu_id=t,-1!==u&&(e.createdDocuments[u].menu_id=-1),e.menuChanged=!0)}):(0,a.Ay)(e=>{let c=e.genericDocuments.findIndex(function(e,t){return e.page_id===n||e.type===n}),u=e.createdDocuments.findIndex(function(e,t){return e.page_id===n||e.type===n});-1!==u&&(e.createdDocuments[u].menu_id=t,-1!==c&&(e.genericDocuments[c].menu_id=-1),e.menuChanged=!0)}))},saveDocumentsMenu:async(t,c)=>{if(e({saving:!0}),n().menuChanged||t){let t={};t.genericDocuments=n().genericDocuments.filter(e=>e.can_region_redirect),t.createdDocuments=n().createdDocuments;const a=u.doAction("save_documents_menu_data",t).then(n=>(e({saving:!1}),n)).catch(e=>{console.error(e)});c&&o.toast.promise(a,{pending:(0,d.__)("Saving menu...","complianz-gdpr"),success:(0,d.__)("Menu saved","complianz-gdpr"),error:(0,d.__)("Something went wrong","complianz-gdpr")})}else c&&o.toast.info((0,d.__)("Settings have not been changed","complianz-gdpr"))}})),s=()=>u.doAction("documents_menu_data",{generate:!1}).then(e=>e).catch(e=>{console.error(e)})},49709:(e,n,t)=>{t.r(n),t.d(n,{default:()=>d});var c=t(17771),u=t(27723),a=t(86087),o=t(10790);const d=(0,a.memo)(e=>{const{menu:n,updateMenu:t}=(0,c.UseMenuData)();return(0,o.jsxs)("div",{className:"cmplz-single-document-menu",children:[(0,o.jsx)("div",{className:"cmplz-document-menu-title",children:e.document.title}),(0,o.jsxs)("select",{value:e.document.menu_id,onChange:n=>(n=>{t(e.document.page_id,n.target.value)})(n),children:[(0,o.jsx)("option",{value:-1,children:(0,u.__)("Select a menu","complianz-gdpr")},-1),n.map((e,n)=>(0,o.jsx)("option",{value:e.id,children:e.label},n))]})]})})}}]);