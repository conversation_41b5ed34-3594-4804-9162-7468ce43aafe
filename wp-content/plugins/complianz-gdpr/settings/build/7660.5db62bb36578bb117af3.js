"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[7660],{95279:(e,t,a)=>{a.r(t),a.d(t,{default:()=>z});var n=a(86087),l=a(85261),d=a(30020),r=a(1806),s=a(31127),o=a(66212),u=a(92998),c=a(20543),i=a(2118),D=a(19312),f=a(17054),y=a(49317),g=a(53039),p=a(81810),m=a(72346),h=a(45111),w=a(27723),b=a(38432),_=a(10790);const z=()=>{const[e,t]=(0,n.useState)(null),a=Boolean(e),z=(0,b.default)(e=>e.startDate),M=(0,b.default)(e=>e.endDate),j=(0,b.default)(e=>e.setStartDate),k=(0,b.default)(e=>e.setEndDate),v=(0,b.default)(e=>e.range),x=(0,b.default)(e=>e.setRange),A={startDate:(0,r.A)(z),endDate:(0,r.A)(M),key:"selection"},C=(0,n.useRef)(0),S=["today","yesterday","last-7-days","last-30-days","last-90-days","last-month","last-year","year-to-date"],L={today:{label:(0,w.__)("Today","complianz-gdpr"),range:()=>({startDate:(0,s.default)(new Date),endDate:(0,o.default)(new Date)})},yesterday:{label:(0,w.__)("Yesterday","complianz-gdpr"),range:()=>({startDate:(0,s.default)((0,u.default)(new Date,-1)),endDate:(0,o.default)((0,u.default)(new Date,-1))})},"last-7-days":{label:(0,w.__)("Last 7 days","complianz-gdpr"),range:()=>({startDate:(0,s.default)((0,u.default)(new Date,-7)),endDate:(0,o.default)((0,u.default)(new Date,-1))})},"last-30-days":{label:(0,w.__)("Last 30 days","complianz-gdpr"),range:()=>({startDate:(0,s.default)((0,u.default)(new Date,-30)),endDate:(0,o.default)((0,u.default)(new Date,-1))})},"last-90-days":{label:(0,w.__)("Last 90 days","complianz-gdpr"),range:()=>({startDate:(0,s.default)((0,u.default)(new Date,-90)),endDate:(0,o.default)((0,u.default)(new Date,-1))})},"last-month":{label:(0,w.__)("Last month","complianz-gdpr"),range:()=>({startDate:(0,c.default)((0,i.default)(new Date,-1)),endDate:(0,D.default)((0,i.default)(new Date,-1))})},"year-to-date":{label:(0,w.__)("Year to date","complianz-gdpr"),range:()=>({startDate:(0,f.A)(new Date),endDate:(0,o.default)(new Date)})},"last-year":{label:(0,w.__)("Last year","complianz-gdpr"),range:()=>({startDate:(0,f.A)((0,y.default)(new Date,-1)),endDate:(0,g.A)((0,y.default)(new Date,-1))})}};function O(e){const t=this.range();return(0,p.default)(e.startDate,t.startDate)&&(0,p.default)(e.endDate,t.endDate)}const R=[];for(const[e,t]of Object.entries(S))t&&(R.push(L[t]),R[R.length-1].isSelected=O);const T=e=>{t(null)},E="MMMM d, yyyy",F=z?(0,m.default)(new Date(z),E):(0,m.default)(defaultStart,E),N=M?(0,m.default)(new Date(M),E):(0,m.default)(defaultEnd,E);return(0,_.jsxs)("div",{className:"cmplz-date-range-container",children:[(0,_.jsxs)("button",{onClick:e=>{t(e.currentTarget)},id:"cmplz-date-range-picker-open-button",children:[(0,_.jsx)(h.default,{name:"calendar",size:"18"}),"custom"===v&&F+" - "+N,"custom"!==v&&L[v].label,(0,_.jsx)(h.default,{name:"chevron-down"})]}),(0,_.jsx)(l.Ay,{anchorEl:e,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},open:a,onClose:T,className:"burst",children:(0,_.jsx)("div",{id:"cmplz-date-range-picker-container",children:(0,_.jsx)(d.Ur,{ranges:[A],rangeColors:["var(--rsp-brand-primary)"],dateDisplayFormat:E,monthDisplayFormat:"MMMM",onChange:e=>{(e=>{C.current++;let t=(0,m.default)(e.selection.startDate,"yyyy-MM-dd"),a=(0,m.default)(e.selection.endDate,"yyyy-MM-dd"),n="custom";for(const[t,a]of Object.entries(L))a.isSelected(e.selection)&&(n=t);e.selection.startDate,e.selection.endDate,2!==C.current&&t===a&&"custom"===n||(C.current=0,j(t),k(a),x(n),T())})(e)},inputRanges:[],showSelectionPreview:!0,months:2,direction:"horizontal",minDate:new Date(2022,0,1),maxDate:new Date,staticRanges:R})})})]})}}}]);