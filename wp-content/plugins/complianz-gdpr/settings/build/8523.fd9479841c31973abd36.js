"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[8523],{7971:(e,n,t)=>{t.d(n,{C:()=>u});var r=t(51609),o=t(91071),i=t(88200),u=e=>{const{present:n,children:t}=e,u=function(e){const[n,t]=r.useState(),o=r.useRef(null),u=r.useRef(e),s=r.useRef("none"),l=e?"mounted":"unmounted",[a,f]=function(e,n){return r.useReducer((e,t)=>n[e][t]??e,e)}(l,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return r.useEffect(()=>{const e=c(o.current);s.current="mounted"===a?e:"none"},[a]),(0,i.N)(()=>{const n=o.current,t=u.current;if(t!==e){const r=s.current,o=c(n);f(e?"MOUNT":"none"===o||"none"===n?.display?"UNMOUNT":t&&r!==o?"ANIMATION_OUT":"UNMOUNT"),u.current=e}},[e,f]),(0,i.N)(()=>{if(n){let e;const t=n.ownerDocument.defaultView??window,r=r=>{const i=c(o.current).includes(r.animationName);if(r.target===n&&i&&(f("ANIMATION_END"),!u.current)){const r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)})}},i=e=>{e.target===n&&(s.current=c(o.current))};return n.addEventListener("animationstart",i),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",i),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}f("ANIMATION_END")},[n,f]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:r.useCallback(e=>{o.current=e?getComputedStyle(e):null,t(e)},[])}}(n),s="function"==typeof t?t({present:u.isPresent}):r.Children.only(t),l=(0,o.s)(u.ref,function(e){let n=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=n&&"isReactWarning"in n&&n.isReactWarning;return t?e.ref:(n=Object.getOwnPropertyDescriptor(e,"ref")?.get,t=n&&"isReactWarning"in n&&n.isReactWarning,t?e.props.ref:e.props.ref||e.ref)}(s));return"function"==typeof t||u.isPresent?r.cloneElement(s,{ref:l}):null};function c(e){return e?.animationName||"none"}u.displayName="Presence"},9957:(e,n,t)=>{function r(e,n,{checkForDefaultPrevented:t=!0}={}){return function(r){if(e?.(r),!1===t||!r.defaultPrevented)return n?.(r)}}t.d(n,{m:()=>r})},12579:(e,n,t)=>{t.d(n,{hO:()=>s,sG:()=>c});var r=t(51609),o=t(75795),i=t(33362),u=t(10790),c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,n)=>{const t=(0,i.TL)(`Primitive.${n}`),o=r.forwardRef((e,r)=>{const{asChild:o,...i}=e,c=o?t:n;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(c,{...i,ref:r})});return o.displayName=`Primitive.${n}`,{...e,[n]:o}},{});function s(e,n){e&&o.flushSync(()=>e.dispatchEvent(n))}},33362:(e,n,t)=>{t.d(n,{TL:()=>u});var r=t(51609),o=t(91071),i=t(10790);function u(e){const n=c(e),t=r.forwardRef((e,t)=>{const{children:o,...u}=e,c=r.Children.toArray(o),s=c.find(l);if(s){const e=s.props.children,o=c.map(n=>n===s?r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null:n);return(0,i.jsx)(n,{...u,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,i.jsx)(n,{...u,ref:t,children:o})});return t.displayName=`${e}.Slot`,t}function c(e){const n=r.forwardRef((e,n)=>{const{children:t,...i}=e;if(r.isValidElement(t)){const e=function(e){let n=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=n&&"isReactWarning"in n&&n.isReactWarning;return t?e.ref:(n=Object.getOwnPropertyDescriptor(e,"ref")?.get,t=n&&"isReactWarning"in n&&n.isReactWarning,t?e.props.ref:e.props.ref||e.ref)}(t),u=function(e,n){const t={...n};for(const r in n){const o=e[r],i=n[r];/^on[A-Z]/.test(r)?o&&i?t[r]=(...e)=>{const n=i(...e);return o(...e),n}:o&&(t[r]=o):"style"===r?t[r]={...o,...i}:"className"===r&&(t[r]=[o,i].filter(Boolean).join(" "))}return{...e,...t}}(i,t.props);return t.type!==r.Fragment&&(u.ref=n?(0,o.t)(n,e):e),r.cloneElement(t,u)}return r.Children.count(t)>1?r.Children.only(null):null});return n.displayName=`${e}.SlotClone`,n}var s=Symbol("radix.slottable");function l(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}},54150:(e,n,t)=>{t.d(n,{X:()=>i});var r=t(51609),o=t(88200);function i(e){const[n,t]=r.useState(void 0);return(0,o.N)(()=>{if(e){t({width:e.offsetWidth,height:e.offsetHeight});const n=new ResizeObserver(n=>{if(!Array.isArray(n))return;if(!n.length)return;const r=n[0];let o,i;if("borderBoxSize"in r){const e=r.borderBoxSize,n=Array.isArray(e)?e[0]:e;o=n.inlineSize,i=n.blockSize}else o=e.offsetWidth,i=e.offsetHeight;t({width:o,height:i})});return n.observe(e,{box:"border-box"}),()=>n.unobserve(e)}t(void 0)},[e]),n}},62133:(e,n,t)=>{t.d(n,{A:()=>i});var r=t(51609),o=t(10790);function i(e,n=[]){let t=[];const i=()=>{const n=t.map(e=>r.createContext(e));return function(t){const o=t?.[e]||n;return r.useMemo(()=>({[`__scope${e}`]:{...t,[e]:o}}),[t,o])}};return i.scopeName=e,[function(n,i){const u=r.createContext(i),c=t.length;t=[...t,i];const s=n=>{const{scope:t,children:i,...s}=n,l=t?.[e]?.[c]||u,a=r.useMemo(()=>s,Object.values(s));return(0,o.jsx)(l.Provider,{value:a,children:i})};return s.displayName=n+"Provider",[s,function(t,o){const s=o?.[e]?.[c]||u,l=r.useContext(s);if(l)return l;if(void 0!==i)return i;throw new Error(`\`${t}\` must be used within \`${n}\``)}]},u(i,...n)]}function u(...e){const n=e[0];if(1===e.length)return n;const t=()=>{const t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){const o=t.reduce((n,{useScope:t,scopeName:r})=>({...n,...t(e)[`__scope${r}`]}),{});return r.useMemo(()=>({[`__scope${n.scopeName}`]:o}),[o])}};return t.scopeName=n.scopeName,t}},81351:(e,n,t)=>{var r;t.d(n,{i:()=>c});var o=t(51609),i=t(88200),u=(r||(r=t.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function c({prop:e,defaultProp:n,onChange:t=()=>{},caller:r}){const[i,c,s]=function({defaultProp:e,onChange:n}){const[t,r]=o.useState(e),i=o.useRef(t),c=o.useRef(n);return u(()=>{c.current=n},[n]),o.useEffect(()=>{i.current!==t&&(c.current?.(t),i.current=t)},[t,i]),[t,r,c]}({defaultProp:n,onChange:t}),l=void 0!==e,a=l?e:i;{const n=o.useRef(void 0!==e);o.useEffect(()=>{const e=n.current;if(e!==l){const n=e?"controlled":"uncontrolled",t=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${n} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}n.current=l},[l,r])}const f=o.useCallback(n=>{if(l){const t=function(e){return"function"==typeof e}(n)?n(e):n;t!==e&&s.current?.(t)}else c(n)},[l,e,c,s]);return[a,f]}Symbol("RADIX:SYNC_STATE")},88200:(e,n,t)=>{t.d(n,{N:()=>o});var r=t(51609),o=globalThis?.document?r.useLayoutEffect:()=>{}},91071:(e,n,t)=>{t.d(n,{s:()=>u,t:()=>i});var r=t(51609);function o(e,n){if("function"==typeof e)return e(n);null!=e&&(e.current=n)}function i(...e){return n=>{let t=!1;const r=e.map(e=>{const r=o(e,n);return t||"function"!=typeof r||(t=!0),r});if(t)return()=>{for(let n=0;n<r.length;n++){const t=r[n];"function"==typeof t?t():o(e[n],null)}}}}function u(...e){return r.useCallback(i(...e),e)}}}]);