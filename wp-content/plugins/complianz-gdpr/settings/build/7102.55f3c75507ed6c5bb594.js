"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[2010,5228,7102,8985,9091,9758],{7102:(e,t,o)=>{o.r(t),o.d(t,{default:()=>m});var a=o(45111),n=o(27723),s=o(52010),l=o(15139),r=o(4219),i=o(86087),c=o(81366),d=o(25228),u=o(10790);const p=e=>{const{getFieldValue:t,showSavedSettingsNotice:o}=(0,r.default)(),{language:a,saving:s,purposesOptions:p,services:m,updateCookie:h,toggleDeleteCookie:_,saveCookie:f}=(0,l.default)(),[g,b]=(0,i.useState)(""),[x,k]=(0,i.useState)(""),[v,j]=(0,i.useState)(""),[C,y]=(0,i.useState)([]);let z="no"!==t("use_cdb_api"),w=!!z&&1==e.sync,N=w;s&&(N=!0);let I=!1;e.slug.length>0&&(I="https://cookiedatabase.org/cookie/"+(e.service?e.service:"unknown-service")+"/"+e.slug),(0,i.useEffect)(()=>{e&&e.cookieFunction&&j(e.cookieFunction)},[e]);const D=(e,t,o)=>{h(t,o,e)};(0,i.useEffect)(()=>{e&&e.name&&b(e.name)},[e.name]),(0,i.useEffect)(()=>{if(!e)return;if(e.name===g)return;const t=setTimeout(()=>{h(e.ID,"name",g)},500);return()=>{clearTimeout(t)}},[g]),(0,i.useEffect)(()=>{if(!e)return;if(e.cookieFunction===v)return;const t=setTimeout(()=>{h(e.ID,"cookieFunction",v)},500);return()=>{clearTimeout(t)}},[v]),(0,i.useEffect)(()=>{e&&e.retention&&k(e.retention)},[e.retention]),(0,i.useEffect)(()=>{if(!e)return;if(e.retention===x)return;const t=setTimeout(()=>{h(e.ID,"retention",x)},500);return()=>{clearTimeout(t)}},[x]),(0,i.useEffect)(()=>{let e=p&&p.hasOwnProperty(a)?p[a]:[];e=e.map(e=>({label:e.label,value:e.label})),y(e)},[a,p]);const S=(e,t,o)=>{h(t,o,e)};if(!e)return null;let O=-1!==e.name.indexOf("cmplz_")||w,P=1!=e.deleted?"cmplz-reset-button":"",E=m.map((e,t)=>({value:e.ID,label:e.name})),L=!1,T="Marketing";C.forEach(function(e,t){e.value&&-1!==e.value.indexOf("/")&&(L=!0,T=e.value,T=T.substring(0,T.indexOf("/")))});let A=e.purpose&&-1!==e.purpose.indexOf("/");A&&(T=e.purpose.substring(0,e.purpose.indexOf("/"))),L&&!A&&C.forEach(function(e,t){e.value&&-1!==e.value.indexOf("/")&&(e.value=T,e.label=T,C[t]=e)});let F=e.purpose;return!L&&A&&(F=T),(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,u.jsx)(c.default,{id:e.ID+"_cdb_api",disabled:!z,value:w,onChange:t=>S(t,e.ID,"sync"),options:{true:(0,n.__)("Sync cookie with cookiedatabase.org","complianz-gdpr")}})}),(0,u.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,u.jsx)(c.default,{id:e.ID+"showOnPolicy",disabled:N,value:e.showOnPolicy,onChange:t=>S(t,e.ID,"showOnPolicy"),options:{true:(0,n.__)("Show cookie on Cookie Policy","complianz-gdpr")}})}),(0,u.jsxs)("div",{className:"cmplz-details-row",children:[(0,u.jsx)("label",{children:(0,n.__)("Name","complianz-gdpr")}),(0,u.jsx)("input",{disabled:N,onChange:e=>b(e.target.value),type:"text",placeholder:(0,n.__)("Name","complianz-gdpr"),value:g})]}),(0,u.jsxs)("div",{className:"cmplz-details-row",children:[(0,u.jsx)("label",{children:(0,n.__)("Service","complianz-gdpr")}),(0,u.jsx)(d.default,{disabled:N,value:e.serviceID,options:E,onChange:t=>D(t,e.ID,"serviceID")})]}),(0,u.jsxs)("div",{className:"cmplz-details-row",children:[(0,u.jsx)("label",{children:(0,n.__)("Expiration","complianz-gdpr")}),(0,u.jsx)("input",{disabled:O,onChange:e=>k(e.target.value),type:"text",placeholder:(0,n.__)("1 year","complianz-gdpr"),value:x})]}),(0,u.jsxs)("div",{className:"cmplz-details-row",children:[(0,u.jsx)("label",{children:(0,n.__)("Cookie function","complianz-gdpr")}),(0,u.jsx)("input",{disabled:N,onChange:e=>j(e.target.value),type:"text",placeholder:(0,n.__)("e.g. store user ID","complianz-gdpr"),value:v})]}),(0,u.jsxs)("div",{className:"cmplz-details-row",children:[(0,u.jsx)("label",{children:(0,n.__)("Purpose","complianz-gdpr")}),(0,u.jsx)(d.default,{disabled:N,value:F,options:C,onChange:t=>D(t,e.ID,"purpose")})]}),I&&(0,u.jsx)("div",{className:"cmplz-details-row",children:(0,u.jsx)("a",{href:I,target:"_blank",rel:"noopener noreferrer",children:(0,n.__)("View cookie on cookiedatabase.org","complianz-gdpr")})}),(0,u.jsxs)("div",{className:"cmplz-details-row cmplz-details-row__buttons",children:[(0,u.jsx)("button",{disabled:s,onClick:t=>(async e=>{await f(e),o((0,n.__)("Saved cookie","complianz-gdpr"))})(e.ID),className:"button button-default",children:(0,n.__)("Save","complianz-gdpr")}),(0,u.jsxs)("button",{className:"button button-default "+P,onClick:t=>(async e=>{await _(e)})(e.ID),children:[1==e.deleted&&(0,n.__)("Restore","complianz-gdpr"),1!=e.deleted&&(0,n.__)("Delete","complianz-gdpr")]})]})]})},m=(0,i.memo)(({cookie:e,id:t})=>{let o="";e.deleted?o=" | "+(0,n.__)("Deleted","complianz-gdpr"):e.showOnPolicy?e.isMembersOnly&&(o=" | "+(0,n.__)("Logged in users only, ignored","complianz-gdpr")):o=" | "+(0,n.__)("Admin, ignored","complianz-gdpr");let l=e.name;return(0,u.jsx)(u.Fragment,{children:(0,u.jsx)(s.default,{id:t,summary:l,comment:o,icons:(0,u.jsxs)(u.Fragment,{children:[e.complete&&(0,u.jsx)(a.default,{tooltip:(0,n.__)("The data for this cookie is complete","complianz-gdpr"),name:"success",color:"green"}),!e.complete&&(0,u.jsx)(a.default,{tooltip:(0,n.__)("This cookie has missing fields","complianz-gdpr"),name:"times",color:"red"}),e.sync&&e.synced&&(0,u.jsx)(a.default,{name:"rotate",color:"green"}),!e.synced||!e.sync&&(0,u.jsx)(a.default,{tooltip:(0,n.__)("This cookie is not synchronized with cookiedatabase.org.","complianz-gdpr"),name:"rotate-error",color:"red"}),e.showOnPolicy&&(0,u.jsx)(a.default,{tooltip:(0,n.__)("This cookie will be on your Cookie Policy","complianz-gdpr"),name:"file",color:"green"}),!e.showOnPolicy&&(0,u.jsx)(a.default,{tooltip:(0,n.__)("This cookie is not shown on the Cookie Policy","complianz-gdpr"),name:"file-disabled",color:"grey"}),e.old&&(0,u.jsx)(a.default,{tooltip:(0,n.__)("This cookie has not been detected on your site in the last three months","complianz-gdpr"),name:"calendar-error",color:"red"}),!e.old&&(0,u.jsx)(a.default,{tooltip:(0,n.__)("This cookie has recently been detected","complianz-gdpr"),name:"calendar",color:"green"})]}),details:p(e),style:(()=>{if(e.deleted)return Object.assign({},{backgroundColor:"var(--rsp-red-faded)"})})()})})})},25228:(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});var a=o(86087),n=o(21366),s=o(45111),l=o(27723),r=o(10790);const i=(0,a.memo)(({value:e=!1,onChange:t,required:o,defaultValue:a,disabled:i,options:c={},canBeEmpty:d=!0,label:u})=>{if(Array.isArray(c)){let e={};c.map(t=>{e[t.value]=t.label}),c=e}return d?(""===e||!1===e||0===e)&&(e="0",c={0:(0,l.__)("Select an option","complianz-gdpr"),...c}):e||(e=Object.keys(c)[0]),(0,r.jsx)("div",{className:"cmplz-input-group cmplz-select-group",children:(0,r.jsxs)(n.bL,{value:e,defaultValue:a,onValueChange:t,required:o,disabled:i&&!Array.isArray(i),children:[(0,r.jsxs)(n.l9,{className:"cmplz-select-group__trigger",children:[(0,r.jsx)(n.WT,{}),(0,r.jsx)(s.default,{name:"chevron-down"})]}),(0,r.jsxs)(n.UC,{className:"cmplz-select-group__content",position:"popper",children:[(0,r.jsx)(n.PP,{className:"cmplz-select-group__scroll-button",children:(0,r.jsx)(s.default,{name:"chevron-up"})}),(0,r.jsx)(n.LM,{className:"cmplz-select-group__viewport",children:(0,r.jsx)(n.YJ,{children:Object.entries(c).map(([e,t])=>(0,r.jsx)(n.q7,{disabled:Array.isArray(i)&&i.includes(e),className:"cmplz-select-group__item",value:e,children:(0,r.jsx)(n.p4,{children:t})},e))})}),(0,r.jsx)(n.wn,{className:"cmplz-select-group__scroll-button",children:(0,r.jsx)(s.default,{name:"chevron-down"})})]})]})},u)})},52010:(e,t,o)=>{o.r(t),o.d(t,{default:()=>l});var a=o(45111),n=o(86087),s=o(10790);const l=e=>{const[t,o]=(0,n.useState)(!1);return(0,s.jsx)("div",{className:"cmplz-panel__list__item",style:e.style?e.style:{},children:(0,s.jsxs)("details",{open:t,children:[(0,s.jsxs)("summary",{onClick:e=>(e=>{e.preventDefault(),o(!t)})(e),children:[e.icon&&(0,s.jsx)(a.default,{name:e.icon}),(0,s.jsx)("h5",{className:"cmplz-panel__list__item__title",children:e.summary}),(0,s.jsx)("div",{className:"cmplz-panel__list__item__comment",children:e.comment}),(0,s.jsx)("div",{className:"cmplz-panel__list__item__icons",children:e.icons}),(0,s.jsx)(a.default,{name:"chevron-down",size:18})]}),(0,s.jsx)("div",{className:"cmplz-panel__list__item__details",children:t&&e.details})]})})}},79758:(e,t,o)=>{o.r(t),o.d(t,{default:()=>u});var a=o(86087),n=o(9588),s=o(4219),l=o(52043),r=o(56427),i=o(99091),c=o(32828),d=o(10790);const u=(0,a.memo)(({type:e="action",style:t="tertiary",label:o,onClick:u,href:p="",target:m="",disabled:h,action:_,field:f,children:g})=>{if(!o&&!g)return null;const b=(f&&f.button_text?f.button_text:o)||g,{fetchFieldsData:x,showSavedSettingsNotice:k}=(0,s.default)(),{setInitialLoadCompleted:v,setProgress:j}=(0,i.UseCookieScanData)(),{setProgressLoaded:C}=(0,c.default)(),{selectedSubMenuItem:y}=(0,l.default)(),[z,w]=(0,a.useState)(!1),N=`button cmplz-button button--${t} button-${e}`,I=async e=>{await n.doAction(f.action,{}).then(e=>{e.success&&(x(y),"reset_settings"===e.id&&(v(!1),j(0),C(!1)),k(e.message))})},D=f&&f.warn?f.warn:"";return"action"===e?(0,d.jsxs)(d.Fragment,{children:[r.__experimentalConfirmDialog&&(0,d.jsx)(r.__experimentalConfirmDialog,{isOpen:z,onConfirm:async()=>{w(!1),await I()},onCancel:()=>{w(!1)},children:D}),(0,d.jsx)("button",{className:N,onClick:async t=>{if("action"!==e||!u)return"action"===e&&_?r.__experimentalConfirmDialog?void(f&&f.warn?w(!0):await I()):void await I():void(window.location.href=f.url);u(t)},disabled:h,children:b})]}):"link"===e?(0,d.jsx)("a",{className:N,href:p,target:m,children:b}):void 0})},81366:(e,t,o)=>{o.r(t),o.d(t,{default:()=>E});var a=o(51609),n=o(91071),s=o(62133),l=o(9957),r=o(81351),i=o(85357),c=o(54150),d=o(7971),u=o(12579),p=o(10790),m="Checkbox",[h,_]=(0,s.A)(m),[f,g]=h(m);function b(e){const{__scopeCheckbox:t,checked:o,children:n,defaultChecked:s,disabled:l,form:i,name:c,onCheckedChange:d,required:u,value:h="on",internal_do_not_use_render:_}=e,[g,b]=(0,r.i)({prop:o,defaultProp:s??!1,onChange:d,caller:m}),[x,k]=a.useState(null),[v,j]=a.useState(null),C=a.useRef(!1),y=!x||!!i||!!x.closest("form"),z={checked:g,disabled:l,setChecked:b,control:x,setControl:k,name:c,form:i,value:h,hasConsumerStoppedPropagationRef:C,required:u,defaultChecked:!N(s)&&s,isFormControl:y,bubbleInput:v,setBubbleInput:j};return(0,p.jsx)(f,{scope:t,...z,children:w(_)?_(z):n})}var x="CheckboxTrigger",k=a.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:o,...s},r)=>{const{control:i,value:c,disabled:d,checked:m,required:h,setControl:_,setChecked:f,hasConsumerStoppedPropagationRef:b,isFormControl:k,bubbleInput:v}=g(x,e),j=(0,n.s)(r,_),C=a.useRef(m);return a.useEffect(()=>{const e=i?.form;if(e){const t=()=>f(C.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[i,f]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":N(m)?"mixed":m,"aria-required":h,"data-state":I(m),"data-disabled":d?"":void 0,disabled:d,value:c,...s,ref:j,onKeyDown:(0,l.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(o,e=>{f(e=>!!N(e)||!e),v&&k&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})})});k.displayName=x;var v=a.forwardRef((e,t)=>{const{__scopeCheckbox:o,name:a,checked:n,defaultChecked:s,required:l,disabled:r,value:i,onCheckedChange:c,form:d,...u}=e;return(0,p.jsx)(b,{__scopeCheckbox:o,checked:n,defaultChecked:s,disabled:r,required:l,onCheckedChange:c,name:a,form:d,value:i,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(k,{...u,ref:t,__scopeCheckbox:o}),e&&(0,p.jsx)(z,{__scopeCheckbox:o})]})})});v.displayName=m;var j="CheckboxIndicator",C=a.forwardRef((e,t)=>{const{__scopeCheckbox:o,forceMount:a,...n}=e,s=g(j,o);return(0,p.jsx)(d.C,{present:a||N(s.checked)||!0===s.checked,children:(0,p.jsx)(u.sG.span,{"data-state":I(s.checked),"data-disabled":s.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});C.displayName=j;var y="CheckboxBubbleInput",z=a.forwardRef(({__scopeCheckbox:e,...t},o)=>{const{control:s,hasConsumerStoppedPropagationRef:l,checked:r,defaultChecked:d,required:m,disabled:h,name:_,value:f,form:b,bubbleInput:x,setBubbleInput:k}=g(y,e),v=(0,n.s)(o,k),j=(0,i.Z)(r),C=(0,c.X)(s);a.useEffect(()=>{const e=x;if(!e)return;const t=window.HTMLInputElement.prototype,o=Object.getOwnPropertyDescriptor(t,"checked").set,a=!l.current;if(j!==r&&o){const t=new Event("click",{bubbles:a});e.indeterminate=N(r),o.call(e,!N(r)&&r),e.dispatchEvent(t)}},[x,j,r,l]);const z=a.useRef(!N(r)&&r);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:d??z.current,required:m,disabled:h,name:_,value:f,form:b,...t,tabIndex:-1,ref:v,style:{...t.style,...C,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function w(e){return"function"==typeof e}function N(e){return"indeterminate"===e}function I(e){return N(e)?"indeterminate":e?"checked":"unchecked"}z.displayName=y;var D=o(27723),S=o(45111),O=o(86087),P=o(79758);const E=(0,O.memo)(({indeterminate:e,label:t,value:o,id:a,onChange:n,required:s,disabled:l,options:r={}})=>{const[i,c]=(0,O.useState)(!1),[d,u]=(0,O.useState)(!1);let m=o;Array.isArray(m)||(m=""===m?[]:[m]),(0,O.useEffect)(()=>{let e=1===Object.keys(r).length&&"true"===Object.keys(r)[0];c(e)},[]),e&&(o=!0);const h=m;let _=!1;Object.keys(r).length>10&&(_=!0);const f=e=>i?o:h.includes(""+e)||h.includes(parseInt(e)),g=()=>{u(!d)};let b=l&&!Array.isArray(l);return 0===Object.keys(r).length?(0,p.jsx)(p.Fragment,{children:(0,D.__)("No options found","complianz-gdpr")}):(0,p.jsxs)("div",{className:"cmplz-checkbox-group",children:[Object.entries(r).map(([r,c],u)=>(0,p.jsxs)("div",{className:"cmplz-checkbox-group__item"+(!d&&u>9?" cmplz-hidden":""),children:[(0,p.jsx)(v,{className:"cmplz-checkbox-group__checkbox",id:a+"_"+r,checked:f(r),"aria-label":t,disabled:b||Array.isArray(l)&&l.includes(r),required:s,onCheckedChange:e=>((e,t)=>{if(i)n(!o);else{const e=h.includes(""+t)||h.includes(parseInt(t))?h.filter(e=>e!==""+t&&e!==parseInt(t)):[...h,t];n(e)}})(0,r),children:(0,p.jsx)(C,{className:"cmplz-checkbox-group__indicator",children:(0,p.jsx)(S.default,{name:e?"indeterminate":"check",size:14,color:"dark-blue"})})}),(0,p.jsx)("label",{className:"cmplz-checkbox-group__label",htmlFor:a+"_"+r,children:c})]},r)),!d&&_&&(0,p.jsx)(P.default,{onClick:()=>g(),children:(0,D.__)("Show more","complianz-gdpr")}),d&&_&&(0,p.jsx)(P.default,{onClick:()=>g(),children:(0,D.__)("Show less","complianz-gdpr")})]})})},85357:(e,t,o)=>{o.d(t,{Z:()=>n});var a=o(51609);function n(e){const t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},99091:(e,t,o)=>{o.r(t),o.d(t,{UseCookieScanData:()=>s});var a=o(81621),n=o(9588);const s=(0,a.vt)((e,t)=>({initialLoadCompleted:!1,setInitialLoadCompleted:t=>e({initialLoadCompleted:t}),iframeLoaded:!1,loading:!1,nextPage:!1,progress:0,cookies:[],lastLoadedIframe:"",setIframeLoaded:t=>e({iframeLoaded:t}),setLastLoadedIframe:t=>e(e=>({lastLoadedIframe:t})),setProgress:t=>e({progress:t}),fetchProgress:()=>(e({loading:!0}),n.doAction("get_scan_progress",{}).then(t=>(e({initialLoadCompleted:!0,loading:!1,nextPage:t.next_page,progress:t.progress,cookies:t.cookies}),t)))}))}}]);