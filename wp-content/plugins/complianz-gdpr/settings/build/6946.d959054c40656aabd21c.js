"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[1629,2010,6946],{46946:(e,t,s)=>{s.r(t),s.d(t,{default:()=>m});var i=s(27723),n=s(45111),a=s(52010),l=s(4219),c=s(86087),r=s(81629),o=s(52043),d=s(10790);const m=(0,c.memo)(e=>{const{updateField:t,setChangedField:s,saveFields:m}=(0,l.default)(),{documentsLoaded:p,documents:u}=(0,r.default)(),{selectedMainMenuItem:g}=(0,o.default)(),[_,h]=wp.element.useState(e.processor.name?e.processor.name:""),[f,v]=wp.element.useState(e.processor.purpose?e.processor.purpose:""),[x,j]=wp.element.useState(e.processor.country?e.processor.country:""),[z,y]=wp.element.useState(e.processor.data?e.processor.data:""),N=(i,n)=>{let a=[...e.field.value];Array.isArray(a)||(a=[]);let l={...a[e.index]};l[n]=i,a[e.index]=l,t(e.field.id,a),s(e.field.id,a)};(0,c.useEffect)(()=>{const e=setTimeout(()=>{N(_,"name")},500);return()=>{clearTimeout(e)}},[_]),(0,c.useEffect)(()=>{const e=setTimeout(()=>{N(z,"data")},500);return()=>{clearTimeout(e)}},[z]),(0,c.useEffect)(()=>{const e=setTimeout(()=>{N(x,"country")},500);return()=>{clearTimeout(e)}},[x]),(0,c.useEffect)(()=>{const e=setTimeout(()=>{N(f,"purpose")},500);return()=>{clearTimeout(e)}},[f]);let w=p?[...u]:[];w.push({id:-1,title:(0,i.__)("A Processing Agreement outside Complianz Privacy Suite","complianz-gdpr"),region:"",service:"",date:""});let C={...e.processor};return C.processing_agreement||(C.processing_agreement=0),(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(a.default,{summary:_,details:(a=>(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"cmplz-details-row",children:[(0,d.jsx)("label",{children:(0,i.__)("Name","complianz-gdpr")}),(0,d.jsx)("input",{onChange:e=>h(e.target.value),type:"text",placeholder:(0,i.__)("Name","complianz-gdpr"),value:_})]}),(0,d.jsxs)("div",{className:"cmplz-details-row",children:[(0,d.jsx)("label",{children:(0,i.__)("Country","complianz-gdpr")}),(0,d.jsx)("input",{onChange:e=>j(e.target.value),type:"text",placeholder:(0,i.__)("Country","complianz-gdpr"),value:x})]}),(0,d.jsxs)("div",{className:"cmplz-details-row",children:[(0,d.jsx)("label",{children:(0,i.__)("Purpose","complianz-gdpr")}),(0,d.jsx)("input",{onChange:e=>v(e.target.value),type:"text",placeholder:(0,i.__)("Purpose","complianz-gdpr"),value:f})]}),(0,d.jsxs)("div",{className:"cmplz-details-row",children:[(0,d.jsx)("label",{children:(0,i.__)("Data","complianz-gdpr")}),(0,d.jsx)("input",{onChange:e=>y(e.target.value),type:"text",placeholder:(0,i.__)("Data","complianz-gdpr"),value:z})]}),(0,d.jsxs)("div",{className:"cmplz-details-row",children:[(0,d.jsx)("label",{children:(0,i.__)("Processing Agreement","complianz-gdpr")}),p&&(0,d.jsxs)("select",{onChange:e=>N(e.target.value,"processing_agreement"),value:a.processing_agreement,children:[(0,d.jsx)("option",{value:"0",children:(0,i.__)("Select an option","complianz-gdpr")}),w.map((e,t)=>(0,d.jsx)("option",{value:e.id,children:e.title},t))]}),!p&&(0,d.jsxs)("div",{className:"cmplz-documents-loader",children:[(0,d.jsx)("div",{children:(0,d.jsx)(n.default,{name:"loading",color:"grey"})}),(0,d.jsx)("div",{children:(0,i.__)("Loading...","complianz-gdpr")})]})]}),(0,d.jsx)("div",{className:"cmplz-details-row__buttons",children:(0,d.jsx)("button",{className:"button button-default cmplz-reset-button",onClick:i=>(async()=>{let i=e.field.value;Array.isArray(i)||(i=[]);let n=[...i];n.hasOwnProperty(e.index)&&n.splice(e.index,1),t(e.field.id,n),s(e.field.id,n),await m(g,!1,!1)})(),children:(0,i.__)("Delete","complianz-gdpr")})})]}))(C)})})})},52010:(e,t,s)=>{s.r(t),s.d(t,{default:()=>l});var i=s(45111),n=s(86087),a=s(10790);const l=e=>{const[t,s]=(0,n.useState)(!1);return(0,a.jsx)("div",{className:"cmplz-panel__list__item",style:e.style?e.style:{},children:(0,a.jsxs)("details",{open:t,children:[(0,a.jsxs)("summary",{onClick:e=>(e=>{e.preventDefault(),s(!t)})(e),children:[e.icon&&(0,a.jsx)(i.default,{name:e.icon}),(0,a.jsx)("h5",{className:"cmplz-panel__list__item__title",children:e.summary}),(0,a.jsx)("div",{className:"cmplz-panel__list__item__comment",children:e.comment}),(0,a.jsx)("div",{className:"cmplz-panel__list__item__icons",children:e.icons}),(0,a.jsx)(i.default,{name:"chevron-down",size:18})]}),(0,a.jsx)("div",{className:"cmplz-panel__list__item__details",children:t&&e.details})]})})}},81629:(e,t,s)=>{s.r(t),s.d(t,{default:()=>c});var i=s(81621),n=s(16535),a=s(9588),l=s(73710);const c=(0,i.vt)((e,t)=>({documentsLoaded:!1,region:"",fileName:"",serviceName:"",fetching:!1,updating:!1,loadingFields:!1,documents:[],regions:[],fields:[],editDocumentId:!1,resetEditDocumentId:t=>{e({editDocumentId:!1,region:"",serviceName:""})},editDocument:async t=>{e({updating:!0}),await a.doAction("load_processing_agreement",{id:t}).then(t=>{e({fields:t.fields,region:t.region,serviceName:t.serviceName,updating:!1,fileName:t.file_name})}).catch(e=>{console.error(e)}),e({editDocumentId:t})},setRegion:t=>{e({region:t})},setServiceName:t=>{e({serviceName:t})},updateField:(s,i)=>{let a=!1,c=!1;e((0,n.Ay)(e=>{e.fields.forEach(function(e,t){e.id===s&&(c=t,a=!0)}),!1!==c&&(e.fields[c].value=i)}));let r=(0,l.updateFieldsListWithConditions)(t().fields);e({fields:r})},save:async(s,i)=>{e({updating:!0});let n=t().editDocumentId;await a.doAction("save_processing_agreement",{fields:t().fields,region:s,serviceName:i,post_id:n}).then(t=>(e({updating:!1}),t)).catch(e=>{console.error(e)}),t().fetchData()},deleteDocuments:async s=>{let i=t().documents.filter(e=>s.includes(e.id));e(e=>({documents:e.documents.filter(e=>!s.includes(e.id))}));let n={};n.documents=i,await a.doAction("delete_processing_agreement",n).then(e=>e).catch(e=>{console.error(e)})},fetchData:async()=>{if(t().fetching)return;e({fetching:!0});const{documents:s,regions:i}=await a.doAction("get_processing_agreements",{}).then(e=>e).catch(e=>{console.error(e)});e(()=>({documentsLoaded:!0,documents:s,regions:i,fetching:!1}))},fetchFields:async t=>{let s={region:t};e({loadingFields:!0});const{fields:i}=await a.doAction("get_processing_agreement_fields",s).then(e=>e).catch(e=>{console.error(e)});let n=(0,l.updateFieldsListWithConditions)(i);e(e=>({fields:n,loadingFields:!1}))}}))}}]);