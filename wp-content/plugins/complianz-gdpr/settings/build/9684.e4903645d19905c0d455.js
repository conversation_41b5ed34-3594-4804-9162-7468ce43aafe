"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[9166,9684],{9684:(e,s,a)=>{a.r(s),a.d(s,{default:()=>h});var t=a(27723),c=a(45111),l=a(47143),i=a(9588),n=a(99166),r=a(4219),d=a(32828),o=a(52043),m=a(99418),p=a(10790);const h=({notice:e,index:s})=>{const{dismissNotice:a,fetchProgressData:h}=(0,d.default)(),{getField:u,setHighLightField:_,fetchFieldsData:g}=(0,r.default)(),{setSelectedSubMenuItem:b}=(0,o.default)();let z="premium"===e.icon,f=e.url&&-1!==e.url.indexOf("complianz.io"),k=e.status.charAt(0).toUpperCase()+e.status.slice(1);return(0,p.jsxs)("div",{className:"cmplz-task-element",children:[(0,p.jsx)("span",{className:"cmplz-task-status cmplz-"+e.status,children:k}),(0,p.jsx)("p",{className:"cmplz-task-message",dangerouslySetInnerHTML:{__html:m.A.sanitize(e.message)}}),f&&e.url&&(0,p.jsx)("a",{target:"_blank",href:e.url,rel:"noopener noreferrer",children:(0,t.__)("More info","complianz-gdpr")}),e.clear_cache_id&&(0,p.jsx)("span",{className:"cmplz-task-enable button button-secondary",onClick:()=>(async e=>{let s={};s.cache_id=e,i.doAction("clear_cache",s).then(async e=>{(0,l.dispatch)("core/notices").createNotice("success",(0,t.__)("Re-started test","complianz-gdpr"),{__unstableHTML:!0,id:"cmplz_clear_cache",type:"snackbar",isDismissible:!0}).then((0,n.default)(3e3)).then(e=>{(0,l.dispatch)("core/notices").removeNotice("rsssl_clear_cache")}),await g(),await h()})})(e.clear_cache_id),children:(0,t.__)("Re-check","complianz-gdpr")}),!z&&!f&&e.url&&(0,p.jsx)("a",{className:"cmplz-task-enable button button-secondary",href:e.url,children:(0,t.__)("View","complianz-gdpr")}),!z&&e.highlight_field_id&&(0,p.jsx)("span",{className:"cmplz-task-enable button button-secondary",onClick:()=>(async()=>{_(e.highlight_field_id);let s=u(e.highlight_field_id);await b(s.menu_id)})(),children:(0,t.__)("View","complianz-gdpr")}),e.plus_one&&(0,p.jsx)("span",{className:"cmplz-plusone",children:"1"}),e.dismissible&&"completed"!==e.status&&(0,p.jsx)("div",{className:"cmplz-task-dismiss",children:(0,p.jsx)("button",{type:"button",onClick:s=>a(e.id),children:(0,p.jsx)(c.default,{name:"times"})})})]},s)}},99166:(e,s,a)=>{a.r(s),a.d(s,{default:()=>t});const t=e=>function(s){return new Promise(a=>setTimeout(()=>a(s),e))}}}]);