"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[4644,5683],{35683:(e,s,i)=>{i.r(s),i.d(s,{default:()=>n});var t=i(81621),l=i(9588);const n=(0,t.vt)((e,s)=>({licenseStatus:cmplz_settings.licenseStatus,processing:!1,licenseNotices:[],noticesLoaded:!1,getLicenseNotices:async()=>{const{licenseStatus:s,notices:i}=await l.doAction("license_notices",{}).then(e=>e);e(e=>({noticesLoaded:!0,licenseNotices:i,licenseStatus:s}))},activateLicense:async s=>{let i={};i.license=s,e({processing:!0});const{licenseStatus:t,notices:n}=await l.doAction("activate_license",i);e(e=>({processing:!1,licenseNotices:n,licenseStatus:t}))},deactivateLicense:async()=>{e({processing:!0});const{licenseStatus:s,notices:i}=await l.doAction("deactivate_license");e(e=>({processing:!1,licenseNotices:i,licenseStatus:s}))}}))},54644:(e,s,i)=>{i.r(s),i.d(s,{default:()=>u});var t=i(44124),l=i(27723),n=i(52043),c=i(35683),a=i(86087),r=i(4219),d=i(88499),o=i(65170),m=i(99418),p=i(10790);const u=e=>{const{highLightField:s,getFieldValue:u}=(0,r.default)(),{licenseStatus:g}=(0,c.default)(),{bannerDataLoaded:h}=(0,d.default)();let _="https://complianz.io/pricing/";const{subMenu:z,getMenuRegions:f,selectedSubMenuItem:v}=(0,n.default)();let x=f(),k=u("regions");Array.isArray(k)||(k=[k]),x=x.filter(e=>k.includes(e));const[j,b]=(0,a.useState)(null);(0,a.useEffect)(()=>{Promise.resolve().then(i.bind(i,32636)).then(({default:e})=>{b(()=>e)})},[]);let N,L=[];for(const s of e.fields)s.group_id===e.group&&L.push(s);for(const e of z.menu_items)if(e.id===v?N=e:e.menu_items&&(N=e.menu_items.filter(e=>e.id===v)[0]),N)break;for(const s of z.menu_items)if(s.id===v&&s.hasOwnProperty("groups")){let i=s.groups.filter(s=>s.id===e.group);i.length>0&&(N=i[0])}if(!N)return null;let y=N.premium_text?N.premium_text:(0,l.__)("Learn more about %sPremium%s","complianz-gdpr");cmplz_settings.is_premium&&(y="empty"===g||"deactivated"===g?cmplz_settings.messageInactive:cmplz_settings.messageInvalid);let S=!1;N.premium&&(S=!cmplz_settings.is_premium),cmplz_settings.is_premium&&(S="valid"!==g&&"license"!==N.id),_=N.upgrade?N.upgrade:_;let w=N.helpLink_text?N.helpLink_text:(0,l.__)("Instructions","complianz-gdpr"),A=S?"cmplz-disabled":"";return L.filter(e=>e.conditionallyDisabled&&!0===e.conditionallyDisabled||e.visible&&!1===e.visible).length===L.length?null:(0,p.jsxs)("div",{className:"cmplz-grid-item cmplz-"+N.id+" "+A,children:[N.title&&(0,p.jsxs)("div",{className:"cmplz-grid-item-header",children:[(0,p.jsx)("h3",{className:"cmplz-h4",children:N.title}),x.length>0&&(0,p.jsx)("div",{className:"cmplz-grid-item-controls",children:x.map((e,s)=>(0,p.jsx)("div",{children:(0,p.jsx)("img",{className:"cmplz-settings-region",src:cmplz_settings.plugin_url+"/assets/images/"+e+".svg",alt:"region"})},s))}),0===x.length&&N.helpLink&&(0,p.jsx)("div",{className:"cmplz-grid-item-controls",children:(0,p.jsx)(t.default,{target:"_blank",rel:"noopener noreferrer",className:"cmplz-helplink",text:w,url:N.helpLink})})]}),(0,p.jsxs)("div",{className:"cmplz-grid-item-content",children:[N.intro&&(0,p.jsx)("div",{className:"cmplz-settings-block-intro",dangerouslySetInnerHTML:{__html:m.A.sanitize(N.intro)}})," ",j&&L.map((e,i)=>(0,p.jsx)(o.default,{fallback:"Could not load field "+e.id,children:(0,p.jsx)(j,{field:e,highLightField:s},e.id)},"field-"+e.id))]}),S&&(0,p.jsx)("div",{className:"cmplz-locked",children:(0,p.jsxs)("div",{className:"cmplz-locked-overlay",children:[(0,p.jsx)("span",{className:"cmplz-task-status cmplz-premium",children:(0,l.__)("Upgrade","complianz-gdpr")}),(0,p.jsxs)("span",{children:[cmplz_settings.is_premium&&(0,p.jsxs)("span",{children:[y," ",(0,p.jsx)("a",{className:"cmplz-locked-link",href:cmplz_settings.license_url,children:(0,l.__)("Check license","complianz-gdpr")})]}),!cmplz_settings.is_premium&&(0,p.jsx)(t.default,{target:"_blank",rel:"noopener noreferrer",text:y,url:_})]})]})}),"banner"===z.id&&!h&&(0,p.jsx)("div",{className:"cmplz-locked",children:(0,p.jsx)("div",{className:"cmplz-locked-overlay"})})]},N.id)}}}]);