"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[5575,7511,8770],{7511:(e,a,t)=>{t.r(a),t.d(a,{default:()=>i});var s=t(81621),r=t(9588);const i=(0,s.vt)((e,a)=>({apiRequestActive:!1,pluginAction:"status",wordPressUrl:"#",upgradeUrl:"#",rating:[],statusLoaded:!1,setStatusLoaded:a=>{e({statusLoaded:a})},startPluginAction:(t,s)=>{let i={};e({apiRequestActive:!0}),i.pluginAction=void 0!==s?s:a().pluginAction,i.slug=t;let l=!1;"download"===i.pluginAction&&(l="activate"),r.doAction("plugin_actions",i).then(s=>{e({pluginAction:s.pluginAction,wordPressUrl:s.wordpress_url,upgradeUrl:s.upgrade_url});let r=Math.round(s.star_rating.rating/10,0)/2;e({rating:r,ratingCount:s.star_rating.rating_count,apiRequestActive:!1,statusLoaded:!0}),"activate"===l&&"installed"!==s.pluginAction&&a().startPluginAction(t,s.pluginAction)})}}))},35575:(e,a,t)=>{t.r(a),t.d(a,{default:()=>c});var s=t(86087),r=t(27723),i=t(88770),l=t(7511),n=t(10790);const c=(0,s.memo)(()=>{const{measuresDataLoaded:e,measures:a,has_7:t,getMeasuresData:c}=(0,i.default)(),{statusLoaded:d,startPluginAction:o,pluginAction:u}=(0,l.default)();(0,s.useEffect)(()=>{"status"!==u&&c()},[u]);let p=d?u:"loading";if(d&&"installed"!==p&&"upgrade-to-premium"!==p){let e="activate"===p?(0,r.__)("Please activate Really Simple Security to unlock this feature.","complianz-gdpr"):(0,r.__)("Please install Really Simple Security to unlock this feature.","complianz-gdpr");return"loading"===p&&(e="..."),(0,n.jsx)("div",{className:"cmplz-locked",children:(0,n.jsxs)("div",{className:"cmplz-locked-overlay",children:[(0,n.jsx)("span",{className:"cmplz-task-status cmplz-open",children:(0,r.__)("Not installed","complianz-gdpr")}),(0,n.jsx)("span",{children:e})]})})}const m={vulnerability_detection:(0,r.__)("Vulnerability Detection","complianz-gdpr"),recommended_headers:(0,r.__)("HTTP Strict Transport Security and related security headers","complianz-gdpr"),ssl:(0,r.__)("TLS / SSL","complianz-gdpr"),hardening:(0,r.__)("Recommended site hardening features","complianz-gdpr")},g=({measure:e})=>{let a=e.enabled?"cmplz-measure-enabled":"cmplz-measure-disabled";return(0,n.jsx)("ul",{className:"cmplz-measure",children:(0,n.jsx)("li",{className:"cmplz-measure-description "+a,children:m[e.id]})})};return(0,n.jsxs)("div",{className:"cmplz-measures-container",children:[e&&t&&a.length>0&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{children:(0,r.__)("We are committed to the security of personal data. We take appropriate security measures to limit abuse of and unauthorized access to personal data. This ensures that only the necessary persons have access to your data, that access to the data is protected, and that our security measures are regularly reviewed.","complianz-gdpr")}),(0,n.jsx)("p",{children:(0,r.__)("The security measures we use consist of, but are not limited to:","complianz-gdpr")}),a.map((e,a)=>(0,n.jsx)(g,{measure:e},a))]}),e&&0===a.length&&t&&(0,r.__)("No security measures enabled in Really Simple Security","complianz-gdpr"),e&&!t&&(0,r.__)("Please upgrade Really Simple Security to the latest version to unlock this feature.","complianz-gdpr"),!e&&(0,n.jsx)(n.Fragment,{children:"..."})]})})},88770:(e,a,t)=>{t.r(a),t.d(a,{default:()=>i});var s=t(81621),r=t(9588);const i=(0,s.vt)((e,a)=>({measures:{},has_7:!1,measuresDataLoaded:!1,getMeasuresData:async()=>{const{measures:a,has_7:t}=await r.doAction("get_security_measures_data",{}).then(e=>e);e(e=>({measuresDataLoaded:!0,measures:a,has_7:t}))}}))}}]);