"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[2010,5228,6729,7102,8985,9091,9758],{7102:(e,t,a)=>{a.r(t),a.d(t,{default:()=>m});var s=a(45111),o=a(27723),n=a(52010),l=a(15139),r=a(4219),i=a(86087),c=a(81366),d=a(25228),u=a(10790);const p=e=>{const{getFieldValue:t,showSavedSettingsNotice:a}=(0,r.default)(),{language:s,saving:n,purposesOptions:p,services:m,updateCookie:h,toggleDeleteCookie:_,saveCookie:g}=(0,l.default)(),[f,b]=(0,i.useState)(""),[v,x]=(0,i.useState)(""),[k,j]=(0,i.useState)(""),[y,z]=(0,i.useState)([]);let C="no"!==t("use_cdb_api"),w=!!C&&1==e.sync,N=w;n&&(N=!0);let S=!1;e.slug.length>0&&(S="https://cookiedatabase.org/cookie/"+(e.service?e.service:"unknown-service")+"/"+e.slug),(0,i.useEffect)(()=>{e&&e.cookieFunction&&j(e.cookieFunction)},[e]);const D=(e,t,a)=>{h(t,a,e)};(0,i.useEffect)(()=>{e&&e.name&&b(e.name)},[e.name]),(0,i.useEffect)(()=>{if(!e)return;if(e.name===f)return;const t=setTimeout(()=>{h(e.ID,"name",f)},500);return()=>{clearTimeout(t)}},[f]),(0,i.useEffect)(()=>{if(!e)return;if(e.cookieFunction===k)return;const t=setTimeout(()=>{h(e.ID,"cookieFunction",k)},500);return()=>{clearTimeout(t)}},[k]),(0,i.useEffect)(()=>{e&&e.retention&&x(e.retention)},[e.retention]),(0,i.useEffect)(()=>{if(!e)return;if(e.retention===v)return;const t=setTimeout(()=>{h(e.ID,"retention",v)},500);return()=>{clearTimeout(t)}},[v]),(0,i.useEffect)(()=>{let e=p&&p.hasOwnProperty(s)?p[s]:[];e=e.map(e=>({label:e.label,value:e.label})),z(e)},[s,p]);const I=(e,t,a)=>{h(t,a,e)};if(!e)return null;let T=-1!==e.name.indexOf("cmplz_")||w,O=1!=e.deleted?"cmplz-reset-button":"",P=m.map((e,t)=>({value:e.ID,label:e.name})),E=!1,L="Marketing";y.forEach(function(e,t){e.value&&-1!==e.value.indexOf("/")&&(E=!0,L=e.value,L=L.substring(0,L.indexOf("/")))});let F=e.purpose&&-1!==e.purpose.indexOf("/");F&&(L=e.purpose.substring(0,e.purpose.indexOf("/"))),E&&!F&&y.forEach(function(e,t){e.value&&-1!==e.value.indexOf("/")&&(e.value=L,e.label=L,y[t]=e)});let A=e.purpose;return!E&&F&&(A=L),(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,u.jsx)(c.default,{id:e.ID+"_cdb_api",disabled:!C,value:w,onChange:t=>I(t,e.ID,"sync"),options:{true:(0,o.__)("Sync cookie with cookiedatabase.org","complianz-gdpr")}})}),(0,u.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,u.jsx)(c.default,{id:e.ID+"showOnPolicy",disabled:N,value:e.showOnPolicy,onChange:t=>I(t,e.ID,"showOnPolicy"),options:{true:(0,o.__)("Show cookie on Cookie Policy","complianz-gdpr")}})}),(0,u.jsxs)("div",{className:"cmplz-details-row",children:[(0,u.jsx)("label",{children:(0,o.__)("Name","complianz-gdpr")}),(0,u.jsx)("input",{disabled:N,onChange:e=>b(e.target.value),type:"text",placeholder:(0,o.__)("Name","complianz-gdpr"),value:f})]}),(0,u.jsxs)("div",{className:"cmplz-details-row",children:[(0,u.jsx)("label",{children:(0,o.__)("Service","complianz-gdpr")}),(0,u.jsx)(d.default,{disabled:N,value:e.serviceID,options:P,onChange:t=>D(t,e.ID,"serviceID")})]}),(0,u.jsxs)("div",{className:"cmplz-details-row",children:[(0,u.jsx)("label",{children:(0,o.__)("Expiration","complianz-gdpr")}),(0,u.jsx)("input",{disabled:T,onChange:e=>x(e.target.value),type:"text",placeholder:(0,o.__)("1 year","complianz-gdpr"),value:v})]}),(0,u.jsxs)("div",{className:"cmplz-details-row",children:[(0,u.jsx)("label",{children:(0,o.__)("Cookie function","complianz-gdpr")}),(0,u.jsx)("input",{disabled:N,onChange:e=>j(e.target.value),type:"text",placeholder:(0,o.__)("e.g. store user ID","complianz-gdpr"),value:k})]}),(0,u.jsxs)("div",{className:"cmplz-details-row",children:[(0,u.jsx)("label",{children:(0,o.__)("Purpose","complianz-gdpr")}),(0,u.jsx)(d.default,{disabled:N,value:A,options:y,onChange:t=>D(t,e.ID,"purpose")})]}),S&&(0,u.jsx)("div",{className:"cmplz-details-row",children:(0,u.jsx)("a",{href:S,target:"_blank",rel:"noopener noreferrer",children:(0,o.__)("View cookie on cookiedatabase.org","complianz-gdpr")})}),(0,u.jsxs)("div",{className:"cmplz-details-row cmplz-details-row__buttons",children:[(0,u.jsx)("button",{disabled:n,onClick:t=>(async e=>{await g(e),a((0,o.__)("Saved cookie","complianz-gdpr"))})(e.ID),className:"button button-default",children:(0,o.__)("Save","complianz-gdpr")}),(0,u.jsxs)("button",{className:"button button-default "+O,onClick:t=>(async e=>{await _(e)})(e.ID),children:[1==e.deleted&&(0,o.__)("Restore","complianz-gdpr"),1!=e.deleted&&(0,o.__)("Delete","complianz-gdpr")]})]})]})},m=(0,i.memo)(({cookie:e,id:t})=>{let a="";e.deleted?a=" | "+(0,o.__)("Deleted","complianz-gdpr"):e.showOnPolicy?e.isMembersOnly&&(a=" | "+(0,o.__)("Logged in users only, ignored","complianz-gdpr")):a=" | "+(0,o.__)("Admin, ignored","complianz-gdpr");let l=e.name;return(0,u.jsx)(u.Fragment,{children:(0,u.jsx)(n.default,{id:t,summary:l,comment:a,icons:(0,u.jsxs)(u.Fragment,{children:[e.complete&&(0,u.jsx)(s.default,{tooltip:(0,o.__)("The data for this cookie is complete","complianz-gdpr"),name:"success",color:"green"}),!e.complete&&(0,u.jsx)(s.default,{tooltip:(0,o.__)("This cookie has missing fields","complianz-gdpr"),name:"times",color:"red"}),e.sync&&e.synced&&(0,u.jsx)(s.default,{name:"rotate",color:"green"}),!e.synced||!e.sync&&(0,u.jsx)(s.default,{tooltip:(0,o.__)("This cookie is not synchronized with cookiedatabase.org.","complianz-gdpr"),name:"rotate-error",color:"red"}),e.showOnPolicy&&(0,u.jsx)(s.default,{tooltip:(0,o.__)("This cookie will be on your Cookie Policy","complianz-gdpr"),name:"file",color:"green"}),!e.showOnPolicy&&(0,u.jsx)(s.default,{tooltip:(0,o.__)("This cookie is not shown on the Cookie Policy","complianz-gdpr"),name:"file-disabled",color:"grey"}),e.old&&(0,u.jsx)(s.default,{tooltip:(0,o.__)("This cookie has not been detected on your site in the last three months","complianz-gdpr"),name:"calendar-error",color:"red"}),!e.old&&(0,u.jsx)(s.default,{tooltip:(0,o.__)("This cookie has recently been detected","complianz-gdpr"),name:"calendar",color:"green"})]}),details:p(e),style:(()=>{if(e.deleted)return Object.assign({},{backgroundColor:"var(--rsp-red-faded)"})})()})})})},25228:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});var s=a(86087),o=a(21366),n=a(45111),l=a(27723),r=a(10790);const i=(0,s.memo)(({value:e=!1,onChange:t,required:a,defaultValue:s,disabled:i,options:c={},canBeEmpty:d=!0,label:u})=>{if(Array.isArray(c)){let e={};c.map(t=>{e[t.value]=t.label}),c=e}return d?(""===e||!1===e||0===e)&&(e="0",c={0:(0,l.__)("Select an option","complianz-gdpr"),...c}):e||(e=Object.keys(c)[0]),(0,r.jsx)("div",{className:"cmplz-input-group cmplz-select-group",children:(0,r.jsxs)(o.bL,{value:e,defaultValue:s,onValueChange:t,required:a,disabled:i&&!Array.isArray(i),children:[(0,r.jsxs)(o.l9,{className:"cmplz-select-group__trigger",children:[(0,r.jsx)(o.WT,{}),(0,r.jsx)(n.default,{name:"chevron-down"})]}),(0,r.jsxs)(o.UC,{className:"cmplz-select-group__content",position:"popper",children:[(0,r.jsx)(o.PP,{className:"cmplz-select-group__scroll-button",children:(0,r.jsx)(n.default,{name:"chevron-up"})}),(0,r.jsx)(o.LM,{className:"cmplz-select-group__viewport",children:(0,r.jsx)(o.YJ,{children:Object.entries(c).map(([e,t])=>(0,r.jsx)(o.q7,{disabled:Array.isArray(i)&&i.includes(e),className:"cmplz-select-group__item",value:e,children:(0,r.jsx)(o.p4,{children:t})},e))})}),(0,r.jsx)(o.wn,{className:"cmplz-select-group__scroll-button",children:(0,r.jsx)(n.default,{name:"chevron-down"})})]})]})},u)})},36729:(e,t,a)=>{a.r(t),a.d(t,{default:()=>h});var s=a(7102),o=a(52010),n=a(15139),l=a(27723),r=a(45111),i=a(4219),c=a(81366),d=a(25228),u=a(86087),p=a(10790);const m=e=>{const{getFieldValue:t,showSavedSettingsNotice:a}=(0,i.default)(),[s,o]=(0,u.useState)(""),[r,m]=(0,u.useState)(""),{language:h,saving:_,deleteService:g,serviceTypeOptions:f,updateService:b,saveService:v}=(0,n.default)();let x="yes"===t("use_cdb_api");const[k,j]=(0,u.useState)([]);(0,u.useEffect)(()=>{let e=f&&f.hasOwnProperty(h)?f[h]:[];e=e.map(e=>({label:e.label,value:e.label})),j(e)},[h,f]);const y=(e,t,a)=>{b(t,a,e)},z=(e,t,a)=>{b(t,a,e)};if((0,u.useEffect)(()=>{e&&e.name&&o(e.name)},[e]),(0,u.useEffect)(()=>{if(!e)return;if(e.name===s)return;if(s.length<2)return;const t=setTimeout(()=>{y(s,e.ID,"name")},500);return()=>{clearTimeout(t)}},[s]),(0,u.useEffect)(()=>{e&&e.privacyStatementURL&&m(e.privacyStatementURL)},[e]),(0,u.useEffect)(()=>{if(!e)return;if(e.privacyStatementURL===r)return;if(0===r.length)return;const t=setTimeout(()=>{y(r,e.ID,"privacyStatementURL")},400);return()=>{clearTimeout(t)}},[r]),!e)return null;let C=!!x&&1==e.sync,w=C;_&&(w=!0);let N=!1;return e.slug.length>0&&(N="https://cookiedatabase.org/service/"+e.slug),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,p.jsx)(c.default,{id:e.ID+"sharesData",disabled:w,value:1==e.sharesData,onChange:t=>z(t,e.ID,"sharesData"),options:{true:(0,l.__)("Data is shared with this service","complianz-gdpr")}})}),(0,p.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,p.jsx)(c.default,{id:e.ID+"sync",disabled:!x,value:C,onChange:t=>z(t,e.ID,"sync"),options:{true:(0,l.__)("Sync service with cookiedatabase.org","complianz-gdpr")}})}),(0,p.jsxs)("div",{className:"cmplz-details-row",children:[(0,p.jsx)("label",{children:(0,l.__)("Name","complianz-gdpr")}),(0,p.jsx)("input",{disabled:w,onChange:e=>o(e.target.value),type:"text",placeholder:(0,l.__)("Name","complianz-gdpr"),value:s})]}),(0,p.jsxs)("div",{className:"cmplz-details-row",children:[(0,p.jsx)("label",{children:(0,l.__)("Service Types","complianz-gdpr")}),(0,p.jsx)(d.default,{disabled:w,value:e.serviceType,options:k,onChange:t=>y(t,e.ID,"serviceType")})]}),(0,p.jsxs)("div",{className:"cmplz-details-row",children:[(0,p.jsx)("label",{children:(0,l.__)("Privacy Statement URL","complianz-gdpr")}),(0,p.jsx)("input",{disabled:w,onChange:e=>m(e.target.value),type:"text",value:r})]}),N&&(0,p.jsx)("div",{className:"cmplz-details-row",children:(0,p.jsx)("a",{href:N,target:"_blank",rel:"noopener noreferrer",children:(0,l.__)("View service on cookiedatabase.org","complianz-gdpr")})}),(0,p.jsxs)("div",{className:"cmplz-details-row cmplz-details-row__buttons",children:[(0,p.jsx)("button",{disabled:_,onClick:t=>(async e=>{await v(e),a((0,l.__)("Saved service","complianz-gdpr"))})(e.ID),className:"button button-default",children:(0,l.__)("Save","complianz-gdpr")}),(0,p.jsx)("button",{className:"button button-default cmplz-reset-button",onClick:t=>(async e=>{await g(e)})(e.ID),children:(0,l.__)("Delete Service","complianz-gdpr")})]})]})},h=(0,u.memo)(e=>{const{adding:t}=(0,n.default)(),a=e.service&&e.service.ID>0&&e.service.hasOwnProperty("name"),i=!e.service||e.service.ID<=0,c=e.service&&e.service.name?e.service.name:(0,l.__)("New Service","complianz-gdpr");return(0,p.jsx)(p.Fragment,{children:(0,p.jsx)(o.default,{id:e.id,summary:e.name,icons:e.service?(0,p.jsxs)(p.Fragment,{children:[e.service.complete&&(0,p.jsx)(r.default,{tooltip:(0,l.__)("The data for this service is complete","complianz-gdpr"),name:"success",color:"green"}),!e.service.complete&&(0,p.jsx)(r.default,{tooltip:(0,l.__)("This service has missing fields","complianz-gdpr"),name:"times",color:"red"}),e.service.synced&&(0,p.jsx)(r.default,{tooltip:(0,l.__)("This service has been synchronized with cookiedatabase.org","complianz-gdpr"),name:"rotate",color:"green"}),!e.service.synced&&(0,p.jsx)(r.default,{tooltip:(0,l.__)("This service is not synchronized with cookiedatabase.org","complianz-gdpr"),name:"rotate-error",color:"red"})]}):(0,p.jsx)(p.Fragment,{}),details:(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("div",{children:m(e.service)}),e.cookies&&e.cookies.length>0&&(0,p.jsx)("div",{className:"cmplz-panel__cookie_list",children:e.cookies.map((e,t)=>(0,p.jsx)(s.default,{cookie:e},t))}),!i&&(0,p.jsxs)("div",{children:[(0,p.jsxs)("button",{disabled:t||!a,onClick:t=>((t,a)=>{e.addCookie(t,a)})(e.service.ID,c),className:"button button-default",children:[(0,l.__)("Add cookie to %s","complianz-gdpr").replace("%s",c),t&&(0,p.jsx)(r.default,{name:"loading",color:"grey"})]}),!a&&(0,p.jsx)("div",{className:"cmplz-comment",children:(0,l.__)("Save service to be able to add cookies","complianz-gdpr")})]})]})})})})},52010:(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var s=a(45111),o=a(86087),n=a(10790);const l=e=>{const[t,a]=(0,o.useState)(!1);return(0,n.jsx)("div",{className:"cmplz-panel__list__item",style:e.style?e.style:{},children:(0,n.jsxs)("details",{open:t,children:[(0,n.jsxs)("summary",{onClick:e=>(e=>{e.preventDefault(),a(!t)})(e),children:[e.icon&&(0,n.jsx)(s.default,{name:e.icon}),(0,n.jsx)("h5",{className:"cmplz-panel__list__item__title",children:e.summary}),(0,n.jsx)("div",{className:"cmplz-panel__list__item__comment",children:e.comment}),(0,n.jsx)("div",{className:"cmplz-panel__list__item__icons",children:e.icons}),(0,n.jsx)(s.default,{name:"chevron-down",size:18})]}),(0,n.jsx)("div",{className:"cmplz-panel__list__item__details",children:t&&e.details})]})})}},79758:(e,t,a)=>{a.r(t),a.d(t,{default:()=>u});var s=a(86087),o=a(9588),n=a(4219),l=a(52043),r=a(56427),i=a(99091),c=a(32828),d=a(10790);const u=(0,s.memo)(({type:e="action",style:t="tertiary",label:a,onClick:u,href:p="",target:m="",disabled:h,action:_,field:g,children:f})=>{if(!a&&!f)return null;const b=(g&&g.button_text?g.button_text:a)||f,{fetchFieldsData:v,showSavedSettingsNotice:x}=(0,n.default)(),{setInitialLoadCompleted:k,setProgress:j}=(0,i.UseCookieScanData)(),{setProgressLoaded:y}=(0,c.default)(),{selectedSubMenuItem:z}=(0,l.default)(),[C,w]=(0,s.useState)(!1),N=`button cmplz-button button--${t} button-${e}`,S=async e=>{await o.doAction(g.action,{}).then(e=>{e.success&&(v(z),"reset_settings"===e.id&&(k(!1),j(0),y(!1)),x(e.message))})},D=g&&g.warn?g.warn:"";return"action"===e?(0,d.jsxs)(d.Fragment,{children:[r.__experimentalConfirmDialog&&(0,d.jsx)(r.__experimentalConfirmDialog,{isOpen:C,onConfirm:async()=>{w(!1),await S()},onCancel:()=>{w(!1)},children:D}),(0,d.jsx)("button",{className:N,onClick:async t=>{if("action"!==e||!u)return"action"===e&&_?r.__experimentalConfirmDialog?void(g&&g.warn?w(!0):await S()):void await S():void(window.location.href=g.url);u(t)},disabled:h,children:b})]}):"link"===e?(0,d.jsx)("a",{className:N,href:p,target:m,children:b}):void 0})},81366:(e,t,a)=>{a.r(t),a.d(t,{default:()=>P});var s=a(51609),o=a(91071),n=a(62133),l=a(9957),r=a(81351),i=a(85357),c=a(54150),d=a(7971),u=a(12579),p=a(10790),m="Checkbox",[h,_]=(0,n.A)(m),[g,f]=h(m);function b(e){const{__scopeCheckbox:t,checked:a,children:o,defaultChecked:n,disabled:l,form:i,name:c,onCheckedChange:d,required:u,value:h="on",internal_do_not_use_render:_}=e,[f,b]=(0,r.i)({prop:a,defaultProp:n??!1,onChange:d,caller:m}),[v,x]=s.useState(null),[k,j]=s.useState(null),y=s.useRef(!1),z=!v||!!i||!!v.closest("form"),C={checked:f,disabled:l,setChecked:b,control:v,setControl:x,name:c,form:i,value:h,hasConsumerStoppedPropagationRef:y,required:u,defaultChecked:!N(n)&&n,isFormControl:z,bubbleInput:k,setBubbleInput:j};return(0,p.jsx)(g,{scope:t,...C,children:w(_)?_(C):o})}var v="CheckboxTrigger",x=s.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:a,...n},r)=>{const{control:i,value:c,disabled:d,checked:m,required:h,setControl:_,setChecked:g,hasConsumerStoppedPropagationRef:b,isFormControl:x,bubbleInput:k}=f(v,e),j=(0,o.s)(r,_),y=s.useRef(m);return s.useEffect(()=>{const e=i?.form;if(e){const t=()=>g(y.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[i,g]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":N(m)?"mixed":m,"aria-required":h,"data-state":S(m),"data-disabled":d?"":void 0,disabled:d,value:c,...n,ref:j,onKeyDown:(0,l.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(a,e=>{g(e=>!!N(e)||!e),k&&x&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})})});x.displayName=v;var k=s.forwardRef((e,t)=>{const{__scopeCheckbox:a,name:s,checked:o,defaultChecked:n,required:l,disabled:r,value:i,onCheckedChange:c,form:d,...u}=e;return(0,p.jsx)(b,{__scopeCheckbox:a,checked:o,defaultChecked:n,disabled:r,required:l,onCheckedChange:c,name:s,form:d,value:i,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(x,{...u,ref:t,__scopeCheckbox:a}),e&&(0,p.jsx)(C,{__scopeCheckbox:a})]})})});k.displayName=m;var j="CheckboxIndicator",y=s.forwardRef((e,t)=>{const{__scopeCheckbox:a,forceMount:s,...o}=e,n=f(j,a);return(0,p.jsx)(d.C,{present:s||N(n.checked)||!0===n.checked,children:(0,p.jsx)(u.sG.span,{"data-state":S(n.checked),"data-disabled":n.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});y.displayName=j;var z="CheckboxBubbleInput",C=s.forwardRef(({__scopeCheckbox:e,...t},a)=>{const{control:n,hasConsumerStoppedPropagationRef:l,checked:r,defaultChecked:d,required:m,disabled:h,name:_,value:g,form:b,bubbleInput:v,setBubbleInput:x}=f(z,e),k=(0,o.s)(a,x),j=(0,i.Z)(r),y=(0,c.X)(n);s.useEffect(()=>{const e=v;if(!e)return;const t=window.HTMLInputElement.prototype,a=Object.getOwnPropertyDescriptor(t,"checked").set,s=!l.current;if(j!==r&&a){const t=new Event("click",{bubbles:s});e.indeterminate=N(r),a.call(e,!N(r)&&r),e.dispatchEvent(t)}},[v,j,r,l]);const C=s.useRef(!N(r)&&r);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:d??C.current,required:m,disabled:h,name:_,value:g,form:b,...t,tabIndex:-1,ref:k,style:{...t.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function w(e){return"function"==typeof e}function N(e){return"indeterminate"===e}function S(e){return N(e)?"indeterminate":e?"checked":"unchecked"}C.displayName=z;var D=a(27723),I=a(45111),T=a(86087),O=a(79758);const P=(0,T.memo)(({indeterminate:e,label:t,value:a,id:s,onChange:o,required:n,disabled:l,options:r={}})=>{const[i,c]=(0,T.useState)(!1),[d,u]=(0,T.useState)(!1);let m=a;Array.isArray(m)||(m=""===m?[]:[m]),(0,T.useEffect)(()=>{let e=1===Object.keys(r).length&&"true"===Object.keys(r)[0];c(e)},[]),e&&(a=!0);const h=m;let _=!1;Object.keys(r).length>10&&(_=!0);const g=e=>i?a:h.includes(""+e)||h.includes(parseInt(e)),f=()=>{u(!d)};let b=l&&!Array.isArray(l);return 0===Object.keys(r).length?(0,p.jsx)(p.Fragment,{children:(0,D.__)("No options found","complianz-gdpr")}):(0,p.jsxs)("div",{className:"cmplz-checkbox-group",children:[Object.entries(r).map(([r,c],u)=>(0,p.jsxs)("div",{className:"cmplz-checkbox-group__item"+(!d&&u>9?" cmplz-hidden":""),children:[(0,p.jsx)(k,{className:"cmplz-checkbox-group__checkbox",id:s+"_"+r,checked:g(r),"aria-label":t,disabled:b||Array.isArray(l)&&l.includes(r),required:n,onCheckedChange:e=>((e,t)=>{if(i)o(!a);else{const e=h.includes(""+t)||h.includes(parseInt(t))?h.filter(e=>e!==""+t&&e!==parseInt(t)):[...h,t];o(e)}})(0,r),children:(0,p.jsx)(y,{className:"cmplz-checkbox-group__indicator",children:(0,p.jsx)(I.default,{name:e?"indeterminate":"check",size:14,color:"dark-blue"})})}),(0,p.jsx)("label",{className:"cmplz-checkbox-group__label",htmlFor:s+"_"+r,children:c})]},r)),!d&&_&&(0,p.jsx)(O.default,{onClick:()=>f(),children:(0,D.__)("Show more","complianz-gdpr")}),d&&_&&(0,p.jsx)(O.default,{onClick:()=>f(),children:(0,D.__)("Show less","complianz-gdpr")})]})})},85357:(e,t,a)=>{a.d(t,{Z:()=>o});var s=a(51609);function o(e){const t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},99091:(e,t,a)=>{a.r(t),a.d(t,{UseCookieScanData:()=>n});var s=a(81621),o=a(9588);const n=(0,s.vt)((e,t)=>({initialLoadCompleted:!1,setInitialLoadCompleted:t=>e({initialLoadCompleted:t}),iframeLoaded:!1,loading:!1,nextPage:!1,progress:0,cookies:[],lastLoadedIframe:"",setIframeLoaded:t=>e({iframeLoaded:t}),setLastLoadedIframe:t=>e(e=>({lastLoadedIframe:t})),setProgress:t=>e({progress:t}),fetchProgress:()=>(e({loading:!0}),o.doAction("get_scan_progress",{}).then(t=>(e({initialLoadCompleted:!0,loading:!1,nextPage:t.next_page,progress:t.progress,cookies:t.cookies}),t)))}))}}]);