"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[7320],{7320:(a,l,e)=>{e.r(l),e.d(l,{default:()=>d});var r=e(29243),i=e(86087),s=e(10790);const d=(0,i.memo)(({label:a,id:l,value:e,onChange:i,required:d,defaultValue:c,disabled:o,options:u={}})=>(0,s.jsx)(r.bL,{disabled:o&&!Array.isArray(o),className:"cmplz-input-group cmplz-radio-group",value:e,"aria-label":a,onValueChange:i,required:d,default:c,children:Object.entries(u).map(([a,e])=>(0,s.jsxs)("div",{className:"cmplz-radio-group__item",children:[(0,s.jsx)(r.q7,{disabled:Array.isArray(o)&&o.includes(a),value:a,id:l+"_"+a,children:(0,s.jsx)(r.C1,{className:"cmplz-radio-group__indicator"})}),(0,s.jsx)("label",{className:"cmplz-radio-label",htmlFor:l+"_"+a,children:e})]},a))}))}}]);