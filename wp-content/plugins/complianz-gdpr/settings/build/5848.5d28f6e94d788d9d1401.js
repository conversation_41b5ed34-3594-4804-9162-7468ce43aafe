"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[5848],{5848:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});var o=n(86087),s=n(27723),i=n(10790);const a=(0,o.memo)(function({field:e,label:t}){const[n,a]=(0,o.useState)(!1),r=()=>{if(n)return;a(!0);let t=new XMLHttpRequest;t.responseType="blob",t.open("get",e.url,!0),t.send(),t.onreadystatechange=function(){if(4===this.readyState&&200===this.status){var e=window.URL.createObjectURL(this.response),t=window.document.createElement("a");t.setAttribute("href",e),t.setAttribute("download","complianz-export.json"),window.document.body.appendChild(t),t.click(),setTimeout(function(){window.URL.revokeObjectURL(e)},6e4)}},t.onprogress=function(e){a(!0)}};return(0,i.jsx)("div",{className:"cmplz-export-container",children:(0,i.jsx)("button",{className:"button button-default",onClick:()=>r(),children:(0,s.__)("Export","complianz-gdpr")})})})}}]);