"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[8458],{18458:(e,a,i)=>{i.r(a),i.d(a,{default:()=>n});var u=i(86087),l=i(10790);const n=(0,u.memo)(({value:e,onChange:a,onError:i,required:n,disabled:r,id:t,name:s})=>{const p=t||s,[c,m]=(0,u.useState)(e);return(0,u.useEffect)(()=>{e||(e=""),m(e)},[]),(0,u.useEffect)(()=>{if(e===c)return;const u=setTimeout(()=>{var e;a(c),e=c,null===(e+="").match(/^[\w.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/)&&i("invalid_email")},500);return()=>{clearTimeout(u)}},[c]),(0,l.jsx)("div",{className:"cmplz-input-group cmplz-email-input-group",children:(0,l.jsx)("input",{type:"email",id:p,name:s,value:c,onChange:e=>(e=>{m(e)})(e.target.value),required:n,disabled:r,className:"cmplz-email-input-group__input"})})})}}]);