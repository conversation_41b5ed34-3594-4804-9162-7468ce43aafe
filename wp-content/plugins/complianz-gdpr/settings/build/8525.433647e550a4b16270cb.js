"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[5683,8525],{8525:(t,e,n)=>{n.r(e),n.d(e,{default:()=>u});var o=n(51609),s=n(27723),i=n(35683),a=n(4219),c=n(10790);const r=()=>{const t=new URL(window.location.href);t.searchParams.delete("tour"),window.history.pushState({},"",t.href)},l={defaultStepOptions:{cancelIcon:{enabled:!0},keyboardNavigation:!1},useModalOverlay:!1,margin:15},d=({ShepherdTourContext:t})=>{const e=(0,o.useContext)(t);return e.on("cancel",r),(0,o.useEffect)(()=>{e&&e.start()},[e]),null},p=[{title:(0,s.__)("Welcome to Complianz","complianz-gdpr"),text:"<p>"+(0,s.__)("Get ready for privacy legislation around the world. Follow a quick tour or start configuring the plugin!","complianz-gdpr")+"</p>",classes:"cmplz-shepherd",buttons:[{type:"cancel",classes:"button button-default",text:(0,s.__)("Configure","complianz-gdpr"),action(){new URL(window.location.href).searchParams.delete("tour"),window.location.hash="wizard"}},{classes:"button button-primary",text:(0,s.__)("Start tour","complianz-gdpr"),action(){return window.location.hash=cmplz_settings.is_premium?"settings/license":"dashboard",this.next()}}]},{title:(0,s.__)("Dashboard","complianz-gdpr"),text:"<p>"+(0,s.__)("This is your Dashboard. When the Wizard is completed, this will give you an overview of tasks, tools, and documentation.","complianz-gdpr")+"</p>",classes:"cmplz-shepherd",buttons:[{classes:"button button-default",text:(0,s.__)("Previous","complianz-gdpr"),action(){return window.location.hash=cmplz_settings.is_premium?"settings/license":"dashboard",this.back()}},{classes:"button button-primary",text:(0,s.__)("Next","complianz-gdpr"),action(){return window.location.hash="wizard/consent",this.next()}}]},{title:(0,s.__)("The Wizard","complianz-gdpr"),text:"<p>"+(0,s.__)("This is where everything regarding cookies is configured. We will come back to the Wizard soon.","complianz-gdpr")+"</p>",classes:"cmplz-shepherd",buttons:[{classes:"button button-default",text:(0,s.__)("Previous","complianz-gdpr"),action(){return window.location.hash="dashboard",this.back()}},{classes:"button button-primary",text:(0,s.__)("Next","complianz-gdpr"),action(){return window.location.hash="banner",this.next()}}]},{title:(0,s.__)("Consent Banner","complianz-gdpr"),text:"<p>"+(0,s.__)("Here you can configure and style your consent banner if the Wizard is completed. An extra tab will be added with region-specific settings.","complianz-gdpr")+"</p>",classes:"cmplz-shepherd",buttons:[{classes:"button button-default",text:(0,s.__)("Previous","complianz-gdpr"),action(){return window.location.hash="wizard/consent",this.back()}},{classes:"button button-primary",text:(0,s.__)("Next","complianz-gdpr"),action(){return window.location.hash="integrations",this.next()}}]},{title:(0,s.__)("Integrations","complianz-gdpr"),text:"<p>"+(0,s.__)("Based on your answers in the Wizard, we will automatically enable integrations with relevant services and plugins. In case you want to block extra scripts, you can add them to the Script Center.","complianz-gdpr")+"</p>",classes:"cmplz-shepherd",buttons:[{classes:"button button-default",text:(0,s.__)("Previous","complianz-gdpr"),action(){return window.location.hash="banner",this.back()}},{classes:"button button-primary",text:(0,s.__)("Next","complianz-gdpr"),action(){return window.location.hash="tools/proof-of-consent",this.next()}}]},{title:(0,s.__)("Proof of Consent","complianz-gdpr"),text:"<p>"+(0,s.__)("Complianz tracks changes in your Cookie Notice and Cookie Policy with time-stamped documents. This is your consent registration while respecting the data minimization guidelines and won't store any user data.","complianz-gdpr")+"</p>",classes:"cmplz-shepherd",buttons:[{classes:"button button-default",text:(0,s.__)("Previous","complianz-gdpr"),action(){return window.location.hash="integrations",this.back()}},{classes:"button button-primary",text:(0,s.__)("Next","complianz-gdpr"),action(){return window.location.hash="wizard/visitors",this.next()}}]},{title:(0,s.__)("Let's start the Wizard","complianz-gdpr"),text:"<p>"+(0,s.__)("You are ready to start the Wizard. For more information, FAQ, and support, please visit Complianz.io.","complianz-gdpr")+"</p>",classes:"cmplz-shepherd",buttons:[{classes:"button button-default",text:(0,s.__)("Previous","complianz-gdpr"),action(){return window.location.hash="tools/proof-of-consent",this.back()}},{type:"cancel",classes:"button button-primary",text:(0,s.__)("End tour","complianz-gdpr")}]}],u=()=>{const{licenseStatus:t}=(0,i.default)(),[e,r]=(0,o.useState)(null),[u,h]=(0,o.useState)(null),{fieldsLoaded:m}=(0,a.default)();return(0,o.useEffect)(()=>{if(m&&(n.e(1350).then(n.bind(n,21350)).then(({ShepherdTour:t,ShepherdTourContext:e})=>{r(()=>t),h(()=>e)}),cmplz_settings.is_premium)){let e;e="valid"===t?(0,s.__)("Great, your license is activated and valid!","complianz-gdpr"):(0,s.__)("To unlock the wizard and future updates, please enter and activate your license.","complianz-gdpr");const n={title:(0,s.__)("Activate your license","complianz-gdpr"),text:"<p>"+e+"</p>",classes:"cmplz-shepherd",buttons:[{classes:"button button-default",text:(0,s.__)("Previous","complianz-gdpr"),action(){return window.location.hash="dashboard",this.back()}},{classes:"button button-primary",text:(0,s.__)("Next","complianz-gdpr"),action(){return window.location.hash="dashboard",this.next()}}]};p.splice(1,0,n)}},[m]),e&&u?(0,c.jsx)(e,{steps:p,tourOptions:l,children:(0,c.jsx)(d,{ShepherdTourContext:u})}):null}},35683:(t,e,n)=>{n.r(e),n.d(e,{default:()=>i});var o=n(81621),s=n(9588);const i=(0,o.vt)((t,e)=>({licenseStatus:cmplz_settings.licenseStatus,processing:!1,licenseNotices:[],noticesLoaded:!1,getLicenseNotices:async()=>{const{licenseStatus:e,notices:n}=await s.doAction("license_notices",{}).then(t=>t);t(t=>({noticesLoaded:!0,licenseNotices:n,licenseStatus:e}))},activateLicense:async e=>{let n={};n.license=e,t({processing:!0});const{licenseStatus:o,notices:i}=await s.doAction("activate_license",n);t(t=>({processing:!1,licenseNotices:i,licenseStatus:o}))},deactivateLicense:async()=>{t({processing:!0});const{licenseStatus:e,notices:n}=await s.doAction("deactivate_license");t(t=>({processing:!1,licenseNotices:n,licenseStatus:e}))}}))}}]);