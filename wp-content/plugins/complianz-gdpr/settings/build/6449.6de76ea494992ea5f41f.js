"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[6449],{88830:(e,t,a)=>{a.r(t),a.d(t,{default:()=>u});var n=a(27723),i=a(4219),s=a(88499),l=a(45111),o=a(86087),d=a(56427),r=a(10790);const u=(0,o.memo)(()=>{const{cssLoading:e,selectedBanner:t}=(0,s.default)(),[a,u]=(0,o.useState)(!1),[c,f]=(0,o.useState)(!1),{updateField:p,setChangedField:m,fields:g}=(0,i.default)(),[h,_]=(0,o.useState)(!1);(0,o.useEffect)(()=>{e||u(!1)},[e]),(0,o.useEffect)(()=>{a||f(!1)},[g]);const b=async()=>{_(!1),f(!0),await C()},C=()=>{u(!0);let e=t.banner_fields;for(const t of e)if(t.hasOwnProperty("default")){if("hidden"===t.type)continue;p(t.id,t.default),m(t.id,t.default)}};return(0,r.jsxs)(r.Fragment,{children:[d.__experimentalConfirmDialog&&(0,r.jsx)(d.__experimentalConfirmDialog,{isOpen:h,onConfirm:()=>b(),onCancel:()=>_(!1),children:(0,n.__)("Are you sure you want to reset this banner to the default settings?","complianz-gdpr")}),(0,r.jsxs)("button",{disabled:c||a,onClick:()=>(async()=>{d.__experimentalConfirmDialog?_(!0):await b()})(),className:"button button-default",children:[(0,n.__)("Reset","complianz-gdpr"),a&&(0,r.jsx)(l.default,{name:"loading",color:"grey"})]})]})})}}]);