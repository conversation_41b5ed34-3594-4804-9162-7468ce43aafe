"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[7489,8198],{77489:(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});var s=a(88198),c=a(86087),n=a(45111),r=a(27723),l=a(10790);const i={enabled:{label:(0,r.__)("Enabled","complianz-gdpr"),color:"green"},disabled:{label:(0,r.__)("Disabled","complianz-gdpr"),color:"grey"},pending:{label:(0,r.__)("Pending","complianz-gdpr"),color:"orange"},error:{label:(0,r.__)("Error","complianz-gdpr"),color:"red"}},o=(0,c.memo)(()=>{const{tokenStatus:e,wscStatus:t,wscSignupDate:a,requestActivationEmail:o}=(0,s.default)(),[d,u]=(0,c.useState)(!1);return(0,c.useEffect)(()=>{if(!a)return void u(!1);const e=Math.floor(Date.now()/1e3);u(e-a>=3600)},[a,e]),(0,l.jsxs)("div",{className:"cmplz-wsc-status-container",children:[e&&(0,l.jsxs)("div",{className:"cmplz-wsc-status-row",children:[(0,l.jsx)(n.default,{name:"circle-check",color:i[e]?.color,size:16}),(0,l.jsxs)("span",{children:[(0,r.__)("Token Status: ","complianz-gdpr"),(0,l.jsx)("strong",{children:i[e]?.label})]})]}),t&&(0,l.jsxs)("div",{className:"cmplz-wsc-status-row",children:[(0,l.jsx)(n.default,{name:"circle-check",color:i[t]?.color,size:16}),(0,l.jsxs)("span",{children:[(0,r.__)("Website Scan Status: ","complianz-gdpr"),(0,l.jsx)("strong",{children:i[t]?.label})]})]}),t&&"pending"===t&&(0,l.jsx)("div",{className:"cmplz-wsc-status-row",children:d?(0,l.jsxs)("div",{className:"cmplz-wsc-alert cmplz-wsc-alert-warning",children:[(0,l.jsx)("span",{className:"cmplz-wsc-alert-title",children:(0,r.__)("Didn't receive the activation email? ","complianz-gdpr")}),(0,l.jsx)("a",{href:"#",onClick:e=>{e.preventDefault(),o()},children:(0,r.__)("Request again!","complianz-gdpr")})]}):(0,l.jsxs)("div",{className:"cmplz-wsc-alert cmplz-wsc-alert-warning",children:[(0,l.jsx)("p",{className:"cmplz-wsc-alert-title",children:(0,r.__)("Check your email to activate the Website Scan!","complianz-gdpr")}),(0,l.jsx)("p",{children:(0,r.__)("Didn't receive the activation email? Please wait 1 hour before requesting again.","complianz-gdpr")})]})})]})})},88198:(e,t,a)=>{a.r(t),a.d(t,{default:()=>r});var s=a(9588),c=a(81621);const n={loaded:!1,tokenStatus:"",wscStatus:"",wscSignupDate:"",syncing:!1},r=(0,c.vt)((e,t)=>({...n,startOnboarding:async()=>{const e=new URL(cmplz_settings.dashboard_url);e.searchParams.set("websitescan",""),setTimeout(()=>{window.location.href=e.href},500)},getStatus:async()=>{if(!t().getStatusCalled)try{let t={};const{wsc_status:a,token_status:c,wsc_signup_date:n}=await s.doAction("get_wsc_status",t).then(e=>e);e({tokenStatus:c,wscStatus:a,wscSignupDate:n,loaded:!0})}catch(e){console.error("Getting status error: ",e)}},resetWsc:async()=>{if(confirm("Are you sure? This will delete all your Website Scan data."))try{const{result:t,redirect:a}=await s.doAction("reset_wsc").then(e=>e);t&&(e(e=>({...n,startOnboarding:e.startOnboarding,getStatus:e.getStatus,enableWsc:e.enableWsc,disableWsc:e.disableWsc,resetWsc:e.resetWsc})),setTimeout(()=>{window.location.reload()},500))}catch(e){console.error("Resetting WSC error: ",e)}finally{setTimeout(()=>{window.location.reload()},300)}},enableWsc:async()=>{try{const{updated:t,wsc_status:a,token_status:c}=await s.doAction("enable_wsc").then(e=>e);e({updated:t,tokenStatus:c,wscStatus:a,loaded:!0})}catch(e){console.error("Enabling WSC error: ",e)}},disableWsc:async()=>{try{const{updated:t,wsc_status:a,token_status:c}=await s.doAction("disable_wsc").then(e=>e);e({updated:t,tokenStatus:c,wscStatus:a,loaded:!0})}catch(e){console.error("Disabling WSC error: ",e)}},requestActivationEmail:async()=>{try{await s.doAction("request_activation_email"),t().getStatus()}catch(e){console.error("Requesting activation email error: ",e)}}}))}}]);