"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[9330],{9417:(e,t,n)=>{function o(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}n.d(t,{A:()=>o})},20816:(e,t,n)=>{n.d(t,{A:()=>i});var o=n(82284);function i(e){var t=function(e){if("object"!=(0,o.A)(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=(0,o.A)(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==(0,o.A)(t)?t:t+""}},22589:(e,t,n)=>{n.d(t,{A:()=>i});var o=n(51609);const i=function(e,t){var n,i=(n=(0,o.useRef)(!0)).current?(n.current=!1,!0):n.current;(0,o.useEffect)(function(){if(!i)return e()},t)}},23029:(e,t,n)=>{function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}n.d(t,{A:()=>o})},27800:(e,t,n)=>{n.d(t,{A:()=>i});var o=n(43145);function i(e,t){if(e){if("string"==typeof e)return(0,o.A)(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?(0,o.A)(e,t):void 0}}},40128:(e,t,n)=>{n.d(t,{A:()=>gt});var o=n(58168),i=n(51609),r=n(64467);function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach(function(t){(0,r.A)(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}var u=n(23029),l=n(92901),c=n(85501),p=n(52962),d=n(45458),f=n(17437),h=n(27800);function v(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,r,a,s=[],u=!0,l=!1;try{if(r=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(o=r.call(n)).done)&&(s.push(o.value),s.length!==t);u=!0);}catch(e){l=!0,i=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}(e,t)||(0,h.A)(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var m=n(98587);function g(e,t){if(null==e)return{};var n,o,i=(0,m.A)(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(o=0;o<r.length;o++)n=r[o],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}var b=n(82284),O=n(75795),y=n(17663),I=i.useLayoutEffect,C=["className","clearValue","cx","getStyles","getClassNames","getValue","hasValue","isMulti","isRtl","options","selectOption","selectProps","setValue","theme"],w=function(){};function S(e,t){return t?"-"===t[0]?e+t:e+"__"+t:e}function A(e,t){for(var n=arguments.length,o=new Array(n>2?n-2:0),i=2;i<n;i++)o[i-2]=arguments[i];var r=[].concat(o);if(t&&e)for(var a in t)t.hasOwnProperty(a)&&t[a]&&r.push("".concat(S(e,a)));return r.filter(function(e){return e}).map(function(e){return String(e).trim()}).join(" ")}var V=function(e){return t=e,Array.isArray(t)?e.filter(Boolean):"object"===(0,b.A)(e)&&null!==e?[e]:[];var t},M=function(e){return e.className,e.clearValue,e.cx,e.getStyles,e.getClassNames,e.getValue,e.hasValue,e.isMulti,e.isRtl,e.options,e.selectOption,e.selectProps,e.setValue,e.theme,s({},g(e,C))},E=function(e,t,n){var o=e.cx,i=e.getStyles,r=e.getClassNames,a=e.className;return{css:i(t,e),className:o(null!=n?n:{},r(t,e),a)}};function x(e){return[document.documentElement,document.body,window].indexOf(e)>-1}function P(e){return x(e)?window.pageYOffset:e.scrollTop}function D(e,t){x(e)?window.scrollTo(0,t):e.scrollTop=t}function R(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:200,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:w,i=P(e),r=t-i,a=0;!function t(){var s,u=r*((s=(s=a+=10)/n-1)*s*s+1)+i;D(e,u),a<n?window.requestAnimationFrame(t):o(e)}()}function k(e,t){var n=e.getBoundingClientRect(),o=t.getBoundingClientRect(),i=t.offsetHeight/3;o.bottom+i>n.bottom?D(e,Math.min(t.offsetTop+t.clientHeight-e.offsetHeight+i,e.scrollHeight)):o.top-i<n.top&&D(e,Math.max(t.offsetTop-i,0))}function L(){try{return document.createEvent("TouchEvent"),!0}catch(e){return!1}}var F=!1,T={get passive(){return F=!0}},H="undefined"!=typeof window?window:{};H.addEventListener&&H.removeEventListener&&(H.addEventListener("p",w,T),H.removeEventListener("p",w,!1));var Y=F;function U(e){return null!=e}function j(e,t,n){return e?t:n}var B=["children","innerProps"],z=["children","innerProps"];var N,W,_,G=function(e){return"auto"===e?"bottom":e},X=(0,i.createContext)(null),q=function(e){var t=e.children,n=e.minMenuHeight,o=e.maxMenuHeight,r=e.menuPlacement,a=e.menuPosition,u=e.menuShouldScrollIntoView,l=e.theme,c=((0,i.useContext)(X)||{}).setPortalPlacement,p=(0,i.useRef)(null),d=v((0,i.useState)(o),2),f=d[0],h=d[1],m=v((0,i.useState)(null),2),g=m[0],b=m[1],O=l.spacing.controlHeight;return I(function(){var e=p.current;if(e){var t="fixed"===a,i=function(e){var t=e.maxHeight,n=e.menuEl,o=e.minHeight,i=e.placement,r=e.shouldScroll,a=e.isFixedPosition,s=e.controlHeight,u=function(e){var t=getComputedStyle(e),n="absolute"===t.position,o=/(auto|scroll)/;if("fixed"===t.position)return document.documentElement;for(var i=e;i=i.parentElement;)if(t=getComputedStyle(i),(!n||"static"!==t.position)&&o.test(t.overflow+t.overflowY+t.overflowX))return i;return document.documentElement}(n),l={placement:"bottom",maxHeight:t};if(!n||!n.offsetParent)return l;var c,p=u.getBoundingClientRect().height,d=n.getBoundingClientRect(),f=d.bottom,h=d.height,v=d.top,m=n.offsetParent.getBoundingClientRect().top,g=a||x(c=u)?window.innerHeight:c.clientHeight,b=P(u),O=parseInt(getComputedStyle(n).marginBottom,10),y=parseInt(getComputedStyle(n).marginTop,10),I=m-y,C=g-v,w=I+b,S=p-b-v,A=f-g+b+O,V=b+v-y,M=160;switch(i){case"auto":case"bottom":if(C>=h)return{placement:"bottom",maxHeight:t};if(S>=h&&!a)return r&&R(u,A,M),{placement:"bottom",maxHeight:t};if(!a&&S>=o||a&&C>=o)return r&&R(u,A,M),{placement:"bottom",maxHeight:a?C-O:S-O};if("auto"===i||a){var E=t,k=a?I:w;return k>=o&&(E=Math.min(k-O-s,t)),{placement:"top",maxHeight:E}}if("bottom"===i)return r&&D(u,A),{placement:"bottom",maxHeight:t};break;case"top":if(I>=h)return{placement:"top",maxHeight:t};if(w>=h&&!a)return r&&R(u,V,M),{placement:"top",maxHeight:t};if(!a&&w>=o||a&&I>=o){var L=t;return(!a&&w>=o||a&&I>=o)&&(L=a?I-y:w-y),r&&R(u,V,M),{placement:"top",maxHeight:L}}return{placement:"bottom",maxHeight:t};default:throw new Error('Invalid placement provided "'.concat(i,'".'))}return l}({maxHeight:o,menuEl:e,minHeight:n,placement:r,shouldScroll:u&&!t,isFixedPosition:t,controlHeight:O});h(i.maxHeight),b(i.placement),null==c||c(i.placement)}},[o,r,a,u,n,c,O]),t({ref:p,placerProps:s(s({},e),{},{placement:g||G(r),maxHeight:f})})},K=function(e,t){var n=e.theme,o=n.spacing.baseUnit,i=n.colors;return s({textAlign:"center"},t?{}:{color:i.neutral40,padding:"".concat(2*o,"px ").concat(3*o,"px")})},Z=K,J=K,Q=["size"],$=["innerProps","isRtl","size"],ee={name:"8mmkcg",styles:"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0"},te=function(e){var t=e.size,n=g(e,Q);return(0,f.Y)("svg",(0,o.A)({height:t,width:t,viewBox:"0 0 20 20","aria-hidden":"true",focusable:"false",css:ee},n))},ne=function(e){return(0,f.Y)(te,(0,o.A)({size:20},e),(0,f.Y)("path",{d:"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"}))},oe=function(e){return(0,f.Y)(te,(0,o.A)({size:20},e),(0,f.Y)("path",{d:"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"}))},ie=function(e,t){var n=e.isFocused,o=e.theme,i=o.spacing.baseUnit,r=o.colors;return s({label:"indicatorContainer",display:"flex",transition:"color 150ms"},t?{}:{color:n?r.neutral60:r.neutral20,padding:2*i,":hover":{color:n?r.neutral80:r.neutral40}})},re=ie,ae=ie,se=(0,f.i7)(N||(W=["\n  0%, 80%, 100% { opacity: 0; }\n  40% { opacity: 1; }\n"],_||(_=W.slice(0)),N=Object.freeze(Object.defineProperties(W,{raw:{value:Object.freeze(_)}})))),ue=function(e){var t=e.delay,n=e.offset;return(0,f.Y)("span",{css:(0,f.AH)({animation:"".concat(se," 1s ease-in-out ").concat(t,"ms infinite;"),backgroundColor:"currentColor",borderRadius:"1em",display:"inline-block",marginLeft:n?"1em":void 0,height:"1em",verticalAlign:"top",width:"1em"},"","")})},le=["data"],ce=["innerRef","isDisabled","isHidden","inputClassName"],pe={gridArea:"1 / 2",font:"inherit",minWidth:"2px",border:0,margin:0,outline:0,padding:0},de={flex:"1 1 auto",display:"inline-grid",gridArea:"1 / 1 / 2 / 3",gridTemplateColumns:"0 min-content","&:after":s({content:'attr(data-value) " "',visibility:"hidden",whiteSpace:"pre"},pe)},fe=function(e){return s({label:"input",color:"inherit",background:0,opacity:e?0:1,width:"100%"},pe)},he=function(e){var t=e.children,n=e.innerProps;return(0,f.Y)("div",n,t)},ve={ClearIndicator:function(e){var t=e.children,n=e.innerProps;return(0,f.Y)("div",(0,o.A)({},E(e,"clearIndicator",{indicator:!0,"clear-indicator":!0}),n),t||(0,f.Y)(ne,null))},Control:function(e){var t=e.children,n=e.isDisabled,i=e.isFocused,r=e.innerRef,a=e.innerProps,s=e.menuIsOpen;return(0,f.Y)("div",(0,o.A)({ref:r},E(e,"control",{control:!0,"control--is-disabled":n,"control--is-focused":i,"control--menu-is-open":s}),a,{"aria-disabled":n||void 0}),t)},DropdownIndicator:function(e){var t=e.children,n=e.innerProps;return(0,f.Y)("div",(0,o.A)({},E(e,"dropdownIndicator",{indicator:!0,"dropdown-indicator":!0}),n),t||(0,f.Y)(oe,null))},DownChevron:oe,CrossIcon:ne,Group:function(e){var t=e.children,n=e.cx,i=e.getStyles,r=e.getClassNames,a=e.Heading,s=e.headingProps,u=e.innerProps,l=e.label,c=e.theme,p=e.selectProps;return(0,f.Y)("div",(0,o.A)({},E(e,"group",{group:!0}),u),(0,f.Y)(a,(0,o.A)({},s,{selectProps:p,theme:c,getStyles:i,getClassNames:r,cx:n}),l),(0,f.Y)("div",null,t))},GroupHeading:function(e){var t=M(e);t.data;var n=g(t,le);return(0,f.Y)("div",(0,o.A)({},E(e,"groupHeading",{"group-heading":!0}),n))},IndicatorsContainer:function(e){var t=e.children,n=e.innerProps;return(0,f.Y)("div",(0,o.A)({},E(e,"indicatorsContainer",{indicators:!0}),n),t)},IndicatorSeparator:function(e){var t=e.innerProps;return(0,f.Y)("span",(0,o.A)({},t,E(e,"indicatorSeparator",{"indicator-separator":!0})))},Input:function(e){var t=e.cx,n=e.value,i=M(e),r=i.innerRef,a=i.isDisabled,s=i.isHidden,u=i.inputClassName,l=g(i,ce);return(0,f.Y)("div",(0,o.A)({},E(e,"input",{"input-container":!0}),{"data-value":n||""}),(0,f.Y)("input",(0,o.A)({className:t({input:!0},u),ref:r,style:fe(s),disabled:a},l)))},LoadingIndicator:function(e){var t=e.innerProps,n=e.isRtl,i=e.size,r=void 0===i?4:i,a=g(e,$);return(0,f.Y)("div",(0,o.A)({},E(s(s({},a),{},{innerProps:t,isRtl:n,size:r}),"loadingIndicator",{indicator:!0,"loading-indicator":!0}),t),(0,f.Y)(ue,{delay:0,offset:n}),(0,f.Y)(ue,{delay:160,offset:!0}),(0,f.Y)(ue,{delay:320,offset:!n}))},Menu:function(e){var t=e.children,n=e.innerRef,i=e.innerProps;return(0,f.Y)("div",(0,o.A)({},E(e,"menu",{menu:!0}),{ref:n},i),t)},MenuList:function(e){var t=e.children,n=e.innerProps,i=e.innerRef,r=e.isMulti;return(0,f.Y)("div",(0,o.A)({},E(e,"menuList",{"menu-list":!0,"menu-list--is-multi":r}),{ref:i},n),t)},MenuPortal:function(e){var t=e.appendTo,n=e.children,r=e.controlElement,a=e.innerProps,u=e.menuPlacement,l=e.menuPosition,c=(0,i.useRef)(null),p=(0,i.useRef)(null),d=v((0,i.useState)(G(u)),2),h=d[0],m=d[1],g=(0,i.useMemo)(function(){return{setPortalPlacement:m}},[]),b=v((0,i.useState)(null),2),C=b[0],w=b[1],S=(0,i.useCallback)(function(){if(r){var e=function(e){var t=e.getBoundingClientRect();return{bottom:t.bottom,height:t.height,left:t.left,right:t.right,top:t.top,width:t.width}}(r),t="fixed"===l?0:window.pageYOffset,n=e[h]+t;n===(null==C?void 0:C.offset)&&e.left===(null==C?void 0:C.rect.left)&&e.width===(null==C?void 0:C.rect.width)||w({offset:n,rect:e})}},[r,l,h,null==C?void 0:C.offset,null==C?void 0:C.rect.left,null==C?void 0:C.rect.width]);I(function(){S()},[S]);var A=(0,i.useCallback)(function(){"function"==typeof p.current&&(p.current(),p.current=null),r&&c.current&&(p.current=(0,y.ll)(r,c.current,S,{elementResize:"ResizeObserver"in window}))},[r,S]);I(function(){A()},[A]);var V=(0,i.useCallback)(function(e){c.current=e,A()},[A]);if(!t&&"fixed"!==l||!C)return null;var M=(0,f.Y)("div",(0,o.A)({ref:V},E(s(s({},e),{},{offset:C.offset,position:l,rect:C.rect}),"menuPortal",{"menu-portal":!0}),a),n);return(0,f.Y)(X.Provider,{value:g},t?(0,O.createPortal)(M,t):M)},LoadingMessage:function(e){var t=e.children,n=void 0===t?"Loading...":t,i=e.innerProps,r=g(e,z);return(0,f.Y)("div",(0,o.A)({},E(s(s({},r),{},{children:n,innerProps:i}),"loadingMessage",{"menu-notice":!0,"menu-notice--loading":!0}),i),n)},NoOptionsMessage:function(e){var t=e.children,n=void 0===t?"No options":t,i=e.innerProps,r=g(e,B);return(0,f.Y)("div",(0,o.A)({},E(s(s({},r),{},{children:n,innerProps:i}),"noOptionsMessage",{"menu-notice":!0,"menu-notice--no-options":!0}),i),n)},MultiValue:function(e){var t=e.children,n=e.components,o=e.data,i=e.innerProps,r=e.isDisabled,a=e.removeProps,u=e.selectProps,l=n.Container,c=n.Label,p=n.Remove;return(0,f.Y)(l,{data:o,innerProps:s(s({},E(e,"multiValue",{"multi-value":!0,"multi-value--is-disabled":r})),i),selectProps:u},(0,f.Y)(c,{data:o,innerProps:s({},E(e,"multiValueLabel",{"multi-value__label":!0})),selectProps:u},t),(0,f.Y)(p,{data:o,innerProps:s(s({},E(e,"multiValueRemove",{"multi-value__remove":!0})),{},{"aria-label":"Remove ".concat(t||"option")},a),selectProps:u}))},MultiValueContainer:he,MultiValueLabel:he,MultiValueRemove:function(e){var t=e.children,n=e.innerProps;return(0,f.Y)("div",(0,o.A)({role:"button"},n),t||(0,f.Y)(ne,{size:14}))},Option:function(e){var t=e.children,n=e.isDisabled,i=e.isFocused,r=e.isSelected,a=e.innerRef,s=e.innerProps;return(0,f.Y)("div",(0,o.A)({},E(e,"option",{option:!0,"option--is-disabled":n,"option--is-focused":i,"option--is-selected":r}),{ref:a,"aria-disabled":n},s),t)},Placeholder:function(e){var t=e.children,n=e.innerProps;return(0,f.Y)("div",(0,o.A)({},E(e,"placeholder",{placeholder:!0}),n),t)},SelectContainer:function(e){var t=e.children,n=e.innerProps,i=e.isDisabled,r=e.isRtl;return(0,f.Y)("div",(0,o.A)({},E(e,"container",{"--is-disabled":i,"--is-rtl":r}),n),t)},SingleValue:function(e){var t=e.children,n=e.isDisabled,i=e.innerProps;return(0,f.Y)("div",(0,o.A)({},E(e,"singleValue",{"single-value":!0,"single-value--is-disabled":n}),i),t)},ValueContainer:function(e){var t=e.children,n=e.innerProps,i=e.isMulti,r=e.hasValue;return(0,f.Y)("div",(0,o.A)({},E(e,"valueContainer",{"value-container":!0,"value-container--is-multi":i,"value-container--has-value":r}),n),t)}},me=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function ge(e,t){return e===t||!(!me(e)||!me(t))}function be(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!ge(e[n],t[n]))return!1;return!0}for(var Oe={name:"7pg0cj-a11yText",styles:"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap"},ye=function(e){return(0,f.Y)("span",(0,o.A)({css:Oe},e))},Ie={guidance:function(e){var t=e.isSearchable,n=e.isMulti,o=e.tabSelectsValue,i=e.context,r=e.isInitialFocus;switch(i){case"menu":return"Use Up and Down to choose options, press Enter to select the currently focused option, press Escape to exit the menu".concat(o?", press Tab to select the option and exit the menu":"",".");case"input":return r?"".concat(e["aria-label"]||"Select"," is focused ").concat(t?",type to refine list":"",", press Down to open the menu, ").concat(n?" press left to focus selected values":""):"";case"value":return"Use left and right to toggle between focused values, press Backspace to remove the currently focused value";default:return""}},onChange:function(e){var t=e.action,n=e.label,o=void 0===n?"":n,i=e.labels,r=e.isDisabled;switch(t){case"deselect-option":case"pop-value":case"remove-value":return"option ".concat(o,", deselected.");case"clear":return"All selected options have been cleared.";case"initial-input-focus":return"option".concat(i.length>1?"s":""," ").concat(i.join(","),", selected.");case"select-option":return"option ".concat(o,r?" is disabled. Select another option.":", selected.");default:return""}},onFocus:function(e){var t=e.context,n=e.focused,o=e.options,i=e.label,r=void 0===i?"":i,a=e.selectValue,s=e.isDisabled,u=e.isSelected,l=e.isAppleDevice,c=function(e,t){return e&&e.length?"".concat(e.indexOf(t)+1," of ").concat(e.length):""};if("value"===t&&a)return"value ".concat(r," focused, ").concat(c(a,n),".");if("menu"===t&&l){var p=s?" disabled":"",d="".concat(u?" selected":"").concat(p);return"".concat(r).concat(d,", ").concat(c(o,n),".")}return""},onFilter:function(e){var t=e.inputValue,n=e.resultsMessage;return"".concat(n).concat(t?" for search term "+t:"",".")}},Ce=function(e){var t=e.ariaSelection,n=e.focusedOption,o=e.focusedValue,r=e.focusableOptions,a=e.isFocused,u=e.selectValue,l=e.selectProps,c=e.id,p=e.isAppleDevice,d=l.ariaLiveMessages,h=l.getOptionLabel,v=l.inputValue,m=l.isMulti,g=l.isOptionDisabled,b=l.isSearchable,O=l.menuIsOpen,y=l.options,I=l.screenReaderStatus,C=l.tabSelectsValue,w=l.isLoading,S=l["aria-label"],A=l["aria-live"],V=(0,i.useMemo)(function(){return s(s({},Ie),d||{})},[d]),M=(0,i.useMemo)(function(){var e,n="";if(t&&V.onChange){var o=t.option,i=t.options,r=t.removedValue,a=t.removedValues,l=t.value,c=r||o||(e=l,Array.isArray(e)?null:e),p=c?h(c):"",d=i||a||void 0,f=d?d.map(h):[],v=s({isDisabled:c&&g(c,u),label:p,labels:f},t);n=V.onChange(v)}return n},[t,V,g,u,h]),E=(0,i.useMemo)(function(){var e="",t=n||o,i=!!(n&&u&&u.includes(n));if(t&&V.onFocus){var a={focused:t,label:h(t),isDisabled:g(t,u),isSelected:i,options:r,context:t===n?"menu":"value",selectValue:u,isAppleDevice:p};e=V.onFocus(a)}return e},[n,o,h,g,V,r,u,p]),x=(0,i.useMemo)(function(){var e="";if(O&&y.length&&!w&&V.onFilter){var t=I({count:r.length});e=V.onFilter({inputValue:v,resultsMessage:t})}return e},[r,v,O,V,y,I,w]),P="initial-input-focus"===(null==t?void 0:t.action),D=(0,i.useMemo)(function(){var e="";if(V.guidance){var t=o?"value":O?"menu":"input";e=V.guidance({"aria-label":S,context:t,isDisabled:n&&g(n,u),isMulti:m,isSearchable:b,tabSelectsValue:C,isInitialFocus:P})}return e},[S,n,o,m,g,b,O,V,u,C,P]),R=(0,f.Y)(i.Fragment,null,(0,f.Y)("span",{id:"aria-selection"},M),(0,f.Y)("span",{id:"aria-focused"},E),(0,f.Y)("span",{id:"aria-results"},x),(0,f.Y)("span",{id:"aria-guidance"},D));return(0,f.Y)(i.Fragment,null,(0,f.Y)(ye,{id:c},P&&R),(0,f.Y)(ye,{"aria-live":A,"aria-atomic":"false","aria-relevant":"additions text",role:"log"},a&&!P&&R))},we=[{base:"A",letters:"AⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"},{base:"AA",letters:"Ꜳ"},{base:"AE",letters:"ÆǼǢ"},{base:"AO",letters:"Ꜵ"},{base:"AU",letters:"Ꜷ"},{base:"AV",letters:"ꜸꜺ"},{base:"AY",letters:"Ꜽ"},{base:"B",letters:"BⒷＢḂḄḆɃƂƁ"},{base:"C",letters:"CⒸＣĆĈĊČÇḈƇȻꜾ"},{base:"D",letters:"DⒹＤḊĎḌḐḒḎĐƋƊƉꝹ"},{base:"DZ",letters:"ǱǄ"},{base:"Dz",letters:"ǲǅ"},{base:"E",letters:"EⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎ"},{base:"F",letters:"FⒻＦḞƑꝻ"},{base:"G",letters:"GⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾ"},{base:"H",letters:"HⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"},{base:"I",letters:"IⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"},{base:"J",letters:"JⒿＪĴɈ"},{base:"K",letters:"KⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"},{base:"L",letters:"LⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"},{base:"LJ",letters:"Ǉ"},{base:"Lj",letters:"ǈ"},{base:"M",letters:"MⓂＭḾṀṂⱮƜ"},{base:"N",letters:"NⓃＮǸŃÑṄŇṆŅṊṈȠƝꞐꞤ"},{base:"NJ",letters:"Ǌ"},{base:"Nj",letters:"ǋ"},{base:"O",letters:"OⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"},{base:"OI",letters:"Ƣ"},{base:"OO",letters:"Ꝏ"},{base:"OU",letters:"Ȣ"},{base:"P",letters:"PⓅＰṔṖƤⱣꝐꝒꝔ"},{base:"Q",letters:"QⓆＱꝖꝘɊ"},{base:"R",letters:"RⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"},{base:"S",letters:"SⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"},{base:"T",letters:"TⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"},{base:"TZ",letters:"Ꜩ"},{base:"U",letters:"UⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"},{base:"V",letters:"VⓋＶṼṾƲꝞɅ"},{base:"VY",letters:"Ꝡ"},{base:"W",letters:"WⓌＷẀẂŴẆẄẈⱲ"},{base:"X",letters:"XⓍＸẊẌ"},{base:"Y",letters:"YⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"},{base:"Z",letters:"ZⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"},{base:"a",letters:"aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐ"},{base:"aa",letters:"ꜳ"},{base:"ae",letters:"æǽǣ"},{base:"ao",letters:"ꜵ"},{base:"au",letters:"ꜷ"},{base:"av",letters:"ꜹꜻ"},{base:"ay",letters:"ꜽ"},{base:"b",letters:"bⓑｂḃḅḇƀƃɓ"},{base:"c",letters:"cⓒｃćĉċčçḉƈȼꜿↄ"},{base:"d",letters:"dⓓｄḋďḍḑḓḏđƌɖɗꝺ"},{base:"dz",letters:"ǳǆ"},{base:"e",letters:"eⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇɛǝ"},{base:"f",letters:"fⓕｆḟƒꝼ"},{base:"g",letters:"gⓖｇǵĝḡğġǧģǥɠꞡᵹꝿ"},{base:"h",letters:"hⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"},{base:"hv",letters:"ƕ"},{base:"i",letters:"iⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"},{base:"j",letters:"jⓙｊĵǰɉ"},{base:"k",letters:"kⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"},{base:"l",letters:"lⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇ"},{base:"lj",letters:"ǉ"},{base:"m",letters:"mⓜｍḿṁṃɱɯ"},{base:"n",letters:"nⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥ"},{base:"nj",letters:"ǌ"},{base:"o",letters:"oⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿɔꝋꝍɵ"},{base:"oi",letters:"ƣ"},{base:"ou",letters:"ȣ"},{base:"oo",letters:"ꝏ"},{base:"p",letters:"pⓟｐṕṗƥᵽꝑꝓꝕ"},{base:"q",letters:"qⓠｑɋꝗꝙ"},{base:"r",letters:"rⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"},{base:"s",letters:"sⓢｓßśṥŝṡšṧṣṩșşȿꞩꞅẛ"},{base:"t",letters:"tⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"},{base:"tz",letters:"ꜩ"},{base:"u",letters:"uⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"},{base:"v",letters:"vⓥｖṽṿʋꝟʌ"},{base:"vy",letters:"ꝡ"},{base:"w",letters:"wⓦｗẁẃŵẇẅẘẉⱳ"},{base:"x",letters:"xⓧｘẋẍ"},{base:"y",letters:"yⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"},{base:"z",letters:"zⓩｚźẑżžẓẕƶȥɀⱬꝣ"}],Se=new RegExp("["+we.map(function(e){return e.letters}).join("")+"]","g"),Ae={},Ve=0;Ve<we.length;Ve++)for(var Me=we[Ve],Ee=0;Ee<Me.letters.length;Ee++)Ae[Me.letters[Ee]]=Me.base;var xe=function(e){return e.replace(Se,function(e){return Ae[e]})},Pe=function(e,t){void 0===t&&(t=be);var n=null;function o(){for(var o=[],i=0;i<arguments.length;i++)o[i]=arguments[i];if(n&&n.lastThis===this&&t(o,n.lastArgs))return n.lastResult;var r=e.apply(this,o);return n={lastResult:r,lastArgs:o,lastThis:this},r}return o.clear=function(){n=null},o}(xe),De=function(e){return e.replace(/^\s+|\s+$/g,"")},Re=function(e){return"".concat(e.label," ").concat(e.value)},ke=["innerRef"];function Le(e){var t=e.innerRef,n=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];var i=Object.entries(e).filter(function(e){var t=v(e,1)[0];return!n.includes(t)});return i.reduce(function(e,t){var n=v(t,2),o=n[0],i=n[1];return e[o]=i,e},{})}(g(e,ke),"onExited","in","enter","exit","appear");return(0,f.Y)("input",(0,o.A)({ref:t},n,{css:(0,f.AH)({label:"dummyInput",background:0,border:0,caretColor:"transparent",fontSize:"inherit",gridArea:"1 / 1 / 2 / 3",outline:0,padding:0,width:1,color:"transparent",left:-100,opacity:0,position:"relative",transform:"scale(.01)"},"","")}))}var Fe=["boxSizing","height","overflow","paddingRight","position"],Te={boxSizing:"border-box",overflow:"hidden",position:"relative",height:"100%"};function He(e){e.cancelable&&e.preventDefault()}function Ye(e){e.stopPropagation()}function Ue(){var e=this.scrollTop,t=this.scrollHeight,n=e+this.offsetHeight;0===e?this.scrollTop=1:n===t&&(this.scrollTop=e-1)}function je(){return"ontouchstart"in window||navigator.maxTouchPoints}var Be=!("undefined"==typeof window||!window.document||!window.document.createElement),ze=0,Ne={capture:!1,passive:!1},We=function(e){var t=e.target;return t.ownerDocument.activeElement&&t.ownerDocument.activeElement.blur()},_e={name:"1kfdb0e",styles:"position:fixed;left:0;bottom:0;right:0;top:0"};function Ge(e){var t=e.children,n=e.lockEnabled,o=e.captureEnabled,r=function(e){var t=e.isEnabled,n=e.onBottomArrive,o=e.onBottomLeave,r=e.onTopArrive,a=e.onTopLeave,s=(0,i.useRef)(!1),u=(0,i.useRef)(!1),l=(0,i.useRef)(0),c=(0,i.useRef)(null),p=(0,i.useCallback)(function(e,t){if(null!==c.current){var i=c.current,l=i.scrollTop,p=i.scrollHeight,d=i.clientHeight,f=c.current,h=t>0,v=p-d-l,m=!1;v>t&&s.current&&(o&&o(e),s.current=!1),h&&u.current&&(a&&a(e),u.current=!1),h&&t>v?(n&&!s.current&&n(e),f.scrollTop=p,m=!0,s.current=!0):!h&&-t>l&&(r&&!u.current&&r(e),f.scrollTop=0,m=!0,u.current=!0),m&&function(e){e.cancelable&&e.preventDefault(),e.stopPropagation()}(e)}},[n,o,r,a]),d=(0,i.useCallback)(function(e){p(e,e.deltaY)},[p]),f=(0,i.useCallback)(function(e){l.current=e.changedTouches[0].clientY},[]),h=(0,i.useCallback)(function(e){var t=l.current-e.changedTouches[0].clientY;p(e,t)},[p]),v=(0,i.useCallback)(function(e){if(e){var t=!!Y&&{passive:!1};e.addEventListener("wheel",d,t),e.addEventListener("touchstart",f,t),e.addEventListener("touchmove",h,t)}},[h,f,d]),m=(0,i.useCallback)(function(e){e&&(e.removeEventListener("wheel",d,!1),e.removeEventListener("touchstart",f,!1),e.removeEventListener("touchmove",h,!1))},[h,f,d]);return(0,i.useEffect)(function(){if(t){var e=c.current;return v(e),function(){m(e)}}},[t,v,m]),function(e){c.current=e}}({isEnabled:void 0===o||o,onBottomArrive:e.onBottomArrive,onBottomLeave:e.onBottomLeave,onTopArrive:e.onTopArrive,onTopLeave:e.onTopLeave}),a=function(e){var t=e.isEnabled,n=e.accountForScrollbars,o=void 0===n||n,r=(0,i.useRef)({}),a=(0,i.useRef)(null),s=(0,i.useCallback)(function(e){if(Be){var t=document.body,n=t&&t.style;if(o&&Fe.forEach(function(e){var t=n&&n[e];r.current[e]=t}),o&&ze<1){var i=parseInt(r.current.paddingRight,10)||0,a=document.body?document.body.clientWidth:0,s=window.innerWidth-a+i||0;Object.keys(Te).forEach(function(e){var t=Te[e];n&&(n[e]=t)}),n&&(n.paddingRight="".concat(s,"px"))}t&&je()&&(t.addEventListener("touchmove",He,Ne),e&&(e.addEventListener("touchstart",Ue,Ne),e.addEventListener("touchmove",Ye,Ne))),ze+=1}},[o]),u=(0,i.useCallback)(function(e){if(Be){var t=document.body,n=t&&t.style;ze=Math.max(ze-1,0),o&&ze<1&&Fe.forEach(function(e){var t=r.current[e];n&&(n[e]=t)}),t&&je()&&(t.removeEventListener("touchmove",He,Ne),e&&(e.removeEventListener("touchstart",Ue,Ne),e.removeEventListener("touchmove",Ye,Ne)))}},[o]);return(0,i.useEffect)(function(){if(t){var e=a.current;return s(e),function(){u(e)}}},[t,s,u]),function(e){a.current=e}}({isEnabled:n});return(0,f.Y)(i.Fragment,null,n&&(0,f.Y)("div",{onClick:We,css:_e}),t(function(e){r(e),a(e)}))}var Xe={name:"1a0ro4n-requiredInput",styles:"label:requiredInput;opacity:0;pointer-events:none;position:absolute;bottom:0;left:0;right:0;width:100%"},qe=function(e){var t=e.name,n=e.onFocus;return(0,f.Y)("input",{required:!0,name:t,tabIndex:-1,"aria-hidden":"true",onFocus:n,css:Xe,value:"",onChange:function(){}})};function Ke(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&e.test((null===(t=window.navigator.userAgentData)||void 0===t?void 0:t.platform)||window.navigator.platform)}function Ze(){return Ke(/^Mac/i)}var Je={clearIndicator:ae,container:function(e){var t=e.isDisabled;return{label:"container",direction:e.isRtl?"rtl":void 0,pointerEvents:t?"none":void 0,position:"relative"}},control:function(e,t){var n=e.isDisabled,o=e.isFocused,i=e.theme,r=i.colors,a=i.borderRadius;return s({label:"control",alignItems:"center",cursor:"default",display:"flex",flexWrap:"wrap",justifyContent:"space-between",minHeight:i.spacing.controlHeight,outline:"0 !important",position:"relative",transition:"all 100ms"},t?{}:{backgroundColor:n?r.neutral5:r.neutral0,borderColor:n?r.neutral10:o?r.primary:r.neutral20,borderRadius:a,borderStyle:"solid",borderWidth:1,boxShadow:o?"0 0 0 1px ".concat(r.primary):void 0,"&:hover":{borderColor:o?r.primary:r.neutral30}})},dropdownIndicator:re,group:function(e,t){var n=e.theme.spacing;return t?{}:{paddingBottom:2*n.baseUnit,paddingTop:2*n.baseUnit}},groupHeading:function(e,t){var n=e.theme,o=n.colors,i=n.spacing;return s({label:"group",cursor:"default",display:"block"},t?{}:{color:o.neutral40,fontSize:"75%",fontWeight:500,marginBottom:"0.25em",paddingLeft:3*i.baseUnit,paddingRight:3*i.baseUnit,textTransform:"uppercase"})},indicatorsContainer:function(){return{alignItems:"center",alignSelf:"stretch",display:"flex",flexShrink:0}},indicatorSeparator:function(e,t){var n=e.isDisabled,o=e.theme,i=o.spacing.baseUnit,r=o.colors;return s({label:"indicatorSeparator",alignSelf:"stretch",width:1},t?{}:{backgroundColor:n?r.neutral10:r.neutral20,marginBottom:2*i,marginTop:2*i})},input:function(e,t){var n=e.isDisabled,o=e.value,i=e.theme,r=i.spacing,a=i.colors;return s(s({visibility:n?"hidden":"visible",transform:o?"translateZ(0)":""},de),t?{}:{margin:r.baseUnit/2,paddingBottom:r.baseUnit/2,paddingTop:r.baseUnit/2,color:a.neutral80})},loadingIndicator:function(e,t){var n=e.isFocused,o=e.size,i=e.theme,r=i.colors,a=i.spacing.baseUnit;return s({label:"loadingIndicator",display:"flex",transition:"color 150ms",alignSelf:"center",fontSize:o,lineHeight:1,marginRight:o,textAlign:"center",verticalAlign:"middle"},t?{}:{color:n?r.neutral60:r.neutral20,padding:2*a})},loadingMessage:J,menu:function(e,t){var n,o=e.placement,i=e.theme,a=i.borderRadius,u=i.spacing,l=i.colors;return s((n={label:"menu"},(0,r.A)(n,function(e){return e?{bottom:"top",top:"bottom"}[e]:"bottom"}(o),"100%"),(0,r.A)(n,"position","absolute"),(0,r.A)(n,"width","100%"),(0,r.A)(n,"zIndex",1),n),t?{}:{backgroundColor:l.neutral0,borderRadius:a,boxShadow:"0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)",marginBottom:u.menuGutter,marginTop:u.menuGutter})},menuList:function(e,t){var n=e.maxHeight,o=e.theme.spacing.baseUnit;return s({maxHeight:n,overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},t?{}:{paddingBottom:o,paddingTop:o})},menuPortal:function(e){var t=e.rect,n=e.offset,o=e.position;return{left:t.left,position:o,top:n,width:t.width,zIndex:1}},multiValue:function(e,t){var n=e.theme,o=n.spacing,i=n.borderRadius,r=n.colors;return s({label:"multiValue",display:"flex",minWidth:0},t?{}:{backgroundColor:r.neutral10,borderRadius:i/2,margin:o.baseUnit/2})},multiValueLabel:function(e,t){var n=e.theme,o=n.borderRadius,i=n.colors,r=e.cropWithEllipsis;return s({overflow:"hidden",textOverflow:r||void 0===r?"ellipsis":void 0,whiteSpace:"nowrap"},t?{}:{borderRadius:o/2,color:i.neutral80,fontSize:"85%",padding:3,paddingLeft:6})},multiValueRemove:function(e,t){var n=e.theme,o=n.spacing,i=n.borderRadius,r=n.colors,a=e.isFocused;return s({alignItems:"center",display:"flex"},t?{}:{borderRadius:i/2,backgroundColor:a?r.dangerLight:void 0,paddingLeft:o.baseUnit,paddingRight:o.baseUnit,":hover":{backgroundColor:r.dangerLight,color:r.danger}})},noOptionsMessage:Z,option:function(e,t){var n=e.isDisabled,o=e.isFocused,i=e.isSelected,r=e.theme,a=r.spacing,u=r.colors;return s({label:"option",cursor:"default",display:"block",fontSize:"inherit",width:"100%",userSelect:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)"},t?{}:{backgroundColor:i?u.primary:o?u.primary25:"transparent",color:n?u.neutral20:i?u.neutral0:"inherit",padding:"".concat(2*a.baseUnit,"px ").concat(3*a.baseUnit,"px"),":active":{backgroundColor:n?void 0:i?u.primary:u.primary50}})},placeholder:function(e,t){var n=e.theme,o=n.spacing,i=n.colors;return s({label:"placeholder",gridArea:"1 / 1 / 2 / 3"},t?{}:{color:i.neutral50,marginLeft:o.baseUnit/2,marginRight:o.baseUnit/2})},singleValue:function(e,t){var n=e.isDisabled,o=e.theme,i=o.spacing,r=o.colors;return s({label:"singleValue",gridArea:"1 / 1 / 2 / 3",maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},t?{}:{color:n?r.neutral40:r.neutral80,marginLeft:i.baseUnit/2,marginRight:i.baseUnit/2})},valueContainer:function(e,t){var n=e.theme.spacing,o=e.isMulti,i=e.hasValue,r=e.selectProps.controlShouldRenderValue;return s({alignItems:"center",display:o&&i&&r?"flex":"grid",flex:1,flexWrap:"wrap",WebkitOverflowScrolling:"touch",position:"relative",overflow:"hidden"},t?{}:{padding:"".concat(n.baseUnit/2,"px ").concat(2*n.baseUnit,"px")})}},Qe={borderRadius:4,colors:{primary:"#2684FF",primary75:"#4C9AFF",primary50:"#B2D4FF",primary25:"#DEEBFF",danger:"#DE350B",dangerLight:"#FFBDAD",neutral0:"hsl(0, 0%, 100%)",neutral5:"hsl(0, 0%, 95%)",neutral10:"hsl(0, 0%, 90%)",neutral20:"hsl(0, 0%, 80%)",neutral30:"hsl(0, 0%, 70%)",neutral40:"hsl(0, 0%, 60%)",neutral50:"hsl(0, 0%, 50%)",neutral60:"hsl(0, 0%, 40%)",neutral70:"hsl(0, 0%, 30%)",neutral80:"hsl(0, 0%, 20%)",neutral90:"hsl(0, 0%, 10%)"},spacing:{baseUnit:4,controlHeight:38,menuGutter:8}},$e={"aria-live":"polite",backspaceRemovesValue:!0,blurInputOnSelect:L(),captureMenuScroll:!L(),classNames:{},closeMenuOnSelect:!0,closeMenuOnScroll:!1,components:{},controlShouldRenderValue:!0,escapeClearsValue:!1,filterOption:function(e,t){if(e.data.__isNew__)return!0;var n=s({ignoreCase:!0,ignoreAccents:!0,stringify:Re,trim:!0,matchFrom:"any"},undefined),o=n.ignoreCase,i=n.ignoreAccents,r=n.stringify,a=n.trim,u=n.matchFrom,l=a?De(t):t,c=a?De(r(e)):r(e);return o&&(l=l.toLowerCase(),c=c.toLowerCase()),i&&(l=Pe(l),c=xe(c)),"start"===u?c.substr(0,l.length)===l:c.indexOf(l)>-1},formatGroupLabel:function(e){return e.label},getOptionLabel:function(e){return e.label},getOptionValue:function(e){return e.value},isDisabled:!1,isLoading:!1,isMulti:!1,isRtl:!1,isSearchable:!0,isOptionDisabled:function(e){return!!e.isDisabled},loadingMessage:function(){return"Loading..."},maxMenuHeight:300,minMenuHeight:140,menuIsOpen:!1,menuPlacement:"bottom",menuPosition:"absolute",menuShouldBlockScroll:!1,menuShouldScrollIntoView:!function(){try{return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}catch(e){return!1}}(),noOptionsMessage:function(){return"No options"},openMenuOnFocus:!1,openMenuOnClick:!0,options:[],pageSize:5,placeholder:"Select...",screenReaderStatus:function(e){var t=e.count;return"".concat(t," result").concat(1!==t?"s":""," available")},styles:{},tabIndex:0,tabSelectsValue:!0,unstyled:!1};function et(e,t,n,o){return{type:"option",data:t,isDisabled:ut(e,t,n),isSelected:lt(e,t,n),label:at(e,t),value:st(e,t),index:o}}function tt(e,t){return e.options.map(function(n,o){if("options"in n){var i=n.options.map(function(n,o){return et(e,n,t,o)}).filter(function(t){return it(e,t)});return i.length>0?{type:"group",data:n,options:i,index:o}:void 0}var r=et(e,n,t,o);return it(e,r)?r:void 0}).filter(U)}function nt(e){return e.reduce(function(e,t){return"group"===t.type?e.push.apply(e,(0,d.A)(t.options.map(function(e){return e.data}))):e.push(t.data),e},[])}function ot(e,t){return e.reduce(function(e,n){return"group"===n.type?e.push.apply(e,(0,d.A)(n.options.map(function(e){return{data:e.data,id:"".concat(t,"-").concat(n.index,"-").concat(e.index)}}))):e.push({data:n.data,id:"".concat(t,"-").concat(n.index)}),e},[])}function it(e,t){var n=e.inputValue,o=void 0===n?"":n,i=t.data,r=t.isSelected,a=t.label,s=t.value;return(!pt(e)||!r)&&ct(e,{label:a,value:s,data:i},o)}var rt=function(e,t){var n;return(null===(n=e.find(function(e){return e.data===t}))||void 0===n?void 0:n.id)||null},at=function(e,t){return e.getOptionLabel(t)},st=function(e,t){return e.getOptionValue(t)};function ut(e,t,n){return"function"==typeof e.isOptionDisabled&&e.isOptionDisabled(t,n)}function lt(e,t,n){if(n.indexOf(t)>-1)return!0;if("function"==typeof e.isOptionSelected)return e.isOptionSelected(t,n);var o=st(e,t);return n.some(function(t){return st(e,t)===o})}function ct(e,t,n){return!e.filterOption||e.filterOption(t,n)}var pt=function(e){var t=e.hideSelectedOptions,n=e.isMulti;return void 0===t?n:t},dt=1,ft=function(e){(0,c.A)(n,e);var t=(0,p.A)(n);function n(e){var o;if((0,u.A)(this,n),(o=t.call(this,e)).state={ariaSelection:null,focusedOption:null,focusedOptionId:null,focusableOptionsWithIds:[],focusedValue:null,inputIsHidden:!1,isFocused:!1,selectValue:[],clearFocusValueOnUpdate:!1,prevWasFocused:!1,inputIsHiddenAfterUpdate:void 0,prevProps:void 0,instancePrefix:"",isAppleDevice:!1},o.blockOptionHover=!1,o.isComposing=!1,o.commonProps=void 0,o.initialTouchX=0,o.initialTouchY=0,o.openAfterFocus=!1,o.scrollToFocusedOptionOnUpdate=!1,o.userIsDragging=void 0,o.controlRef=null,o.getControlRef=function(e){o.controlRef=e},o.focusedOptionRef=null,o.getFocusedOptionRef=function(e){o.focusedOptionRef=e},o.menuListRef=null,o.getMenuListRef=function(e){o.menuListRef=e},o.inputRef=null,o.getInputRef=function(e){o.inputRef=e},o.focus=o.focusInput,o.blur=o.blurInput,o.onChange=function(e,t){var n=o.props,i=n.onChange,r=n.name;t.name=r,o.ariaOnChange(e,t),i(e,t)},o.setValue=function(e,t,n){var i=o.props,r=i.closeMenuOnSelect,a=i.isMulti,s=i.inputValue;o.onInputChange("",{action:"set-value",prevInputValue:s}),r&&(o.setState({inputIsHiddenAfterUpdate:!a}),o.onMenuClose()),o.setState({clearFocusValueOnUpdate:!0}),o.onChange(e,{action:t,option:n})},o.selectOption=function(e){var t=o.props,n=t.blurInputOnSelect,i=t.isMulti,r=t.name,a=o.state.selectValue,s=i&&o.isOptionSelected(e,a),u=o.isOptionDisabled(e,a);if(s){var l=o.getOptionValue(e);o.setValue(a.filter(function(e){return o.getOptionValue(e)!==l}),"deselect-option",e)}else{if(u)return void o.ariaOnChange(e,{action:"select-option",option:e,name:r});i?o.setValue([].concat((0,d.A)(a),[e]),"select-option",e):o.setValue(e,"select-option")}n&&o.blurInput()},o.removeValue=function(e){var t=o.props.isMulti,n=o.state.selectValue,i=o.getOptionValue(e),r=n.filter(function(e){return o.getOptionValue(e)!==i}),a=j(t,r,r[0]||null);o.onChange(a,{action:"remove-value",removedValue:e}),o.focusInput()},o.clearValue=function(){var e=o.state.selectValue;o.onChange(j(o.props.isMulti,[],null),{action:"clear",removedValues:e})},o.popValue=function(){var e=o.props.isMulti,t=o.state.selectValue,n=t[t.length-1],i=t.slice(0,t.length-1),r=j(e,i,i[0]||null);n&&o.onChange(r,{action:"pop-value",removedValue:n})},o.getFocusedOptionId=function(e){return rt(o.state.focusableOptionsWithIds,e)},o.getFocusableOptionsWithIds=function(){return ot(tt(o.props,o.state.selectValue),o.getElementId("option"))},o.getValue=function(){return o.state.selectValue},o.cx=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return A.apply(void 0,[o.props.classNamePrefix].concat(t))},o.getOptionLabel=function(e){return at(o.props,e)},o.getOptionValue=function(e){return st(o.props,e)},o.getStyles=function(e,t){var n=o.props.unstyled,i=Je[e](t,n);i.boxSizing="border-box";var r=o.props.styles[e];return r?r(i,t):i},o.getClassNames=function(e,t){var n,i;return null===(n=(i=o.props.classNames)[e])||void 0===n?void 0:n.call(i,t)},o.getElementId=function(e){return"".concat(o.state.instancePrefix,"-").concat(e)},o.getComponents=function(){return e=o.props,s(s({},ve),e.components);var e},o.buildCategorizedOptions=function(){return tt(o.props,o.state.selectValue)},o.getCategorizedOptions=function(){return o.props.menuIsOpen?o.buildCategorizedOptions():[]},o.buildFocusableOptions=function(){return nt(o.buildCategorizedOptions())},o.getFocusableOptions=function(){return o.props.menuIsOpen?o.buildFocusableOptions():[]},o.ariaOnChange=function(e,t){o.setState({ariaSelection:s({value:e},t)})},o.onMenuMouseDown=function(e){0===e.button&&(e.stopPropagation(),e.preventDefault(),o.focusInput())},o.onMenuMouseMove=function(e){o.blockOptionHover=!1},o.onControlMouseDown=function(e){if(!e.defaultPrevented){var t=o.props.openMenuOnClick;o.state.isFocused?o.props.menuIsOpen?"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&o.onMenuClose():t&&o.openMenu("first"):(t&&(o.openAfterFocus=!0),o.focusInput()),"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&e.preventDefault()}},o.onDropdownIndicatorMouseDown=function(e){if(!(e&&"mousedown"===e.type&&0!==e.button||o.props.isDisabled)){var t=o.props,n=t.isMulti,i=t.menuIsOpen;o.focusInput(),i?(o.setState({inputIsHiddenAfterUpdate:!n}),o.onMenuClose()):o.openMenu("first"),e.preventDefault()}},o.onClearIndicatorMouseDown=function(e){e&&"mousedown"===e.type&&0!==e.button||(o.clearValue(),e.preventDefault(),o.openAfterFocus=!1,"touchend"===e.type?o.focusInput():setTimeout(function(){return o.focusInput()}))},o.onScroll=function(e){"boolean"==typeof o.props.closeMenuOnScroll?e.target instanceof HTMLElement&&x(e.target)&&o.props.onMenuClose():"function"==typeof o.props.closeMenuOnScroll&&o.props.closeMenuOnScroll(e)&&o.props.onMenuClose()},o.onCompositionStart=function(){o.isComposing=!0},o.onCompositionEnd=function(){o.isComposing=!1},o.onTouchStart=function(e){var t=e.touches,n=t&&t.item(0);n&&(o.initialTouchX=n.clientX,o.initialTouchY=n.clientY,o.userIsDragging=!1)},o.onTouchMove=function(e){var t=e.touches,n=t&&t.item(0);if(n){var i=Math.abs(n.clientX-o.initialTouchX),r=Math.abs(n.clientY-o.initialTouchY);o.userIsDragging=i>5||r>5}},o.onTouchEnd=function(e){o.userIsDragging||(o.controlRef&&!o.controlRef.contains(e.target)&&o.menuListRef&&!o.menuListRef.contains(e.target)&&o.blurInput(),o.initialTouchX=0,o.initialTouchY=0)},o.onControlTouchEnd=function(e){o.userIsDragging||o.onControlMouseDown(e)},o.onClearIndicatorTouchEnd=function(e){o.userIsDragging||o.onClearIndicatorMouseDown(e)},o.onDropdownIndicatorTouchEnd=function(e){o.userIsDragging||o.onDropdownIndicatorMouseDown(e)},o.handleInputChange=function(e){var t=o.props.inputValue,n=e.currentTarget.value;o.setState({inputIsHiddenAfterUpdate:!1}),o.onInputChange(n,{action:"input-change",prevInputValue:t}),o.props.menuIsOpen||o.onMenuOpen()},o.onInputFocus=function(e){o.props.onFocus&&o.props.onFocus(e),o.setState({inputIsHiddenAfterUpdate:!1,isFocused:!0}),(o.openAfterFocus||o.props.openMenuOnFocus)&&o.openMenu("first"),o.openAfterFocus=!1},o.onInputBlur=function(e){var t=o.props.inputValue;o.menuListRef&&o.menuListRef.contains(document.activeElement)?o.inputRef.focus():(o.props.onBlur&&o.props.onBlur(e),o.onInputChange("",{action:"input-blur",prevInputValue:t}),o.onMenuClose(),o.setState({focusedValue:null,isFocused:!1}))},o.onOptionHover=function(e){if(!o.blockOptionHover&&o.state.focusedOption!==e){var t=o.getFocusableOptions().indexOf(e);o.setState({focusedOption:e,focusedOptionId:t>-1?o.getFocusedOptionId(e):null})}},o.shouldHideSelectedOptions=function(){return pt(o.props)},o.onValueInputFocus=function(e){e.preventDefault(),e.stopPropagation(),o.focus()},o.onKeyDown=function(e){var t=o.props,n=t.isMulti,i=t.backspaceRemovesValue,r=t.escapeClearsValue,a=t.inputValue,s=t.isClearable,u=t.isDisabled,l=t.menuIsOpen,c=t.onKeyDown,p=t.tabSelectsValue,d=t.openMenuOnFocus,f=o.state,h=f.focusedOption,v=f.focusedValue,m=f.selectValue;if(!(u||"function"==typeof c&&(c(e),e.defaultPrevented))){switch(o.blockOptionHover=!0,e.key){case"ArrowLeft":if(!n||a)return;o.focusValue("previous");break;case"ArrowRight":if(!n||a)return;o.focusValue("next");break;case"Delete":case"Backspace":if(a)return;if(v)o.removeValue(v);else{if(!i)return;n?o.popValue():s&&o.clearValue()}break;case"Tab":if(o.isComposing)return;if(e.shiftKey||!l||!p||!h||d&&o.isOptionSelected(h,m))return;o.selectOption(h);break;case"Enter":if(229===e.keyCode)break;if(l){if(!h)return;if(o.isComposing)return;o.selectOption(h);break}return;case"Escape":l?(o.setState({inputIsHiddenAfterUpdate:!1}),o.onInputChange("",{action:"menu-close",prevInputValue:a}),o.onMenuClose()):s&&r&&o.clearValue();break;case" ":if(a)return;if(!l){o.openMenu("first");break}if(!h)return;o.selectOption(h);break;case"ArrowUp":l?o.focusOption("up"):o.openMenu("last");break;case"ArrowDown":l?o.focusOption("down"):o.openMenu("first");break;case"PageUp":if(!l)return;o.focusOption("pageup");break;case"PageDown":if(!l)return;o.focusOption("pagedown");break;case"Home":if(!l)return;o.focusOption("first");break;case"End":if(!l)return;o.focusOption("last");break;default:return}e.preventDefault()}},o.state.instancePrefix="react-select-"+(o.props.instanceId||++dt),o.state.selectValue=V(e.value),e.menuIsOpen&&o.state.selectValue.length){var i=o.getFocusableOptionsWithIds(),r=o.buildFocusableOptions(),a=r.indexOf(o.state.selectValue[0]);o.state.focusableOptionsWithIds=i,o.state.focusedOption=r[a],o.state.focusedOptionId=rt(i,r[a])}return o}return(0,l.A)(n,[{key:"componentDidMount",value:function(){this.startListeningComposition(),this.startListeningToTouch(),this.props.closeMenuOnScroll&&document&&document.addEventListener&&document.addEventListener("scroll",this.onScroll,!0),this.props.autoFocus&&this.focusInput(),this.props.menuIsOpen&&this.state.focusedOption&&this.menuListRef&&this.focusedOptionRef&&k(this.menuListRef,this.focusedOptionRef),(Ze()||Ke(/^iPhone/i)||Ke(/^iPad/i)||Ze()&&navigator.maxTouchPoints>1)&&this.setState({isAppleDevice:!0})}},{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.isDisabled,o=t.menuIsOpen,i=this.state.isFocused;(i&&!n&&e.isDisabled||i&&o&&!e.menuIsOpen)&&this.focusInput(),i&&n&&!e.isDisabled?this.setState({isFocused:!1},this.onMenuClose):i||n||!e.isDisabled||this.inputRef!==document.activeElement||this.setState({isFocused:!0}),this.menuListRef&&this.focusedOptionRef&&this.scrollToFocusedOptionOnUpdate&&(k(this.menuListRef,this.focusedOptionRef),this.scrollToFocusedOptionOnUpdate=!1)}},{key:"componentWillUnmount",value:function(){this.stopListeningComposition(),this.stopListeningToTouch(),document.removeEventListener("scroll",this.onScroll,!0)}},{key:"onMenuOpen",value:function(){this.props.onMenuOpen()}},{key:"onMenuClose",value:function(){this.onInputChange("",{action:"menu-close",prevInputValue:this.props.inputValue}),this.props.onMenuClose()}},{key:"onInputChange",value:function(e,t){this.props.onInputChange(e,t)}},{key:"focusInput",value:function(){this.inputRef&&this.inputRef.focus()}},{key:"blurInput",value:function(){this.inputRef&&this.inputRef.blur()}},{key:"openMenu",value:function(e){var t=this,n=this.state,o=n.selectValue,i=n.isFocused,r=this.buildFocusableOptions(),a="first"===e?0:r.length-1;if(!this.props.isMulti){var s=r.indexOf(o[0]);s>-1&&(a=s)}this.scrollToFocusedOptionOnUpdate=!(i&&this.menuListRef),this.setState({inputIsHiddenAfterUpdate:!1,focusedValue:null,focusedOption:r[a],focusedOptionId:this.getFocusedOptionId(r[a])},function(){return t.onMenuOpen()})}},{key:"focusValue",value:function(e){var t=this.state,n=t.selectValue,o=t.focusedValue;if(this.props.isMulti){this.setState({focusedOption:null});var i=n.indexOf(o);o||(i=-1);var r=n.length-1,a=-1;if(n.length){switch(e){case"previous":a=0===i?0:-1===i?r:i-1;break;case"next":i>-1&&i<r&&(a=i+1)}this.setState({inputIsHidden:-1!==a,focusedValue:n[a]})}}}},{key:"focusOption",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"first",t=this.props.pageSize,n=this.state.focusedOption,o=this.getFocusableOptions();if(o.length){var i=0,r=o.indexOf(n);n||(r=-1),"up"===e?i=r>0?r-1:o.length-1:"down"===e?i=(r+1)%o.length:"pageup"===e?(i=r-t)<0&&(i=0):"pagedown"===e?(i=r+t)>o.length-1&&(i=o.length-1):"last"===e&&(i=o.length-1),this.scrollToFocusedOptionOnUpdate=!0,this.setState({focusedOption:o[i],focusedValue:null,focusedOptionId:this.getFocusedOptionId(o[i])})}}},{key:"getTheme",value:function(){return this.props.theme?"function"==typeof this.props.theme?this.props.theme(Qe):s(s({},Qe),this.props.theme):Qe}},{key:"getCommonProps",value:function(){var e=this.clearValue,t=this.cx,n=this.getStyles,o=this.getClassNames,i=this.getValue,r=this.selectOption,a=this.setValue,s=this.props,u=s.isMulti,l=s.isRtl,c=s.options;return{clearValue:e,cx:t,getStyles:n,getClassNames:o,getValue:i,hasValue:this.hasValue(),isMulti:u,isRtl:l,options:c,selectOption:r,selectProps:s,setValue:a,theme:this.getTheme()}}},{key:"hasValue",value:function(){return this.state.selectValue.length>0}},{key:"hasOptions",value:function(){return!!this.getFocusableOptions().length}},{key:"isClearable",value:function(){var e=this.props,t=e.isClearable,n=e.isMulti;return void 0===t?n:t}},{key:"isOptionDisabled",value:function(e,t){return ut(this.props,e,t)}},{key:"isOptionSelected",value:function(e,t){return lt(this.props,e,t)}},{key:"filterOption",value:function(e,t){return ct(this.props,e,t)}},{key:"formatOptionLabel",value:function(e,t){if("function"==typeof this.props.formatOptionLabel){var n=this.props.inputValue,o=this.state.selectValue;return this.props.formatOptionLabel(e,{context:t,inputValue:n,selectValue:o})}return this.getOptionLabel(e)}},{key:"formatGroupLabel",value:function(e){return this.props.formatGroupLabel(e)}},{key:"startListeningComposition",value:function(){document&&document.addEventListener&&(document.addEventListener("compositionstart",this.onCompositionStart,!1),document.addEventListener("compositionend",this.onCompositionEnd,!1))}},{key:"stopListeningComposition",value:function(){document&&document.removeEventListener&&(document.removeEventListener("compositionstart",this.onCompositionStart),document.removeEventListener("compositionend",this.onCompositionEnd))}},{key:"startListeningToTouch",value:function(){document&&document.addEventListener&&(document.addEventListener("touchstart",this.onTouchStart,!1),document.addEventListener("touchmove",this.onTouchMove,!1),document.addEventListener("touchend",this.onTouchEnd,!1))}},{key:"stopListeningToTouch",value:function(){document&&document.removeEventListener&&(document.removeEventListener("touchstart",this.onTouchStart),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"renderInput",value:function(){var e=this.props,t=e.isDisabled,n=e.isSearchable,r=e.inputId,a=e.inputValue,u=e.tabIndex,l=e.form,c=e.menuIsOpen,p=e.required,d=this.getComponents().Input,f=this.state,h=f.inputIsHidden,v=f.ariaSelection,m=this.commonProps,g=r||this.getElementId("input"),b=s(s(s({"aria-autocomplete":"list","aria-expanded":c,"aria-haspopup":!0,"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props["aria-invalid"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-required":p,role:"combobox","aria-activedescendant":this.state.isAppleDevice?void 0:this.state.focusedOptionId||""},c&&{"aria-controls":this.getElementId("listbox")}),!n&&{"aria-readonly":!0}),this.hasValue()?"initial-input-focus"===(null==v?void 0:v.action)&&{"aria-describedby":this.getElementId("live-region")}:{"aria-describedby":this.getElementId("placeholder")});return n?i.createElement(d,(0,o.A)({},m,{autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",id:g,innerRef:this.getInputRef,isDisabled:t,isHidden:h,onBlur:this.onInputBlur,onChange:this.handleInputChange,onFocus:this.onInputFocus,spellCheck:"false",tabIndex:u,form:l,type:"text",value:a},b)):i.createElement(Le,(0,o.A)({id:g,innerRef:this.getInputRef,onBlur:this.onInputBlur,onChange:w,onFocus:this.onInputFocus,disabled:t,tabIndex:u,inputMode:"none",form:l,value:""},b))}},{key:"renderPlaceholderOrValue",value:function(){var e=this,t=this.getComponents(),n=t.MultiValue,r=t.MultiValueContainer,a=t.MultiValueLabel,s=t.MultiValueRemove,u=t.SingleValue,l=t.Placeholder,c=this.commonProps,p=this.props,d=p.controlShouldRenderValue,f=p.isDisabled,h=p.isMulti,v=p.inputValue,m=p.placeholder,g=this.state,b=g.selectValue,O=g.focusedValue,y=g.isFocused;if(!this.hasValue()||!d)return v?null:i.createElement(l,(0,o.A)({},c,{key:"placeholder",isDisabled:f,isFocused:y,innerProps:{id:this.getElementId("placeholder")}}),m);if(h)return b.map(function(t,u){var l=t===O,p="".concat(e.getOptionLabel(t),"-").concat(e.getOptionValue(t));return i.createElement(n,(0,o.A)({},c,{components:{Container:r,Label:a,Remove:s},isFocused:l,isDisabled:f,key:p,index:u,removeProps:{onClick:function(){return e.removeValue(t)},onTouchEnd:function(){return e.removeValue(t)},onMouseDown:function(e){e.preventDefault()}},data:t}),e.formatOptionLabel(t,"value"))});if(v)return null;var I=b[0];return i.createElement(u,(0,o.A)({},c,{data:I,isDisabled:f}),this.formatOptionLabel(I,"value"))}},{key:"renderClearIndicator",value:function(){var e=this.getComponents().ClearIndicator,t=this.commonProps,n=this.props,r=n.isDisabled,a=n.isLoading,s=this.state.isFocused;if(!this.isClearable()||!e||r||!this.hasValue()||a)return null;var u={onMouseDown:this.onClearIndicatorMouseDown,onTouchEnd:this.onClearIndicatorTouchEnd,"aria-hidden":"true"};return i.createElement(e,(0,o.A)({},t,{innerProps:u,isFocused:s}))}},{key:"renderLoadingIndicator",value:function(){var e=this.getComponents().LoadingIndicator,t=this.commonProps,n=this.props,r=n.isDisabled,a=n.isLoading,s=this.state.isFocused;return e&&a?i.createElement(e,(0,o.A)({},t,{innerProps:{"aria-hidden":"true"},isDisabled:r,isFocused:s})):null}},{key:"renderIndicatorSeparator",value:function(){var e=this.getComponents(),t=e.DropdownIndicator,n=e.IndicatorSeparator;if(!t||!n)return null;var r=this.commonProps,a=this.props.isDisabled,s=this.state.isFocused;return i.createElement(n,(0,o.A)({},r,{isDisabled:a,isFocused:s}))}},{key:"renderDropdownIndicator",value:function(){var e=this.getComponents().DropdownIndicator;if(!e)return null;var t=this.commonProps,n=this.props.isDisabled,r=this.state.isFocused,a={onMouseDown:this.onDropdownIndicatorMouseDown,onTouchEnd:this.onDropdownIndicatorTouchEnd,"aria-hidden":"true"};return i.createElement(e,(0,o.A)({},t,{innerProps:a,isDisabled:n,isFocused:r}))}},{key:"renderMenu",value:function(){var e=this,t=this.getComponents(),n=t.Group,r=t.GroupHeading,a=t.Menu,s=t.MenuList,u=t.MenuPortal,l=t.LoadingMessage,c=t.NoOptionsMessage,p=t.Option,d=this.commonProps,f=this.state.focusedOption,h=this.props,v=h.captureMenuScroll,m=h.inputValue,g=h.isLoading,b=h.loadingMessage,O=h.minMenuHeight,y=h.maxMenuHeight,I=h.menuIsOpen,C=h.menuPlacement,w=h.menuPosition,S=h.menuPortalTarget,A=h.menuShouldBlockScroll,V=h.menuShouldScrollIntoView,M=h.noOptionsMessage,E=h.onMenuScrollToTop,x=h.onMenuScrollToBottom;if(!I)return null;var P,D=function(t,n){var r=t.type,a=t.data,s=t.isDisabled,u=t.isSelected,l=t.label,c=t.value,h=f===a,v=s?void 0:function(){return e.onOptionHover(a)},m=s?void 0:function(){return e.selectOption(a)},g="".concat(e.getElementId("option"),"-").concat(n),b={id:g,onClick:m,onMouseMove:v,onMouseOver:v,tabIndex:-1,role:"option","aria-selected":e.state.isAppleDevice?void 0:u};return i.createElement(p,(0,o.A)({},d,{innerProps:b,data:a,isDisabled:s,isSelected:u,key:g,label:l,type:r,value:c,isFocused:h,innerRef:h?e.getFocusedOptionRef:void 0}),e.formatOptionLabel(t.data,"menu"))};if(this.hasOptions())P=this.getCategorizedOptions().map(function(t){if("group"===t.type){var a=t.data,s=t.options,u=t.index,l="".concat(e.getElementId("group"),"-").concat(u),c="".concat(l,"-heading");return i.createElement(n,(0,o.A)({},d,{key:l,data:a,options:s,Heading:r,headingProps:{id:c,data:t.data},label:e.formatGroupLabel(t.data)}),t.options.map(function(e){return D(e,"".concat(u,"-").concat(e.index))}))}if("option"===t.type)return D(t,"".concat(t.index))});else if(g){var R=b({inputValue:m});if(null===R)return null;P=i.createElement(l,d,R)}else{var k=M({inputValue:m});if(null===k)return null;P=i.createElement(c,d,k)}var L={minMenuHeight:O,maxMenuHeight:y,menuPlacement:C,menuPosition:w,menuShouldScrollIntoView:V},F=i.createElement(q,(0,o.A)({},d,L),function(t){var n=t.ref,r=t.placerProps,u=r.placement,l=r.maxHeight;return i.createElement(a,(0,o.A)({},d,L,{innerRef:n,innerProps:{onMouseDown:e.onMenuMouseDown,onMouseMove:e.onMenuMouseMove},isLoading:g,placement:u}),i.createElement(Ge,{captureEnabled:v,onTopArrive:E,onBottomArrive:x,lockEnabled:A},function(t){return i.createElement(s,(0,o.A)({},d,{innerRef:function(n){e.getMenuListRef(n),t(n)},innerProps:{role:"listbox","aria-multiselectable":d.isMulti,id:e.getElementId("listbox")},isLoading:g,maxHeight:l,focusedOption:f}),P)}))});return S||"fixed"===w?i.createElement(u,(0,o.A)({},d,{appendTo:S,controlElement:this.controlRef,menuPlacement:C,menuPosition:w}),F):F}},{key:"renderFormField",value:function(){var e=this,t=this.props,n=t.delimiter,o=t.isDisabled,r=t.isMulti,a=t.name,s=t.required,u=this.state.selectValue;if(s&&!this.hasValue()&&!o)return i.createElement(qe,{name:a,onFocus:this.onValueInputFocus});if(a&&!o){if(r){if(n){var l=u.map(function(t){return e.getOptionValue(t)}).join(n);return i.createElement("input",{name:a,type:"hidden",value:l})}var c=u.length>0?u.map(function(t,n){return i.createElement("input",{key:"i-".concat(n),name:a,type:"hidden",value:e.getOptionValue(t)})}):i.createElement("input",{name:a,type:"hidden",value:""});return i.createElement("div",null,c)}var p=u[0]?this.getOptionValue(u[0]):"";return i.createElement("input",{name:a,type:"hidden",value:p})}}},{key:"renderLiveRegion",value:function(){var e=this.commonProps,t=this.state,n=t.ariaSelection,r=t.focusedOption,a=t.focusedValue,s=t.isFocused,u=t.selectValue,l=this.getFocusableOptions();return i.createElement(Ce,(0,o.A)({},e,{id:this.getElementId("live-region"),ariaSelection:n,focusedOption:r,focusedValue:a,isFocused:s,selectValue:u,focusableOptions:l,isAppleDevice:this.state.isAppleDevice}))}},{key:"render",value:function(){var e=this.getComponents(),t=e.Control,n=e.IndicatorsContainer,r=e.SelectContainer,a=e.ValueContainer,s=this.props,u=s.className,l=s.id,c=s.isDisabled,p=s.menuIsOpen,d=this.state.isFocused,f=this.commonProps=this.getCommonProps();return i.createElement(r,(0,o.A)({},f,{className:u,innerProps:{id:l,onKeyDown:this.onKeyDown},isDisabled:c,isFocused:d}),this.renderLiveRegion(),i.createElement(t,(0,o.A)({},f,{innerRef:this.getControlRef,innerProps:{onMouseDown:this.onControlMouseDown,onTouchEnd:this.onControlTouchEnd},isDisabled:c,isFocused:d,menuIsOpen:p}),i.createElement(a,(0,o.A)({},f,{isDisabled:c}),this.renderPlaceholderOrValue(),this.renderInput()),i.createElement(n,(0,o.A)({},f,{isDisabled:c}),this.renderClearIndicator(),this.renderLoadingIndicator(),this.renderIndicatorSeparator(),this.renderDropdownIndicator())),this.renderMenu(),this.renderFormField())}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=t.prevProps,o=t.clearFocusValueOnUpdate,i=t.inputIsHiddenAfterUpdate,r=t.ariaSelection,a=t.isFocused,u=t.prevWasFocused,l=t.instancePrefix,c=e.options,p=e.value,d=e.menuIsOpen,f=e.inputValue,h=e.isMulti,v=V(p),m={};if(n&&(p!==n.value||c!==n.options||d!==n.menuIsOpen||f!==n.inputValue)){var g=d?function(e,t){return nt(tt(e,t))}(e,v):[],b=d?ot(tt(e,v),"".concat(l,"-option")):[],O=o?function(e,t){var n=e.focusedValue,o=e.selectValue.indexOf(n);if(o>-1){if(t.indexOf(n)>-1)return n;if(o<t.length)return t[o]}return null}(t,v):null,y=function(e,t){var n=e.focusedOption;return n&&t.indexOf(n)>-1?n:t[0]}(t,g);m={selectValue:v,focusedOption:y,focusedOptionId:rt(b,y),focusableOptionsWithIds:b,focusedValue:O,clearFocusValueOnUpdate:!1}}var I=null!=i&&e!==n?{inputIsHidden:i,inputIsHiddenAfterUpdate:void 0}:{},C=r,w=a&&u;return a&&!w&&(C={value:j(h,v,v[0]||null),options:v,action:"initial-input-focus"},w=!u),"initial-input-focus"===(null==r?void 0:r.action)&&(C=null),s(s(s({},m),I),{},{prevProps:e,ariaSelection:C,prevWasFocused:w})}}]),n}(i.Component);ft.defaultProps=$e;var ht=["defaultInputValue","defaultMenuIsOpen","defaultValue","inputValue","menuIsOpen","onChange","onInputChange","onMenuClose","onMenuOpen","value"],vt=["defaultOptions","cacheOptions","loadOptions","options","isLoading","onInputChange","filterOption"],mt=(0,i.forwardRef)(function(e,t){var n=function(e){var t=e.defaultOptions,n=void 0!==t&&t,o=e.cacheOptions,a=void 0!==o&&o,u=e.loadOptions;e.options;var l=e.isLoading,c=void 0!==l&&l,p=e.onInputChange,d=e.filterOption,f=void 0===d?null:d,h=g(e,vt),m=h.inputValue,b=(0,i.useRef)(void 0),O=(0,i.useRef)(!1),y=v((0,i.useState)(Array.isArray(n)?n:void 0),2),I=y[0],C=y[1],w=v((0,i.useState)(void 0!==m?m:""),2),S=w[0],A=w[1],V=v((0,i.useState)(!0===n),2),M=V[0],E=V[1],x=v((0,i.useState)(void 0),2),P=x[0],D=x[1],R=v((0,i.useState)([]),2),k=R[0],L=R[1],F=v((0,i.useState)(!1),2),T=F[0],H=F[1],Y=v((0,i.useState)({}),2),U=Y[0],j=Y[1],B=v((0,i.useState)(void 0),2),z=B[0],N=B[1],W=v((0,i.useState)(void 0),2),_=W[0],G=W[1];a!==_&&(j({}),G(a)),n!==z&&(C(Array.isArray(n)?n:void 0),N(n)),(0,i.useEffect)(function(){return O.current=!0,function(){O.current=!1}},[]);var X=(0,i.useCallback)(function(e,t){if(!u)return t();var n=u(e,t);n&&"function"==typeof n.then&&n.then(t,function(){return t()})},[u]);(0,i.useEffect)(function(){!0===n&&X(S,function(e){O.current&&(C(e||[]),E(!!b.current))})},[]);var q=(0,i.useCallback)(function(e,t){var n=function(e,t,n){if(n){var o=n(e,t);if("string"==typeof o)return o}return e}(e,t,p);if(!n)return b.current=void 0,A(""),D(""),L([]),E(!1),void H(!1);if(a&&U[n])A(n),D(n),L(U[n]),E(!1),H(!1);else{var o=b.current={};A(n),E(!0),H(!P),X(n,function(e){O&&o===b.current&&(b.current=void 0,E(!1),D(n),L(e||[]),H(!1),j(e?s(s({},U),{},(0,r.A)({},n,e)):U))})}},[a,X,P,U,p]),K=T?[]:S&&P?k:I||[];return s(s({},h),{},{options:K,isLoading:M||c,onInputChange:q,filterOption:f})}(e),a=function(e){var t=e.defaultInputValue,n=void 0===t?"":t,o=e.defaultMenuIsOpen,r=void 0!==o&&o,a=e.defaultValue,u=void 0===a?null:a,l=e.inputValue,c=e.menuIsOpen,p=e.onChange,d=e.onInputChange,f=e.onMenuClose,h=e.onMenuOpen,m=e.value,b=g(e,ht),O=v((0,i.useState)(void 0!==l?l:n),2),y=O[0],I=O[1],C=v((0,i.useState)(void 0!==c?c:r),2),w=C[0],S=C[1],A=v((0,i.useState)(void 0!==m?m:u),2),V=A[0],M=A[1],E=(0,i.useCallback)(function(e,t){"function"==typeof p&&p(e,t),M(e)},[p]),x=(0,i.useCallback)(function(e,t){var n;"function"==typeof d&&(n=d(e,t)),I(void 0!==n?n:e)},[d]),P=(0,i.useCallback)(function(){"function"==typeof h&&h(),S(!0)},[h]),D=(0,i.useCallback)(function(){"function"==typeof f&&f(),S(!1)},[f]),R=void 0!==l?l:y,k=void 0!==c?c:w,L=void 0!==m?m:V;return s(s({},b),{},{inputValue:R,menuIsOpen:k,onChange:E,onInputChange:x,onMenuClose:D,onMenuOpen:P,value:L})}(n);return i.createElement(ft,(0,o.A)({ref:t},a))}),gt=mt},43145:(e,t,n)=>{function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}n.d(t,{A:()=>o})},45458:(e,t,n)=>{n.d(t,{A:()=>r});var o=n(43145),i=n(27800);function r(e){return function(e){if(Array.isArray(e))return(0,o.A)(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||(0,i.A)(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},52962:(e,t,n)=>{function o(e){return o=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},o(e)}function i(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(i=function(){return!!e})()}n.d(t,{A:()=>s});var r=n(82284),a=n(9417);function s(e){var t=i();return function(){var n,i=o(e);if(t){var s=o(this).constructor;n=Reflect.construct(i,arguments,s)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"==(0,r.A)(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return(0,a.A)(e)}(this,n)}}},64467:(e,t,n)=>{n.d(t,{A:()=>i});var o=n(20816);function i(e,t,n){return(t=(0,o.A)(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}},82284:(e,t,n)=>{function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}n.d(t,{A:()=>o})},85501:(e,t,n)=>{n.d(t,{A:()=>i});var o=n(63662);function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,o.A)(e,t)}},92901:(e,t,n)=>{n.d(t,{A:()=>r});var o=n(20816);function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(0,o.A)(i.key),i)}}function r(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}}}]);