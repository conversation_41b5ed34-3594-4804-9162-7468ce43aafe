"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[622,4575,8432],{38432:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var r=a(81621),s=a(72346),o=a(31127),n=a(979),d=a(66212);const c=(0,r.vt)(e=>({startDate:(0,s.default)((0,o.default)((0,n.A)(new Date,7)),"yyyy-MM-dd"),setStartDate:t=>e(e=>({startDate:t})),endDate:(0,s.default)((0,d.default)((0,n.A)(new Date,1)),"yyyy-MM-dd"),setEndDate:t=>e(e=>({endDate:t})),range:"last-7-days",setRange:t=>e(e=>({range:t}))}))},44575:(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var r=a(27723),s=a(86087),o=a(90622),n=a(45111),d=a(38432),c=a(10790);const l=(0,s.memo)(()=>{const{noData:e,startExport:t,exportLink:l,fetchExportDatarequestsProgress:i,generating:u,progress:p}=(0,o.default)(),[g,h]=(0,s.useState)(null),{startDate:f,endDate:m}=(0,d.default)();return(0,s.useEffect)(()=>{Promise.all([a.e(3569),a.e(4715),a.e(9808),a.e(7660)]).then(a.bind(a,95279)).then(({default:e})=>{h(()=>e)})},[]),(0,s.useEffect)(()=>{i(!0)},[]),(0,s.useEffect)(()=>{p<100&&u&&i(!1,f,m)},[p]),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsxs)("div",{className:"cmplz-table-header-controls",children:[g&&(0,c.jsx)(g,{}),(0,c.jsxs)("button",{disabled:u,className:"button button-default cmplz-field-button",onClick:()=>t(),children:[(0,r.__)("Export to CSV","complianz-gdpr"),u&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(n.default,{name:"loading",color:"grey"})," ",p,"%"]})]})]}),p>=100&&(""!==l||e)&&(0,c.jsxs)("div",{className:"cmplz-selected-document",children:[!e&&(0,r.__)("Your Data Requests Export has been completed.","complianz-gdpr"),e&&(0,r.__)("Your selection does not contain any data.","complianz-gdpr"),(0,c.jsx)("div",{className:"cmplz-selected-document-controls",children:!e&&(0,c.jsx)("a",{className:"button button-default",href:l,children:(0,r.__)("Download","complianz-gdpr")})})]})]})})},90622:(e,t,a)=>{a.r(t),a.d(t,{default:()=>n});var r=a(81621),s=a(9588),o=a(16535);a(86087);const n=(0,r.vt)((e,t)=>({recordsLoaded:!1,searchValue:"",setSearchValue:t=>e({searchValue:t}),status:"open",setStatus:t=>e({status:t}),selectedRecords:[],setSelectedRecords:t=>e({selectedRecords:t}),fetching:!1,generating:!1,progress:!1,records:[],totalRecords:0,totalOpen:0,exportLink:"",noData:!1,indeterminate:!1,setIndeterminate:t=>e({indeterminate:t}),paginationPerPage:10,pagination:{currentPage:1},setPagination:t=>e({pagination:t}),orderBy:"ID",setOrderBy:t=>e({orderBy:t}),order:"DESC",setOrder:t=>e({order:t}),deleteRecords:async a=>{let r={};r.per_page=t().paginationPerPage,r.page=t().pagination.currentPage,r.order=t().order.toUpperCase(),r.orderBy=t().orderBy,r.search=t().searchValue,r.status=t().status;let o=t().records.filter(e=>a.includes(e.ID));e(e=>({records:e.records.filter(e=>!a.includes(e.ID))})),r.records=o,await s.doAction("delete_datarequests",r).then(e=>e).catch(e=>{console.error(e)}),await t().fetchData(),t().setSelectedRecords([]),t().setIndeterminate(!1)},resolveRecords:async a=>{let r={};r.per_page=t().paginationPerPage,r.page=t().pagination.currentPage,r.order=t().order.toUpperCase(),r.orderBy=t().orderBy,r.search=t().searchValue,r.status=t().status,e((0,o.Ay)(e=>{e.records.forEach(function(t,r){a.includes(t.ID)&&(e.records[r].resolved=!0)})})),r.records=t().records.filter(e=>a.includes(e.ID)),await s.doAction("resolve_datarequests",r).then(e=>e).catch(e=>{console.error(e)}),await t().fetchData(),t().setSelectedRecords([]),t().setIndeterminate(!1)},fetchData:async()=>{if(t().fetching)return;e({fetching:!0});let a={};a.per_page=t().paginationPerPage,a.page=t().pagination.currentPage,a.order=t().order.toUpperCase(),a.orderBy=t().orderBy,a.search=t().searchValue,a.status=t().status;const{records:r,totalRecords:o,totalOpen:n}=await s.doAction("get_datarequests",a).then(e=>e).catch(e=>{console.error(e)});e(()=>({recordsLoaded:!0,records:r,totalRecords:o,totalOpen:n,fetching:!1}))},startExport:async()=>{e({generating:!0,progress:0,exportLink:""})},fetchExportDatarequestsProgress:async(t,a,r)=>{(t=void 0!==t&&t)||e({generating:!0});let o={};o.startDate=a,o.endDate=r,o.statusOnly=t;const{progress:n,exportLink:d,noData:c}=await s.doAction("export_datarequests",o).then(e=>e).catch(e=>{console.error(e)});let l=!1;n<100&&(l=!0),e({progress:n,exportLink:d,generating:l,noData:c})}}))}}]);