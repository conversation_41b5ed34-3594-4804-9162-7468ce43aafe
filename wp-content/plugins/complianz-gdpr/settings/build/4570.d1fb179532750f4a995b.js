"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[293,622,1203,1249,1699,3192,3407,3518,3971,4186,4570,4759,5228,5683,6644,6716,7338,7907,8084,8217,8830,8895,8931,9166,9406,9567,9582,9684],{1203:(e,t,s)=>{s.r(t),s.d(t,{default:()=>a});const a=()=>null},3192:(e,t,s)=>{s.r(t),s.d(t,{default:()=>c});var a=s(27723),n=s(86087),i=s(56716),l=s(4219),r=s(98217),o=s(10790);const c=()=>{const{getFieldValue:e,fields:t}=(0,l.default)(),[s,c]=(0,n.useState)(!1);(0,n.useEffect)(()=>{c(e("records_of_consent"))},[t]);const{processingAgreementOptions:d,dataBreachOptions:p,proofOfConsentOptions:u}=(0,i.default)();return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("h3",{className:"cmplz-h4",children:(0,a.__)("Other documents","complianz-gdpr")}),(0,o.jsx)(r.default,{type:"processing-agreements",link:"#tools/processing-agreements",name:(0,a.__)("Processing Agreement","complianz-gdpr"),options:d}),(0,o.jsx)(r.default,{type:"data-breaches",link:"#tools/data-breach-reports",name:(0,a.__)("Data Breach","complianz-gdpr"),options:p}),(0,o.jsx)(r.default,{type:"proof-of-consent",link:s?"#tools/records-of-consent":"#tools/proof-of-consent",name:(0,a.__)("Proof of Consent","complianz-gdpr"),options:u})]})}},9684:(e,t,s)=>{s.r(t),s.d(t,{default:()=>m});var a=s(27723),n=s(45111),i=s(47143),l=s(9588),r=s(99166),o=s(4219),c=s(32828),d=s(52043),p=s(99418),u=s(10790);const m=({notice:e,index:t})=>{const{dismissNotice:s,fetchProgressData:m}=(0,c.default)(),{getField:g,setHighLightField:h,fetchFieldsData:f}=(0,o.default)(),{setSelectedSubMenuItem:_}=(0,d.default)();let z="premium"===e.icon,x=e.url&&-1!==e.url.indexOf("complianz.io"),b=e.status.charAt(0).toUpperCase()+e.status.slice(1);return(0,u.jsxs)("div",{className:"cmplz-task-element",children:[(0,u.jsx)("span",{className:"cmplz-task-status cmplz-"+e.status,children:b}),(0,u.jsx)("p",{className:"cmplz-task-message",dangerouslySetInnerHTML:{__html:p.A.sanitize(e.message)}}),x&&e.url&&(0,u.jsx)("a",{target:"_blank",href:e.url,rel:"noopener noreferrer",children:(0,a.__)("More info","complianz-gdpr")}),e.clear_cache_id&&(0,u.jsx)("span",{className:"cmplz-task-enable button button-secondary",onClick:()=>(async e=>{let t={};t.cache_id=e,l.doAction("clear_cache",t).then(async e=>{(0,i.dispatch)("core/notices").createNotice("success",(0,a.__)("Re-started test","complianz-gdpr"),{__unstableHTML:!0,id:"cmplz_clear_cache",type:"snackbar",isDismissible:!0}).then((0,r.default)(3e3)).then(e=>{(0,i.dispatch)("core/notices").removeNotice("rsssl_clear_cache")}),await f(),await m()})})(e.clear_cache_id),children:(0,a.__)("Re-check","complianz-gdpr")}),!z&&!x&&e.url&&(0,u.jsx)("a",{className:"cmplz-task-enable button button-secondary",href:e.url,children:(0,a.__)("View","complianz-gdpr")}),!z&&e.highlight_field_id&&(0,u.jsx)("span",{className:"cmplz-task-enable button button-secondary",onClick:()=>(async()=>{h(e.highlight_field_id);let t=g(e.highlight_field_id);await _(t.menu_id)})(),children:(0,a.__)("View","complianz-gdpr")}),e.plus_one&&(0,u.jsx)("span",{className:"cmplz-plusone",children:"1"}),e.dismissible&&"completed"!==e.status&&(0,u.jsx)("div",{className:"cmplz-task-dismiss",children:(0,u.jsx)("button",{type:"button",onClick:t=>s(e.id),children:(0,u.jsx)(n.default,{name:"times"})})})]},t)}},19567:(e,t,s)=>{s.r(t),s.d(t,{default:()=>g});var a=s(86087),n=s(56716),i=s(91699),l=s(3192),r=s(45111),o=s(27723),c=s(4219),d=s(99695),p=s(96979),u=s(10790);const m=e=>{const{document:t}=e,{showSavedSettingsNotice:s}=(0,c.default)();let a="sync"===t.status?"green":"grey",n="sync"===t.status?(0,o.__)("Document is kept up to date by Complianz","complianz-gdpr"):(0,o.__)("Document is not kept up to date by Complianz","complianz-gdpr"),i=t.exists?"green":"grey",l=t.exists?(0,o.__)("Validated","complianz-gdpr"):(0,o.__)("Missing document","complianz-gdpr"),d=t.required?(0,o.__)("Click to copy the document shortcode","complianz-gdpr"):(0,o.__)("Not enabled","complianz-gdpr");return t.required&&t.exists||(i=a="grey",l=n=(0,o.__)("Not enabled","complianz-gdpr")),(0,u.jsxs)("div",{className:"cmplz-single-document",children:[(0,u.jsxs)("div",{className:"cmplz-single-document-title",children:[t.permalink&&(0,u.jsx)("a",{href:t.permalink,children:t.title}),!t.permalink&&t.readmore&&(0,u.jsx)("a",{href:t.readmore,children:t.title}),!t.permalink&&!t.readmore&&t.title]}),(0,u.jsx)(r.default,{name:"sync",color:a,tooltip:n,size:14}),(0,u.jsx)(r.default,{name:"circle-check",color:i,tooltip:l,size:14}),(0,u.jsx)("div",{onClick:e=>((e,t)=>{let a;e.target.classList.add("cmplz-click-animation");let n=window.document.createElement("input");window.document.getElementsByTagName("body")[0].appendChild(n),n.value=t,n.select();try{a=window.document.execCommand("copy")}catch(e){a=!1}n.parentElement.removeChild(n),a&&s((0,o.__)("Copied shortcode","complianz-gdpr"))})(e,t.shortcode),children:(0,u.jsx)(r.default,{name:"shortcode",color:i,tooltip:d,size:14})})]})},g=()=>{const{region:e,documentDataLoaded:t,getDocuments:s,documents:r}=(0,n.default)(),{documentsChanged:o}=(0,p.UseDocumentsData)(),[c,g]=(0,a.useState)([]);return(0,a.useEffect)(()=>{t||s()},[]),(0,a.useEffect)(()=>{t&&o&&s()},[o]),(0,a.useEffect)(()=>{let t=r.filter(t=>t.region===e)[0];t&&(t=t.documents,g(t))},[e,r]),t?(0,u.jsxs)(u.Fragment,{children:[c.map((e,t)=>(0,u.jsx)(m,{document:e},t)),!cmplz_settings.is_premium&&(0,u.jsx)(i.default,{}),cmplz_settings.is_premium&&(0,u.jsx)(l.default,{})]}):(0,u.jsx)(d.default,{lines:"3"})}},20293:(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});var a=s(27723),n=s(10790);const i=()=>(0,n.jsx)("a",{href:"https://complianz.io/docs/",className:"button button-default cmplz-flex-push-left",target:"_blank",rel:"noopener noreferrer",children:(0,a.__)("View all","complianz-gdpr")})},23971:(e,t,s)=>{s.r(t),s.d(t,{default:()=>c});var a=s(27723),n=s(45111),i=s(4219),l=s(32828),r=s(86087),o=s(10790);const c=()=>{const{fields:e,getFieldValue:t}=(0,i.default)(),{showCookieBanner:s,fetchProgressData:c,progressLoaded:d}=(0,l.default)(),[p,u]=(0,r.useState)(!1),[m,g]=(0,r.useState)(!1),[h,f]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{d||c()},[]),(0,r.useEffect)(()=>{let e="yes"===t("enable_cookie_blocker")?"green":"grey";u(e),e=1==t("dont_use_placeholders")?"grey":"green",g(e),e=s?"green":"grey",f(e)},[e,s]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("a",{href:"#wizard",className:"button button-primary",children:(0,a.__)("Continue Wizard","complianz-gdpr")}),(0,o.jsxs)("div",{className:"cmplz-legend cmplz-flex-push-right",children:[(0,o.jsx)(n.default,{name:"circle-check",color:p,size:14}),(0,o.jsx)("span",{children:(0,a.__)("Cookie Blocker","complianz-gdpr")})]}),(0,o.jsxs)("div",{className:"cmplz-legend",children:[(0,o.jsx)(n.default,{name:"circle-check",color:m,size:14}),(0,o.jsx)("span",{children:(0,a.__)("Placeholders","complianz-gdpr")})]}),(0,o.jsxs)("div",{className:"cmplz-legend",children:[(0,o.jsx)(n.default,{name:"circle-check",color:h,size:14}),(0,o.jsx)("span",{children:(0,a.__)("Consent Banner","complianz-gdpr")})]})]})}},24570:(e,t,s)=>{s.r(t),s.d(t,{default:()=>b});var a=s(27723),n=s(93407),i=s(99582),l=s(23971),r=s(19567),o=s(61249),c=s(38931),d=s(56644),p=s(71137),u=s(1203),m=s(64186),g=s(38830),h=s(20293),f=s(57907),_=s(65170),z=s(10790),x={ProgressBlock:n.default,ProgressHeader:i.default,ProgressFooter:l.default,DocumentsBlock:r.default,DocumentsHeader:o.default,DocumentsFooter:c.default,TipsTricks:g.default,TipsTricksFooter:h.default,ToolsHeader:p.default,ToolsFooter:u.default,Tools:d.default,OtherPluginsHeader:f.default,OtherPlugins:m.default};const b=({block:e})=>{const t=e,s="cmplz-grid-item "+t.class+" cmplz-"+t.id,n=!!e.footer&&e.footer.data;return(0,z.jsx)(_.default,{fallback:"Could not load: "+t.id,children:(0,z.jsxs)("div",{className:s,children:[(0,z.jsxs)("div",{className:"cmplz-grid-item-header",children:["text"===t.header.type&&(0,z.jsxs)(z.Fragment,{children:[(0,z.jsx)("h3",{className:"cmplz-grid-title cmplz-h4",children:t.header.data}),(0,z.jsxs)("div",{className:"cmplz-grid-item-controls",children:[t.controls&&"url"===t.controls.type&&(0,z.jsx)("a",{href:t.controls.data,children:(0,a.__)("Instructions","complianz-gdpr")}),t.controls&&"react"===t.controls.type&&wp.element.createElement(x[t.controls.data])]})]}),"react"===t.header.type&&(0,z.jsx)(z.Fragment,{children:wp.element.createElement(x[t.header.data])})]}),(0,z.jsx)("div",{className:"cmplz-grid-item-content",children:wp.element.createElement(x[e.content.data])}),!n&&(0,z.jsx)("div",{className:"cmplz-grid-item-footer"}),n&&(0,z.jsx)("div",{className:"cmplz-grid-item-footer",children:wp.element.createElement(x[e.footer.data])})]},"block-"+t.id)})}},25228:(e,t,s)=>{s.r(t),s.d(t,{default:()=>o});var a=s(86087),n=s(21366),i=s(45111),l=s(27723),r=s(10790);const o=(0,a.memo)(({value:e=!1,onChange:t,required:s,defaultValue:a,disabled:o,options:c={},canBeEmpty:d=!0,label:p})=>{if(Array.isArray(c)){let e={};c.map(t=>{e[t.value]=t.label}),c=e}return d?(""===e||!1===e||0===e)&&(e="0",c={0:(0,l.__)("Select an option","complianz-gdpr"),...c}):e||(e=Object.keys(c)[0]),(0,r.jsx)("div",{className:"cmplz-input-group cmplz-select-group",children:(0,r.jsxs)(n.bL,{value:e,defaultValue:a,onValueChange:t,required:s,disabled:o&&!Array.isArray(o),children:[(0,r.jsxs)(n.l9,{className:"cmplz-select-group__trigger",children:[(0,r.jsx)(n.WT,{}),(0,r.jsx)(i.default,{name:"chevron-down"})]}),(0,r.jsxs)(n.UC,{className:"cmplz-select-group__content",position:"popper",children:[(0,r.jsx)(n.PP,{className:"cmplz-select-group__scroll-button",children:(0,r.jsx)(i.default,{name:"chevron-up"})}),(0,r.jsx)(n.LM,{className:"cmplz-select-group__viewport",children:(0,r.jsx)(n.YJ,{children:Object.entries(c).map(([e,t])=>(0,r.jsx)(n.q7,{disabled:Array.isArray(o)&&o.includes(e),className:"cmplz-select-group__item",value:e,children:(0,r.jsx)(n.p4,{children:t})},e))})}),(0,r.jsx)(n.wn,{className:"cmplz-select-group__scroll-button",children:(0,r.jsx)(i.default,{name:"chevron-down"})})]})]})},p)})},34759:(e,t,s)=>{s.r(t),s.d(t,{default:()=>l});var a=s(81621),n=s(16535),i=s(9588);const l=(0,a.vt)((e,t)=>({integrationsLoaded:!1,fetching:!1,services:[],plugins:[],scripts:[],placeholders:[],blockedScripts:[],setScript:(t,s)=>{e((0,n.Ay)(e=>{if("block_script"===s){let s=e.blockedScripts;if(t.urls){for(const[e,a]of Object.entries(t.urls)){if(!a||0===a.length)continue;let e=!1;for(const[t,n]of Object.entries(s))a===t&&(e=!0);e||(s[a]=a)}e.blockedScripts=s}}const a=e.scripts[s].findIndex(e=>e.id===t.id);-1!==a&&(e.scripts[s][a]=t)}))},fetchIntegrationsData:async()=>{if(t().fetching)return;e({fetching:!0});const{services:s,plugins:a,scripts:n,placeholders:i,blocked_scripts:l}=await r();let o=n;o.block_script&&o.block_script.length>0&&o.block_script.forEach((e,t)=>{e.id=t}),o.add_script&&o.add_script.length>0&&o.add_script.forEach((e,t)=>{e.id=t}),o.whitelist_script&&o.whitelist_script.length>0&&o.whitelist_script.forEach((e,t)=>{e.id=t}),e(()=>({integrationsLoaded:!0,services:s,plugins:a,scripts:o,fetching:!1,placeholders:i,blockedScripts:l}))},addScript:s=>{e({fetching:!0}),t().scripts[s]&&Array.isArray(t().scripts[s])||e((0,n.Ay)(e=>{e.scripts[s]=[]})),e((0,n.Ay)(e=>{e.scripts[s].push({name:"general",id:e.scripts[s].length,enable:!0})}));let a=t().scripts;return i.doAction("update_scripts",{scripts:a}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},saveScript:(s,a)=>{e({fetching:!0}),t().scripts[a]&&Array.isArray(t().scripts[a])||e((0,n.Ay)(e=>{e.scripts[a]=[]})),e((0,n.Ay)(e=>{const t=e.scripts[a].findIndex(e=>e.id===s.id);-1!==t&&(e.scripts[a][t]=s)}));let l=t().scripts;return i.doAction("update_scripts",{scripts:l}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},deleteScript:(s,a)=>{e({fetching:!0}),t().scripts[a]&&Array.isArray(t().scripts[a])||e((0,n.Ay)(e=>{e.scripts[a]=[]})),e((0,n.Ay)(e=>{const t=e.scripts[a].findIndex(e=>e.id===s.id);-1!==t&&e.scripts[a].splice(t,1)}));let l=t().scripts;return i.doAction("update_scripts",{scripts:l}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},updatePluginStatus:async(t,s)=>{e({fetching:!0}),e((0,n.Ay)(e=>{const a=e.plugins.findIndex(e=>e.id===t);-1!==a&&(e.plugins[a].enabled=s)}));const a=await i.doAction("update_plugin_status",{plugin:t,enabled:s}).then(e=>e).catch(e=>{console.error(e)});return e({fetching:!1}),a},updatePlaceholderStatus:async(t,s,a)=>{e({fetching:!0}),a&&e((0,n.Ay)(e=>{const a=e.plugins.findIndex(e=>e.id===t);-1!==a&&(e.plugins[a].placeholder=s?"enabled":"disabled")}));const l=await i.doAction("update_placeholder_status",{id:t,enabled:s}).then(e=>e).catch(e=>{console.error(e)});return e({fetching:!1}),l}})),r=()=>i.doAction("get_integrations_data",{}).then(e=>e).catch(e=>{console.error(e)})},35683:(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});var a=s(81621),n=s(9588);const i=(0,a.vt)((e,t)=>({licenseStatus:cmplz_settings.licenseStatus,processing:!1,licenseNotices:[],noticesLoaded:!1,getLicenseNotices:async()=>{const{licenseStatus:t,notices:s}=await n.doAction("license_notices",{}).then(e=>e);e(e=>({noticesLoaded:!0,licenseNotices:s,licenseStatus:t}))},activateLicense:async t=>{let s={};s.license=t,e({processing:!0});const{licenseStatus:a,notices:i}=await n.doAction("activate_license",s);e(e=>({processing:!1,licenseNotices:i,licenseStatus:a}))},deactivateLicense:async()=>{e({processing:!0});const{licenseStatus:t,notices:s}=await n.doAction("deactivate_license");e(e=>({processing:!1,licenseNotices:s,licenseStatus:t}))}}))},38830:(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});var a=s(10790);const n=({link:e,content:t})=>(0,a.jsx)("div",{className:"cmplz-tips-tricks-element",children:(0,a.jsxs)("a",{href:e,target:"_blank",rel:"noopener noreferrer",title:t,children:[(0,a.jsx)("div",{className:"cmplz-icon",children:(0,a.jsx)("svg",{"aria-hidden":"true",focusable:"false",role:"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",height:"15",children:(0,a.jsx)("path",{fill:"var(--rsp-grey-300)",d:"M256 512c141.4 0 256-114.6 256-256S397.4 0 256 0S0 114.6 0 256S114.6 512 256 512zM216 336h24V272H216c-13.3 0-24-10.7-24-24s10.7-24 24-24h48c13.3 0 24 10.7 24 24v88h8c13.3 0 24 10.7 24 24s-10.7 24-24 24H216c-13.3 0-24-10.7-24-24s10.7-24 24-24zm40-144c-17.7 0-32-14.3-32-32s14.3-32 32-32s32 14.3 32 32s-14.3 32-32 32z"})})}),(0,a.jsx)("div",{className:"cmplz-tips-tricks-content",children:t})]})}),i=()=>(0,a.jsx)("div",{className:"cmplz-tips-tricks-container",children:[{content:"Excluding/Deferring Complianz from Caching plugins",link:"https://complianz.link/VoIVo5L"},{content:"Simplified Guide to Google Consent Mode v2",link:"https://complianz.link/ro0W1dn"},{content:"Customize your banner - CSS Lessons",link:"https://complianz.link/C7rh74D"},{content:"Customizing the TCF Banner – Do’s and Don’ts",link:"https://complianz.link/vilss48"},{content:"Translate your cookie notice and legal documents",link:"https://complianz.link/ceB95Tx"},{content:"Debugging issues with Complianz",link:"https://complianz.link/NAjPkE8"}].map((e,t)=>(0,a.jsx)(n,{link:e.link,content:e.content},"trick-"+t))})},38931:(e,t,s)=>{s.r(t),s.d(t,{default:()=>l});var a=s(27723),n=s(45111),i=s(10790);const l=()=>(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"cmplz-legend",children:[(0,i.jsx)(n.default,{name:"sync",color:"green",size:14}),(0,i.jsx)("span",{children:(0,a.__)("Synchronized","complianz-gdpr")})]}),(0,i.jsxs)("div",{className:"cmplz-legend",children:[(0,i.jsx)(n.default,{name:"circle-check",color:"green",size:14}),(0,i.jsx)("span",{children:(0,a.__)("Validated","complianz-gdpr")})]})]})},56644:(e,t,s)=>{s.r(t),s.d(t,{default:()=>p});var a=s(4219),n=s(86087),i=s(27723),l=s(90622),r=s(98084),o=s(69406),c=s(10790);const d=e=>(0,c.jsx)("div",{className:"cmplz-plusone",children:e.count}),p=()=>{const{fields:e,getFieldValue:t}=(0,a.default)(),[s,p]=(0,n.useState)(!1),[u,m]=(0,n.useState)(!1),{recordsLoaded:g,fetchData:h,totalOpen:f}=(0,l.default)();(0,n.useEffect)(()=>{g||h(10,1,"ID","ASC")},[g]),(0,n.useEffect)(()=>{let e=1==t("a_b_testing");p(e);let s=1==t("a_b_testing_buttons");m(s)},[e]);const _=[{title:(0,i.__)("Data Requests","complianz-gdpr"),viewLink:"#tools/data-requests",enableLink:"#wizard/security-consent",field:{name:"datarequest",value:"yes"},link:"https://complianz.io/definition/what-is-a-data-request/",plusone:(0,c.jsx)(d,{count:f})},{title:(0,i.__)("Records of Consent","complianz-gdpr"),viewLink:"#tools/records-of-consent",enableLink:"#wizard/security-consent",field:{name:"records_of_consent",value:"yes"},link:"https://complianz.io/records-of-consent/"},{title:(0,i.__)("Processing Agreements","complianz-gdpr"),viewLink:"#tools/processing-agreements",link:"https://complianz.io/definition/what-is-a-processing-agreement/"},{title:(0,i.__)("Consent Statistics","complianz-gdpr"),viewLink:"#tools/ab-testing",link:"https://complianz.io/a-quick-introduction-to-a-b-testing/"},{title:(0,i.__)("A/B Testing","complianz-gdpr"),viewLink:"#tools/ab-testing",link:"https://complianz.io/a-quick-introduction-to-a-b-testing/"},{title:(0,i.__)("Documentation","complianz-gdpr"),link:"https://complianz.io/support/"},{title:(0,i.__)("Premium Support","complianz-gdpr"),viewLink:"#tools/support",link:"https://complianz.io/about-premium-support/"},{title:"WooCommerce",plugin:"woocommerce",link:cmplz_settings.admin_url+"admin.php?page=wc-settings&tab=account"},{title:(0,i.__)("Security","complianz-gdpr"),link:"#tools/security",viewLink:"#tools/security"}];let z=cmplz_settings.is_multisite_plugin?"#tools/tools-multisite":"https://complianz.io/complianz-for-wordpress-multisite-installations/";return cmplz_settings.is_multisite&&_.push({title:(0,i.__)("Multisite","complianz-gdpr"),link:z,viewLink:z}),s?(0,c.jsx)(c.Fragment,{children:(0,c.jsx)(r.default,{abTestingEnabled:u})}):(0,c.jsx)(c.Fragment,{children:_.map((e,t)=>(0,c.jsx)(o.default,{item:e},t))})}},56716:(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});var a=s(81621),n=s(9588);const i=(0,a.vt)((e,t)=>({documents:[],documentDataLoaded:!1,processingAgreementOptions:[],proofOfConsentOptions:[],dataBreachOptions:[],region:"",setRegion:t=>{"undefined"!=typeof Storage&&(sessionStorage.cmplzSelectedRegion=t),e(e=>({region:t}))},getRegion:()=>{let t="all";"undefined"!=typeof Storage&&sessionStorage.cmplzSelectedRegion&&(t=sessionStorage.cmplzSelectedRegion),e(e=>({region:t}))},getDocuments:async()=>{const{documents:t,processingAgreementOptions:s,proofOfConsentOptions:a,dataBreachOptions:i}=await n.doAction("documents_block_data").then(e=>e);e(e=>({documentDataLoaded:!0,documents:t,processingAgreementOptions:s,proofOfConsentOptions:a,dataBreachOptions:i}))}}))},57338:(e,t,s)=>{s.r(t),s.d(t,{default:()=>l});var a=s(81621),n=s(9588),i=s(27723);const l=(0,a.vt)((e,t)=>({error:!1,dataLoaded:!1,pluginData:[],updatePluginData:(s,a)=>{let n=t().pluginData;n.forEach(function(e,t){e.slug===s&&(n[t]=a)}),e(e=>({dataLoaded:!0,pluginData:n}))},getPluginData:e=>t().pluginData.filter(t=>t.slug===e)[0],fetchOtherPluginsData:async()=>{const{pluginData:t,error:s}=await n.doAction("otherpluginsdata").then(e=>{let t=[];t=e.plugins;let s=e.error;return s||t.forEach(function(e,s){t[s].pluginActionNice=r(e.pluginAction)}),{pluginData:t,error:s}});e(e=>({dataLoaded:!0,pluginData:t,error:s}))},pluginActions:(e,s,a)=>{a&&a.preventDefault();let i={};i.slug=e,i.pluginAction=s;let l=t().getPluginData(e);"download"===s?l.pluginAction="downloading":"activate"===s&&(l.pluginAction="activating"),l.pluginActionNice=r(l.pluginAction),t().updatePluginData(e,l),"installed"!==s&&"upgrade-to-premium"!==s&&n.doAction("plugin_actions",i).then(s=>{l=s,t().updatePluginData(e,l),t().pluginActions(e,l.pluginAction)})}})),r=e=>({download:(0,i.__)("Install","complianz-gdpr"),activate:(0,i.__)("Activate","complianz-gdpr"),activating:(0,i.__)("Activating...","complianz-gdpr"),downloading:(0,i.__)("Downloading...","complianz-gdpr"),"upgrade-to-premium":(0,i.__)("Downloading...","complianz-gdpr")}[e])},57907:(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});var a=s(27723),n=s(10790);const i=()=>(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("h3",{className:"cmplz-grid-title cmplz-h4",children:(0,a.__)("Other Plugins","complianz-gdpr")})})},61249:(e,t,s)=>{s.r(t),s.d(t,{default:()=>c});var a=s(86087),n=s(27723),i=s(4219),l=s(56716),r=s(25228),o=s(10790);const c=e=>{const{getFieldValue:t,fieldsLoaded:s}=(0,i.default)(),{getRegion:c,setRegion:d,region:p}=(0,l.default)();if((0,a.useEffect)(()=>{c()},[]),!s)return null;let u=t("regions");Array.isArray(u)||(u=[u]),0===u.length&&(u=["eu"]),u||(u=[]);let m=[];for(const e of u){if(!cmplz_settings.regions.hasOwnProperty(e))continue;let t={};t.label=cmplz_settings.regions[e].label_full,t.value=e,m.push(t)}let g={};return g.label=(0,n.__)("General","complianz-gdpr"),g.value="all",m.push(g),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("h3",{className:"cmplz-grid-title cmplz-h4",children:(0,n.__)("Documents","complianz-gdpr")}),(0,o.jsx)("div",{className:"cmplz-grid-item-controls",children:(0,o.jsx)(r.default,{defaultValue:"all",canBeEmpty:!1,onChange:e=>d(e),value:p,options:m})})]})}},64186:(e,t,s)=>{s.r(t),s.d(t,{default:()=>o});var a=s(86087),n=s(27723),i=s(99695),l=s(57338),r=s(10790);const o=()=>{const{dataLoaded:e,pluginData:t,pluginActions:s,fetchOtherPluginsData:o,error:c}=(0,l.default)();return(0,a.useEffect)(()=>{e||o()},[]),!e||c?(0,r.jsx)(i.default,{lines:"3",error:c}):(0,r.jsx)("div",{className:"cmplz-other-plugins-container",children:t.map((e,t)=>((e,t)=>(0,r.jsxs)("div",{className:"cmplz-suggested-plugin cmplz-"+e.slug,children:[(0,r.jsx)("img",{className:"cmplz-suggested-plugin-img",src:cmplz_settings.plugin_url+"/upgrade/img/"+e.image}),(0,r.jsxs)("div",{className:"cmplz-suggested-plugin-group",children:[(0,r.jsx)("div",{className:"cmplz-suggested-plugin-group-title",children:e.title}),(0,r.jsx)("div",{className:"cmplz-suggested-plugin-group-desc",children:e.summary})]}),(0,r.jsx)("div",{className:"cmplz-suggested-plugin-desc-long",children:e.description}),(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"cmplz-other-plugin-status",children:["upgrade-to-premium"===e.pluginAction&&(0,r.jsx)("a",{type:"button",className:"button-secondary cmplz-install-plugin",target:"_blank",href:e.upgrade_url,rel:"noopener noreferrer",children:(0,n.__)("Upgrade","complianz-gdpr")}),"upgrade-to-premium"!==e.pluginAction&&"installed"!==e.pluginAction&&(0,r.jsx)("button",{type:"button",className:"button-secondary cmplz-install-plugin",onClick:t=>s(e.slug,e.pluginAction,t),children:e.pluginActionNice}),"installed"===e.pluginAction&&(0,n.__)("Installed","complianz-gdpr")]})})]},t))(e,t))})}},69406:(e,t,s)=>{s.r(t),s.d(t,{default:()=>d});var a=s(4219),n=s(34759),i=s(45111),l=s(86087),r=s(27723),o=s(35683),c=s(10790);const d=e=>{const{fields:t,getFieldValue:s}=(0,a.default)(),[d,p]=(0,l.useState)(!1),{integrationsLoaded:u,plugins:m,fetchIntegrationsData:g}=(0,n.default)(),{licenseStatus:h}=(0,o.default)();(0,l.useEffect)(()=>{let t=e.item;if(t.field){let e=s(t.field.name)==t.field.value;p(e)}},[t]),(0,l.useEffect)(()=>{u||g()},[]);let f=e.item;if(f.plugin)return m.filter(e=>e.id===f.plugin).length>0?(0,c.jsxs)("div",{className:"cmplz-tool",children:[(0,c.jsx)("div",{className:"cmplz-tool-title",children:f.title}),(0,c.jsx)("div",{className:"cmplz-tool-link",children:(0,c.jsx)("a",{href:f.link,target:"_blank",rel:"noopener noreferrer",children:(0,c.jsx)(i.default,{name:"circle-chevron-right",color:"black",size:14})})})]}):null;let _=cmplz_settings.is_premium&&"valid"===h,z=((0,r.__)("Read more","complianz-gdpr"),f.link);_&&(!d&&f.enableLink&&(z=f.enableLink),f.field&&!d||!f.viewLink||(z=f.viewLink));let x=-1!==z.indexOf("https://"),b=x?"_blank":"_self",k=x?"external-link":"circle-chevron-right";return(0,c.jsxs)("div",{className:"cmplz-tool",children:[(0,c.jsxs)("div",{className:"cmplz-tool-title",children:[f.title,f.plusone&&f.plusone]}),(0,c.jsx)("div",{className:"cmplz-tool-link",children:(0,c.jsx)("a",{href:z,target:b,rel:x?"noopener noreferrer":"",children:(0,c.jsx)(i.default,{name:k,color:"black",size:14})})})]})}},71137:(e,t,s)=>{s.r(t),s.d(t,{default:()=>c});var a=s(27723),n=s(4219),i=s(86087),l=s(88895),r=s(25228),o=s(10790);const c=()=>{const{consentType:e,setConsentType:t,consentTypes:s,fetchStatisticsData:c,loaded:d}=(0,l.default)(),{fields:p,getFieldValue:u}=(0,n.default)(),[m,g]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=1==u("a_b_testing");g(e)},[u("a_b_testing")]),(0,i.useEffect)(()=>{m&&!d&&c()},[m]);let h=[];return s&&(h=s.map(e=>({value:e.id,label:e.label}))),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("h3",{className:"cmplz-grid-title cmplz-h4",children:[m&&(0,a.__)("Statistics","complianz-gdpr"),!m&&(0,a.__)("Tools","complianz-gdpr")]}),(0,o.jsx)("div",{className:"cmplz-grid-item-controls",children:m&&h&&h.length>1&&(0,o.jsx)(r.default,{canBeEmpty:!1,value:e,onChange:e=>t(e),options:h})})]})}},88895:(e,t,s)=>{s.r(t),s.d(t,{default:()=>r});var a=s(81621),n=s(9588);const i={optin:{labels:["Functional","Statistics","Marketing","Do Not Track","No Choice","No Warning"],categories:["functional","statistics","marketing","do_not_track","no_choice","no_warning"],datasets:[{data:["0","0","0","0","0","0"],backgroundColor:"rgba(46, 138, 55, 1)",borderColor:"rgba(46, 138, 55, 1)",label:"A (default)",fill:"false",borderDash:[0,0]},{data:["0","0","0","0","0","0"],backgroundColor:"rgba(244, 191, 62, 1)",borderColor:"rgba(244, 191, 62, 1)",label:"B",fill:"false",borderDash:[0,0]}],max:5},optout:{labels:["Functional","Statistics","Marketing","Do Not Track","No Choice","No Warning"],categories:["functional","statistics","marketing","do_not_track","no_choice","no_warning"],datasets:[{data:["0","0","0","0","0","0"],backgroundColor:"rgba(46, 138, 55, 1)",borderColor:"rgba(46, 138, 55, 1)",label:"A (default)",fill:"false",borderDash:[0,0]},{data:["0","0","0","0","0","0"],backgroundColor:"rgba(244, 191, 62, 1)",borderColor:"rgba(244, 191, 62, 1)",label:"B",fill:"false",borderDash:[0,0]}],max:5}},l={optin:{labels:["Functional","Statistics","Marketing","Do Not Track","No Choice","No Warning"],categories:["functional","statistics","marketing","do_not_track","no_choice","no_warning"],datasets:[{data:["29","747","174","292","30","10"],backgroundColor:"rgba(46, 138, 55, 1)",borderColor:"rgba(46, 138, 55, 1)",label:"Demo A (default)",fill:"false",borderDash:[0,0]},{data:["3","536","240","389","45","32"],backgroundColor:"rgba(244, 191, 62, 1)",borderColor:"rgba(244, 191, 62, 1)",label:"Demo B",fill:"false",borderDash:[0,0]}],max:5},optout:{labels:["Functional","Statistics","Marketing","Do Not Track","No Choice","No Warning"],categories:["functional","statistics","marketing","do_not_track","no_choice","no_warning"],datasets:[{data:["29","747","174","292","30","10"],backgroundColor:"rgba(46, 138, 55, 1)",borderColor:"rgba(46, 138, 55, 1)",label:"A (default)",fill:"false",borderDash:[0,0]},{data:["3","536","240","389","45","32"],backgroundColor:"rgba(244, 191, 62, 1)",borderColor:"rgba(244, 191, 62, 1)",label:"Demo B",fill:"false",borderDash:[0,0]}],max:5}},r=(0,a.vt)((e,t)=>({consentType:"optin",setConsentType:t=>{e({consentType:t})},statisticsLoading:!1,consentTypes:[],regions:[],defaultConsentType:"optin",loaded:!1,statisticsData:i,emptyStatisticsData:i,bestPerformerEnabled:!1,daysLeft:"",abTrackingCompleted:!1,labels:[],setLabels:t=>{e({labels:t})},fetchStatisticsData:async()=>{if(!cmplz_settings.is_premium)return void e({saving:!1,loaded:!0,consentType:"optin",consentTypes:["optin","optout"],statisticsData:l,defaultConsentType:"optin",bestPerformerEnabled:!1,regions:"eu",daysLeft:11,abTrackingCompleted:!1});if(e({saving:!0}),t().loaded)return;const{daysLeft:s,abTrackingCompleted:a,consentTypes:i,statisticsData:r,defaultConsentType:o,regions:c,bestPerformerEnabled:d}=await n.doAction("get_statistics_data",{}).then(e=>e).catch(e=>{console.error(e)});e({saving:!1,loaded:!0,consentType:o,consentTypes:i,statisticsData:r,defaultConsentType:o,bestPerformerEnabled:d,regions:c,daysLeft:s,abTrackingCompleted:a})}}))},90622:(e,t,s)=>{s.r(t),s.d(t,{default:()=>l});var a=s(81621),n=s(9588),i=s(16535);s(86087);const l=(0,a.vt)((e,t)=>({recordsLoaded:!1,searchValue:"",setSearchValue:t=>e({searchValue:t}),status:"open",setStatus:t=>e({status:t}),selectedRecords:[],setSelectedRecords:t=>e({selectedRecords:t}),fetching:!1,generating:!1,progress:!1,records:[],totalRecords:0,totalOpen:0,exportLink:"",noData:!1,indeterminate:!1,setIndeterminate:t=>e({indeterminate:t}),paginationPerPage:10,pagination:{currentPage:1},setPagination:t=>e({pagination:t}),orderBy:"ID",setOrderBy:t=>e({orderBy:t}),order:"DESC",setOrder:t=>e({order:t}),deleteRecords:async s=>{let a={};a.per_page=t().paginationPerPage,a.page=t().pagination.currentPage,a.order=t().order.toUpperCase(),a.orderBy=t().orderBy,a.search=t().searchValue,a.status=t().status;let i=t().records.filter(e=>s.includes(e.ID));e(e=>({records:e.records.filter(e=>!s.includes(e.ID))})),a.records=i,await n.doAction("delete_datarequests",a).then(e=>e).catch(e=>{console.error(e)}),await t().fetchData(),t().setSelectedRecords([]),t().setIndeterminate(!1)},resolveRecords:async s=>{let a={};a.per_page=t().paginationPerPage,a.page=t().pagination.currentPage,a.order=t().order.toUpperCase(),a.orderBy=t().orderBy,a.search=t().searchValue,a.status=t().status,e((0,i.Ay)(e=>{e.records.forEach(function(t,a){s.includes(t.ID)&&(e.records[a].resolved=!0)})})),a.records=t().records.filter(e=>s.includes(e.ID)),await n.doAction("resolve_datarequests",a).then(e=>e).catch(e=>{console.error(e)}),await t().fetchData(),t().setSelectedRecords([]),t().setIndeterminate(!1)},fetchData:async()=>{if(t().fetching)return;e({fetching:!0});let s={};s.per_page=t().paginationPerPage,s.page=t().pagination.currentPage,s.order=t().order.toUpperCase(),s.orderBy=t().orderBy,s.search=t().searchValue,s.status=t().status;const{records:a,totalRecords:i,totalOpen:l}=await n.doAction("get_datarequests",s).then(e=>e).catch(e=>{console.error(e)});e(()=>({recordsLoaded:!0,records:a,totalRecords:i,totalOpen:l,fetching:!1}))},startExport:async()=>{e({generating:!0,progress:0,exportLink:""})},fetchExportDatarequestsProgress:async(t,s,a)=>{(t=void 0!==t&&t)||e({generating:!0});let i={};i.startDate=s,i.endDate=a,i.statusOnly=t;const{progress:l,exportLink:r,noData:o}=await n.doAction("export_datarequests",i).then(e=>e).catch(e=>{console.error(e)});let c=!1;l<100&&(c=!0),e({progress:l,exportLink:r,generating:c,noData:o})}}))},91699:(e,t,s)=>{s.r(t),s.d(t,{default:()=>r});var a=s(27723),n=s(56716),i=s(10790);const l=({document:e,index:t})=>{const{region:s}=(0,n.default)();let a=e.regions.filter(e=>e!==s);return(0,i.jsxs)("div",{className:"cmplz-single-document-other-regions",children:[(0,i.jsx)("a",{href:e.readmore,target:"_blank",rel:"noopener noreferrer",children:e.title}),a.map((e,t)=>(0,i.jsx)("div",{className:"cmplz-region-indicator",children:(0,i.jsx)("img",{alt:e,width:"16px",height:"16px",src:cmplz_settings.plugin_url+"/assets/images/"+e+".svg"})},t))]},t)},r=()=>(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"cmplz-document-header",children:[(0,i.jsx)("h3",{className:"cmplz-h4",children:(0,a.__)("Other regions")}),(0,i.jsx)("a",{href:"https://complianz.io/features/",target:"_blank",rel:"noopener noreferrer",children:(0,a.__)("Read more","complianz-gdpr")})]}),[{id:"privacy-statement",title:"Privacy Statements",regions:["eu","us","uk","ca","za","au","br"],readmore:"https://complianz.io/definition/what-is-a-privacy-statement/"},{id:"cookie-statement",title:"Cookie Policy",regions:["eu","us","uk","ca","za","au","br"],readmore:" https://complianz.io/definition/what-is-a-cookie-policy/"},{id:"impressum",title:"Impressum",regions:["eu"],readmore:"https://complianz.io/definition/what-is-an-impressum/"},{id:"do-not-sell-my-info",title:"Opt-out preferences",regions:["us"],readmore:"https://complianz.io/definition/what-is-do-not-sell-my-personal-information/"},{id:"privacy-statement-for-children",title:"Privacy Statement for Children",regions:["us","uk","ca","za","au","br"],readmore:"https://complianz.io/definition/what-is-a-privacy-statement-for-children/"}].map((e,t)=>(0,i.jsx)(l,{index:t,document:e},t))]})},93407:(e,t,s)=>{s.r(t),s.d(t,{default:()=>d});var a=s(86087),n=s(27723),i=s(9684),l=s(99695),r=s(32828),o=s(4219),c=s(10790);const d=()=>{const{percentageCompleted:e,filter:t,notices:s,progressLoaded:d,fetchProgressData:p,error:u}=(0,r.default)(),{fetchAllFieldsCompleted:m,allRequiredFieldsCompleted:g,fields:h}=(0,o.default)();(0,a.useEffect)(()=>{(async()=>{d||await p(),m()})()},[t,h]);let f="";if(e<80&&(f+="cmplz-orange"),!d||u)return(0,c.jsx)(l.default,{lines:"9",error:u});let _=s;if("remaining"===t&&(_=_.filter(function(e){return"completed"!==e.status})),!g&&0===_.filter(e=>"all_fields_completed"===e.id).length){let e={id:"all_fields_completed",status:"urgent",message:(0,n.__)('Not all fields have been entered, or you have not clicked the "finish" button yet.',"complianz-gdpr")};_.push(e)}g&&(_=_.filter(e=>"all_fields_completed"!==e.id)),_.sort(function(e,t){return e.status===t.status?0:e.status<t.status?1:-1});let z=_.filter(e=>"open"===e.status||"urgent"===e.status);return(0,c.jsxs)("div",{className:"cmplz-progress-block",children:[(0,c.jsx)("div",{className:"cmplz-progress-bar",children:(0,c.jsx)("div",{className:"cmplz-progress",children:(0,c.jsx)("div",{className:"cmplz-bar "+f,style:Object.assign({},{width:e+"%"})})})}),(0,c.jsxs)("div",{className:"cmplz-progress-text",children:[(0,c.jsxs)("h1",{className:"cmplz-progress-percentage",children:[e,"%"]}),(0,c.jsxs)("h5",{className:"cmplz-progress-text-span",children:[e<100&&(0,n.__)("Consent Management is activated on your site.","complianz-gdpr")+" ",e<100&&1===z.length&&(0,n.__)("You still have 1 task open.","complianz-gdpr"),e<100&&z.length>1&&(0,n.__)("You still have %s tasks open.","complianz-gdpr").replace("%s",z.length),100===e&&(0,n.__)("Well done! Your website is ready for your selected regions.","complianz-gdpr")]})]}),(0,c.jsx)("div",{className:"cmplz-scroll-container",children:_.map((e,t)=>(0,c.jsx)(i.default,{index:t,notice:e},t))})]})}},98084:(e,t,s)=>{s.r(t),s.d(t,{default:()=>o});var a=s(45111),n=s(27723),i=s(88895),l=s(86087),r=s(10790);const o=()=>{const[e,t]=(0,l.useState)(!1),[s,o]=(0,l.useState)(1),[c,d]=(0,l.useState)(0),[p,u]=(0,l.useState)(0),{consentType:m,statisticsData:g,loaded:h,fetchStatisticsData:f,labels:_,setLabels:z}=(0,i.default)();(0,l.useEffect)(()=>{!h&&cmplz_settings.is_premium&&f()},[]),(0,l.useEffect)(()=>{if(""===m||!h)return;if(!g||!g.hasOwnProperty(m))return;let e=[...g[m].labels],t=g[m].categories;t="optin"===m?t.filter(e=>"functional"===e||"no_warning"===e||"do_not_track"===e):t.filter(e=>"functional"===e||"marketing"===e||"statistics"===e||"preferences"===e);let s=t.map(e=>g[m].categories.indexOf(e));for(let t=s.length-1;t>=0;t--)e.splice(s[t],1);z(e)},[h,m]),(0,l.useEffect)(()=>{if(""===m||!h||!g)return;let e=g[m].datasets.filter(e=>e.default);if(e.length>0){let s=e[0].data,a=s.reduce((e,t)=>parseInt(e)+parseInt(t),0);a=a>0?a:1,o(a),d(e[0].full_consent),u(e[0].no_consent),s=s.slice(2),t(s)}},[h,m]);const x=e=>{let t="dial-med-low-light";return 1===e?t="dial-med-light":2===e?t="dial-light":3===e?t="dial-off-light":4===e&&(t="dial-min-light"),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(a.default,{name:t,color:"black"})})};return(0,r.jsxs)("div",{className:"cmplz-statistics",children:[(0,r.jsxs)("div",{className:"cmplz-statistics-select",children:[(0,r.jsxs)("div",{className:"cmplz-statistics-select-item",children:[(0,r.jsx)(a.default,{name:"dial-max-light",color:"green",size:"22"}),(0,r.jsx)("h2",{children:c}),(0,r.jsx)("span",{children:(0,n.__)("Full Consent","complianz-gdpr")})]}),(0,r.jsxs)("div",{className:"cmplz-statistics-select-item",children:[(0,r.jsx)(a.default,{name:"dial-min-light",color:"red",size:"22"}),(0,r.jsx)("h2",{children:p}),(0,r.jsx)("span",{children:(0,n.__)("No Consent","complianz-gdpr")})]})]}),(0,r.jsx)("div",{className:"cmplz-statistics-list",children:_.length>0&&_.map((t,a)=>{return(0,r.jsxs)("div",{className:"cmplz-statistics-list-item",children:[x(a),(0,r.jsx)("p",{className:"cmplz-statistics-list-item-text",children:t}),(0,r.jsxs)("p",{className:"cmplz-statistics-list-item-number",children:[e.hasOwnProperty(a)?(n=e[a],n=parseInt(n),Math.round(n/s*100)):0,"%"]})]},a);var n})})]})}},98217:(e,t,s)=>{s.r(t),s.d(t,{default:()=>o});var a=s(45111),n=s(27723),i=s(86087),l=s(25228),r=s(10790);const o=e=>{const[t,s]=(0,i.useState)(!1),[o,c]=(0,i.useState)(!1),[d,p]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let t=e.options;if(0===t.length){let s={label:e.name,value:0};t.unshift(s)}else if(!t.filter(e=>0===e.value).length>0){let s={label:e.name,value:0};t.unshift(s)}p(t)},[e.options]);const u=()=>{if(o||!t||0===t)return;c(!0);let e=new XMLHttpRequest;e.responseType="blob",e.open("get",t,!0),e.send(),e.onreadystatechange=function(){if(4==this.readyState&&200==this.status){var e=window.URL.createObjectURL(this.response),s=window.document.createElement("a");s.setAttribute("href",e),s.setAttribute("download",d.filter(e=>e.value===t)[0].label),window.document.body.appendChild(s),s.click(),setTimeout(function(){window.URL.revokeObjectURL(e)},6e4)}},e.onprogress=function(e){c(!0)}};return(0,r.jsxs)("div",{className:"cmplz-single-document-other-documents",children:[(0,r.jsx)(l.default,{onChange:e=>s(e),defaultValue:"0",canBeEmpty:!1,value:t,options:d}),(0,r.jsx)("div",{onClick:()=>u(),children:(0,r.jsx)(a.default,{name:"file-download",color:0==t||o?"grey":"black",tooltip:(0,n.__)("Download file","complianz-gdpr"),size:14})}),d.length>0&&(0,r.jsx)("a",{href:e.link,children:(0,r.jsx)(a.default,{name:"circle-chevron-right",color:"black",tooltip:(0,n.__)("Go to overview","complianz-gdpr"),size:14})}),0===d.length&&(0,r.jsx)("a",{href:e.link,children:(0,r.jsx)(a.default,{name:"plus",color:"black",tooltip:(0,n.__)("Create new","complianz-gdpr"),size:14})})]})}},99166:(e,t,s)=>{s.r(t),s.d(t,{default:()=>a});const a=e=>function(t){return new Promise(s=>setTimeout(()=>s(t),e))}},99582:(e,t,s)=>{s.r(t),s.d(t,{default:()=>r});var a=s(27723),n=s(86087),i=s(32828),l=s(10790);const r=()=>{const{setFilter:e,filter:t,fetchFilter:s,notices:r,progressLoaded:o}=(0,i.default)();(0,n.useEffect)(()=>{s()},[]);let c=0,d=0;return c=o?r.length:0,d=r.filter(function(e){return"completed"!==e.status}).length,(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("h3",{className:"cmplz-grid-title cmplz-h4",children:(0,a.__)("Progress","complianz-gdpr")}),(0,l.jsx)("div",{className:"cmplz-grid-item-controls",children:(0,l.jsxs)("div",{className:"cmplz-task-switcher-container cmplz-active-filter-"+t,children:[(0,l.jsxs)("a",{href:"#",className:"cmplz-task-switcher cmplz-all-tasks",onClick:()=>e("all"),"data-filter":"all",children:[(0,a.__)("All tasks","complianz-gdpr"),(0,l.jsxs)("span",{className:"rsssl_task_count",children:["(",c,")"]})]}),(0,l.jsxs)("a",{href:"#",className:"cmplz-task-switcher cmplz-remaining-tasks",onClick:()=>e("remaining"),"data-filter":"remaining",children:[(0,a.__)("Remaining tasks","complianz-gdpr"),(0,l.jsxs)("span",{className:"rsssl_task_count",children:["(",d,")"]})]})]})})]})}}}]);