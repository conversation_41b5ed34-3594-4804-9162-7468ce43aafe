"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[5279,7660,8432],{38432:(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});var n=a(81621),l=a(72346),d=a(31127),r=a(979),s=a(66212);const o=(0,n.vt)(e=>({startDate:(0,l.default)((0,d.default)((0,r.A)(new Date,7)),"yyyy-MM-dd"),setStartDate:t=>e(e=>({startDate:t})),endDate:(0,l.default)((0,s.default)((0,r.A)(new Date,1)),"yyyy-MM-dd"),setEndDate:t=>e(e=>({endDate:t})),range:"last-7-days",setRange:t=>e(e=>({range:t}))}))},95279:(e,t,a)=>{a.r(t),a.d(t,{default:()=>z});var n=a(86087),l=a(85261),d=a(30020),r=a(1806),s=a(31127),o=a(66212),u=a(92998),c=a(20543),D=a(2118),i=a(19312),f=a(17054),y=a(49317),g=a(53039),p=a(81810),m=a(72346),h=a(45111),w=a(27723),b=a(38432),_=a(10790);const z=()=>{const[e,t]=(0,n.useState)(null),a=Boolean(e),z=(0,b.default)(e=>e.startDate),M=(0,b.default)(e=>e.endDate),v=(0,b.default)(e=>e.setStartDate),j=(0,b.default)(e=>e.setEndDate),k=(0,b.default)(e=>e.range),x=(0,b.default)(e=>e.setRange),A={startDate:(0,r.A)(z),endDate:(0,r.A)(M),key:"selection"},S=(0,n.useRef)(0),C=["today","yesterday","last-7-days","last-30-days","last-90-days","last-month","last-year","year-to-date"],L={today:{label:(0,w.__)("Today","complianz-gdpr"),range:()=>({startDate:(0,s.default)(new Date),endDate:(0,o.default)(new Date)})},yesterday:{label:(0,w.__)("Yesterday","complianz-gdpr"),range:()=>({startDate:(0,s.default)((0,u.default)(new Date,-1)),endDate:(0,o.default)((0,u.default)(new Date,-1))})},"last-7-days":{label:(0,w.__)("Last 7 days","complianz-gdpr"),range:()=>({startDate:(0,s.default)((0,u.default)(new Date,-7)),endDate:(0,o.default)((0,u.default)(new Date,-1))})},"last-30-days":{label:(0,w.__)("Last 30 days","complianz-gdpr"),range:()=>({startDate:(0,s.default)((0,u.default)(new Date,-30)),endDate:(0,o.default)((0,u.default)(new Date,-1))})},"last-90-days":{label:(0,w.__)("Last 90 days","complianz-gdpr"),range:()=>({startDate:(0,s.default)((0,u.default)(new Date,-90)),endDate:(0,o.default)((0,u.default)(new Date,-1))})},"last-month":{label:(0,w.__)("Last month","complianz-gdpr"),range:()=>({startDate:(0,c.default)((0,D.default)(new Date,-1)),endDate:(0,i.default)((0,D.default)(new Date,-1))})},"year-to-date":{label:(0,w.__)("Year to date","complianz-gdpr"),range:()=>({startDate:(0,f.A)(new Date),endDate:(0,o.default)(new Date)})},"last-year":{label:(0,w.__)("Last year","complianz-gdpr"),range:()=>({startDate:(0,f.A)((0,y.default)(new Date,-1)),endDate:(0,g.A)((0,y.default)(new Date,-1))})}};function R(e){const t=this.range();return(0,p.default)(e.startDate,t.startDate)&&(0,p.default)(e.endDate,t.endDate)}const E=[];for(const[e,t]of Object.entries(C))t&&(E.push(L[t]),E[E.length-1].isSelected=R);const O=e=>{t(null)},T="MMMM d, yyyy",F=z?(0,m.default)(new Date(z),T):(0,m.default)(defaultStart,T),N=M?(0,m.default)(new Date(M),T):(0,m.default)(defaultEnd,T);return(0,_.jsxs)("div",{className:"cmplz-date-range-container",children:[(0,_.jsxs)("button",{onClick:e=>{t(e.currentTarget)},id:"cmplz-date-range-picker-open-button",children:[(0,_.jsx)(h.default,{name:"calendar",size:"18"}),"custom"===k&&F+" - "+N,"custom"!==k&&L[k].label,(0,_.jsx)(h.default,{name:"chevron-down"})]}),(0,_.jsx)(l.Ay,{anchorEl:e,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},open:a,onClose:O,className:"burst",children:(0,_.jsx)("div",{id:"cmplz-date-range-picker-container",children:(0,_.jsx)(d.Ur,{ranges:[A],rangeColors:["var(--rsp-brand-primary)"],dateDisplayFormat:T,monthDisplayFormat:"MMMM",onChange:e=>{(e=>{S.current++;let t=(0,m.default)(e.selection.startDate,"yyyy-MM-dd"),a=(0,m.default)(e.selection.endDate,"yyyy-MM-dd"),n="custom";for(const[t,a]of Object.entries(L))a.isSelected(e.selection)&&(n=t);e.selection.startDate,e.selection.endDate,2!==S.current&&t===a&&"custom"===n||(S.current=0,v(t),j(a),x(n),O())})(e)},inputRanges:[],showSelectionPreview:!0,months:2,direction:"horizontal",minDate:new Date(2022,0,1),maxDate:new Date,staticRanges:E})})})]})}}}]);