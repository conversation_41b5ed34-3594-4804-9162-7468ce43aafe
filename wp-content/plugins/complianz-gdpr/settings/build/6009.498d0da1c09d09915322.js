"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[6009],{6009:(e,l,a)=>{a.r(l),a.d(l,{default:()=>t});var d=a(27723),r=a(4219),i=a(10790);const t=e=>{const{updateField:l,setChangedField:a}=(0,r.default)(),t=(d,r)=>{let i={...e.field.value};i[d]=r,l(e.field.id,i),a(e.field.id,i)},s=e.field.value.hasOwnProperty("top")?e.field.value.top:e.field.default.top,n=e.field.value.hasOwnProperty("right")?e.field.value.right:e.field.default.right,p=e.field.value.hasOwnProperty("bottom")?e.field.value.bottom:e.field.default.bottom,u=e.field.value.hasOwnProperty("left")?e.field.value.left:e.field.default.left;return e.field.value.hasOwnProperty("type")?e.field.value.type:e.field.default.type,(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"cmplz-borderradius-label",children:e.label}),(0,i.jsxs)("div",{className:"cmplz-borderradius-control",children:[(0,i.jsxs)("div",{className:"cmplz-borderradius-element",children:[(0,i.jsx)("div",{className:"cmplz-borderradius-element-label",children:(0,d.__)("Top","complianz-gdpr")}),(0,i.jsx)("input",{type:"number",onChange:e=>t("top",e.target.value),value:s},"1")]}),(0,i.jsxs)("div",{className:"cmplz-borderradius-element",children:[(0,i.jsx)("div",{className:"cmplz-borderradius-element-label",children:(0,d.__)("Right","complianz-gdpr")}),(0,i.jsx)("input",{type:"number",onChange:e=>t("right",e.target.value),value:n},"2")]}),(0,i.jsxs)("div",{className:"cmplz-borderradius-element",children:[(0,i.jsx)("div",{className:"cmplz-borderradius-element-label",children:(0,d.__)("Bottom","complianz-gdpr")}),(0,i.jsx)("input",{type:"number",onChange:e=>t("bottom",e.target.value),value:p},"3")]}),(0,i.jsxs)("div",{className:"cmplz-borderradius-element",children:[(0,i.jsx)("div",{className:"cmplz-borderradius-element-label",children:(0,d.__)("Left","complianz-gdpr")}),(0,i.jsx)("input",{type:"number",onChange:e=>t("left",e.target.value),value:u},"4")]}),(0,i.jsx)("div",{className:"cmplz-borderradius-inputtype",children:(0,i.jsx)("div",{className:"cmplz-borderradius-inputtype-pixel ",children:"px"})})]})]})}}}]);