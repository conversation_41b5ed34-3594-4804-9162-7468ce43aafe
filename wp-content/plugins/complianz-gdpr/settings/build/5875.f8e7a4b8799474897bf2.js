"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[2010,5228,5875,6729,7102,8985,9091,9758],{7102:(e,a,t)=>{t.r(a),t.d(a,{default:()=>m});var s=t(45111),o=t(27723),n=t(52010),i=t(15139),l=t(4219),r=t(86087),c=t(81366),d=t(25228),u=t(10790);const p=e=>{const{getFieldValue:a,showSavedSettingsNotice:t}=(0,l.default)(),{language:s,saving:n,purposesOptions:p,services:m,updateCookie:h,toggleDeleteCookie:_,saveCookie:g}=(0,i.default)(),[f,b]=(0,r.useState)(""),[v,x]=(0,r.useState)(""),[k,j]=(0,r.useState)(""),[y,z]=(0,r.useState)([]);let C="no"!==a("use_cdb_api"),w=!!C&&1==e.sync,N=w;n&&(N=!0);let S=!1;e.slug.length>0&&(S="https://cookiedatabase.org/cookie/"+(e.service?e.service:"unknown-service")+"/"+e.slug),(0,r.useEffect)(()=>{e&&e.cookieFunction&&j(e.cookieFunction)},[e]);const D=(e,a,t)=>{h(a,t,e)};(0,r.useEffect)(()=>{e&&e.name&&b(e.name)},[e.name]),(0,r.useEffect)(()=>{if(!e)return;if(e.name===f)return;const a=setTimeout(()=>{h(e.ID,"name",f)},500);return()=>{clearTimeout(a)}},[f]),(0,r.useEffect)(()=>{if(!e)return;if(e.cookieFunction===k)return;const a=setTimeout(()=>{h(e.ID,"cookieFunction",k)},500);return()=>{clearTimeout(a)}},[k]),(0,r.useEffect)(()=>{e&&e.retention&&x(e.retention)},[e.retention]),(0,r.useEffect)(()=>{if(!e)return;if(e.retention===v)return;const a=setTimeout(()=>{h(e.ID,"retention",v)},500);return()=>{clearTimeout(a)}},[v]),(0,r.useEffect)(()=>{let e=p&&p.hasOwnProperty(s)?p[s]:[];e=e.map(e=>({label:e.label,value:e.label})),z(e)},[s,p]);const I=(e,a,t)=>{h(a,t,e)};if(!e)return null;let E=-1!==e.name.indexOf("cmplz_")||w,P=1!=e.deleted?"cmplz-reset-button":"",O=m.map((e,a)=>({value:e.ID,label:e.name})),T=!1,L="Marketing";y.forEach(function(e,a){e.value&&-1!==e.value.indexOf("/")&&(T=!0,L=e.value,L=L.substring(0,L.indexOf("/")))});let F=e.purpose&&-1!==e.purpose.indexOf("/");F&&(L=e.purpose.substring(0,e.purpose.indexOf("/"))),T&&!F&&y.forEach(function(e,a){e.value&&-1!==e.value.indexOf("/")&&(e.value=L,e.label=L,y[a]=e)});let A=e.purpose;return!T&&F&&(A=L),(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,u.jsx)(c.default,{id:e.ID+"_cdb_api",disabled:!C,value:w,onChange:a=>I(a,e.ID,"sync"),options:{true:(0,o.__)("Sync cookie with cookiedatabase.org","complianz-gdpr")}})}),(0,u.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,u.jsx)(c.default,{id:e.ID+"showOnPolicy",disabled:N,value:e.showOnPolicy,onChange:a=>I(a,e.ID,"showOnPolicy"),options:{true:(0,o.__)("Show cookie on Cookie Policy","complianz-gdpr")}})}),(0,u.jsxs)("div",{className:"cmplz-details-row",children:[(0,u.jsx)("label",{children:(0,o.__)("Name","complianz-gdpr")}),(0,u.jsx)("input",{disabled:N,onChange:e=>b(e.target.value),type:"text",placeholder:(0,o.__)("Name","complianz-gdpr"),value:f})]}),(0,u.jsxs)("div",{className:"cmplz-details-row",children:[(0,u.jsx)("label",{children:(0,o.__)("Service","complianz-gdpr")}),(0,u.jsx)(d.default,{disabled:N,value:e.serviceID,options:O,onChange:a=>D(a,e.ID,"serviceID")})]}),(0,u.jsxs)("div",{className:"cmplz-details-row",children:[(0,u.jsx)("label",{children:(0,o.__)("Expiration","complianz-gdpr")}),(0,u.jsx)("input",{disabled:E,onChange:e=>x(e.target.value),type:"text",placeholder:(0,o.__)("1 year","complianz-gdpr"),value:v})]}),(0,u.jsxs)("div",{className:"cmplz-details-row",children:[(0,u.jsx)("label",{children:(0,o.__)("Cookie function","complianz-gdpr")}),(0,u.jsx)("input",{disabled:N,onChange:e=>j(e.target.value),type:"text",placeholder:(0,o.__)("e.g. store user ID","complianz-gdpr"),value:k})]}),(0,u.jsxs)("div",{className:"cmplz-details-row",children:[(0,u.jsx)("label",{children:(0,o.__)("Purpose","complianz-gdpr")}),(0,u.jsx)(d.default,{disabled:N,value:A,options:y,onChange:a=>D(a,e.ID,"purpose")})]}),S&&(0,u.jsx)("div",{className:"cmplz-details-row",children:(0,u.jsx)("a",{href:S,target:"_blank",rel:"noopener noreferrer",children:(0,o.__)("View cookie on cookiedatabase.org","complianz-gdpr")})}),(0,u.jsxs)("div",{className:"cmplz-details-row cmplz-details-row__buttons",children:[(0,u.jsx)("button",{disabled:n,onClick:a=>(async e=>{await g(e),t((0,o.__)("Saved cookie","complianz-gdpr"))})(e.ID),className:"button button-default",children:(0,o.__)("Save","complianz-gdpr")}),(0,u.jsxs)("button",{className:"button button-default "+P,onClick:a=>(async e=>{await _(e)})(e.ID),children:[1==e.deleted&&(0,o.__)("Restore","complianz-gdpr"),1!=e.deleted&&(0,o.__)("Delete","complianz-gdpr")]})]})]})},m=(0,r.memo)(({cookie:e,id:a})=>{let t="";e.deleted?t=" | "+(0,o.__)("Deleted","complianz-gdpr"):e.showOnPolicy?e.isMembersOnly&&(t=" | "+(0,o.__)("Logged in users only, ignored","complianz-gdpr")):t=" | "+(0,o.__)("Admin, ignored","complianz-gdpr");let i=e.name;return(0,u.jsx)(u.Fragment,{children:(0,u.jsx)(n.default,{id:a,summary:i,comment:t,icons:(0,u.jsxs)(u.Fragment,{children:[e.complete&&(0,u.jsx)(s.default,{tooltip:(0,o.__)("The data for this cookie is complete","complianz-gdpr"),name:"success",color:"green"}),!e.complete&&(0,u.jsx)(s.default,{tooltip:(0,o.__)("This cookie has missing fields","complianz-gdpr"),name:"times",color:"red"}),e.sync&&e.synced&&(0,u.jsx)(s.default,{name:"rotate",color:"green"}),!e.synced||!e.sync&&(0,u.jsx)(s.default,{tooltip:(0,o.__)("This cookie is not synchronized with cookiedatabase.org.","complianz-gdpr"),name:"rotate-error",color:"red"}),e.showOnPolicy&&(0,u.jsx)(s.default,{tooltip:(0,o.__)("This cookie will be on your Cookie Policy","complianz-gdpr"),name:"file",color:"green"}),!e.showOnPolicy&&(0,u.jsx)(s.default,{tooltip:(0,o.__)("This cookie is not shown on the Cookie Policy","complianz-gdpr"),name:"file-disabled",color:"grey"}),e.old&&(0,u.jsx)(s.default,{tooltip:(0,o.__)("This cookie has not been detected on your site in the last three months","complianz-gdpr"),name:"calendar-error",color:"red"}),!e.old&&(0,u.jsx)(s.default,{tooltip:(0,o.__)("This cookie has recently been detected","complianz-gdpr"),name:"calendar",color:"green"})]}),details:p(e),style:(()=>{if(e.deleted)return Object.assign({},{backgroundColor:"var(--rsp-red-faded)"})})()})})})},25228:(e,a,t)=>{t.r(a),t.d(a,{default:()=>r});var s=t(86087),o=t(21366),n=t(45111),i=t(27723),l=t(10790);const r=(0,s.memo)(({value:e=!1,onChange:a,required:t,defaultValue:s,disabled:r,options:c={},canBeEmpty:d=!0,label:u})=>{if(Array.isArray(c)){let e={};c.map(a=>{e[a.value]=a.label}),c=e}return d?(""===e||!1===e||0===e)&&(e="0",c={0:(0,i.__)("Select an option","complianz-gdpr"),...c}):e||(e=Object.keys(c)[0]),(0,l.jsx)("div",{className:"cmplz-input-group cmplz-select-group",children:(0,l.jsxs)(o.bL,{value:e,defaultValue:s,onValueChange:a,required:t,disabled:r&&!Array.isArray(r),children:[(0,l.jsxs)(o.l9,{className:"cmplz-select-group__trigger",children:[(0,l.jsx)(o.WT,{}),(0,l.jsx)(n.default,{name:"chevron-down"})]}),(0,l.jsxs)(o.UC,{className:"cmplz-select-group__content",position:"popper",children:[(0,l.jsx)(o.PP,{className:"cmplz-select-group__scroll-button",children:(0,l.jsx)(n.default,{name:"chevron-up"})}),(0,l.jsx)(o.LM,{className:"cmplz-select-group__viewport",children:(0,l.jsx)(o.YJ,{children:Object.entries(c).map(([e,a])=>(0,l.jsx)(o.q7,{disabled:Array.isArray(r)&&r.includes(e),className:"cmplz-select-group__item",value:e,children:(0,l.jsx)(o.p4,{children:a})},e))})}),(0,l.jsx)(o.wn,{className:"cmplz-select-group__scroll-button",children:(0,l.jsx)(n.default,{name:"chevron-down"})})]})]})},u)})},36729:(e,a,t)=>{t.r(a),t.d(a,{default:()=>h});var s=t(7102),o=t(52010),n=t(15139),i=t(27723),l=t(45111),r=t(4219),c=t(81366),d=t(25228),u=t(86087),p=t(10790);const m=e=>{const{getFieldValue:a,showSavedSettingsNotice:t}=(0,r.default)(),[s,o]=(0,u.useState)(""),[l,m]=(0,u.useState)(""),{language:h,saving:_,deleteService:g,serviceTypeOptions:f,updateService:b,saveService:v}=(0,n.default)();let x="yes"===a("use_cdb_api");const[k,j]=(0,u.useState)([]);(0,u.useEffect)(()=>{let e=f&&f.hasOwnProperty(h)?f[h]:[];e=e.map(e=>({label:e.label,value:e.label})),j(e)},[h,f]);const y=(e,a,t)=>{b(a,t,e)},z=(e,a,t)=>{b(a,t,e)};if((0,u.useEffect)(()=>{e&&e.name&&o(e.name)},[e]),(0,u.useEffect)(()=>{if(!e)return;if(e.name===s)return;if(s.length<2)return;const a=setTimeout(()=>{y(s,e.ID,"name")},500);return()=>{clearTimeout(a)}},[s]),(0,u.useEffect)(()=>{e&&e.privacyStatementURL&&m(e.privacyStatementURL)},[e]),(0,u.useEffect)(()=>{if(!e)return;if(e.privacyStatementURL===l)return;if(0===l.length)return;const a=setTimeout(()=>{y(l,e.ID,"privacyStatementURL")},400);return()=>{clearTimeout(a)}},[l]),!e)return null;let C=!!x&&1==e.sync,w=C;_&&(w=!0);let N=!1;return e.slug.length>0&&(N="https://cookiedatabase.org/service/"+e.slug),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,p.jsx)(c.default,{id:e.ID+"sharesData",disabled:w,value:1==e.sharesData,onChange:a=>z(a,e.ID,"sharesData"),options:{true:(0,i.__)("Data is shared with this service","complianz-gdpr")}})}),(0,p.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,p.jsx)(c.default,{id:e.ID+"sync",disabled:!x,value:C,onChange:a=>z(a,e.ID,"sync"),options:{true:(0,i.__)("Sync service with cookiedatabase.org","complianz-gdpr")}})}),(0,p.jsxs)("div",{className:"cmplz-details-row",children:[(0,p.jsx)("label",{children:(0,i.__)("Name","complianz-gdpr")}),(0,p.jsx)("input",{disabled:w,onChange:e=>o(e.target.value),type:"text",placeholder:(0,i.__)("Name","complianz-gdpr"),value:s})]}),(0,p.jsxs)("div",{className:"cmplz-details-row",children:[(0,p.jsx)("label",{children:(0,i.__)("Service Types","complianz-gdpr")}),(0,p.jsx)(d.default,{disabled:w,value:e.serviceType,options:k,onChange:a=>y(a,e.ID,"serviceType")})]}),(0,p.jsxs)("div",{className:"cmplz-details-row",children:[(0,p.jsx)("label",{children:(0,i.__)("Privacy Statement URL","complianz-gdpr")}),(0,p.jsx)("input",{disabled:w,onChange:e=>m(e.target.value),type:"text",value:l})]}),N&&(0,p.jsx)("div",{className:"cmplz-details-row",children:(0,p.jsx)("a",{href:N,target:"_blank",rel:"noopener noreferrer",children:(0,i.__)("View service on cookiedatabase.org","complianz-gdpr")})}),(0,p.jsxs)("div",{className:"cmplz-details-row cmplz-details-row__buttons",children:[(0,p.jsx)("button",{disabled:_,onClick:a=>(async e=>{await v(e),t((0,i.__)("Saved service","complianz-gdpr"))})(e.ID),className:"button button-default",children:(0,i.__)("Save","complianz-gdpr")}),(0,p.jsx)("button",{className:"button button-default cmplz-reset-button",onClick:a=>(async e=>{await g(e)})(e.ID),children:(0,i.__)("Delete Service","complianz-gdpr")})]})]})},h=(0,u.memo)(e=>{const{adding:a}=(0,n.default)(),t=e.service&&e.service.ID>0&&e.service.hasOwnProperty("name"),r=!e.service||e.service.ID<=0,c=e.service&&e.service.name?e.service.name:(0,i.__)("New Service","complianz-gdpr");return(0,p.jsx)(p.Fragment,{children:(0,p.jsx)(o.default,{id:e.id,summary:e.name,icons:e.service?(0,p.jsxs)(p.Fragment,{children:[e.service.complete&&(0,p.jsx)(l.default,{tooltip:(0,i.__)("The data for this service is complete","complianz-gdpr"),name:"success",color:"green"}),!e.service.complete&&(0,p.jsx)(l.default,{tooltip:(0,i.__)("This service has missing fields","complianz-gdpr"),name:"times",color:"red"}),e.service.synced&&(0,p.jsx)(l.default,{tooltip:(0,i.__)("This service has been synchronized with cookiedatabase.org","complianz-gdpr"),name:"rotate",color:"green"}),!e.service.synced&&(0,p.jsx)(l.default,{tooltip:(0,i.__)("This service is not synchronized with cookiedatabase.org","complianz-gdpr"),name:"rotate-error",color:"red"})]}):(0,p.jsx)(p.Fragment,{}),details:(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("div",{children:m(e.service)}),e.cookies&&e.cookies.length>0&&(0,p.jsx)("div",{className:"cmplz-panel__cookie_list",children:e.cookies.map((e,a)=>(0,p.jsx)(s.default,{cookie:e},a))}),!r&&(0,p.jsxs)("div",{children:[(0,p.jsxs)("button",{disabled:a||!t,onClick:a=>((a,t)=>{e.addCookie(a,t)})(e.service.ID,c),className:"button button-default",children:[(0,i.__)("Add cookie to %s","complianz-gdpr").replace("%s",c),a&&(0,p.jsx)(l.default,{name:"loading",color:"grey"})]}),!t&&(0,p.jsx)("div",{className:"cmplz-comment",children:(0,i.__)("Save service to be able to add cookies","complianz-gdpr")})]})]})})})})},45875:(e,a,t)=>{t.r(a),t.d(a,{default:()=>u});var s=t(86087),o=t(15139),n=t(36729),i=t(27723),l=t(4219),r=t(81366),c=t(66087),d=t(10790);const u=(0,s.memo)(()=>{const{filterAndSort:e,showDeletedCookies:a,setShowDeletedCookies:t,syncDataLoaded:u,loadingSyncData:p,language:m,setLanguage:h,languages:_,fCookies:g,cookieCount:f,addCookie:b,addService:v,fServices:x,syncProgress:k,curlExists:j,hasSyncableData:y,setSyncProgress:z,restart:C,fetchSyncProgressData:w,errorMessage:N}=(0,o.default)(),{addHelpNotice:S,removeHelpNotice:D,getFieldValue:I}=(0,l.default)(),[E,P]=(0,s.useState)(!1),[O,T]=(0,s.useState)(!1),[L,F]=(0,s.useState)([]);(0,s.useEffect)(()=>{!p&&k<100&&w()},[k]),(0,s.useEffect)(()=>{w()},[m]),(0,s.useEffect)(()=>{if("no"!==I("use_cdb_api"))if(j)if(""!==N)P(!0),S("cookiedatabase_sync","warning",N,"Cookiedatabase","complianz-gdpr");else if(y){if(u)if(0===f){T(!0);let e=(0,i.__)("No cookies have been found currently. Please try another site scan, or check the most common causes in the article below ","complianz-gdpr");S("cookiedatabase_sync","warning",e,(0,i.__)("No cookies found","complianz-gdpr"),"https://complianz.io/cookie-scan-results/")}else O&&D("cookiedatabase_sync")}else{P(!0);let e=(0,i.__)("Synchronization disabled: All detected cookies and services have been synchronised.","complianz-gdpr");S("cookiedatabase_sync","warning",e,"Cookiedatabase","complianz-gdpr")}else{P(!0);let e=(0,i.__)("CURL is not enabled on your site, which is required for the Cookiedatabase sync to function.","complianz-gdpr");S("cookiedatabase_sync","warning",e,"Cookiedatabase","complianz-gdpr")}else{P(!0);let e=(0,i.__)("You have opted out of the use of the Cookiedatabase.org synchronization.","complianz-gdpr");S("cookiedatabase_sync","warning",e,"Cookiedatabase","complianz-gdpr")}},[I("use_cdb_api"),j,N,y,L,u,g]),(0,s.useEffect)(()=>{k<100&&k>0&&P(!0)},[k]),(0,s.useEffect)(()=>{e()},[a]);const A=(0,c.memoize)(()=>{let e=[...g];const a={};[...x].forEach(function(e){a[e.ID]={id:e.ID,name:e.name,service:e,cookies:[]}}),e.forEach(function(e){let t=e.service?e.serviceID:0;a[t]||(a[t]={id:t,name:e.service?e.service:(0,i.__)("Unknown Service","complianz-gdpr"),service:x.filter(e=>e.ID===t)[0],cookies:[]}),a[t].cookies.push(e)}),F(Object.values(a))});return(0,s.useEffect)(()=>{A()},[x,g]),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"cmplz-cookiedatabase-controls",children:[(0,d.jsx)("button",{disabled:E||p,className:"button button-default",onClick:e=>(z(1),void C()),children:(0,i.__)("Sync","complianz-gdpr")}),_.length>1&&(0,d.jsx)("select",{disabled:p,value:m,onChange:e=>h(e.target.value),children:_.map((e,a)=>(0,d.jsx)("option",{value:e,children:e},a))}),(0,d.jsx)(r.default,{id:"show_deleted_cookies",value:a,onChange:e=>t(e),options:{true:(0,i.__)("Show deleted cookies","complianz-gdpr")}})]}),(0,d.jsx)("div",{id:"cmplz-scan-progress",children:(0,d.jsx)("div",{className:"cmplz-progress-bar",style:Object.assign({},{width:k+"%"})})}),(0,d.jsx)("div",{className:"cmplz-panel__list",children:L.map((e,a)=>(0,d.jsx)(n.default,{addCookie:b,id:e.id,cookies:e.cookies,name:e.name,service:e.service},a))}),(0,d.jsx)("div",{className:"cmplz-panel__buttons",children:(0,d.jsx)("button",{disabled:p,onClick:e=>{v()},className:"button button-default",children:(0,i.__)("Add service","complianz-gdpr")})})]})})},52010:(e,a,t)=>{t.r(a),t.d(a,{default:()=>i});var s=t(45111),o=t(86087),n=t(10790);const i=e=>{const[a,t]=(0,o.useState)(!1);return(0,n.jsx)("div",{className:"cmplz-panel__list__item",style:e.style?e.style:{},children:(0,n.jsxs)("details",{open:a,children:[(0,n.jsxs)("summary",{onClick:e=>(e=>{e.preventDefault(),t(!a)})(e),children:[e.icon&&(0,n.jsx)(s.default,{name:e.icon}),(0,n.jsx)("h5",{className:"cmplz-panel__list__item__title",children:e.summary}),(0,n.jsx)("div",{className:"cmplz-panel__list__item__comment",children:e.comment}),(0,n.jsx)("div",{className:"cmplz-panel__list__item__icons",children:e.icons}),(0,n.jsx)(s.default,{name:"chevron-down",size:18})]}),(0,n.jsx)("div",{className:"cmplz-panel__list__item__details",children:a&&e.details})]})})}},79758:(e,a,t)=>{t.r(a),t.d(a,{default:()=>u});var s=t(86087),o=t(9588),n=t(4219),i=t(52043),l=t(56427),r=t(99091),c=t(32828),d=t(10790);const u=(0,s.memo)(({type:e="action",style:a="tertiary",label:t,onClick:u,href:p="",target:m="",disabled:h,action:_,field:g,children:f})=>{if(!t&&!f)return null;const b=(g&&g.button_text?g.button_text:t)||f,{fetchFieldsData:v,showSavedSettingsNotice:x}=(0,n.default)(),{setInitialLoadCompleted:k,setProgress:j}=(0,r.UseCookieScanData)(),{setProgressLoaded:y}=(0,c.default)(),{selectedSubMenuItem:z}=(0,i.default)(),[C,w]=(0,s.useState)(!1),N=`button cmplz-button button--${a} button-${e}`,S=async e=>{await o.doAction(g.action,{}).then(e=>{e.success&&(v(z),"reset_settings"===e.id&&(k(!1),j(0),y(!1)),x(e.message))})},D=g&&g.warn?g.warn:"";return"action"===e?(0,d.jsxs)(d.Fragment,{children:[l.__experimentalConfirmDialog&&(0,d.jsx)(l.__experimentalConfirmDialog,{isOpen:C,onConfirm:async()=>{w(!1),await S()},onCancel:()=>{w(!1)},children:D}),(0,d.jsx)("button",{className:N,onClick:async a=>{if("action"!==e||!u)return"action"===e&&_?l.__experimentalConfirmDialog?void(g&&g.warn?w(!0):await S()):void await S():void(window.location.href=g.url);u(a)},disabled:h,children:b})]}):"link"===e?(0,d.jsx)("a",{className:N,href:p,target:m,children:b}):void 0})},81366:(e,a,t)=>{t.r(a),t.d(a,{default:()=>O});var s=t(51609),o=t(91071),n=t(62133),i=t(9957),l=t(81351),r=t(85357),c=t(54150),d=t(7971),u=t(12579),p=t(10790),m="Checkbox",[h,_]=(0,n.A)(m),[g,f]=h(m);function b(e){const{__scopeCheckbox:a,checked:t,children:o,defaultChecked:n,disabled:i,form:r,name:c,onCheckedChange:d,required:u,value:h="on",internal_do_not_use_render:_}=e,[f,b]=(0,l.i)({prop:t,defaultProp:n??!1,onChange:d,caller:m}),[v,x]=s.useState(null),[k,j]=s.useState(null),y=s.useRef(!1),z=!v||!!r||!!v.closest("form"),C={checked:f,disabled:i,setChecked:b,control:v,setControl:x,name:c,form:r,value:h,hasConsumerStoppedPropagationRef:y,required:u,defaultChecked:!N(n)&&n,isFormControl:z,bubbleInput:k,setBubbleInput:j};return(0,p.jsx)(g,{scope:a,...C,children:w(_)?_(C):o})}var v="CheckboxTrigger",x=s.forwardRef(({__scopeCheckbox:e,onKeyDown:a,onClick:t,...n},l)=>{const{control:r,value:c,disabled:d,checked:m,required:h,setControl:_,setChecked:g,hasConsumerStoppedPropagationRef:b,isFormControl:x,bubbleInput:k}=f(v,e),j=(0,o.s)(l,_),y=s.useRef(m);return s.useEffect(()=>{const e=r?.form;if(e){const a=()=>g(y.current);return e.addEventListener("reset",a),()=>e.removeEventListener("reset",a)}},[r,g]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":N(m)?"mixed":m,"aria-required":h,"data-state":S(m),"data-disabled":d?"":void 0,disabled:d,value:c,...n,ref:j,onKeyDown:(0,i.m)(a,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(t,e=>{g(e=>!!N(e)||!e),k&&x&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})})});x.displayName=v;var k=s.forwardRef((e,a)=>{const{__scopeCheckbox:t,name:s,checked:o,defaultChecked:n,required:i,disabled:l,value:r,onCheckedChange:c,form:d,...u}=e;return(0,p.jsx)(b,{__scopeCheckbox:t,checked:o,defaultChecked:n,disabled:l,required:i,onCheckedChange:c,name:s,form:d,value:r,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(x,{...u,ref:a,__scopeCheckbox:t}),e&&(0,p.jsx)(C,{__scopeCheckbox:t})]})})});k.displayName=m;var j="CheckboxIndicator",y=s.forwardRef((e,a)=>{const{__scopeCheckbox:t,forceMount:s,...o}=e,n=f(j,t);return(0,p.jsx)(d.C,{present:s||N(n.checked)||!0===n.checked,children:(0,p.jsx)(u.sG.span,{"data-state":S(n.checked),"data-disabled":n.disabled?"":void 0,...o,ref:a,style:{pointerEvents:"none",...e.style}})})});y.displayName=j;var z="CheckboxBubbleInput",C=s.forwardRef(({__scopeCheckbox:e,...a},t)=>{const{control:n,hasConsumerStoppedPropagationRef:i,checked:l,defaultChecked:d,required:m,disabled:h,name:_,value:g,form:b,bubbleInput:v,setBubbleInput:x}=f(z,e),k=(0,o.s)(t,x),j=(0,r.Z)(l),y=(0,c.X)(n);s.useEffect(()=>{const e=v;if(!e)return;const a=window.HTMLInputElement.prototype,t=Object.getOwnPropertyDescriptor(a,"checked").set,s=!i.current;if(j!==l&&t){const a=new Event("click",{bubbles:s});e.indeterminate=N(l),t.call(e,!N(l)&&l),e.dispatchEvent(a)}},[v,j,l,i]);const C=s.useRef(!N(l)&&l);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:d??C.current,required:m,disabled:h,name:_,value:g,form:b,...a,tabIndex:-1,ref:k,style:{...a.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function w(e){return"function"==typeof e}function N(e){return"indeterminate"===e}function S(e){return N(e)?"indeterminate":e?"checked":"unchecked"}C.displayName=z;var D=t(27723),I=t(45111),E=t(86087),P=t(79758);const O=(0,E.memo)(({indeterminate:e,label:a,value:t,id:s,onChange:o,required:n,disabled:i,options:l={}})=>{const[r,c]=(0,E.useState)(!1),[d,u]=(0,E.useState)(!1);let m=t;Array.isArray(m)||(m=""===m?[]:[m]),(0,E.useEffect)(()=>{let e=1===Object.keys(l).length&&"true"===Object.keys(l)[0];c(e)},[]),e&&(t=!0);const h=m;let _=!1;Object.keys(l).length>10&&(_=!0);const g=e=>r?t:h.includes(""+e)||h.includes(parseInt(e)),f=()=>{u(!d)};let b=i&&!Array.isArray(i);return 0===Object.keys(l).length?(0,p.jsx)(p.Fragment,{children:(0,D.__)("No options found","complianz-gdpr")}):(0,p.jsxs)("div",{className:"cmplz-checkbox-group",children:[Object.entries(l).map(([l,c],u)=>(0,p.jsxs)("div",{className:"cmplz-checkbox-group__item"+(!d&&u>9?" cmplz-hidden":""),children:[(0,p.jsx)(k,{className:"cmplz-checkbox-group__checkbox",id:s+"_"+l,checked:g(l),"aria-label":a,disabled:b||Array.isArray(i)&&i.includes(l),required:n,onCheckedChange:e=>((e,a)=>{if(r)o(!t);else{const e=h.includes(""+a)||h.includes(parseInt(a))?h.filter(e=>e!==""+a&&e!==parseInt(a)):[...h,a];o(e)}})(0,l),children:(0,p.jsx)(y,{className:"cmplz-checkbox-group__indicator",children:(0,p.jsx)(I.default,{name:e?"indeterminate":"check",size:14,color:"dark-blue"})})}),(0,p.jsx)("label",{className:"cmplz-checkbox-group__label",htmlFor:s+"_"+l,children:c})]},l)),!d&&_&&(0,p.jsx)(P.default,{onClick:()=>f(),children:(0,D.__)("Show more","complianz-gdpr")}),d&&_&&(0,p.jsx)(P.default,{onClick:()=>f(),children:(0,D.__)("Show less","complianz-gdpr")})]})})},85357:(e,a,t)=>{t.d(a,{Z:()=>o});var s=t(51609);function o(e){const a=s.useRef({value:e,previous:e});return s.useMemo(()=>(a.current.value!==e&&(a.current.previous=a.current.value,a.current.value=e),a.current.previous),[e])}},99091:(e,a,t)=>{t.r(a),t.d(a,{UseCookieScanData:()=>n});var s=t(81621),o=t(9588);const n=(0,s.vt)((e,a)=>({initialLoadCompleted:!1,setInitialLoadCompleted:a=>e({initialLoadCompleted:a}),iframeLoaded:!1,loading:!1,nextPage:!1,progress:0,cookies:[],lastLoadedIframe:"",setIframeLoaded:a=>e({iframeLoaded:a}),setLastLoadedIframe:a=>e(e=>({lastLoadedIframe:a})),setProgress:a=>e({progress:a}),fetchProgress:()=>(e({loading:!0}),o.doAction("get_scan_progress",{}).then(a=>(e({initialLoadCompleted:!0,loading:!1,nextPage:a.next_page,progress:a.progress,cookies:a.cookies}),a)))}))}}]);