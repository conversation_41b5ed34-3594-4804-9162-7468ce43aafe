"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[8084,8895],{88895:(t,a,e)=>{e.r(a),e.d(a,{default:()=>r});var s=e(81621),o=e(9588);const i={optin:{labels:["Functional","Statistics","Marketing","Do Not Track","No Choice","No Warning"],categories:["functional","statistics","marketing","do_not_track","no_choice","no_warning"],datasets:[{data:["0","0","0","0","0","0"],backgroundColor:"rgba(46, 138, 55, 1)",borderColor:"rgba(46, 138, 55, 1)",label:"A (default)",fill:"false",borderDash:[0,0]},{data:["0","0","0","0","0","0"],backgroundColor:"rgba(244, 191, 62, 1)",borderColor:"rgba(244, 191, 62, 1)",label:"B",fill:"false",borderDash:[0,0]}],max:5},optout:{labels:["Functional","Statistics","Marketing","Do Not Track","No Choice","No Warning"],categories:["functional","statistics","marketing","do_not_track","no_choice","no_warning"],datasets:[{data:["0","0","0","0","0","0"],backgroundColor:"rgba(46, 138, 55, 1)",borderColor:"rgba(46, 138, 55, 1)",label:"A (default)",fill:"false",borderDash:[0,0]},{data:["0","0","0","0","0","0"],backgroundColor:"rgba(244, 191, 62, 1)",borderColor:"rgba(244, 191, 62, 1)",label:"B",fill:"false",borderDash:[0,0]}],max:5}},l={optin:{labels:["Functional","Statistics","Marketing","Do Not Track","No Choice","No Warning"],categories:["functional","statistics","marketing","do_not_track","no_choice","no_warning"],datasets:[{data:["29","747","174","292","30","10"],backgroundColor:"rgba(46, 138, 55, 1)",borderColor:"rgba(46, 138, 55, 1)",label:"Demo A (default)",fill:"false",borderDash:[0,0]},{data:["3","536","240","389","45","32"],backgroundColor:"rgba(244, 191, 62, 1)",borderColor:"rgba(244, 191, 62, 1)",label:"Demo B",fill:"false",borderDash:[0,0]}],max:5},optout:{labels:["Functional","Statistics","Marketing","Do Not Track","No Choice","No Warning"],categories:["functional","statistics","marketing","do_not_track","no_choice","no_warning"],datasets:[{data:["29","747","174","292","30","10"],backgroundColor:"rgba(46, 138, 55, 1)",borderColor:"rgba(46, 138, 55, 1)",label:"A (default)",fill:"false",borderDash:[0,0]},{data:["3","536","240","389","45","32"],backgroundColor:"rgba(244, 191, 62, 1)",borderColor:"rgba(244, 191, 62, 1)",label:"Demo B",fill:"false",borderDash:[0,0]}],max:5}},r=(0,s.vt)((t,a)=>({consentType:"optin",setConsentType:a=>{t({consentType:a})},statisticsLoading:!1,consentTypes:[],regions:[],defaultConsentType:"optin",loaded:!1,statisticsData:i,emptyStatisticsData:i,bestPerformerEnabled:!1,daysLeft:"",abTrackingCompleted:!1,labels:[],setLabels:a=>{t({labels:a})},fetchStatisticsData:async()=>{if(!cmplz_settings.is_premium)return void t({saving:!1,loaded:!0,consentType:"optin",consentTypes:["optin","optout"],statisticsData:l,defaultConsentType:"optin",bestPerformerEnabled:!1,regions:"eu",daysLeft:11,abTrackingCompleted:!1});if(t({saving:!0}),a().loaded)return;const{daysLeft:e,abTrackingCompleted:s,consentTypes:i,statisticsData:r,defaultConsentType:n,regions:c,bestPerformerEnabled:d}=await o.doAction("get_statistics_data",{}).then(t=>t).catch(t=>{console.error(t)});t({saving:!1,loaded:!0,consentType:n,consentTypes:i,statisticsData:r,defaultConsentType:n,bestPerformerEnabled:d,regions:c,daysLeft:e,abTrackingCompleted:s})}}))},98084:(t,a,e)=>{e.r(a),e.d(a,{default:()=>n});var s=e(45111),o=e(27723),i=e(88895),l=e(86087),r=e(10790);const n=()=>{const[t,a]=(0,l.useState)(!1),[e,n]=(0,l.useState)(1),[c,d]=(0,l.useState)(0),[g,b]=(0,l.useState)(0),{consentType:f,statisticsData:p,loaded:m,fetchStatisticsData:u,labels:h,setLabels:k}=(0,i.default)();(0,l.useEffect)(()=>{!m&&cmplz_settings.is_premium&&u()},[]),(0,l.useEffect)(()=>{if(""===f||!m)return;if(!p||!p.hasOwnProperty(f))return;let t=[...p[f].labels],a=p[f].categories;a="optin"===f?a.filter(t=>"functional"===t||"no_warning"===t||"do_not_track"===t):a.filter(t=>"functional"===t||"marketing"===t||"statistics"===t||"preferences"===t);let e=a.map(t=>p[f].categories.indexOf(t));for(let a=e.length-1;a>=0;a--)t.splice(e[a],1);k(t)},[m,f]),(0,l.useEffect)(()=>{if(""===f||!m||!p)return;let t=p[f].datasets.filter(t=>t.default);if(t.length>0){let e=t[0].data,s=e.reduce((t,a)=>parseInt(t)+parseInt(a),0);s=s>0?s:1,n(s),d(t[0].full_consent),b(t[0].no_consent),e=e.slice(2),a(e)}},[m,f]);const C=t=>{let a="dial-med-low-light";return 1===t?a="dial-med-light":2===t?a="dial-light":3===t?a="dial-off-light":4===t&&(a="dial-min-light"),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(s.default,{name:a,color:"black"})})};return(0,r.jsxs)("div",{className:"cmplz-statistics",children:[(0,r.jsxs)("div",{className:"cmplz-statistics-select",children:[(0,r.jsxs)("div",{className:"cmplz-statistics-select-item",children:[(0,r.jsx)(s.default,{name:"dial-max-light",color:"green",size:"22"}),(0,r.jsx)("h2",{children:c}),(0,r.jsx)("span",{children:(0,o.__)("Full Consent","complianz-gdpr")})]}),(0,r.jsxs)("div",{className:"cmplz-statistics-select-item",children:[(0,r.jsx)(s.default,{name:"dial-min-light",color:"red",size:"22"}),(0,r.jsx)("h2",{children:g}),(0,r.jsx)("span",{children:(0,o.__)("No Consent","complianz-gdpr")})]})]}),(0,r.jsx)("div",{className:"cmplz-statistics-list",children:h.length>0&&h.map((a,s)=>{return(0,r.jsxs)("div",{className:"cmplz-statistics-list-item",children:[C(s),(0,r.jsx)("p",{className:"cmplz-statistics-list-item-text",children:a}),(0,r.jsxs)("p",{className:"cmplz-statistics-list-item-number",children:[t.hasOwnProperty(s)?(o=t[s],o=parseInt(o),Math.round(o/e*100)):0,"%"]})]},s);var o})})]})}}}]);