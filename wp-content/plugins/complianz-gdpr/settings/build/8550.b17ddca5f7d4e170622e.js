"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[5207,8550,8985,9091,9758],{5207:(e,t,n)=>{n.r(t),n.d(t,{default:()=>i});var a=n(81621),o=n(16535),s=n(9588),r=n(73710);const i=(0,a.vt)((e,t)=>({documentsLoaded:!1,savedDocument:{},conclusions:[],region:"",fileName:"",fetching:!1,updating:!1,loadingFields:!1,documents:[],regions:[],fields:[],editDocumentId:!1,resetEditDocumentId:t=>{e({editDocumentId:!1,region:""})},editDocument:async t=>{e({updating:!0}),await s.doAction("load_databreach_report",{id:t}).then(t=>{e({fields:t.fields,region:t.region,updating:!1,fileName:t.file_name})}).catch(e=>{console.error(e)}),e({editDocumentId:t})},setRegion:t=>{e({region:t})},updateField:(n,a)=>{let s=!1,i=!1;e((0,o.Ay)(e=>{e.fields.forEach(function(e,t){e.id===n&&(i=t,s=!0)}),!1!==i&&(e.fields[i].value=a)}));let c=(0,r.updateFieldsListWithConditions)(t().fields);e({fields:c})},save:async n=>{e({updating:!0});let a=t().editDocumentId,o=0;await s.doAction("save_databreach_report",{fields:t().fields,region:n,post_id:a}).then(t=>(o=t.post_id,e({updating:!1,conclusions:t.conclusions}),t)).catch(e=>{console.error(e)}),await t().fetchData();let r=t().documents.filter(e=>e.id===o);r.length>0&&e({savedDocument:r[0]})},deleteDocuments:async n=>{let a=t().documents.filter(e=>n.includes(e.id));e(e=>({documents:e.documents.filter(e=>!n.includes(e.id))}));let o={};o.documents=a,await s.doAction("delete_databreach_report",o).then(e=>e).catch(e=>{console.error(e)})},fetchData:async()=>{if(t().fetching)return;e({fetching:!0});const{documents:n,regions:a}=await s.doAction("get_databreach_reports",{}).then(e=>e).catch(e=>{console.error(e)});e(e=>({documentsLoaded:!0,documents:n,regions:a,fetching:!1}))},fetchFields:async t=>{let n={region:t};e({loadingFields:!0});const{fields:a}=await s.doAction("get_databreach_report_fields",n).then(e=>e).catch(e=>{console.error(e)});let o=(0,r.updateFieldsListWithConditions)(a);e(e=>({fields:o,loadingFields:!1}))}}))},28550:(e,t,n)=>{n.r(t),n.d(t,{default:()=>l});var a=n(5207),o=n(86087),s=n(27723),r=n(45111),i=n(81366),c=n(10790);const l=(0,o.memo)(()=>{const{documents:e,documentsLoaded:t,fetchData:l,deleteDocuments:d,editDocument:u}=(0,a.default)(),[p,h]=(0,o.useState)(""),[m]=(0,o.useState)(""),[g,f]=(0,o.useState)(""),[b,_]=(0,o.useState)([]),[k,C]=(0,o.useState)(!1),[x,v]=(0,o.useState)({}),[w,y]=(0,o.useState)(!1),[j,z]=(0,o.useState)(!1),[D,L]=(0,o.useState)(null);(0,o.useEffect)(()=>{n.e(3757).then(n.bind(n,83757)).then(({default:e})=>{L(()=>e)})},[]),(0,o.useEffect)(()=>{t||l()},[t]);const S=async()=>{let t=e.filter(e=>b.includes(e.id));C(!0),f(!0);const n=async()=>{if(t.length>0){const e=t.shift(),a=e.download_url;try{let o=new XMLHttpRequest;o.responseType="blob",o.open("get",a,!0),o.send(),o.onreadystatechange=function(){if(4===this.readyState&&200===this.status){let n=window.URL.createObjectURL(this.response),a=window.document.createElement("a");a.setAttribute("href",n),a.setAttribute("download",e.title),window.document.body.appendChild(a),a.click(),_(t),C(!1),f(!1),setTimeout(function(){window.URL.revokeObjectURL(n)},6e4)}},await n()}catch(e){console.error(e),f(!1)}}};await n()},N=e=>{let t=[...e];return""!==m&&(t=t.filter(e=>e.region===m)),t.sort((e,t)=>e.title<t.title?-1:e.title>t.title?1:0),t.filter(e=>e.title.toLowerCase().includes(p.toLowerCase())),t},I=[{name:(0,c.jsx)(i.default,{options:{true:""},indeterminate:w,value:j,onChange:t=>(t=>{if(t){z(!0);let t=x.currentPage?x.currentPage:1,n=N(e).slice(5*(t-1),5*t);_(n.map(e=>e.id))}else z(!1),_([]);y(!1)})(t)}),selector:e=>e.selectControl,grow:1,minWidth:"50px"},{name:(0,s.__)("Document","complianz-gdpr"),selector:e=>e.title,sortable:!0,grow:6},{name:(0,s.__)("Region","complianz-gdpr"),selector:e=>(0,c.jsx)("img",{alt:"region",width:"20px",height:"20px",src:cmplz_settings.plugin_url+"assets/images/"+e.region+".svg"}),sortable:!0,grow:2,right:!0},{name:(0,s.__)("Date","complianz-gdpr"),selector:e=>e.date,sortable:!0,grow:4,right:!0,minWidth:"200px"}];let R=N(e),P=[];R.forEach(t=>{let n={...t};n.selectControl=(0,c.jsx)(i.default,{value:b.includes(n.id),options:{true:""},onChange:t=>((t,n)=>{let a=t,o=[...b];a?o.includes(n)||(o.push(n),_(o)):(o=[...b.filter(e=>e!==n)],_(o));let s=x.currentPage?x.currentPage:1,r=N(e).slice(5*(s-1),5*s),i=!0,c=!1;r.forEach(e=>{o.includes(e.id)?c=!0:i=!1}),i?(z(!0),y(!1)):c?(z(!1),y(!0)):y(!1)})(t,n.id)}),P.push(n)});let E=b.length>1;return E||1!==b.length||(E=""!==e.filter(e=>b.includes(e.id))[0].download_url),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{className:"cmplz-table-header",children:(0,c.jsx)("div",{className:"cmplz-table-header-controls",children:(0,c.jsx)("input",{className:"cmplz-datatable-search",type:"text",placeholder:(0,s.__)("Search","complianz-gdpr"),value:p,onChange:e=>h(e.target.value)})})}),b.length>0&&(0,c.jsxs)("div",{className:"cmplz-selected-document",children:[b.length>1&&(0,s.__)("%s items selected","complianz-gdpr").replace("%s",b.length),1===b.length&&(0,s.__)("1 item selected","complianz-gdpr"),(0,c.jsxs)("div",{className:"cmplz-selected-document-controls",children:[(0,c.jsx)("button",{disabled:g||b.length>1,className:"button button-default",onClick:()=>u(b[0]),children:(0,s.__)("Edit","complianz-gdpr")}),E&&(0,c.jsxs)("button",{disabled:g,className:"button button-default cmplz-btn-reset",onClick:()=>S(),children:[(0,s.__)("Download Data Breach Report","complianz-gdpr"),k&&(0,c.jsx)(r.default,{name:"loading",color:"grey"})]}),!E&&(0,c.jsx)("button",{disabled:!0,className:"button button-default cmplz-btn-reset",onClick:()=>S(),children:(0,s.__)("Reporting not required","complianz-gdpr")}),(0,c.jsx)("button",{className:"button button-default cmplz-reset-button",onClick:()=>(async e=>{_([]),await d(e)})(b),children:(0,s.__)("Delete","complianz-gdpr")})]})]}),D&&(0,c.jsx)(c.Fragment,{children:(0,c.jsx)(D,{columns:I,data:P,dense:!0,pagination:!0,paginationPerPage:5,onChangePage:e=>{v({...x,currentPage:e})},paginationState:x,noDataComponent:(0,c.jsx)("div",{className:"cmplz-no-documents",children:(0,s.__)("No documents","complianz-gdpr")}),persistTableHead:!0,theme:"really-simple-plugins",customStyles:{headCells:{style:{paddingLeft:"0",paddingRight:"0"}},cells:{style:{paddingLeft:"0",paddingRight:"0"}}}})})]})})},79758:(e,t,n)=>{n.r(t),n.d(t,{default:()=>u});var a=n(86087),o=n(9588),s=n(4219),r=n(52043),i=n(56427),c=n(99091),l=n(32828),d=n(10790);const u=(0,a.memo)(({type:e="action",style:t="tertiary",label:n,onClick:u,href:p="",target:h="",disabled:m,action:g,field:f,children:b})=>{if(!n&&!b)return null;const _=(f&&f.button_text?f.button_text:n)||b,{fetchFieldsData:k,showSavedSettingsNotice:C}=(0,s.default)(),{setInitialLoadCompleted:x,setProgress:v}=(0,c.UseCookieScanData)(),{setProgressLoaded:w}=(0,l.default)(),{selectedSubMenuItem:y}=(0,r.default)(),[j,z]=(0,a.useState)(!1),D=`button cmplz-button button--${t} button-${e}`,L=async e=>{await o.doAction(f.action,{}).then(e=>{e.success&&(k(y),"reset_settings"===e.id&&(x(!1),v(0),w(!1)),C(e.message))})},S=f&&f.warn?f.warn:"";return"action"===e?(0,d.jsxs)(d.Fragment,{children:[i.__experimentalConfirmDialog&&(0,d.jsx)(i.__experimentalConfirmDialog,{isOpen:j,onConfirm:async()=>{z(!1),await L()},onCancel:()=>{z(!1)},children:S}),(0,d.jsx)("button",{className:D,onClick:async t=>{if("action"!==e||!u)return"action"===e&&g?i.__experimentalConfirmDialog?void(f&&f.warn?z(!0):await L()):void await L():void(window.location.href=f.url);u(t)},disabled:m,children:_})]}):"link"===e?(0,d.jsx)("a",{className:D,href:p,target:h,children:_}):void 0})},81366:(e,t,n)=>{n.r(t),n.d(t,{default:()=>P});var a=n(51609),o=n(91071),s=n(62133),r=n(9957),i=n(81351),c=n(85357),l=n(54150),d=n(7971),u=n(12579),p=n(10790),h="Checkbox",[m,g]=(0,s.A)(h),[f,b]=m(h);function _(e){const{__scopeCheckbox:t,checked:n,children:o,defaultChecked:s,disabled:r,form:c,name:l,onCheckedChange:d,required:u,value:m="on",internal_do_not_use_render:g}=e,[b,_]=(0,i.i)({prop:n,defaultProp:s??!1,onChange:d,caller:h}),[k,C]=a.useState(null),[x,v]=a.useState(null),w=a.useRef(!1),y=!k||!!c||!!k.closest("form"),j={checked:b,disabled:r,setChecked:_,control:k,setControl:C,name:l,form:c,value:m,hasConsumerStoppedPropagationRef:w,required:u,defaultChecked:!D(s)&&s,isFormControl:y,bubbleInput:x,setBubbleInput:v};return(0,p.jsx)(f,{scope:t,...j,children:z(g)?g(j):o})}var k="CheckboxTrigger",C=a.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...s},i)=>{const{control:c,value:l,disabled:d,checked:h,required:m,setControl:g,setChecked:f,hasConsumerStoppedPropagationRef:_,isFormControl:C,bubbleInput:x}=b(k,e),v=(0,o.s)(i,g),w=a.useRef(h);return a.useEffect(()=>{const e=c?.form;if(e){const t=()=>f(w.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,f]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":D(h)?"mixed":h,"aria-required":m,"data-state":L(h),"data-disabled":d?"":void 0,disabled:d,value:l,...s,ref:v,onKeyDown:(0,r.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,r.m)(n,e=>{f(e=>!!D(e)||!e),x&&C&&(_.current=e.isPropagationStopped(),_.current||e.stopPropagation())})})});C.displayName=k;var x=a.forwardRef((e,t)=>{const{__scopeCheckbox:n,name:a,checked:o,defaultChecked:s,required:r,disabled:i,value:c,onCheckedChange:l,form:d,...u}=e;return(0,p.jsx)(_,{__scopeCheckbox:n,checked:o,defaultChecked:s,disabled:i,required:r,onCheckedChange:l,name:a,form:d,value:c,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(C,{...u,ref:t,__scopeCheckbox:n}),e&&(0,p.jsx)(j,{__scopeCheckbox:n})]})})});x.displayName=h;var v="CheckboxIndicator",w=a.forwardRef((e,t)=>{const{__scopeCheckbox:n,forceMount:a,...o}=e,s=b(v,n);return(0,p.jsx)(d.C,{present:a||D(s.checked)||!0===s.checked,children:(0,p.jsx)(u.sG.span,{"data-state":L(s.checked),"data-disabled":s.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=v;var y="CheckboxBubbleInput",j=a.forwardRef(({__scopeCheckbox:e,...t},n)=>{const{control:s,hasConsumerStoppedPropagationRef:r,checked:i,defaultChecked:d,required:h,disabled:m,name:g,value:f,form:_,bubbleInput:k,setBubbleInput:C}=b(y,e),x=(0,o.s)(n,C),v=(0,c.Z)(i),w=(0,l.X)(s);a.useEffect(()=>{const e=k;if(!e)return;const t=window.HTMLInputElement.prototype,n=Object.getOwnPropertyDescriptor(t,"checked").set,a=!r.current;if(v!==i&&n){const t=new Event("click",{bubbles:a});e.indeterminate=D(i),n.call(e,!D(i)&&i),e.dispatchEvent(t)}},[k,v,i,r]);const j=a.useRef(!D(i)&&i);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:d??j.current,required:h,disabled:m,name:g,value:f,form:_,...t,tabIndex:-1,ref:x,style:{...t.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function z(e){return"function"==typeof e}function D(e){return"indeterminate"===e}function L(e){return D(e)?"indeterminate":e?"checked":"unchecked"}j.displayName=y;var S=n(27723),N=n(45111),I=n(86087),R=n(79758);const P=(0,I.memo)(({indeterminate:e,label:t,value:n,id:a,onChange:o,required:s,disabled:r,options:i={}})=>{const[c,l]=(0,I.useState)(!1),[d,u]=(0,I.useState)(!1);let h=n;Array.isArray(h)||(h=""===h?[]:[h]),(0,I.useEffect)(()=>{let e=1===Object.keys(i).length&&"true"===Object.keys(i)[0];l(e)},[]),e&&(n=!0);const m=h;let g=!1;Object.keys(i).length>10&&(g=!0);const f=e=>c?n:m.includes(""+e)||m.includes(parseInt(e)),b=()=>{u(!d)};let _=r&&!Array.isArray(r);return 0===Object.keys(i).length?(0,p.jsx)(p.Fragment,{children:(0,S.__)("No options found","complianz-gdpr")}):(0,p.jsxs)("div",{className:"cmplz-checkbox-group",children:[Object.entries(i).map(([i,l],u)=>(0,p.jsxs)("div",{className:"cmplz-checkbox-group__item"+(!d&&u>9?" cmplz-hidden":""),children:[(0,p.jsx)(x,{className:"cmplz-checkbox-group__checkbox",id:a+"_"+i,checked:f(i),"aria-label":t,disabled:_||Array.isArray(r)&&r.includes(i),required:s,onCheckedChange:e=>((e,t)=>{if(c)o(!n);else{const e=m.includes(""+t)||m.includes(parseInt(t))?m.filter(e=>e!==""+t&&e!==parseInt(t)):[...m,t];o(e)}})(0,i),children:(0,p.jsx)(w,{className:"cmplz-checkbox-group__indicator",children:(0,p.jsx)(N.default,{name:e?"indeterminate":"check",size:14,color:"dark-blue"})})}),(0,p.jsx)("label",{className:"cmplz-checkbox-group__label",htmlFor:a+"_"+i,children:l})]},i)),!d&&g&&(0,p.jsx)(R.default,{onClick:()=>b(),children:(0,S.__)("Show more","complianz-gdpr")}),d&&g&&(0,p.jsx)(R.default,{onClick:()=>b(),children:(0,S.__)("Show less","complianz-gdpr")})]})})},85357:(e,t,n)=>{n.d(t,{Z:()=>o});var a=n(51609);function o(e){const t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},99091:(e,t,n)=>{n.r(t),n.d(t,{UseCookieScanData:()=>s});var a=n(81621),o=n(9588);const s=(0,a.vt)((e,t)=>({initialLoadCompleted:!1,setInitialLoadCompleted:t=>e({initialLoadCompleted:t}),iframeLoaded:!1,loading:!1,nextPage:!1,progress:0,cookies:[],lastLoadedIframe:"",setIframeLoaded:t=>e({iframeLoaded:t}),setLastLoadedIframe:t=>e(e=>({lastLoadedIframe:t})),setProgress:t=>e({progress:t}),fetchProgress:()=>(e({loading:!0}),o.doAction("get_scan_progress",{}).then(t=>(e({initialLoadCompleted:!0,loading:!1,nextPage:t.next_page,progress:t.progress,cookies:t.cookies}),t)))}))}}]);