"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[4101],{74101:(a,p,n)=>{n.r(p),n.d(p,{upload:()=>e});var t=n(71083);const e=(a,p,n)=>{let e=new FormData;return e.append("data",p),void 0!==n&&e.append("details",JSON.stringify(n)),t.A.post(cmplz_settings.admin_url+"?page=complianz&cmplz_upload_file=1&action="+a,e,{headers:{"Content-Type":"multipart/form-data","X-WP-Nonce":cmplz_settings.nonce}})}}}]);