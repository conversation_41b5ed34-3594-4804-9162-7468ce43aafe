"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[800],{9957:(e,t,n)=>{function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}n.d(t,{m:()=>r})},10800:(e,t,n)=>{n.r(t),n.d(t,{default:()=>_});var r=n(86087),o=n(51609),c=n(9957),i=n(91071),s=n(62133),u=n(81351),l=n(85357),a=n(54150),d=n(12579),f=n(10790),p="Switch",[h,m]=(0,s.A)(p),[b,v]=h(p),g=o.forwardRef((e,t)=>{const{__scopeSwitch:n,name:r,checked:s,defaultChecked:l,required:a,disabled:h,value:m="on",onCheckedChange:v,form:g,...y}=e,[w,x]=o.useState(null),S=(0,i.s)(t,e=>x(e)),N=o.useRef(!1),_=!w||g||!!w.closest("form"),[E,R]=(0,u.i)({prop:s,defaultProp:l??!1,onChange:v,caller:p});return(0,f.jsxs)(b,{scope:n,checked:E,disabled:h,children:[(0,f.jsx)(d.sG.button,{type:"button",role:"switch","aria-checked":E,"aria-required":a,"data-state":k(E),"data-disabled":h?"":void 0,disabled:h,value:m,...y,ref:S,onClick:(0,c.m)(e.onClick,e=>{R(e=>!e),_&&(N.current=e.isPropagationStopped(),N.current||e.stopPropagation())})}),_&&(0,f.jsx)(C,{control:w,bubbles:!N.current,name:r,value:m,checked:E,required:a,disabled:h,form:g,style:{transform:"translateX(-100%)"}})]})});g.displayName=p;var y="SwitchThumb",w=o.forwardRef((e,t)=>{const{__scopeSwitch:n,...r}=e,o=v(y,n);return(0,f.jsx)(d.sG.span,{"data-state":k(o.checked),"data-disabled":o.disabled?"":void 0,...r,ref:t})});w.displayName=y;var C=o.forwardRef(({__scopeSwitch:e,control:t,checked:n,bubbles:r=!0,...c},s)=>{const u=o.useRef(null),d=(0,i.s)(u,s),p=(0,l.Z)(n),h=(0,a.X)(t);return o.useEffect(()=>{const e=u.current;if(!e)return;const t=window.HTMLInputElement.prototype,o=Object.getOwnPropertyDescriptor(t,"checked").set;if(p!==n&&o){const t=new Event("click",{bubbles:r});o.call(e,n),e.dispatchEvent(t)}},[p,n,r]),(0,f.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...c,tabIndex:-1,ref:d,style:{...c.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function k(e){return e?"checked":"unchecked"}C.displayName="SwitchBubbleInput";var x=g,S=w,N=n(4219);const _=(0,r.memo)(({value:e,onChange:t,required:n,disabled:r,className:o,label:c,id:i})=>{const{getField:s}=(0,N.default)();let u=e;return"0"!==e&&"1"!==e||(u="1"===e),(0,f.jsx)("div",{className:"cmplz-input-group cmplz-switch-group",children:(0,f.jsx)(x,{className:"cmplz-switch-root "+o,checked:u,onCheckedChange:e=>{"banner"===s(i).data_target&&(e=e?"1":"0"),t(e)},disabled:r,required:n,children:(0,f.jsx)(S,{className:"cmplz-switch-thumb"})})})})},12579:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>s});var r=n(51609),o=n(75795),c=n(33362),i=n(10790),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{const n=(0,c.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{const{asChild:o,...c}=e,s=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(s,{...c,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},33362:(e,t,n)=>{n.d(t,{TL:()=>i});var r=n(51609),o=n(91071),c=n(10790);function i(e){const t=s(e),n=r.forwardRef((e,n)=>{const{children:o,...i}=e,s=r.Children.toArray(o),u=s.find(l);if(u){const e=u.props.children,o=s.map(t=>t===u?r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null:t);return(0,c.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,c.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}function s(e){const t=r.forwardRef((e,t)=>{const{children:n,...c}=e;if(r.isValidElement(n)){const e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}(n),i=function(e,t){const n={...t};for(const r in t){const o=e[r],c=t[r];/^on[A-Z]/.test(r)?o&&c?n[r]=(...e)=>{const t=c(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...c}:"className"===r&&(n[r]=[o,c].filter(Boolean).join(" "))}return{...e,...n}}(c,n.props);return n.type!==r.Fragment&&(i.ref=t?(0,o.t)(t,e):e),r.cloneElement(n,i)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var u=Symbol("radix.slottable");function l(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}},54150:(e,t,n)=>{n.d(t,{X:()=>c});var r=n(51609),o=n(88200);function c(e){const[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const t=new ResizeObserver(t=>{if(!Array.isArray(t))return;if(!t.length)return;const r=t[0];let o,c;if("borderBoxSize"in r){const e=r.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,c=t.blockSize}else o=e.offsetWidth,c=e.offsetHeight;n({width:o,height:c})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},62133:(e,t,n)=>{n.d(t,{A:()=>c});var r=n(51609),o=n(10790);function c(e,t=[]){let n=[];const c=()=>{const t=n.map(e=>r.createContext(e));return function(n){const o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return c.scopeName=e,[function(t,c){const i=r.createContext(c),s=n.length;n=[...n,c];const u=t=>{const{scope:n,children:c,...u}=t,l=n?.[e]?.[s]||i,a=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(l.Provider,{value:a,children:c})};return u.displayName=t+"Provider",[u,function(n,o){const u=o?.[e]?.[s]||i,l=r.useContext(u);if(l)return l;if(void 0!==c)return c;throw new Error(`\`${n}\` must be used within \`${t}\``)}]},i(c,...t)]}function i(...e){const t=e[0];if(1===e.length)return t;const n=()=>{const n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){const o=n.reduce((t,{useScope:n,scopeName:r})=>({...t,...n(e)[`__scope${r}`]}),{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}},81351:(e,t,n)=>{var r;n.d(t,{i:()=>s});var o=n(51609),c=n(88200),i=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||c.N;function s({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[c,s,u]=function({defaultProp:e,onChange:t}){const[n,r]=o.useState(e),c=o.useRef(n),s=o.useRef(t);return i(()=>{s.current=t},[t]),o.useEffect(()=>{c.current!==n&&(s.current?.(n),c.current=n)},[n,c]),[n,r,s]}({defaultProp:t,onChange:n}),l=void 0!==e,a=l?e:c;{const t=o.useRef(void 0!==e);o.useEffect(()=>{const e=t.current;if(e!==l){const t=e?"controlled":"uncontrolled",n=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${t} to ${n}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,r])}const d=o.useCallback(t=>{if(l){const n=function(e){return"function"==typeof e}(t)?t(e):t;n!==e&&u.current?.(n)}else s(t)},[l,e,s,u]);return[a,d]}Symbol("RADIX:SYNC_STATE")},85357:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(51609);function o(e){const t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},88200:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(51609),o=globalThis?.document?r.useLayoutEffect:()=>{}},91071:(e,t,n)=>{n.d(t,{s:()=>i,t:()=>c});var r=n(51609);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function c(...e){return t=>{let n=!1;const r=e.map(e=>{const r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){const n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function i(...e){return r.useCallback(c(...e),e)}}}]);