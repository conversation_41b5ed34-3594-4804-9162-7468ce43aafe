"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[8198],{88198:(t,s,e)=>{e.r(s),e.d(s,{default:()=>n});var a=e(9588),o=e(81621);const c={loaded:!1,tokenStatus:"",wscStatus:"",wscSignupDate:"",syncing:!1},n=(0,o.vt)((t,s)=>({...c,startOnboarding:async()=>{const t=new URL(cmplz_settings.dashboard_url);t.searchParams.set("websitescan",""),setTimeout(()=>{window.location.href=t.href},500)},getStatus:async()=>{if(!s().getStatusCalled)try{let s={};const{wsc_status:e,token_status:o,wsc_signup_date:c}=await a.doAction("get_wsc_status",s).then(t=>t);t({tokenStatus:o,wscStatus:e,wscSignupDate:c,loaded:!0})}catch(t){console.error("Getting status error: ",t)}},resetWsc:async()=>{if(confirm("Are you sure? This will delete all your Website Scan data."))try{const{result:s,redirect:e}=await a.doAction("reset_wsc").then(t=>t);s&&(t(t=>({...c,startOnboarding:t.startOnboarding,getStatus:t.getStatus,enableWsc:t.enableWsc,disableWsc:t.disableWsc,resetWsc:t.resetWsc})),setTimeout(()=>{window.location.reload()},500))}catch(t){console.error("Resetting WSC error: ",t)}finally{setTimeout(()=>{window.location.reload()},300)}},enableWsc:async()=>{try{const{updated:s,wsc_status:e,token_status:o}=await a.doAction("enable_wsc").then(t=>t);t({updated:s,tokenStatus:o,wscStatus:e,loaded:!0})}catch(t){console.error("Enabling WSC error: ",t)}},disableWsc:async()=>{try{const{updated:s,wsc_status:e,token_status:o}=await a.doAction("disable_wsc").then(t=>t);t({updated:s,tokenStatus:o,wscStatus:e,loaded:!0})}catch(t){console.error("Disabling WSC error: ",t)}},requestActivationEmail:async()=>{try{await a.doAction("request_activation_email"),s().getStatus()}catch(t){console.error("Requesting activation email error: ",t)}}}))}}]);