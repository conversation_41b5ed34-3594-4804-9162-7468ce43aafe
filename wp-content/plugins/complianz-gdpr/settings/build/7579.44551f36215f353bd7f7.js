"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[7579],{57579:(e,t,a)=>{a.r(t),a.d(t,{default:()=>d});var n=a(86087),i=a(96540),r=a(62819),s=a.n(r),l=a(27723),o=a(4219),u=a(10790);const d=(0,n.memo)(({id:e,value:t,onChange:a})=>{const[r,d]=(0,n.useState)(t),[c,g]=(0,n.useState)("wysiwyg"),[h,m]=(0,n.useState)(t),{getFieldValue:b,updateField:w,setChangedField:f}=(0,o.default)();return(0,n.useEffect)(()=>{if(h===t)return;const e=setTimeout(()=>{d(h)},500);return()=>{clearTimeout(e)}},[h]),(0,n.useEffect)(()=>{if(r===t)return;const n=setTimeout(()=>{w(e,r),m(r),f(e,r),a(r)},500);return()=>{clearTimeout(n)}},[r]),(0,n.useEffect)(()=>{d(t)},[b(e)]),(0,u.jsxs)(u.Fragment,{children:[(0,u.jsxs)("button",{className:"button button-default",onClick:e=>{g("wysiwyg"===c?"html":"wysiwyg")},children:["wysiwyg"===c&&"HTML","html"===c&&(0,l.__)("Editor","complianz-gdpr")]}),"html"===c&&(0,u.jsx)(u.Fragment,{children:(0,u.jsx)("textarea",{rows:"8",onChange:e=>{return t=e.target.value,m(t),void d(t);var t},value:h})}),"wysiwyg"===c&&(0,u.jsx)(u.Fragment,{children:(0,u.jsx)(i.CKEditor,{editor:s(),config:{toolbar:["undo","redo","heading","|","bold","italic","link","bulletedList","numberedList","blockQuote","insertTable"]},data:r,onChange:(e,t)=>{const a=t.getData();d(a)}})})]})})}}]);