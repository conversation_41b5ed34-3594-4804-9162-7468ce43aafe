"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[3971],{23971:(e,l,s)=>{s.r(l),s.d(l,{default:()=>o});var c=s(27723),a=s(45111),r=s(4219),n=s(32828),d=s(86087),i=s(10790);const o=()=>{const{fields:e,getFieldValue:l}=(0,r.default)(),{showCookieBanner:s,fetchProgressData:o,progressLoaded:t}=(0,n.default)(),[p,h]=(0,d.useState)(!1),[g,u]=(0,d.useState)(!1),[m,z]=(0,d.useState)(!1);return(0,d.useEffect)(()=>{t||o()},[]),(0,d.useEffect)(()=>{let e="yes"===l("enable_cookie_blocker")?"green":"grey";h(e),e=1==l("dont_use_placeholders")?"grey":"green",u(e),e=s?"green":"grey",z(e)},[e,s]),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("a",{href:"#wizard",className:"button button-primary",children:(0,c.__)("Continue Wizard","complianz-gdpr")}),(0,i.jsxs)("div",{className:"cmplz-legend cmplz-flex-push-right",children:[(0,i.jsx)(a.default,{name:"circle-check",color:p,size:14}),(0,i.jsx)("span",{children:(0,c.__)("Cookie Blocker","complianz-gdpr")})]}),(0,i.jsxs)("div",{className:"cmplz-legend",children:[(0,i.jsx)(a.default,{name:"circle-check",color:g,size:14}),(0,i.jsx)("span",{children:(0,c.__)("Placeholders","complianz-gdpr")})]}),(0,i.jsxs)("div",{className:"cmplz-legend",children:[(0,i.jsx)(a.default,{name:"circle-check",color:m,size:14}),(0,i.jsx)("span",{children:(0,c.__)("Consent Banner","complianz-gdpr")})]})]})}}}]);