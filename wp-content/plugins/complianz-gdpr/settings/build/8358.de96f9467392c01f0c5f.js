"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[7320,8358],{7320:(e,a,l)=>{l.r(a),l.d(a,{default:()=>n});var t=l(29243),s=l(86087),d=l(10790);const n=(0,s.memo)(({label:e,id:a,value:l,onChange:s,required:n,defaultValue:r,disabled:o,options:i={}})=>(0,d.jsx)(t.bL,{disabled:o&&!Array.isArray(o),className:"cmplz-input-group cmplz-radio-group",value:l,"aria-label":e,onValueChange:s,required:n,default:r,children:Object.entries(i).map(([e,l])=>(0,d.jsxs)("div",{className:"cmplz-radio-group__item",children:[(0,d.jsx)(t.q7,{disabled:Array.isArray(o)&&o.includes(e),value:e,id:a+"_"+e,children:(0,d.jsx)(t.C1,{className:"cmplz-radio-group__indicator"})}),(0,d.jsx)("label",{className:"cmplz-radio-label",htmlFor:a+"_"+e,children:l})]},e))}))},78358:(e,a,l)=>{l.r(a),l.d(a,{default:()=>p});var t=l(40128),s=l(27723),d=l(9588),n=l(86087),r=l(22589),o=l(45111),i=l(4219),u=l(7320),c=l(10790);const p=(0,n.memo)(({id:e,value:a,options:l,defaultValue:p,disabled:m})=>{const[g,h]=(0,n.useState)(!1),[_,x]=(0,n.useState)(""),[v,j]=(0,n.useState)([]),[b,f]=(0,n.useState)(!1),[y,z]=(0,n.useState)(!1),[A,C]=(0,n.useState)(null),T=(0,n.useRef)(a),{updateField:F,setChangedField:N}=(0,i.default)(),S=l=>{let t=T.current!==a||l;if("custom"!==a||b||(T.current=a,t&&k(!1)),"url"===a&&!y){let l={};T.current=a,l.type=e,d.doAction("get_custom_legal_document_url",l).then(e=>{x(e.pageUrl),z(!0)})}};(0,n.useEffect)(()=>{l.map(e=>e.value).includes(a)||F(e,p),S(!0)},[]),(0,r.A)(()=>{S(!1)});const k=a=>{let l={};return l.type=e,l.search=a,d.doAction("get_pages_list",l).then(e=>{let a=e.pages.filter(function(a){return a.value===e.pageId});return h(a),f(!0),j(e.pages),e.pages})},w={};for(const e in l){const a=l[e];w[a.value]=a.label}return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(u.default,{id:e,options:w,value:a,onChange:a=>{F(e,a),N(e,a)},disabled:m}),"custom"===a&&!b&&(0,c.jsxs)("div",{className:"cmplz-documents-loader",children:[(0,c.jsx)("div",{children:(0,c.jsx)(o.default,{name:"loading",color:"grey"})}),(0,c.jsx)("div",{children:(0,s.__)("Loading...","complianz-gdpr")})]}),"custom"===a&&b&&(0,c.jsx)(c.Fragment,{children:(0,c.jsx)(t.A,{label:(0,s.__)("Link to custom page","complianz-gdpr"),defaultOptions:v,loadOptions:e=>new Promise(a=>{setTimeout(()=>{a(k(e))},1e3)}),menuPortalTarget:document.body,menuPosition:"fixed",placeholder:(0,s.__)("Type at least two characters","complianz-gdpr"),onChange:a=>(a=>{let l={};l.pageId=a.value,l.type=e,h(a),d.doAction("update_custom_legal_document_id",l).then(e=>{})})(a),value:g,styles:{menuPortal:e=>({...e,zIndex:9999})}})}),"url"===a&&!y&&(0,c.jsxs)("div",{className:"cmplz-documents-loader",children:[(0,c.jsx)("div",{children:(0,c.jsx)(o.default,{name:"loading",color:"grey"})}),(0,c.jsx)("div",{children:(0,s.__)("Loading...","complianz-gdpr")})]}),"url"===a&&y&&(0,c.jsx)(c.Fragment,{children:(0,c.jsx)("input",{type:"text",value:_,onChange:a=>{let l={},t=a.target.value;l.pageUrl=t,l.type=e,x(t),clearTimeout(A);const s=setTimeout(()=>{d.doAction("update_custom_legal_document_url",l).then(e=>{})},500);C(s)},placeholder:"https://domain.com/your-policy"})})]})})}}]);