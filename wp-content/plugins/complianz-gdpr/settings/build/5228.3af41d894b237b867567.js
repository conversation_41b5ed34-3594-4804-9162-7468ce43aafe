"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[5228],{25228:(e,l,s)=>{s.r(l),s.d(l,{default:()=>i});var a=s(86087),r=s(21366),c=s(45111),n=s(27723),p=s(10790);const i=(0,a.memo)(({value:e=!1,onChange:l,required:s,defaultValue:a,disabled:i,options:t={},canBeEmpty:o=!0,label:u})=>{if(Array.isArray(t)){let e={};t.map(l=>{e[l.value]=l.label}),t=e}return o?(""===e||!1===e||0===e)&&(e="0",t={0:(0,n.__)("Select an option","complianz-gdpr"),...t}):e||(e=Object.keys(t)[0]),(0,p.jsx)("div",{className:"cmplz-input-group cmplz-select-group",children:(0,p.jsxs)(r.bL,{value:e,defaultValue:a,onValueChange:l,required:s,disabled:i&&!Array.isArray(i),children:[(0,p.jsxs)(r.l9,{className:"cmplz-select-group__trigger",children:[(0,p.jsx)(r.WT,{}),(0,p.jsx)(c.default,{name:"chevron-down"})]}),(0,p.jsxs)(r.UC,{className:"cmplz-select-group__content",position:"popper",children:[(0,p.jsx)(r.PP,{className:"cmplz-select-group__scroll-button",children:(0,p.jsx)(c.default,{name:"chevron-up"})}),(0,p.jsx)(r.LM,{className:"cmplz-select-group__viewport",children:(0,p.jsx)(r.YJ,{children:Object.entries(t).map(([e,l])=>(0,p.jsx)(r.q7,{disabled:Array.isArray(i)&&i.includes(e),className:"cmplz-select-group__item",value:e,children:(0,p.jsx)(r.p4,{children:l})},e))})}),(0,p.jsx)(r.wn,{className:"cmplz-select-group__scroll-button",children:(0,p.jsx)(c.default,{name:"chevron-down"})})]})]})},u)})}}]);