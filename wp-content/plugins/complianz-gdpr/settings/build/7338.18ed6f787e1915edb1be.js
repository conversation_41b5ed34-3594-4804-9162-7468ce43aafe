"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[7338],{57338:(a,n,t)=>{t.r(n),t.d(n,{default:()=>g});var i=t(81621),l=t(9588),o=t(27723);const g=(0,i.vt)((a,n)=>({error:!1,dataLoaded:!1,pluginData:[],updatePluginData:(t,i)=>{let l=n().pluginData;l.forEach(function(a,n){a.slug===t&&(l[n]=i)}),a(a=>({dataLoaded:!0,pluginData:l}))},getPluginData:a=>n().pluginData.filter(n=>n.slug===a)[0],fetchOtherPluginsData:async()=>{const{pluginData:n,error:t}=await l.doAction("otherpluginsdata").then(a=>{let n=[];n=a.plugins;let t=a.error;return t||n.forEach(function(a,t){n[t].pluginActionNice=p(a.pluginAction)}),{pluginData:n,error:t}});a(a=>({dataLoaded:!0,pluginData:n,error:t}))},pluginActions:(a,t,i)=>{i&&i.preventDefault();let o={};o.slug=a,o.pluginAction=t;let g=n().getPluginData(a);"download"===t?g.pluginAction="downloading":"activate"===t&&(g.pluginAction="activating"),g.pluginActionNice=p(g.pluginAction),n().updatePluginData(a,g),"installed"!==t&&"upgrade-to-premium"!==t&&l.doAction("plugin_actions",o).then(t=>{g=t,n().updatePluginData(a,g),n().pluginActions(a,g.pluginAction)})}})),p=a=>({download:(0,o.__)("Install","complianz-gdpr"),activate:(0,o.__)("Activate","complianz-gdpr"),activating:(0,o.__)("Activating...","complianz-gdpr"),downloading:(0,o.__)("Downloading...","complianz-gdpr"),"upgrade-to-premium":(0,o.__)("Downloading...","complianz-gdpr")}[a])}}]);