"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[6745,8708],{16745:(t,e,s)=>{s.r(e),s.d(e,{default:()=>n});var a=s(81621),i=s(9588);const n=(0,a.vt)((t,e)=>({progress:0,total:0,start:0,next:0,active:!1,copySites:async e=>{let s={restart:e};t({active:!0});const{start:a,next:n,total:o}=await i.doAction("copy_multisite",s).then(t=>t);let r=Math.round(n/o*100);t({progress:r,start:a,next:n,total:o}),r>=100&&t({active:!1})}}))},48708:(t,e,s)=>{s.r(e),s.d(e,{default:()=>p});var a=s(16745),i=s(86087),n=s(45111),o=s(27723),r=s(56427),l=s(4219),c=s(10790);const p=(0,i.memo)(()=>{const{progress:t,active:e,start:s,next:p,total:d,copySites:u}=(0,a.default)(),[g,m]=(0,i.useState)(!1),{addHelpNotice:h,removeHelpNotice:y}=(0,l.default)();return(0,i.useEffect)(()=>{t<100?e&&((async()=>{await u(!1)})(),h("copy-multisite","warning",(0,o.__)("Complianz is currently copying settings of site %1$s to %2$s of %3$s sites.","complianz-gdpr").replace("%1$s",s).replace("%2$s",p).replace("%3$s",d),(0,o.__)("Copying settings...","complianz-gdpr"),!1)):y("copy-multisite")},[t]),(0,c.jsx)(c.Fragment,{children:(0,c.jsxs)("div",{className:"cmplz-export-container",children:[(0,c.jsx)(r.__experimentalConfirmDialog,{isOpen:g,onConfirm:async()=>{m(!1),await u(!0)},onCancel:()=>{m(!1)},children:(0,o.__)("Are you sure? This will overwrite the settings in all your subsites with the Complianz settings of this site.","complianz-gdpr")}),(0,c.jsxs)("button",{className:"button button-default",onClick:()=>m(!0),children:[(0,o.__)("Start","complianz-gdpr"),e&&(0,c.jsxs)(c.Fragment,{children:[" ",t,"%",(0,c.jsx)(n.default,{name:"loading",color:"grey"})]})]})]})})})}}]);