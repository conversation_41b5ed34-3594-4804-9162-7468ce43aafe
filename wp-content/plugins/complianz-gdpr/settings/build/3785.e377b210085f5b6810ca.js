"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[3785,4101],{43785:(e,t,n)=>{n.r(t),n.d(t,{default:()=>d});var o=n(56427),a=n(86087),i=n(4219),l=n(45111),c=n(27723),s=n(74101),p=n(52043),r=n(10790);const d=(0,a.memo)(function(){const{removeHelpNotice:e,addHelpNotice:t,fetchFieldsData:n,showSavedSettingsNotice:d}=(0,i.default)(),{selectedSubMenuItem:m}=(0,p.default)(),[u,g]=(0,a.useState)(!1),[_,f]=(0,a.useState)(!0),[h,z]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{u&&("application/json"!==u.type?(f(!0),t("import_settings","warning",(0,c.__)("You can only upload .json files","complianz-gdpr"),(0,c.__)("Incorrect extension","complianz-gdpr"),!1)):(f(!1),e("import_settings")))},[u]),(0,r.jsx)("div",{className:"cmplz-import-form",children:(0,r.jsxs)("div",{className:"cmplz-import-button-container",children:[u&&u.name,(0,r.jsx)(o.FormFileUpload,{accept:"",icon:(0,r.jsx)(l.default,{name:"upload",color:"black"}),onChange:e=>g(e.currentTarget.files[0]),children:(0,c.__)("Select file","complianz-gdpr")}),(0,r.jsxs)("button",{disabled:_,className:"button button-default",onClick:e=>(f(!0),z(!0),void(0,s.upload)("import_settings",u).then(e=>(e.data.success?n(m).then(()=>{d((0,c.__)("Settings imported","complianz-gdpr"))}):t("import_settings","warning",(0,c.__)("You can only upload .json files","complianz-gdpr"),(0,c.__)("Incorrect extension","complianz-gdpr"),!1),z(!1),g(!1),!0)).catch(e=>{console.error(e)})),children:[(0,c.__)("Import","complianz-gdpr"),h&&(0,r.jsx)(l.default,{name:"loading",color:"grey"})]})]})})})},74101:(e,t,n)=>{n.r(t),n.d(t,{upload:()=>a});var o=n(71083);const a=(e,t,n)=>{let a=new FormData;return a.append("data",t),void 0!==n&&a.append("details",JSON.stringify(n)),o.A.post(cmplz_settings.admin_url+"?page=complianz&cmplz_upload_file=1&action="+e,a,{headers:{"Content-Type":"multipart/form-data","X-WP-Nonce":cmplz_settings.nonce}})}}}]);