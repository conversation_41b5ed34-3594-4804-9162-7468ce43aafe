"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[2010,3990,5552,5553],{3990:(e,t,a)=>{a.r(t),a.d(t,{default:()=>d});var n=a(52010),l=a(27723),i=a(45111),s=a(86087),c=a(4219),o=a(99418),r=a(10790);const d=(0,s.memo)(e=>{const{updateField:t,setChangedField:a,getFieldValue:s}=(0,c.default)();return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(n.default,{summary:e.plugin.plugin_name,icon:e.icon,icons:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{className:"cmplz-button-icon",onClick:n=>((e,n,l)=>{l.preventDefault();let i=s("custom_privacy_policy_text");i+="<h1>"+e+"</h1>"+n,t("custom_privacy_policy_text",i),a("custom_privacy_policy_text",i)})(e.plugin.plugin_name,e.plugin.policy_text,n),children:(0,r.jsx)(i.default,{tooltip:(0,l.__)("Add to annex of Privacy Statement","complianz-gdpr"),name:"plus"})}),"na"!==e.plugin.consent_api&&(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("button",{className:"cmplz-button-icon",children:[!e.plugin.consent_api&&(0,r.jsx)(i.default,{tooltip:(0,l.__)("Does not conform with the Consent API","complianz-gdpr"),name:"circle",color:"red"}),e.plugin.consent_api&&(0,r.jsx)(i.default,{tooltip:(0,l.__)("Conforms to the Consent API","complianz-gdpr"),name:"circle",color:"green"})]})})]}),details:(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"cmplz-details-row",dangerouslySetInnerHTML:{__html:o.A.sanitize(e.plugin.policy_text)}})})})})})},52010:(e,t,a)=>{a.r(t),a.d(t,{default:()=>s});var n=a(45111),l=a(86087),i=a(10790);const s=e=>{const[t,a]=(0,l.useState)(!1);return(0,i.jsx)("div",{className:"cmplz-panel__list__item",style:e.style?e.style:{},children:(0,i.jsxs)("details",{open:t,children:[(0,i.jsxs)("summary",{onClick:e=>(e=>{e.preventDefault(),a(!t)})(e),children:[e.icon&&(0,i.jsx)(n.default,{name:e.icon}),(0,i.jsx)("h5",{className:"cmplz-panel__list__item__title",children:e.summary}),(0,i.jsx)("div",{className:"cmplz-panel__list__item__comment",children:e.comment}),(0,i.jsx)("div",{className:"cmplz-panel__list__item__icons",children:e.icons}),(0,i.jsx)(n.default,{name:"chevron-down",size:18})]}),(0,i.jsx)("div",{className:"cmplz-panel__list__item__details",children:t&&e.details})]})})}},75553:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});var n=a(81621),l=a(9588);const i=(0,n.vt)((e,t)=>({privacyStatementsLoaded:!1,privacyStatements:[],fetchPrivacyStatementsData:async()=>{const{privacyStatements:t}=await l.doAction("wp_privacy_policy_data").then(e=>e).catch(e=>{console.error(e)});e({privacyStatementsLoaded:!0,privacyStatements:t})}}))},95552:(e,t,a)=>{a.r(t),a.d(t,{default:()=>d});var n=a(86087),l=a(4219),i=a(75553),s=a(3990),c=a(27723),o=a(99695),r=a(10790);const d=(0,n.memo)(()=>{const[e,t]=(0,n.useState)(!0),{getFieldValue:a,fields:d}=(0,l.default)(),{privacyStatementsLoaded:m,fetchPrivacyStatementsData:p,privacyStatements:_}=(0,i.default)();return(0,n.useEffect)(()=>{let e="generated"===a("privacy-statement");t(e),!m&&e&&p()},[d]),!m&&e?(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(o.default,{lines:"3"})}):(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{children:[e&&0===_.length&&(0,r.jsx)(r.Fragment,{children:(0,c.__)("No plugins with suggested statements found.","complianz-gdpr")}),!e&&(0,r.jsx)(r.Fragment,{children:(0,c.__)("You have chosen to generate your own Privacy Statement, which means the option to add custom text to it is not applicable.","complianz-gdpr")}),e&&(0,r.jsx)("div",{className:"cmplz-panel__list",children:Array.isArray(_)&&_.map((e,t)=>(0,r.jsx)(s.default,{plugin:e,icon:"plugin"},t))})]})})})}}]);