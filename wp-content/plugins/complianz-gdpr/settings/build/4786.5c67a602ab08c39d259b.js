"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[4786],{84786:(e,s,n)=>{n.r(s),n.d(s,{default:()=>a});var o=n(10790);const a=e=>{const s=s=>{let n=!e.field.value;e.onChangeHandler(n)};let n=e.field,a=n.value?"is-checked":"",l=e.disabled?"is-disabled":"";return(0,o.jsx)("div",{className:"components-base-control components-toggle-control",children:(0,o.jsx)("div",{className:"components-base-control__field",children:(0,o.jsxs)("div",{"data-wp-component":"HStack",className:"components-flex components-h-stack",children:[(0,o.jsxs)("span",{className:"components-form-toggle "+a+" "+l,children:[(0,o.jsx)("input",{onKeyDown:e=>(e=>{"Enter"===e.key&&(e.preventDefault(),s())})(e),checked:n.value,className:"components-form-toggle__input",onChange:e=>s(),id:n.id,type:"checkbox",disabled:e.disabled}),(0,o.jsx)("span",{className:"components-form-toggle__track"}),(0,o.jsx)("span",{className:"components-form-toggle__thumb"})]}),e.label]})})})}}}]);