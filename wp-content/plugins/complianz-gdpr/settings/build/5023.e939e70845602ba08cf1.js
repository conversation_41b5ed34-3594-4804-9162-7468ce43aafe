"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[800,2921,4759,5023],{9957:(e,t,r)=>{function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{m:()=>n})},10800:(e,t,r)=>{r.r(t),r.d(t,{default:()=>C});var n=r(86087),s=r(51609),i=r(9957),c=r(91071),o=r(62133),a=r(81351),l=r(85357),d=r(54150),u=r(12579),p=r(10790),h="Switch",[f,g]=(0,o.A)(h),[m,b]=f(h),v=s.forwardRef((e,t)=>{const{__scopeSwitch:r,name:n,checked:o,defaultChecked:l,required:d,disabled:f,value:g="on",onCheckedChange:b,form:v,...y}=e,[_,S]=s.useState(null),k=(0,c.s)(t,e=>S(e)),j=s.useRef(!1),C=!_||v||!!_.closest("form"),[A,z]=(0,a.i)({prop:o,defaultProp:l??!1,onChange:b,caller:h});return(0,p.jsxs)(m,{scope:r,checked:A,disabled:f,children:[(0,p.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":A,"aria-required":d,"data-state":x(A),"data-disabled":f?"":void 0,disabled:f,value:g,...y,ref:k,onClick:(0,i.m)(e.onClick,e=>{z(e=>!e),C&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})}),C&&(0,p.jsx)(w,{control:_,bubbles:!j.current,name:n,value:g,checked:A,required:d,disabled:f,form:v,style:{transform:"translateX(-100%)"}})]})});v.displayName=h;var y="SwitchThumb",_=s.forwardRef((e,t)=>{const{__scopeSwitch:r,...n}=e,s=b(y,r);return(0,p.jsx)(u.sG.span,{"data-state":x(s.checked),"data-disabled":s.disabled?"":void 0,...n,ref:t})});_.displayName=y;var w=s.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:n=!0,...i},o)=>{const a=s.useRef(null),u=(0,c.s)(a,o),h=(0,l.Z)(r),f=(0,d.X)(t);return s.useEffect(()=>{const e=a.current;if(!e)return;const t=window.HTMLInputElement.prototype,s=Object.getOwnPropertyDescriptor(t,"checked").set;if(h!==r&&s){const t=new Event("click",{bubbles:n});s.call(e,r),e.dispatchEvent(t)}},[h,r,n]),(0,p.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...i,tabIndex:-1,ref:u,style:{...i.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}w.displayName="SwitchBubbleInput";var S=v,k=_,j=r(4219);const C=(0,n.memo)(({value:e,onChange:t,required:r,disabled:n,className:s,label:i,id:c})=>{const{getField:o}=(0,j.default)();let a=e;return"0"!==e&&"1"!==e||(a="1"===e),(0,p.jsx)("div",{className:"cmplz-input-group cmplz-switch-group",children:(0,p.jsx)(S,{className:"cmplz-switch-root "+s,checked:a,onCheckedChange:e=>{"banner"===o(c).data_target&&(e=e?"1":"0"),t(e)},disabled:n,required:r,children:(0,p.jsx)(k,{className:"cmplz-switch-thumb"})})})})},12579:(e,t,r)=>{r.d(t,{hO:()=>a,sG:()=>o});var n=r(51609),s=r(75795),i=r(33362),c=r(10790),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{const r=(0,i.TL)(`Primitive.${t}`),s=n.forwardRef((e,n)=>{const{asChild:s,...i}=e,o=s?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,c.jsx)(o,{...i,ref:n})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function a(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}},32921:(e,t,r)=>{r.r(t),r.d(t,{default:()=>c});var n=r(27723),s=r(44124),i=r(10790);const c=e=>(0,i.jsxs)(i.Fragment,{children:[" ",(0,i.jsx)(s.default,{url:e,target:"_blank",rel:"noopener noreferrer",text:(0,n.__)("For more information, please read this %sarticle%s.","complianz-gdpr")})," "]})},33362:(e,t,r)=>{r.d(t,{TL:()=>c});var n=r(51609),s=r(91071),i=r(10790);function c(e){const t=o(e),r=n.forwardRef((e,r)=>{const{children:s,...c}=e,o=n.Children.toArray(s),a=o.find(l);if(a){const e=a.props.children,s=o.map(t=>t===a?n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null:t);return(0,i.jsx)(t,{...c,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,i.jsx)(t,{...c,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}function o(e){const t=n.forwardRef((e,t)=>{const{children:r,...i}=e;if(n.isValidElement(r)){const e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}(r),c=function(e,t){const r={...t};for(const n in t){const s=e[n],i=t[n];/^on[A-Z]/.test(n)?s&&i?r[n]=(...e)=>{const t=i(...e);return s(...e),t}:s&&(r[n]=s):"style"===n?r[n]={...s,...i}:"className"===n&&(r[n]=[s,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,s.t)(t,e):e),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var a=Symbol("radix.slottable");function l(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},34759:(e,t,r)=>{r.r(t),r.d(t,{default:()=>c});var n=r(81621),s=r(16535),i=r(9588);const c=(0,n.vt)((e,t)=>({integrationsLoaded:!1,fetching:!1,services:[],plugins:[],scripts:[],placeholders:[],blockedScripts:[],setScript:(t,r)=>{e((0,s.Ay)(e=>{if("block_script"===r){let r=e.blockedScripts;if(t.urls){for(const[e,n]of Object.entries(t.urls)){if(!n||0===n.length)continue;let e=!1;for(const[t,s]of Object.entries(r))n===t&&(e=!0);e||(r[n]=n)}e.blockedScripts=r}}const n=e.scripts[r].findIndex(e=>e.id===t.id);-1!==n&&(e.scripts[r][n]=t)}))},fetchIntegrationsData:async()=>{if(t().fetching)return;e({fetching:!0});const{services:r,plugins:n,scripts:s,placeholders:i,blocked_scripts:c}=await o();let a=s;a.block_script&&a.block_script.length>0&&a.block_script.forEach((e,t)=>{e.id=t}),a.add_script&&a.add_script.length>0&&a.add_script.forEach((e,t)=>{e.id=t}),a.whitelist_script&&a.whitelist_script.length>0&&a.whitelist_script.forEach((e,t)=>{e.id=t}),e(()=>({integrationsLoaded:!0,services:r,plugins:n,scripts:a,fetching:!1,placeholders:i,blockedScripts:c}))},addScript:r=>{e({fetching:!0}),t().scripts[r]&&Array.isArray(t().scripts[r])||e((0,s.Ay)(e=>{e.scripts[r]=[]})),e((0,s.Ay)(e=>{e.scripts[r].push({name:"general",id:e.scripts[r].length,enable:!0})}));let n=t().scripts;return i.doAction("update_scripts",{scripts:n}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},saveScript:(r,n)=>{e({fetching:!0}),t().scripts[n]&&Array.isArray(t().scripts[n])||e((0,s.Ay)(e=>{e.scripts[n]=[]})),e((0,s.Ay)(e=>{const t=e.scripts[n].findIndex(e=>e.id===r.id);-1!==t&&(e.scripts[n][t]=r)}));let c=t().scripts;return i.doAction("update_scripts",{scripts:c}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},deleteScript:(r,n)=>{e({fetching:!0}),t().scripts[n]&&Array.isArray(t().scripts[n])||e((0,s.Ay)(e=>{e.scripts[n]=[]})),e((0,s.Ay)(e=>{const t=e.scripts[n].findIndex(e=>e.id===r.id);-1!==t&&e.scripts[n].splice(t,1)}));let c=t().scripts;return i.doAction("update_scripts",{scripts:c}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},updatePluginStatus:async(t,r)=>{e({fetching:!0}),e((0,s.Ay)(e=>{const n=e.plugins.findIndex(e=>e.id===t);-1!==n&&(e.plugins[n].enabled=r)}));const n=await i.doAction("update_plugin_status",{plugin:t,enabled:r}).then(e=>e).catch(e=>{console.error(e)});return e({fetching:!1}),n},updatePlaceholderStatus:async(t,r,n)=>{e({fetching:!0}),n&&e((0,s.Ay)(e=>{const n=e.plugins.findIndex(e=>e.id===t);-1!==n&&(e.plugins[n].placeholder=r?"enabled":"disabled")}));const c=await i.doAction("update_placeholder_status",{id:t,enabled:r}).then(e=>e).catch(e=>{console.error(e)});return e({fetching:!1}),c}})),o=()=>i.doAction("get_integrations_data",{}).then(e=>e).catch(e=>{console.error(e)})},54150:(e,t,r)=>{r.d(t,{X:()=>i});var n=r(51609),s=r(88200);function i(e){const[t,r]=n.useState(void 0);return(0,s.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});const t=new ResizeObserver(t=>{if(!Array.isArray(t))return;if(!t.length)return;const n=t[0];let s,i;if("borderBoxSize"in n){const e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;s=t.inlineSize,i=t.blockSize}else s=e.offsetWidth,i=e.offsetHeight;r({width:s,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},62133:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(51609),s=r(10790);function i(e,t=[]){let r=[];const i=()=>{const t=r.map(e=>n.createContext(e));return function(r){const s=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:s}}),[r,s])}};return i.scopeName=e,[function(t,i){const c=n.createContext(i),o=r.length;r=[...r,i];const a=t=>{const{scope:r,children:i,...a}=t,l=r?.[e]?.[o]||c,d=n.useMemo(()=>a,Object.values(a));return(0,s.jsx)(l.Provider,{value:d,children:i})};return a.displayName=t+"Provider",[a,function(r,s){const a=s?.[e]?.[o]||c,l=n.useContext(a);if(l)return l;if(void 0!==i)return i;throw new Error(`\`${r}\` must be used within \`${t}\``)}]},c(i,...t)]}function c(...e){const t=e[0];if(1===e.length)return t;const r=()=>{const r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){const s=r.reduce((t,{useScope:r,scopeName:n})=>({...t,...r(e)[`__scope${n}`]}),{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return r.scopeName=t.scopeName,r}},81351:(e,t,r)=>{var n;r.d(t,{i:()=>o});var s=r(51609),i=r(88200),c=(n||(n=r.t(s,2)))[" useInsertionEffect ".trim().toString()]||i.N;function o({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){const[i,o,a]=function({defaultProp:e,onChange:t}){const[r,n]=s.useState(e),i=s.useRef(r),o=s.useRef(t);return c(()=>{o.current=t},[t]),s.useEffect(()=>{i.current!==r&&(o.current?.(r),i.current=r)},[r,i]),[r,n,o]}({defaultProp:t,onChange:r}),l=void 0!==e,d=l?e:i;{const t=s.useRef(void 0!==e);s.useEffect(()=>{const e=t.current;if(e!==l){const t=e?"controlled":"uncontrolled",r=l?"controlled":"uncontrolled";console.warn(`${n} is changing from ${t} to ${r}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,n])}const u=s.useCallback(t=>{if(l){const r=function(e){return"function"==typeof e}(t)?t(e):t;r!==e&&a.current?.(r)}else o(t)},[l,e,o,a]);return[d,u]}Symbol("RADIX:SYNC_STATE")},85023:(e,t,r)=>{r.r(t),r.d(t,{default:()=>u});var n=r(34759),s=r(86087),i=r(27723),c=r(4219),o=r(32921),a=r(52043),l=r(10800),d=r(10790);const u=(0,s.memo)(()=>{const{updatePlaceholderStatus:e,integrationsLoaded:t,services:u,fetchIntegrationsData:p}=(0,n.default)(),[h,f]=(0,s.useState)([]),[g,m]=(0,s.useState)(""),[b,v]=(0,s.useState)(!1),[y,_]=(0,s.useState)(!1),[w,x]=(0,s.useState)(""),[S,k]=(0,s.useState)(""),{updateField:j,getField:C,getFieldValue:A,saveFields:z,setChangedField:N,addHelpNotice:E}=(0,c.default)(),{selectedSubMenuItem:P}=(0,a.default)(),[R,F]=(0,s.useState)(null);(0,s.useEffect)(()=>{r.e(3757).then(r.bind(r,83757)).then(({default:e})=>{F(()=>e)})},[]),(0,s.useEffect)(()=>{t||p(),t&&(1==A("safe_mode")?(x((0,i.__)("Safe Mode enabled. To manage integrations, disable Safe Mode under Tools - Support.","complianz-gdpr")),v(!0)):"yes"!==A("uses_thirdparty_services")&&"yes"!==A("uses_social_media")&&"yes"!==A("uses_ad_cookies")&&(x((0,i.__)("Third-party services and social media are marked as not being used on your website in the wizard.","complianz-gdpr")),k("#wizard/services"),v(!0)))},[t]),(0,s.useEffect)(()=>{I()},[u]);const I=()=>{let e=[...u];e.forEach(function(t,r){let n={...t},s=C(t.source);if("multicheckbox"===s.type){let e=s.value;Array.isArray(e)||(e=[]),n.enabled=e.includes(t.id)}else n.enabled="yes"===s.value;e[r]=n}),f(e);let t="yes"===A("block_recaptcha_service"),r=u.filter(e=>"google-recaptcha"===e.id)[0];t&&r&&r.enabled&&E("integrations-services","warning",(0,i.__)("reCaptcha is connected and will be blocked before consent. To change your settings, disable reCaptcha in the list.","complianz-gdpr"),(0,i.__)("reCaptcha blocking enabled","complianz-gdpr"),"#wizard/services")};(0,s.useEffect)(()=>{if(0===h.length)return;let e="yes";0===h.filter(e=>!0===e.enabled&&"thirdparty_services_on_site"===e.source).length&&(e="no"),A("uses_thirdparty_services")!==e&&(j("uses_thirdparty_services",e),N("uses_thirdparty_services",e));let t="yes";0===h.filter(e=>!0===e.enabled&&"socialmedia_on_site"===e.source).length&&(t="no"),A("uses_social_media")!==t&&(j("uses_social_media",t),N("uses_social_media",t))},[h]);const T=[{name:(0,i.__)("Service","complianz-gdpr"),selector:e=>e.label,sortable:!0,grow:5},{name:(0,i.__)("Placeholder","complianz-gdpr"),selector:e=>e.placeholderControl,sortable:!0,sortFunction:(e,t)=>{const r=e.placeholder,n=t.placeholder;return r>n?1:n>r?-1:0},grow:2},{name:(0,i.__)("Status","complianz-gdpr"),selector:e=>e.enabledControl,sortable:!0,sortFunction:(e,t)=>{const r=e.enabled,n=t.enabled;return r>n?1:n>r?-1:0},grow:1,right:!0}];let $=h.filter(e=>e.label.toLowerCase().includes(g.toLowerCase()));return $.sort((e,t)=>e.label<t.label?-1:e.label>t.label?1:0),$.forEach(t=>{let r=A(t.source);Array.isArray(r)?t.enabled=r.includes(t.id):t.enabled="yes"===r,t.enabledControl=(0,d.jsx)(l.default,{disabled:y,value:t.enabled,onChange:e=>(async(e,t)=>{_(!0);let r,n=C(e.source);if("multicheckbox"===n.type){let s=n.value;Array.isArray(s)||(s=[]),r=[...s],Array.isArray(r)||(r=[]),t?r.push(e.id):r=r.filter(t=>t!==e.id)}else r=t?"yes":"no";j(e.source,r),N(e.source,r),await z(P,!1),await p(),_(!1)})(t,e),className:"cmplz-switch-input-tiny"}),t.placeholderControl=(0,d.jsxs)(d.Fragment,{children:[" ","none"!==t.placeholder&&t.enabled&&(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(l.default,{disabled:y,value:"enabled"===t.placeholder,onChange:r=>(async(t,r)=>{_(!0);let n=[...h],s=n.findIndex(e=>e.id===t.id);n[s].placeholder=r?"enabled":"disabled",f(n),await e(t.id,r),_(!1)})(t,r),className:"cmplz-switch-input-tiny"})})]})}),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("p",{children:[(0,i.__)("Enabled services will be blocked on the front-end of your website until the user has given consent (opt-in), or after the user has revoked consent (opt-out). When possible a placeholder is activated. You can also disable or configure the placeholder to your liking.","complianz-gdpr"),(0,o.default)("https://complianz.io/blocking-recaptcha-manually/")]}),(0,d.jsx)("div",{className:"cmplz-table-header",children:(0,d.jsx)("div",{className:"cmplz-table-header-controls",children:(0,d.jsx)("input",{type:"text",placeholder:(0,i.__)("Search","complianz-gdpr"),value:g,onChange:e=>m(e.target.value)})})}),(b||0===$.length)&&(0,d.jsx)(d.Fragment,{children:(0,d.jsx)("div",{className:"cmplz-settings-overlay",children:(0,d.jsxs)("div",{className:"cmplz-settings-overlay-message",children:[w,S&&(0,d.jsxs)(d.Fragment,{children:[" ",(0,d.jsx)("a",{href:S,children:(0,i.__)("View services","complianz-gdpr")})]})]})})}),0===$.length&&(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("div",{className:"cmplz-integrations-placeholder",children:[(0,d.jsx)("div",{}),(0,d.jsx)("div",{}),(0,d.jsx)("div",{}),(0,d.jsx)("div",{}),(0,d.jsx)("div",{}),(0,d.jsx)("div",{})]})}),!b&&$.length>0&&R&&(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(R,{columns:T,data:$,dense:!0,pagination:!0,paginationPerPage:5,noDataComponent:(0,d.jsx)("div",{className:"cmplz-no-documents",children:(0,i.__)("No services","complianz-gdpr")}),persistTableHead:!0,theme:"really-simple-plugins",customStyles:{headCells:{style:{paddingLeft:"0",paddingRight:"0"}},cells:{style:{paddingLeft:"0",paddingRight:"0"}}}})})]})})},85357:(e,t,r)=>{r.d(t,{Z:()=>s});var n=r(51609);function s(e){const t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},88200:(e,t,r)=>{r.d(t,{N:()=>s});var n=r(51609),s=globalThis?.document?n.useLayoutEffect:()=>{}},91071:(e,t,r)=>{r.d(t,{s:()=>c,t:()=>i});var n=r(51609);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1;const n=e.map(e=>{const n=s(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){const r=n[t];"function"==typeof r?r():s(e[t],null)}}}}function c(...e){return n.useCallback(i(...e),e)}}}]);