"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[622,4759,5683,6644,8084,8895,9406],{34759:(e,t,s)=>{s.r(t),s.d(t,{default:()=>r});var a=s(81621),i=s(16535),n=s(9588);const r=(0,a.vt)((e,t)=>({integrationsLoaded:!1,fetching:!1,services:[],plugins:[],scripts:[],placeholders:[],blockedScripts:[],setScript:(t,s)=>{e((0,i.Ay)(e=>{if("block_script"===s){let s=e.blockedScripts;if(t.urls){for(const[e,a]of Object.entries(t.urls)){if(!a||0===a.length)continue;let e=!1;for(const[t,i]of Object.entries(s))a===t&&(e=!0);e||(s[a]=a)}e.blockedScripts=s}}const a=e.scripts[s].findIndex(e=>e.id===t.id);-1!==a&&(e.scripts[s][a]=t)}))},fetchIntegrationsData:async()=>{if(t().fetching)return;e({fetching:!0});const{services:s,plugins:a,scripts:i,placeholders:n,blocked_scripts:r}=await o();let c=i;c.block_script&&c.block_script.length>0&&c.block_script.forEach((e,t)=>{e.id=t}),c.add_script&&c.add_script.length>0&&c.add_script.forEach((e,t)=>{e.id=t}),c.whitelist_script&&c.whitelist_script.length>0&&c.whitelist_script.forEach((e,t)=>{e.id=t}),e(()=>({integrationsLoaded:!0,services:s,plugins:a,scripts:c,fetching:!1,placeholders:n,blockedScripts:r}))},addScript:s=>{e({fetching:!0}),t().scripts[s]&&Array.isArray(t().scripts[s])||e((0,i.Ay)(e=>{e.scripts[s]=[]})),e((0,i.Ay)(e=>{e.scripts[s].push({name:"general",id:e.scripts[s].length,enable:!0})}));let a=t().scripts;return n.doAction("update_scripts",{scripts:a}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},saveScript:(s,a)=>{e({fetching:!0}),t().scripts[a]&&Array.isArray(t().scripts[a])||e((0,i.Ay)(e=>{e.scripts[a]=[]})),e((0,i.Ay)(e=>{const t=e.scripts[a].findIndex(e=>e.id===s.id);-1!==t&&(e.scripts[a][t]=s)}));let r=t().scripts;return n.doAction("update_scripts",{scripts:r}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},deleteScript:(s,a)=>{e({fetching:!0}),t().scripts[a]&&Array.isArray(t().scripts[a])||e((0,i.Ay)(e=>{e.scripts[a]=[]})),e((0,i.Ay)(e=>{const t=e.scripts[a].findIndex(e=>e.id===s.id);-1!==t&&e.scripts[a].splice(t,1)}));let r=t().scripts;return n.doAction("update_scripts",{scripts:r}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},updatePluginStatus:async(t,s)=>{e({fetching:!0}),e((0,i.Ay)(e=>{const a=e.plugins.findIndex(e=>e.id===t);-1!==a&&(e.plugins[a].enabled=s)}));const a=await n.doAction("update_plugin_status",{plugin:t,enabled:s}).then(e=>e).catch(e=>{console.error(e)});return e({fetching:!1}),a},updatePlaceholderStatus:async(t,s,a)=>{e({fetching:!0}),a&&e((0,i.Ay)(e=>{const a=e.plugins.findIndex(e=>e.id===t);-1!==a&&(e.plugins[a].placeholder=s?"enabled":"disabled")}));const r=await n.doAction("update_placeholder_status",{id:t,enabled:s}).then(e=>e).catch(e=>{console.error(e)});return e({fetching:!1}),r}})),o=()=>n.doAction("get_integrations_data",{}).then(e=>e).catch(e=>{console.error(e)})},35683:(e,t,s)=>{s.r(t),s.d(t,{default:()=>n});var a=s(81621),i=s(9588);const n=(0,a.vt)((e,t)=>({licenseStatus:cmplz_settings.licenseStatus,processing:!1,licenseNotices:[],noticesLoaded:!1,getLicenseNotices:async()=>{const{licenseStatus:t,notices:s}=await i.doAction("license_notices",{}).then(e=>e);e(e=>({noticesLoaded:!0,licenseNotices:s,licenseStatus:t}))},activateLicense:async t=>{let s={};s.license=t,e({processing:!0});const{licenseStatus:a,notices:n}=await i.doAction("activate_license",s);e(e=>({processing:!1,licenseNotices:n,licenseStatus:a}))},deactivateLicense:async()=>{e({processing:!0});const{licenseStatus:t,notices:s}=await i.doAction("deactivate_license");e(e=>({processing:!1,licenseNotices:s,licenseStatus:t}))}}))},56644:(e,t,s)=>{s.r(t),s.d(t,{default:()=>p});var a=s(4219),i=s(86087),n=s(27723),r=s(90622),o=s(98084),c=s(69406),l=s(10790);const d=e=>(0,l.jsx)("div",{className:"cmplz-plusone",children:e.count}),p=()=>{const{fields:e,getFieldValue:t}=(0,a.default)(),[s,p]=(0,i.useState)(!1),[g,u]=(0,i.useState)(!1),{recordsLoaded:f,fetchData:h,totalOpen:m}=(0,r.default)();(0,i.useEffect)(()=>{f||h(10,1,"ID","ASC")},[f]),(0,i.useEffect)(()=>{let e=1==t("a_b_testing");p(e);let s=1==t("a_b_testing_buttons");u(s)},[e]);const b=[{title:(0,n.__)("Data Requests","complianz-gdpr"),viewLink:"#tools/data-requests",enableLink:"#wizard/security-consent",field:{name:"datarequest",value:"yes"},link:"https://complianz.io/definition/what-is-a-data-request/",plusone:(0,l.jsx)(d,{count:m})},{title:(0,n.__)("Records of Consent","complianz-gdpr"),viewLink:"#tools/records-of-consent",enableLink:"#wizard/security-consent",field:{name:"records_of_consent",value:"yes"},link:"https://complianz.io/records-of-consent/"},{title:(0,n.__)("Processing Agreements","complianz-gdpr"),viewLink:"#tools/processing-agreements",link:"https://complianz.io/definition/what-is-a-processing-agreement/"},{title:(0,n.__)("Consent Statistics","complianz-gdpr"),viewLink:"#tools/ab-testing",link:"https://complianz.io/a-quick-introduction-to-a-b-testing/"},{title:(0,n.__)("A/B Testing","complianz-gdpr"),viewLink:"#tools/ab-testing",link:"https://complianz.io/a-quick-introduction-to-a-b-testing/"},{title:(0,n.__)("Documentation","complianz-gdpr"),link:"https://complianz.io/support/"},{title:(0,n.__)("Premium Support","complianz-gdpr"),viewLink:"#tools/support",link:"https://complianz.io/about-premium-support/"},{title:"WooCommerce",plugin:"woocommerce",link:cmplz_settings.admin_url+"admin.php?page=wc-settings&tab=account"},{title:(0,n.__)("Security","complianz-gdpr"),link:"#tools/security",viewLink:"#tools/security"}];let _=cmplz_settings.is_multisite_plugin?"#tools/tools-multisite":"https://complianz.io/complianz-for-wordpress-multisite-installations/";return cmplz_settings.is_multisite&&b.push({title:(0,n.__)("Multisite","complianz-gdpr"),link:_,viewLink:_}),s?(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(o.default,{abTestingEnabled:g})}):(0,l.jsx)(l.Fragment,{children:b.map((e,t)=>(0,l.jsx)(c.default,{item:e},t))})}},69406:(e,t,s)=>{s.r(t),s.d(t,{default:()=>d});var a=s(4219),i=s(34759),n=s(45111),r=s(86087),o=s(27723),c=s(35683),l=s(10790);const d=e=>{const{fields:t,getFieldValue:s}=(0,a.default)(),[d,p]=(0,r.useState)(!1),{integrationsLoaded:g,plugins:u,fetchIntegrationsData:f}=(0,i.default)(),{licenseStatus:h}=(0,c.default)();(0,r.useEffect)(()=>{let t=e.item;if(t.field){let e=s(t.field.name)==t.field.value;p(e)}},[t]),(0,r.useEffect)(()=>{g||f()},[]);let m=e.item;if(m.plugin)return u.filter(e=>e.id===m.plugin).length>0?(0,l.jsxs)("div",{className:"cmplz-tool",children:[(0,l.jsx)("div",{className:"cmplz-tool-title",children:m.title}),(0,l.jsx)("div",{className:"cmplz-tool-link",children:(0,l.jsx)("a",{href:m.link,target:"_blank",rel:"noopener noreferrer",children:(0,l.jsx)(n.default,{name:"circle-chevron-right",color:"black",size:14})})})]}):null;let b=cmplz_settings.is_premium&&"valid"===h,_=((0,o.__)("Read more","complianz-gdpr"),m.link);b&&(!d&&m.enableLink&&(_=m.enableLink),m.field&&!d||!m.viewLink||(_=m.viewLink));let k=-1!==_.indexOf("https://"),y=k?"_blank":"_self",v=k?"external-link":"circle-chevron-right";return(0,l.jsxs)("div",{className:"cmplz-tool",children:[(0,l.jsxs)("div",{className:"cmplz-tool-title",children:[m.title,m.plusone&&m.plusone]}),(0,l.jsx)("div",{className:"cmplz-tool-link",children:(0,l.jsx)("a",{href:_,target:y,rel:k?"noopener noreferrer":"",children:(0,l.jsx)(n.default,{name:v,color:"black",size:14})})})]})}},88895:(e,t,s)=>{s.r(t),s.d(t,{default:()=>o});var a=s(81621),i=s(9588);const n={optin:{labels:["Functional","Statistics","Marketing","Do Not Track","No Choice","No Warning"],categories:["functional","statistics","marketing","do_not_track","no_choice","no_warning"],datasets:[{data:["0","0","0","0","0","0"],backgroundColor:"rgba(46, 138, 55, 1)",borderColor:"rgba(46, 138, 55, 1)",label:"A (default)",fill:"false",borderDash:[0,0]},{data:["0","0","0","0","0","0"],backgroundColor:"rgba(244, 191, 62, 1)",borderColor:"rgba(244, 191, 62, 1)",label:"B",fill:"false",borderDash:[0,0]}],max:5},optout:{labels:["Functional","Statistics","Marketing","Do Not Track","No Choice","No Warning"],categories:["functional","statistics","marketing","do_not_track","no_choice","no_warning"],datasets:[{data:["0","0","0","0","0","0"],backgroundColor:"rgba(46, 138, 55, 1)",borderColor:"rgba(46, 138, 55, 1)",label:"A (default)",fill:"false",borderDash:[0,0]},{data:["0","0","0","0","0","0"],backgroundColor:"rgba(244, 191, 62, 1)",borderColor:"rgba(244, 191, 62, 1)",label:"B",fill:"false",borderDash:[0,0]}],max:5}},r={optin:{labels:["Functional","Statistics","Marketing","Do Not Track","No Choice","No Warning"],categories:["functional","statistics","marketing","do_not_track","no_choice","no_warning"],datasets:[{data:["29","747","174","292","30","10"],backgroundColor:"rgba(46, 138, 55, 1)",borderColor:"rgba(46, 138, 55, 1)",label:"Demo A (default)",fill:"false",borderDash:[0,0]},{data:["3","536","240","389","45","32"],backgroundColor:"rgba(244, 191, 62, 1)",borderColor:"rgba(244, 191, 62, 1)",label:"Demo B",fill:"false",borderDash:[0,0]}],max:5},optout:{labels:["Functional","Statistics","Marketing","Do Not Track","No Choice","No Warning"],categories:["functional","statistics","marketing","do_not_track","no_choice","no_warning"],datasets:[{data:["29","747","174","292","30","10"],backgroundColor:"rgba(46, 138, 55, 1)",borderColor:"rgba(46, 138, 55, 1)",label:"A (default)",fill:"false",borderDash:[0,0]},{data:["3","536","240","389","45","32"],backgroundColor:"rgba(244, 191, 62, 1)",borderColor:"rgba(244, 191, 62, 1)",label:"Demo B",fill:"false",borderDash:[0,0]}],max:5}},o=(0,a.vt)((e,t)=>({consentType:"optin",setConsentType:t=>{e({consentType:t})},statisticsLoading:!1,consentTypes:[],regions:[],defaultConsentType:"optin",loaded:!1,statisticsData:n,emptyStatisticsData:n,bestPerformerEnabled:!1,daysLeft:"",abTrackingCompleted:!1,labels:[],setLabels:t=>{e({labels:t})},fetchStatisticsData:async()=>{if(!cmplz_settings.is_premium)return void e({saving:!1,loaded:!0,consentType:"optin",consentTypes:["optin","optout"],statisticsData:r,defaultConsentType:"optin",bestPerformerEnabled:!1,regions:"eu",daysLeft:11,abTrackingCompleted:!1});if(e({saving:!0}),t().loaded)return;const{daysLeft:s,abTrackingCompleted:a,consentTypes:n,statisticsData:o,defaultConsentType:c,regions:l,bestPerformerEnabled:d}=await i.doAction("get_statistics_data",{}).then(e=>e).catch(e=>{console.error(e)});e({saving:!1,loaded:!0,consentType:c,consentTypes:n,statisticsData:o,defaultConsentType:c,bestPerformerEnabled:d,regions:l,daysLeft:s,abTrackingCompleted:a})}}))},90622:(e,t,s)=>{s.r(t),s.d(t,{default:()=>r});var a=s(81621),i=s(9588),n=s(16535);s(86087);const r=(0,a.vt)((e,t)=>({recordsLoaded:!1,searchValue:"",setSearchValue:t=>e({searchValue:t}),status:"open",setStatus:t=>e({status:t}),selectedRecords:[],setSelectedRecords:t=>e({selectedRecords:t}),fetching:!1,generating:!1,progress:!1,records:[],totalRecords:0,totalOpen:0,exportLink:"",noData:!1,indeterminate:!1,setIndeterminate:t=>e({indeterminate:t}),paginationPerPage:10,pagination:{currentPage:1},setPagination:t=>e({pagination:t}),orderBy:"ID",setOrderBy:t=>e({orderBy:t}),order:"DESC",setOrder:t=>e({order:t}),deleteRecords:async s=>{let a={};a.per_page=t().paginationPerPage,a.page=t().pagination.currentPage,a.order=t().order.toUpperCase(),a.orderBy=t().orderBy,a.search=t().searchValue,a.status=t().status;let n=t().records.filter(e=>s.includes(e.ID));e(e=>({records:e.records.filter(e=>!s.includes(e.ID))})),a.records=n,await i.doAction("delete_datarequests",a).then(e=>e).catch(e=>{console.error(e)}),await t().fetchData(),t().setSelectedRecords([]),t().setIndeterminate(!1)},resolveRecords:async s=>{let a={};a.per_page=t().paginationPerPage,a.page=t().pagination.currentPage,a.order=t().order.toUpperCase(),a.orderBy=t().orderBy,a.search=t().searchValue,a.status=t().status,e((0,n.Ay)(e=>{e.records.forEach(function(t,a){s.includes(t.ID)&&(e.records[a].resolved=!0)})})),a.records=t().records.filter(e=>s.includes(e.ID)),await i.doAction("resolve_datarequests",a).then(e=>e).catch(e=>{console.error(e)}),await t().fetchData(),t().setSelectedRecords([]),t().setIndeterminate(!1)},fetchData:async()=>{if(t().fetching)return;e({fetching:!0});let s={};s.per_page=t().paginationPerPage,s.page=t().pagination.currentPage,s.order=t().order.toUpperCase(),s.orderBy=t().orderBy,s.search=t().searchValue,s.status=t().status;const{records:a,totalRecords:n,totalOpen:r}=await i.doAction("get_datarequests",s).then(e=>e).catch(e=>{console.error(e)});e(()=>({recordsLoaded:!0,records:a,totalRecords:n,totalOpen:r,fetching:!1}))},startExport:async()=>{e({generating:!0,progress:0,exportLink:""})},fetchExportDatarequestsProgress:async(t,s,a)=>{(t=void 0!==t&&t)||e({generating:!0});let n={};n.startDate=s,n.endDate=a,n.statusOnly=t;const{progress:r,exportLink:o,noData:c}=await i.doAction("export_datarequests",n).then(e=>e).catch(e=>{console.error(e)});let l=!1;r<100&&(l=!0),e({progress:r,exportLink:o,generating:l,noData:c})}}))},98084:(e,t,s)=>{s.r(t),s.d(t,{default:()=>c});var a=s(45111),i=s(27723),n=s(88895),r=s(86087),o=s(10790);const c=()=>{const[e,t]=(0,r.useState)(!1),[s,c]=(0,r.useState)(1),[l,d]=(0,r.useState)(0),[p,g]=(0,r.useState)(0),{consentType:u,statisticsData:f,loaded:h,fetchStatisticsData:m,labels:b,setLabels:_}=(0,n.default)();(0,r.useEffect)(()=>{!h&&cmplz_settings.is_premium&&m()},[]),(0,r.useEffect)(()=>{if(""===u||!h)return;if(!f||!f.hasOwnProperty(u))return;let e=[...f[u].labels],t=f[u].categories;t="optin"===u?t.filter(e=>"functional"===e||"no_warning"===e||"do_not_track"===e):t.filter(e=>"functional"===e||"marketing"===e||"statistics"===e||"preferences"===e);let s=t.map(e=>f[u].categories.indexOf(e));for(let t=s.length-1;t>=0;t--)e.splice(s[t],1);_(e)},[h,u]),(0,r.useEffect)(()=>{if(""===u||!h||!f)return;let e=f[u].datasets.filter(e=>e.default);if(e.length>0){let s=e[0].data,a=s.reduce((e,t)=>parseInt(e)+parseInt(t),0);a=a>0?a:1,c(a),d(e[0].full_consent),g(e[0].no_consent),s=s.slice(2),t(s)}},[h,u]);const k=e=>{let t="dial-med-low-light";return 1===e?t="dial-med-light":2===e?t="dial-light":3===e?t="dial-off-light":4===e&&(t="dial-min-light"),(0,o.jsx)(o.Fragment,{children:(0,o.jsx)(a.default,{name:t,color:"black"})})};return(0,o.jsxs)("div",{className:"cmplz-statistics",children:[(0,o.jsxs)("div",{className:"cmplz-statistics-select",children:[(0,o.jsxs)("div",{className:"cmplz-statistics-select-item",children:[(0,o.jsx)(a.default,{name:"dial-max-light",color:"green",size:"22"}),(0,o.jsx)("h2",{children:l}),(0,o.jsx)("span",{children:(0,i.__)("Full Consent","complianz-gdpr")})]}),(0,o.jsxs)("div",{className:"cmplz-statistics-select-item",children:[(0,o.jsx)(a.default,{name:"dial-min-light",color:"red",size:"22"}),(0,o.jsx)("h2",{children:p}),(0,o.jsx)("span",{children:(0,i.__)("No Consent","complianz-gdpr")})]})]}),(0,o.jsx)("div",{className:"cmplz-statistics-list",children:b.length>0&&b.map((t,a)=>{return(0,o.jsxs)("div",{className:"cmplz-statistics-list-item",children:[k(a),(0,o.jsx)("p",{className:"cmplz-statistics-list-item-text",children:t}),(0,o.jsxs)("p",{className:"cmplz-statistics-list-item-number",children:[e.hasOwnProperty(a)?(i=e[a],i=parseInt(i),Math.round(i/s*100)):0,"%"]})]},a);var i})})]})}}}]);