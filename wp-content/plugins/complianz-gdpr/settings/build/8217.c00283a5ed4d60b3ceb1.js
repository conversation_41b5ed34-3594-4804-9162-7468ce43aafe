"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[5228,8217],{25228:(e,l,t)=>{t.r(l),t.d(l,{default:()=>c});var a=t(86087),n=t(21366),s=t(45111),o=t(27723),r=t(10790);const c=(0,a.memo)(({value:e=!1,onChange:l,required:t,defaultValue:a,disabled:c,options:i={},canBeEmpty:d=!0,label:u})=>{if(Array.isArray(i)){let e={};i.map(l=>{e[l.value]=l.label}),i=e}return d?(""===e||!1===e||0===e)&&(e="0",i={0:(0,o.__)("Select an option","complianz-gdpr"),...i}):e||(e=Object.keys(i)[0]),(0,r.jsx)("div",{className:"cmplz-input-group cmplz-select-group",children:(0,r.jsxs)(n.bL,{value:e,defaultValue:a,onValueChange:l,required:t,disabled:c&&!Array.isArray(c),children:[(0,r.jsxs)(n.l9,{className:"cmplz-select-group__trigger",children:[(0,r.jsx)(n.WT,{}),(0,r.jsx)(s.default,{name:"chevron-down"})]}),(0,r.jsxs)(n.UC,{className:"cmplz-select-group__content",position:"popper",children:[(0,r.jsx)(n.PP,{className:"cmplz-select-group__scroll-button",children:(0,r.jsx)(s.default,{name:"chevron-up"})}),(0,r.jsx)(n.LM,{className:"cmplz-select-group__viewport",children:(0,r.jsx)(n.YJ,{children:Object.entries(i).map(([e,l])=>(0,r.jsx)(n.q7,{disabled:Array.isArray(c)&&c.includes(e),className:"cmplz-select-group__item",value:e,children:(0,r.jsx)(n.p4,{children:l})},e))})}),(0,r.jsx)(n.wn,{className:"cmplz-select-group__scroll-button",children:(0,r.jsx)(s.default,{name:"chevron-down"})})]})]})},u)})},98217:(e,l,t)=>{t.r(l),t.d(l,{default:()=>c});var a=t(45111),n=t(27723),s=t(86087),o=t(25228),r=t(10790);const c=e=>{const[l,t]=(0,s.useState)(!1),[c,i]=(0,s.useState)(!1),[d,u]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let l=e.options;if(0===l.length){let t={label:e.name,value:0};l.unshift(t)}else if(!l.filter(e=>0===e.value).length>0){let t={label:e.name,value:0};l.unshift(t)}u(l)},[e.options]);const p=()=>{if(c||!l||0===l)return;i(!0);let e=new XMLHttpRequest;e.responseType="blob",e.open("get",l,!0),e.send(),e.onreadystatechange=function(){if(4==this.readyState&&200==this.status){var e=window.URL.createObjectURL(this.response),t=window.document.createElement("a");t.setAttribute("href",e),t.setAttribute("download",d.filter(e=>e.value===l)[0].label),window.document.body.appendChild(t),t.click(),setTimeout(function(){window.URL.revokeObjectURL(e)},6e4)}},e.onprogress=function(e){i(!0)}};return(0,r.jsxs)("div",{className:"cmplz-single-document-other-documents",children:[(0,r.jsx)(o.default,{onChange:e=>t(e),defaultValue:"0",canBeEmpty:!1,value:l,options:d}),(0,r.jsx)("div",{onClick:()=>p(),children:(0,r.jsx)(a.default,{name:"file-download",color:0==l||c?"grey":"black",tooltip:(0,n.__)("Download file","complianz-gdpr"),size:14})}),d.length>0&&(0,r.jsx)("a",{href:e.link,children:(0,r.jsx)(a.default,{name:"circle-chevron-right",color:"black",tooltip:(0,n.__)("Go to overview","complianz-gdpr"),size:14})}),0===d.length&&(0,r.jsx)("a",{href:e.link,children:(0,r.jsx)(a.default,{name:"plus",color:"black",tooltip:(0,n.__)("Create new","complianz-gdpr"),size:14})})]})}}}]);