"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[4604,8198],{74604:(t,e,s)=>{s.r(e),s.d(e,{default:()=>i});var a=s(86087),n=s(88198),c=s(27723),o=s(10790);const i=(0,a.memo)(()=>{const{loaded:t,wscStatus:e,tokenStatus:s,getStatus:i,startOnboarding:r,resetWsc:l,enableWsc:d,disableWsc:u}=(0,n.default)();(0,a.useEffect)(()=>{i()},[]);const b=(0,a.useCallback)(()=>{if(s)switch(s){case"enabled":l();break;case"disabled":case"pending":r()}},[s]),w=(0,a.useCallback)(()=>{if(e)switch(e){case"enabled":u();break;case"disabled":d()}},[e]);let g="enabled"===e?(0,c.__)("Disable Website Scan","complianz-gdpr"):(0,c.__)("Enable Website Scan","complianz-gdpr"),p="enabled"===e?"button-secondary":"button-primary",_="enabled"===s?(0,c.__)("Reset Website Scan","complianz-gdpr"):(0,c.__)("Activate Website Scan","complianz-gdpr"),m="enabled"===s?"button-danger":"button-primary",h="pending"===s;return(0,o.jsx)("div",{className:"cmplz-wsc_actions-container",children:(0,o.jsx)("div",{className:"cmplz-wsc_actions-row",children:(0,o.jsxs)("div",{className:"cmplz-wsc_actions-buttons",children:["disabled"!==s&&"pending"!==e&&(0,o.jsx)("button",{className:`button ${p}`,onClick:w,children:g}),(0,o.jsx)("button",{disabled:h,className:`button ${m}`,onClick:b,children:_})]})})})})},88198:(t,e,s)=>{s.r(e),s.d(e,{default:()=>o});var a=s(9588),n=s(81621);const c={loaded:!1,tokenStatus:"",wscStatus:"",wscSignupDate:"",syncing:!1},o=(0,n.vt)((t,e)=>({...c,startOnboarding:async()=>{const t=new URL(cmplz_settings.dashboard_url);t.searchParams.set("websitescan",""),setTimeout(()=>{window.location.href=t.href},500)},getStatus:async()=>{if(!e().getStatusCalled)try{let e={};const{wsc_status:s,token_status:n,wsc_signup_date:c}=await a.doAction("get_wsc_status",e).then(t=>t);t({tokenStatus:n,wscStatus:s,wscSignupDate:c,loaded:!0})}catch(t){console.error("Getting status error: ",t)}},resetWsc:async()=>{if(confirm("Are you sure? This will delete all your Website Scan data."))try{const{result:e,redirect:s}=await a.doAction("reset_wsc").then(t=>t);e&&(t(t=>({...c,startOnboarding:t.startOnboarding,getStatus:t.getStatus,enableWsc:t.enableWsc,disableWsc:t.disableWsc,resetWsc:t.resetWsc})),setTimeout(()=>{window.location.reload()},500))}catch(t){console.error("Resetting WSC error: ",t)}finally{setTimeout(()=>{window.location.reload()},300)}},enableWsc:async()=>{try{const{updated:e,wsc_status:s,token_status:n}=await a.doAction("enable_wsc").then(t=>t);t({updated:e,tokenStatus:n,wscStatus:s,loaded:!0})}catch(t){console.error("Enabling WSC error: ",t)}},disableWsc:async()=>{try{const{updated:e,wsc_status:s,token_status:n}=await a.doAction("disable_wsc").then(t=>t);t({updated:e,tokenStatus:n,wscStatus:s,loaded:!0})}catch(t){console.error("Disabling WSC error: ",t)}},requestActivationEmail:async()=>{try{await a.doAction("request_activation_email"),e().getStatus()}catch(t){console.error("Requesting activation email error: ",t)}}}))}}]);