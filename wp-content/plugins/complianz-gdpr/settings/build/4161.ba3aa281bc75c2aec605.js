"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[4161,9091,9758],{79758:(e,t,a)=>{a.r(t),a.d(t,{default:()=>u});var n=a(86087),o=a(9588),s=a(4219),i=a(52043),l=a(56427),r=a(99091),d=a(32828),c=a(10790);const u=(0,n.memo)(({type:e="action",style:t="tertiary",label:a,onClick:u,href:m="",target:f="",disabled:g,action:p,field:b,children:h})=>{if(!a&&!h)return null;const x=(b&&b.button_text?b.button_text:a)||h,{fetchFieldsData:C,showSavedSettingsNotice:w}=(0,s.default)(),{setInitialLoadCompleted:_,setProgress:k}=(0,r.UseCookieScanData)(),{setProgressLoaded:y}=(0,d.default)(),{selectedSubMenuItem:L}=(0,i.default)(),[v,S]=(0,n.useState)(!1),j=`button cmplz-button button--${t} button-${e}`,D=async e=>{await o.doAction(b.action,{}).then(e=>{e.success&&(C(L),"reset_settings"===e.id&&(_(!1),k(0),y(!1)),w(e.message))})},I=b&&b.warn?b.warn:"";return"action"===e?(0,c.jsxs)(c.Fragment,{children:[l.__experimentalConfirmDialog&&(0,c.jsx)(l.__experimentalConfirmDialog,{isOpen:v,onConfirm:async()=>{S(!1),await D()},onCancel:()=>{S(!1)},children:I}),(0,c.jsx)("button",{className:j,onClick:async t=>{if("action"!==e||!u)return"action"===e&&p?l.__experimentalConfirmDialog?void(b&&b.warn?S(!0):await D()):void await D():void(window.location.href=b.url);u(t)},disabled:g,children:x})]}):"link"===e?(0,c.jsx)("a",{className:j,href:m,target:f,children:x}):void 0})},84161:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var n=a(79758),o=a(9588),s=a(4219),i=a(52043),l=a(86087),r=a(56427),d=a(10790);const c=({label:e,field:t,disabled:a})=>{const{fetchFieldsData:c,showSavedSettingsNotice:u}=(0,s.default)(),{selectedSubMenuItem:m}=(0,i.default)(),[f,g]=(0,l.useState)(!1);let p=t.button_text?t.button_text:t.label;if(t.action){const e=async e=>{t.warn?g(!0):await l()},s=async()=>{g(!1),await l()},i=()=>{g(!1)},l=async e=>{await o.doAction(t.action,{}).then(e=>{e.success&&(c(m),u(e.message))})};return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(n.default,{text:p,style:"secondary",disabled:a,onClick:t=>e(t)}),(0,d.jsx)(r.__experimentalConfirmDialog,{isOpen:f,onConfirm:s,onCancel:i,children:t.warn})]})}return(0,d.jsx)(n.default,{style:"secondary",label:p,disabled:a,href:t.url})}},99091:(e,t,a)=>{a.r(t),a.d(t,{UseCookieScanData:()=>s});var n=a(81621),o=a(9588);const s=(0,n.vt)((e,t)=>({initialLoadCompleted:!1,setInitialLoadCompleted:t=>e({initialLoadCompleted:t}),iframeLoaded:!1,loading:!1,nextPage:!1,progress:0,cookies:[],lastLoadedIframe:"",setIframeLoaded:t=>e({iframeLoaded:t}),setLastLoadedIframe:t=>e(e=>({lastLoadedIframe:t})),setProgress:t=>e({progress:t}),fetchProgress:()=>(e({loading:!0}),o.doAction("get_scan_progress",{}).then(t=>(e({initialLoadCompleted:!0,loading:!1,nextPage:t.next_page,progress:t.progress,cookies:t.cookies}),t)))}))}}]);