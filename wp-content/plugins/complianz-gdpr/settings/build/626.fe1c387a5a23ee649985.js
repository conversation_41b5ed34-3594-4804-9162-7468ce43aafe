"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[626,756,2010,9091],{10626:(e,a,s)=>{s.r(a),s.d(a,{default:()=>g});var t=s(86087),n=s(9588),o=s(99091),c=s(27723),i=s(52010),l=s(45111),r=s(15139),d=s(52043),m=s(4219),p=s(80756),u=s(32828),_=s(10790);const g=(0,t.memo)(()=>{const{setSyncProgress:e,fetchSyncProgressData:a}=(0,r.default)(),{initialLoadCompleted:s,loading:g,nextPage:h,progress:f,setProgress:b,cookies:z,fetchProgress:v,lastLoadedIframe:w,setLastLoadedIframe:k}=(0,o.UseCookieScanData)(),[x,j]=(0,t.useState)(!1),{addHelpNotice:y,fieldsLoaded:N}=(0,m.default)(),{selectedSubMenuItem:C}=(0,d.default)(),{setProgressLoaded:L}=(0,u.default)(),[P,S]=(0,t.useState)(!1);(0,t.useEffect)(()=>{w!==h&&(x||(j(!0),I()))},[h,w,x]),(0,t.useEffect)(()=>{!x&&!g&&f<100&&v()},[x,g,f]),(0,t.useEffect)(()=>{N&&(void 0===window.canRunAds&&y("cookie_scan","warning",(0,c.__)("You are using an ad blocker. This will prevent most cookies from being placed. Please run the scan without an adblocker enabled.","complianz-gdpr"),(0,c.__)("Ad Blocker detected.","complianz-gdpr"),null),T()&&y("cookie_scan","warning",(0,c.__)("Your browser has the Do Not Track or Global Privacy Control setting enabled.","complianz-gdpr")+"&nbsp;"+(0,c.__)("This will prevent most cookies from being placed.","complianz-gdpr")+"&nbsp;"+(0,c.__)("Please run the scan with these browser options disabled.","complianz-gdpr"),(0,c.__)("DNT or GPC enabled.","complianz-gdpr"),null))},[N]),(0,t.useEffect)(()=>{(async()=>{try{const e=await n.doAction("get_wsc_status",{});S(e.wsc_lock)}catch(e){console.error("Error activating Website Scan:",e)}})()},[]);const T=()=>{let e="doNotTrack"in navigator&&"1"===navigator.doNotTrack;return"globalPrivacyControl"in navigator&&navigator.globalPrivacyControl||e},I=()=>{if(!h)return void j(!1);let e=document.getElementById("cmplz_cookie_scan_frame");e||(e=document.createElement("iframe"),e.setAttribute("id","cmplz_cookie_scan_frame"),e.classList.add("hidden")),e.setAttribute("src",h),e.onload=function(e){setTimeout(()=>{j(!1),k(h)},200)},document.body.appendChild(e)};if("cookie-scan"!==C)return null;let A=z?z.length:0,E="";E=0===A?(0,c.__)("No cookies found on your domain yet.","complianz-gdpr"):1===A?(0,c.__)("The scan found 1 cookie on your domain.","complianz-gdpr"):(0,c.__)("The scan found %s cookies on your domain.","complianz-gdpr").replace("%s",A),f>=100?A>0&&(E+=" "+(0,c.__)("Continue the wizard to categorize cookies and configure consent.","complianz-gdpr")):E+=" "+(0,c.__)("Scanning, %s complete.","complianz-gdpr").replace("%s",Math.round(f)+"%"),s||(E=(0,_.jsx)(l.default,{name:"loading",color:"grey"}));let D=!!P||f<100&&f>0;return(0,_.jsxs)(_.Fragment,{children:[P&&(0,_.jsxs)("div",{className:"cmplz-wscscan-alert",children:[(0,_.jsx)(l.default,{name:"warning",color:"orange",size:48}),(0,_.jsxs)("div",{className:"cmplz-wscscan-alert-group",children:[(0,_.jsx)("div",{className:"cmplz-wscscan-alert-group-title",children:(0,c.__)("Advanced Scan Unavailable","complianz-gdpr")}),(0,_.jsx)("div",{className:"cmplz-wscscan-alert-group-desc",children:(0,c.__)("We need to authenticate this domain.","complianz-gpdr")})]}),(0,_.jsx)("div",{className:"cmplz-wscscan-alert-desc-long",children:(0,c.__)("The new advanced Website Scan needs to authenticate your website for security purposes. It only takes a second!")}),(0,_.jsx)("div",{children:(0,_.jsx)("button",{type:"button",onClick:()=>{const e=new URL(cmplz_settings.dashboard_url);e.searchParams.set("websitescan",""),setTimeout(()=>{window.location.href=e.href},500)},className:"cmplz-wscscan-alert button-secondary",children:(0,c.__)("Start","complianz-gdpr")})})]}),(0,_.jsxs)("div",{className:"cmplz-table-header",children:[(0,_.jsx)("button",{disabled:D,className:"button button-default",onClick:s=>(async()=>{b(1),await n.doAction("scan",{scan_action:"restart"}),await v(),100===f&&(await a(),z.length>0&&e(1))})(),children:(0,c.__)("Scan","complianz-gdpr")}),(0,_.jsx)("button",{disabled:D,className:"button button-default cmplz-reset-button",onClick:s=>(async()=>{b(1),await n.doAction("scan",{scan_action:"reset"}),await v(),100===f&&(L(!1),await a(),z.length>0&&e(1))})(),children:(0,c.__)("Clear Cookies","complianz-gdpr")})]}),(0,_.jsx)("div",{id:"cmplz-scan-progress",children:(0,_.jsx)("div",{className:"cmplz-progress-bar",style:Object.assign({},{width:f+"%"})})}),(0,_.jsx)("div",{children:(0,_.jsx)("div",{className:"cmplz-panel__list",children:(0,_.jsx)(i.default,{summary:E,details:(0,p.default)(s,z)})})})]})})},52010:(e,a,s)=>{s.r(a),s.d(a,{default:()=>c});var t=s(45111),n=s(86087),o=s(10790);const c=e=>{const[a,s]=(0,n.useState)(!1);return(0,o.jsx)("div",{className:"cmplz-panel__list__item",style:e.style?e.style:{},children:(0,o.jsxs)("details",{open:a,children:[(0,o.jsxs)("summary",{onClick:e=>(e=>{e.preventDefault(),s(!a)})(e),children:[e.icon&&(0,o.jsx)(t.default,{name:e.icon}),(0,o.jsx)("h5",{className:"cmplz-panel__list__item__title",children:e.summary}),(0,o.jsx)("div",{className:"cmplz-panel__list__item__comment",children:e.comment}),(0,o.jsx)("div",{className:"cmplz-panel__list__item__icons",children:e.icons}),(0,o.jsx)(t.default,{name:"chevron-down",size:18})]}),(0,o.jsx)("div",{className:"cmplz-panel__list__item__details",children:a&&e.details})]})})}},80756:(e,a,s)=>{s.r(a),s.d(a,{default:()=>n});var t=s(10790);const n=(e,a)=>(0,t.jsx)(t.Fragment,{children:e&&a.map((e,a)=>(0,t.jsx)("div",{children:e},a))})},99091:(e,a,s)=>{s.r(a),s.d(a,{UseCookieScanData:()=>o});var t=s(81621),n=s(9588);const o=(0,t.vt)((e,a)=>({initialLoadCompleted:!1,setInitialLoadCompleted:a=>e({initialLoadCompleted:a}),iframeLoaded:!1,loading:!1,nextPage:!1,progress:0,cookies:[],lastLoadedIframe:"",setIframeLoaded:a=>e({iframeLoaded:a}),setLastLoadedIframe:a=>e(e=>({lastLoadedIframe:a})),setProgress:a=>e({progress:a}),fetchProgress:()=>(e({loading:!0}),n.doAction("get_scan_progress",{}).then(a=>(e({initialLoadCompleted:!0,loading:!1,nextPage:a.next_page,progress:a.progress,cookies:a.cookies}),a)))}))}}]);