"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[9091],{99091:(e,a,o)=>{o.r(a),o.d(a,{UseCookieScanData:()=>t});var s=o(81621),d=o(9588);const t=(0,s.vt)((e,a)=>({initialLoadCompleted:!1,setInitialLoadCompleted:a=>e({initialLoadCompleted:a}),iframeLoaded:!1,loading:!1,nextPage:!1,progress:0,cookies:[],lastLoadedIframe:"",setIframeLoaded:a=>e({iframeLoaded:a}),setLastLoadedIframe:a=>e(e=>({lastLoadedIframe:a})),setProgress:a=>e({progress:a}),fetchProgress:()=>(e({loading:!0}),d.doAction("get_scan_progress",{}).then(a=>(e({initialLoadCompleted:!0,loading:!1,nextPage:a.next_page,progress:a.progress,cookies:a.cookies}),a)))}))}}]);