"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[54],{40054:(e,r,o)=>{o.r(r),o.d(r,{default:()=>n});var t=o(81621),a=o(9588);const n=(0,t.vt)((e,r)=>({recordsLoaded:!1,fetching:!1,generating:!1,progress:!1,records:[],exportLink:"",downloadUrl:"",regions:[],fields:[],noData:!1,totalRecords:0,searchValue:"",setSearchValue:r=>e({searchValue:r}),paginationPerPage:10,pagination:{currentPage:1},setPagination:r=>e({pagination:r}),orderBy:"ID",setOrderBy:r=>e({orderBy:r}),order:"DESC",setOrder:r=>e({order:r}),deleteRecords:async o=>{let t=r().records.filter(e=>o.includes(e.id));e(e=>({records:e.records.filter(e=>!o.includes(e.id))}));let n={};n.records=t,await a.doAction("delete_records_of_consent",n).then(e=>e).catch(e=>{console.error(e)})},fetchData:async()=>{if(r().fetching)return;e({fetching:!0});let o={};o.per_page=r().paginationPerPage,o.page=r().pagination.currentPage,o.order=r().order.toUpperCase(),o.orderBy=r().orderBy,o.search=r().searchValue;const{records:t,totalRecords:n,regions:s,download_url:c}=await a.doAction("get_records_of_consent",o).then(e=>e).catch(e=>{console.error(e)});e(()=>({recordsLoaded:!0,records:t,regions:s,totalRecords:n,downloadUrl:c,fetching:!1}))},startExport:async()=>{e({generating:!0,progress:0,exportLink:""})},fetchExportRecordsOfConsentProgress:async(r,o,t)=>{(r=void 0!==r&&r)||e({generating:!0});let n={};n.startDate=o,n.endDate=t,n.statusOnly=r;const{progress:s,exportLink:c,noData:d}=await a.doAction("export_records_of_consent",n).then(e=>e).catch(e=>{console.error(e)});let i=!1;s<100&&(i=!0),e({progress:s,exportLink:c,generating:i,noData:d})}}))}}]);