"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[7511],{7511:(t,i,a)=>{a.r(i),a.d(i,{default:()=>r});var n=a(81621),u=a(9588);const r=(0,n.vt)((t,i)=>({apiRequestActive:!1,pluginAction:"status",wordPressUrl:"#",upgradeUrl:"#",rating:[],statusLoaded:!1,setStatusLoaded:i=>{t({statusLoaded:i})},startPluginAction:(a,n)=>{let r={};t({apiRequestActive:!0}),r.pluginAction=void 0!==n?n:i().pluginAction,r.slug=a;let s=!1;"download"===r.pluginAction&&(s="activate"),u.doAction("plugin_actions",r).then(n=>{t({pluginAction:n.pluginAction,wordPressUrl:n.wordpress_url,upgradeUrl:n.upgrade_url});let u=Math.round(n.star_rating.rating/10,0)/2;t({rating:u,ratingCount:n.star_rating.rating_count,apiRequestActive:!1,statusLoaded:!0}),"activate"===s&&"installed"!==n.pluginAction&&i().startPluginAction(a,n.pluginAction)})}}))}}]);