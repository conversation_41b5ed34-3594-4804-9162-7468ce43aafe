"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[4759,5228,8033,8985,9091,9758],{10800:(e,t,r)=>{r.r(t),r.d(t,{default:()=>A});var s=r(86087),n=r(51609),c=r(9957),a=r(91071),i=r(62133),o=r(81351),d=r(85357),l=r(54150),p=r(12579),u=r(10790),h="Switch",[f,m]=(0,i.A)(h),[b,g]=f(h),_=n.forwardRef((e,t)=>{const{__scopeSwitch:r,name:s,checked:i,defaultChecked:d,required:l,disabled:f,value:m="on",onCheckedChange:g,form:_,...k}=e,[x,C]=n.useState(null),j=(0,a.s)(t,e=>C(e)),w=n.useRef(!1),A=!x||_||!!x.closest("form"),[S,z]=(0,o.i)({prop:i,defaultProp:d??!1,onChange:g,caller:h});return(0,u.jsxs)(b,{scope:r,checked:S,disabled:f,children:[(0,u.jsx)(p.sG.button,{type:"button",role:"switch","aria-checked":S,"aria-required":l,"data-state":v(S),"data-disabled":f?"":void 0,disabled:f,value:m,...k,ref:j,onClick:(0,c.m)(e.onClick,e=>{z(e=>!e),A&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})}),A&&(0,u.jsx)(y,{control:x,bubbles:!w.current,name:s,value:m,checked:S,required:l,disabled:f,form:_,style:{transform:"translateX(-100%)"}})]})});_.displayName=h;var k="SwitchThumb",x=n.forwardRef((e,t)=>{const{__scopeSwitch:r,...s}=e,n=g(k,r);return(0,u.jsx)(p.sG.span,{"data-state":v(n.checked),"data-disabled":n.disabled?"":void 0,...s,ref:t})});x.displayName=k;var y=n.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:s=!0,...c},i)=>{const o=n.useRef(null),p=(0,a.s)(o,i),h=(0,d.Z)(r),f=(0,l.X)(t);return n.useEffect(()=>{const e=o.current;if(!e)return;const t=window.HTMLInputElement.prototype,n=Object.getOwnPropertyDescriptor(t,"checked").set;if(h!==r&&n){const t=new Event("click",{bubbles:s});n.call(e,r),e.dispatchEvent(t)}},[h,r,s]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...c,tabIndex:-1,ref:p,style:{...c.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function v(e){return e?"checked":"unchecked"}y.displayName="SwitchBubbleInput";var C=_,j=x,w=r(4219);const A=(0,s.memo)(({value:e,onChange:t,required:r,disabled:s,className:n,label:c,id:a})=>{const{getField:i}=(0,w.default)();let o=e;return"0"!==e&&"1"!==e||(o="1"===e),(0,u.jsx)("div",{className:"cmplz-input-group cmplz-switch-group",children:(0,u.jsx)(C,{className:"cmplz-switch-root "+n,checked:o,onCheckedChange:e=>{"banner"===i(a).data_target&&(e=e?"1":"0"),t(e)},disabled:s,required:r,children:(0,u.jsx)(j,{className:"cmplz-switch-thumb"})})})})},25228:(e,t,r)=>{r.r(t),r.d(t,{default:()=>o});var s=r(86087),n=r(21366),c=r(45111),a=r(27723),i=r(10790);const o=(0,s.memo)(({value:e=!1,onChange:t,required:r,defaultValue:s,disabled:o,options:d={},canBeEmpty:l=!0,label:p})=>{if(Array.isArray(d)){let e={};d.map(t=>{e[t.value]=t.label}),d=e}return l?(""===e||!1===e||0===e)&&(e="0",d={0:(0,a.__)("Select an option","complianz-gdpr"),...d}):e||(e=Object.keys(d)[0]),(0,i.jsx)("div",{className:"cmplz-input-group cmplz-select-group",children:(0,i.jsxs)(n.bL,{value:e,defaultValue:s,onValueChange:t,required:r,disabled:o&&!Array.isArray(o),children:[(0,i.jsxs)(n.l9,{className:"cmplz-select-group__trigger",children:[(0,i.jsx)(n.WT,{}),(0,i.jsx)(c.default,{name:"chevron-down"})]}),(0,i.jsxs)(n.UC,{className:"cmplz-select-group__content",position:"popper",children:[(0,i.jsx)(n.PP,{className:"cmplz-select-group__scroll-button",children:(0,i.jsx)(c.default,{name:"chevron-up"})}),(0,i.jsx)(n.LM,{className:"cmplz-select-group__viewport",children:(0,i.jsx)(n.YJ,{children:Object.entries(d).map(([e,t])=>(0,i.jsx)(n.q7,{disabled:Array.isArray(o)&&o.includes(e),className:"cmplz-select-group__item",value:e,children:(0,i.jsx)(n.p4,{children:t})},e))})}),(0,i.jsx)(n.wn,{className:"cmplz-select-group__scroll-button",children:(0,i.jsx)(c.default,{name:"chevron-down"})})]})]})},p)})},34759:(e,t,r)=>{r.r(t),r.d(t,{default:()=>a});var s=r(81621),n=r(16535),c=r(9588);const a=(0,s.vt)((e,t)=>({integrationsLoaded:!1,fetching:!1,services:[],plugins:[],scripts:[],placeholders:[],blockedScripts:[],setScript:(t,r)=>{e((0,n.Ay)(e=>{if("block_script"===r){let r=e.blockedScripts;if(t.urls){for(const[e,s]of Object.entries(t.urls)){if(!s||0===s.length)continue;let e=!1;for(const[t,n]of Object.entries(r))s===t&&(e=!0);e||(r[s]=s)}e.blockedScripts=r}}const s=e.scripts[r].findIndex(e=>e.id===t.id);-1!==s&&(e.scripts[r][s]=t)}))},fetchIntegrationsData:async()=>{if(t().fetching)return;e({fetching:!0});const{services:r,plugins:s,scripts:n,placeholders:c,blocked_scripts:a}=await i();let o=n;o.block_script&&o.block_script.length>0&&o.block_script.forEach((e,t)=>{e.id=t}),o.add_script&&o.add_script.length>0&&o.add_script.forEach((e,t)=>{e.id=t}),o.whitelist_script&&o.whitelist_script.length>0&&o.whitelist_script.forEach((e,t)=>{e.id=t}),e(()=>({integrationsLoaded:!0,services:r,plugins:s,scripts:o,fetching:!1,placeholders:c,blockedScripts:a}))},addScript:r=>{e({fetching:!0}),t().scripts[r]&&Array.isArray(t().scripts[r])||e((0,n.Ay)(e=>{e.scripts[r]=[]})),e((0,n.Ay)(e=>{e.scripts[r].push({name:"general",id:e.scripts[r].length,enable:!0})}));let s=t().scripts;return c.doAction("update_scripts",{scripts:s}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},saveScript:(r,s)=>{e({fetching:!0}),t().scripts[s]&&Array.isArray(t().scripts[s])||e((0,n.Ay)(e=>{e.scripts[s]=[]})),e((0,n.Ay)(e=>{const t=e.scripts[s].findIndex(e=>e.id===r.id);-1!==t&&(e.scripts[s][t]=r)}));let a=t().scripts;return c.doAction("update_scripts",{scripts:a}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},deleteScript:(r,s)=>{e({fetching:!0}),t().scripts[s]&&Array.isArray(t().scripts[s])||e((0,n.Ay)(e=>{e.scripts[s]=[]})),e((0,n.Ay)(e=>{const t=e.scripts[s].findIndex(e=>e.id===r.id);-1!==t&&e.scripts[s].splice(t,1)}));let a=t().scripts;return c.doAction("update_scripts",{scripts:a}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},updatePluginStatus:async(t,r)=>{e({fetching:!0}),e((0,n.Ay)(e=>{const s=e.plugins.findIndex(e=>e.id===t);-1!==s&&(e.plugins[s].enabled=r)}));const s=await c.doAction("update_plugin_status",{plugin:t,enabled:r}).then(e=>e).catch(e=>{console.error(e)});return e({fetching:!1}),s},updatePlaceholderStatus:async(t,r,s)=>{e({fetching:!0}),s&&e((0,n.Ay)(e=>{const s=e.plugins.findIndex(e=>e.id===t);-1!==s&&(e.plugins[s].placeholder=r?"enabled":"disabled")}));const a=await c.doAction("update_placeholder_status",{id:t,enabled:r}).then(e=>e).catch(e=>{console.error(e)});return e({fetching:!1}),a}})),i=()=>c.doAction("get_integrations_data",{}).then(e=>e).catch(e=>{console.error(e)})},48033:(e,t,r)=>{r.r(t),r.d(t,{default:()=>o});var s=r(27723),n=(r(10800),r(25228)),c=r(34759),a=r(81366),i=r(10790);const o=e=>{const{setScript:t,blockedScripts:r,fetching:o}=(0,c.default)(),d=r,l=e.script,p=e=>{if(!l.dependency||0===l.dependency.length)return"";let t=Object.entries(l.dependency);for(const[r,s]of t)if(r===e)return s;return""},u=(e,t)=>{let r={...e};for(const[e,s]of Object.entries(r))if(s===t){delete r[e];break}return r};let h=l.hasOwnProperty("urls")?Object.entries(l.urls):[""];return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,i.jsx)(a.default,{id:l.id+"dependency",disabled:o,value:l.enable_dependency,onChange:r=>(r=>{let s={...l};s.enable_dependency=r,t(s,e.type)})(r),options:{true:(0,s.__)("Enable dependency","complianz-gdpr")}})}),!!l.enable_dependency&&(0,i.jsxs)("div",{className:"cmplz-details-row cmplz-details-row",children:[h.length>1&&h.map(([r,c],a)=>(0,i.jsxs)("div",{className:"cmplz-scriptcenter-dependencies",children:[(0,i.jsx)(n.default,{disabled:o,value:p(c),options:u(d,c),onChange:r=>((r,s)=>{let n={...l},c={...n.dependency};c[s]=r,n.dependency=c,t(n,e.type)})(r,c)}),(0,i.jsxs)("div",{children:[(0,s.__)("waits for: ","complianz-gdpr"),c||(0,s.__)("Empty URL","complianz-gdpr")]})]},a)),h.length<=1&&(0,i.jsx)(i.Fragment,{children:(0,s.__)("Add a URL to create a dependency between two URLs","complianz-gdpr")})]})]})}},79758:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var s=r(86087),n=r(9588),c=r(4219),a=r(52043),i=r(56427),o=r(99091),d=r(32828),l=r(10790);const p=(0,s.memo)(({type:e="action",style:t="tertiary",label:r,onClick:p,href:u="",target:h="",disabled:f,action:m,field:b,children:g})=>{if(!r&&!g)return null;const _=(b&&b.button_text?b.button_text:r)||g,{fetchFieldsData:k,showSavedSettingsNotice:x}=(0,c.default)(),{setInitialLoadCompleted:y,setProgress:v}=(0,o.UseCookieScanData)(),{setProgressLoaded:C}=(0,d.default)(),{selectedSubMenuItem:j}=(0,a.default)(),[w,A]=(0,s.useState)(!1),S=`button cmplz-button button--${t} button-${e}`,z=async e=>{await n.doAction(b.action,{}).then(e=>{e.success&&(k(j),"reset_settings"===e.id&&(y(!1),v(0),C(!1)),x(e.message))})},N=b&&b.warn?b.warn:"";return"action"===e?(0,l.jsxs)(l.Fragment,{children:[i.__experimentalConfirmDialog&&(0,l.jsx)(i.__experimentalConfirmDialog,{isOpen:w,onConfirm:async()=>{A(!1),await z()},onCancel:()=>{A(!1)},children:N}),(0,l.jsx)("button",{className:S,onClick:async t=>{if("action"!==e||!p)return"action"===e&&m?i.__experimentalConfirmDialog?void(b&&b.warn?A(!0):await z()):void await z():void(window.location.href=b.url);p(t)},disabled:f,children:_})]}):"link"===e?(0,l.jsx)("a",{className:S,href:u,target:h,children:_}):void 0})},81366:(e,t,r)=>{r.r(t),r.d(t,{default:()=>P});var s=r(51609),n=r(91071),c=r(62133),a=r(9957),i=r(81351),o=r(85357),d=r(54150),l=r(7971),p=r(12579),u=r(10790),h="Checkbox",[f,m]=(0,c.A)(h),[b,g]=f(h);function _(e){const{__scopeCheckbox:t,checked:r,children:n,defaultChecked:c,disabled:a,form:o,name:d,onCheckedChange:l,required:p,value:f="on",internal_do_not_use_render:m}=e,[g,_]=(0,i.i)({prop:r,defaultProp:c??!1,onChange:l,caller:h}),[k,x]=s.useState(null),[y,v]=s.useState(null),C=s.useRef(!1),j=!k||!!o||!!k.closest("form"),w={checked:g,disabled:a,setChecked:_,control:k,setControl:x,name:d,form:o,value:f,hasConsumerStoppedPropagationRef:C,required:p,defaultChecked:!S(c)&&c,isFormControl:j,bubbleInput:y,setBubbleInput:v};return(0,u.jsx)(b,{scope:t,...w,children:A(m)?m(w):n})}var k="CheckboxTrigger",x=s.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...c},i)=>{const{control:o,value:d,disabled:l,checked:h,required:f,setControl:m,setChecked:b,hasConsumerStoppedPropagationRef:_,isFormControl:x,bubbleInput:y}=g(k,e),v=(0,n.s)(i,m),C=s.useRef(h);return s.useEffect(()=>{const e=o?.form;if(e){const t=()=>b(C.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[o,b]),(0,u.jsx)(p.sG.button,{type:"button",role:"checkbox","aria-checked":S(h)?"mixed":h,"aria-required":f,"data-state":z(h),"data-disabled":l?"":void 0,disabled:l,value:d,...c,ref:v,onKeyDown:(0,a.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,a.m)(r,e=>{b(e=>!!S(e)||!e),y&&x&&(_.current=e.isPropagationStopped(),_.current||e.stopPropagation())})})});x.displayName=k;var y=s.forwardRef((e,t)=>{const{__scopeCheckbox:r,name:s,checked:n,defaultChecked:c,required:a,disabled:i,value:o,onCheckedChange:d,form:l,...p}=e;return(0,u.jsx)(_,{__scopeCheckbox:r,checked:n,defaultChecked:c,disabled:i,required:a,onCheckedChange:d,name:s,form:l,value:o,internal_do_not_use_render:({isFormControl:e})=>(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(x,{...p,ref:t,__scopeCheckbox:r}),e&&(0,u.jsx)(w,{__scopeCheckbox:r})]})})});y.displayName=h;var v="CheckboxIndicator",C=s.forwardRef((e,t)=>{const{__scopeCheckbox:r,forceMount:s,...n}=e,c=g(v,r);return(0,u.jsx)(l.C,{present:s||S(c.checked)||!0===c.checked,children:(0,u.jsx)(p.sG.span,{"data-state":z(c.checked),"data-disabled":c.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});C.displayName=v;var j="CheckboxBubbleInput",w=s.forwardRef(({__scopeCheckbox:e,...t},r)=>{const{control:c,hasConsumerStoppedPropagationRef:a,checked:i,defaultChecked:l,required:h,disabled:f,name:m,value:b,form:_,bubbleInput:k,setBubbleInput:x}=g(j,e),y=(0,n.s)(r,x),v=(0,o.Z)(i),C=(0,d.X)(c);s.useEffect(()=>{const e=k;if(!e)return;const t=window.HTMLInputElement.prototype,r=Object.getOwnPropertyDescriptor(t,"checked").set,s=!a.current;if(v!==i&&r){const t=new Event("click",{bubbles:s});e.indeterminate=S(i),r.call(e,!S(i)&&i),e.dispatchEvent(t)}},[k,v,i,a]);const w=s.useRef(!S(i)&&i);return(0,u.jsx)(p.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:l??w.current,required:h,disabled:f,name:m,value:b,form:_,...t,tabIndex:-1,ref:y,style:{...t.style,...C,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function A(e){return"function"==typeof e}function S(e){return"indeterminate"===e}function z(e){return S(e)?"indeterminate":e?"checked":"unchecked"}w.displayName=j;var N=r(27723),I=r(45111),L=r(86087),E=r(79758);const P=(0,L.memo)(({indeterminate:e,label:t,value:r,id:s,onChange:n,required:c,disabled:a,options:i={}})=>{const[o,d]=(0,L.useState)(!1),[l,p]=(0,L.useState)(!1);let h=r;Array.isArray(h)||(h=""===h?[]:[h]),(0,L.useEffect)(()=>{let e=1===Object.keys(i).length&&"true"===Object.keys(i)[0];d(e)},[]),e&&(r=!0);const f=h;let m=!1;Object.keys(i).length>10&&(m=!0);const b=e=>o?r:f.includes(""+e)||f.includes(parseInt(e)),g=()=>{p(!l)};let _=a&&!Array.isArray(a);return 0===Object.keys(i).length?(0,u.jsx)(u.Fragment,{children:(0,N.__)("No options found","complianz-gdpr")}):(0,u.jsxs)("div",{className:"cmplz-checkbox-group",children:[Object.entries(i).map(([i,d],p)=>(0,u.jsxs)("div",{className:"cmplz-checkbox-group__item"+(!l&&p>9?" cmplz-hidden":""),children:[(0,u.jsx)(y,{className:"cmplz-checkbox-group__checkbox",id:s+"_"+i,checked:b(i),"aria-label":t,disabled:_||Array.isArray(a)&&a.includes(i),required:c,onCheckedChange:e=>((e,t)=>{if(o)n(!r);else{const e=f.includes(""+t)||f.includes(parseInt(t))?f.filter(e=>e!==""+t&&e!==parseInt(t)):[...f,t];n(e)}})(0,i),children:(0,u.jsx)(C,{className:"cmplz-checkbox-group__indicator",children:(0,u.jsx)(I.default,{name:e?"indeterminate":"check",size:14,color:"dark-blue"})})}),(0,u.jsx)("label",{className:"cmplz-checkbox-group__label",htmlFor:s+"_"+i,children:d})]},i)),!l&&m&&(0,u.jsx)(E.default,{onClick:()=>g(),children:(0,N.__)("Show more","complianz-gdpr")}),l&&m&&(0,u.jsx)(E.default,{onClick:()=>g(),children:(0,N.__)("Show less","complianz-gdpr")})]})})},85357:(e,t,r)=>{r.d(t,{Z:()=>n});var s=r(51609);function n(e){const t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},99091:(e,t,r)=>{r.r(t),r.d(t,{UseCookieScanData:()=>c});var s=r(81621),n=r(9588);const c=(0,s.vt)((e,t)=>({initialLoadCompleted:!1,setInitialLoadCompleted:t=>e({initialLoadCompleted:t}),iframeLoaded:!1,loading:!1,nextPage:!1,progress:0,cookies:[],lastLoadedIframe:"",setIframeLoaded:t=>e({iframeLoaded:t}),setLastLoadedIframe:t=>e(e=>({lastLoadedIframe:t})),setProgress:t=>e({progress:t}),fetchProgress:()=>(e({loading:!0}),n.doAction("get_scan_progress",{}).then(t=>(e({initialLoadCompleted:!0,loading:!1,nextPage:t.next_page,progress:t.progress,cookies:t.cookies}),t)))}))}}]);