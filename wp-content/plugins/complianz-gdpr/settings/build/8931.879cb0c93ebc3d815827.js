"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[8931],{38931:(e,l,c)=>{c.r(l),c.d(l,{default:()=>d});var s=c(27723),n=c(45111),a=c(10790);const d=()=>(0,a.jsxs)(a.Frag<PERSON>,{children:[(0,a.jsxs)("div",{className:"cmplz-legend",children:[(0,a.jsx)(n.default,{name:"sync",color:"green",size:14}),(0,a.jsx)("span",{children:(0,s.__)("Synchronized","complianz-gdpr")})]}),(0,a.jsxs)("div",{className:"cmplz-legend",children:[(0,a.jsx)(n.default,{name:"circle-check",color:"green",size:14}),(0,a.jsx)("span",{children:(0,s.__)("Validated","complianz-gdpr")})]})]})}}]);