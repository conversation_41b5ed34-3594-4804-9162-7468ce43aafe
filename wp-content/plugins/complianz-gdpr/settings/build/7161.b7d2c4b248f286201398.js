"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[54,7161,8432],{38432:(e,t,r)=>{r.r(t),r.d(t,{default:()=>c});var a=r(81621),o=r(72346),n=r(31127),s=r(979),d=r(66212);const c=(0,a.vt)(e=>({startDate:(0,o.default)((0,n.default)((0,s.A)(new Date,7)),"yyyy-MM-dd"),setStartDate:t=>e(e=>({startDate:t})),endDate:(0,o.default)((0,d.default)((0,s.A)(new Date,1)),"yyyy-MM-dd"),setEndDate:t=>e(e=>({endDate:t})),range:"last-7-days",setRange:t=>e(e=>({range:t}))}))},40054:(e,t,r)=>{r.r(t),r.d(t,{default:()=>n});var a=r(81621),o=r(9588);const n=(0,a.vt)((e,t)=>({recordsLoaded:!1,fetching:!1,generating:!1,progress:!1,records:[],exportLink:"",downloadUrl:"",regions:[],fields:[],noData:!1,totalRecords:0,searchValue:"",setSearchValue:t=>e({searchValue:t}),paginationPerPage:10,pagination:{currentPage:1},setPagination:t=>e({pagination:t}),orderBy:"ID",setOrderBy:t=>e({orderBy:t}),order:"DESC",setOrder:t=>e({order:t}),deleteRecords:async r=>{let a=t().records.filter(e=>r.includes(e.id));e(e=>({records:e.records.filter(e=>!r.includes(e.id))}));let n={};n.records=a,await o.doAction("delete_records_of_consent",n).then(e=>e).catch(e=>{console.error(e)})},fetchData:async()=>{if(t().fetching)return;e({fetching:!0});let r={};r.per_page=t().paginationPerPage,r.page=t().pagination.currentPage,r.order=t().order.toUpperCase(),r.orderBy=t().orderBy,r.search=t().searchValue;const{records:a,totalRecords:n,regions:s,download_url:d}=await o.doAction("get_records_of_consent",r).then(e=>e).catch(e=>{console.error(e)});e(()=>({recordsLoaded:!0,records:a,regions:s,totalRecords:n,downloadUrl:d,fetching:!1}))},startExport:async()=>{e({generating:!0,progress:0,exportLink:""})},fetchExportRecordsOfConsentProgress:async(t,r,a)=>{(t=void 0!==t&&t)||e({generating:!0});let n={};n.startDate=r,n.endDate=a,n.statusOnly=t;const{progress:s,exportLink:d,noData:c}=await o.doAction("export_records_of_consent",n).then(e=>e).catch(e=>{console.error(e)});let l=!1;s<100&&(l=!0),e({progress:s,exportLink:d,generating:l,noData:c})}}))},87161:(e,t,r)=>{r.r(t),r.d(t,{default:()=>l});var a=r(27723),o=r(86087),n=r(40054),s=r(45111),d=r(38432),c=r(10790);const l=(0,o.memo)(()=>{const{noData:e,startExport:t,exportLink:l,fetchExportRecordsOfConsentProgress:i,generating:g,progress:p}=(0,n.default)(),[u,f]=(0,o.useState)(null),{startDate:h,endDate:m}=(0,d.default)();return(0,o.useEffect)(()=>{Promise.all([r.e(3569),r.e(4715),r.e(9808),r.e(7660)]).then(r.bind(r,95279)).then(({default:e})=>{f(()=>e)})},[]),(0,o.useEffect)(()=>{i(!0)},[]),(0,o.useEffect)(()=>{p<100&&g&&i(!1,h,m)},[p]),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{className:"cmplz-field-button cmplz-table-header",children:(0,c.jsxs)("div",{className:"cmplz-table-header-controls",children:[u&&(0,c.jsx)(u,{}),(0,c.jsxs)("button",{disabled:g,className:"button button-default cmplz-field-button",onClick:()=>t(),children:[(0,a.__)("Export to CSV","complianz-gdpr"),g&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(s.default,{name:"loading",color:"grey"})," ",p,"%"]})]})]})}),p>=100&&(""!==l||e)&&(0,c.jsxs)("div",{className:"cmplz-selected-document",children:[!e&&(0,a.__)("Your Records Of Consent Export has been completed.","complianz-gdpr"),e&&(0,a.__)("Your selection does not contain any data.","complianz-gdpr"),(0,c.jsx)("div",{className:"cmplz-selected-document-controls",children:!e&&(0,c.jsx)("a",{className:"button button-default",download:!0,href:l,children:(0,a.__)("Download","complianz-gdpr")})})]})]})})}}]);