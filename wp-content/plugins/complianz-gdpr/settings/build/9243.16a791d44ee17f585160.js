"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[9243],{10263:(e,r,o)=>{o.d(r,{c:()=>n});var t=o(51609);function n(e){const r=t.useRef(e);return t.useEffect(()=>{r.current=e}),t.useMemo(()=>(...e)=>r.current?.(...e),[])}},18723:(e,r,o)=>{var t;o.d(r,{B:()=>u});var n=o(51609),a=o(88200),c=(t||(t=o.t(n,2)))[" useId ".trim().toString()]||(()=>{}),s=0;function u(e){const[r,o]=n.useState(c());return(0,a.N)(()=>{e||o(e=>e??String(s++))},[e]),e||(r?`radix-${r}`:"")}},29243:(e,r,o)=>{o.d(r,{C1:()=>ce,q7:()=>ae,bL:()=>ne});var t=o(51609),n=o(9957),a=o(91071),c=o(62133),s=o(12579),u=o(33362),i=o(10790);Map;var l=o(18723),d=o(10263),f=o(81351),p=t.createContext(void 0);function v(e){const r=t.useContext(p);return e||r||"ltr"}var m="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[R,w,y]=function(e){const r=e+"CollectionProvider",[o,n]=(0,c.A)(r),[s,l]=o(r,{collectionRef:{current:null},itemMap:new Map}),d=e=>{const{scope:r,children:o}=e,n=t.useRef(null),a=t.useRef(new Map).current;return(0,i.jsx)(s,{scope:r,itemMap:a,collectionRef:n,children:o})};d.displayName=r;const f=e+"CollectionSlot",p=(0,u.TL)(f),v=t.forwardRef((e,r)=>{const{scope:o,children:t}=e,n=l(f,o),c=(0,a.s)(r,n.collectionRef);return(0,i.jsx)(p,{ref:c,children:t})});v.displayName=f;const m=e+"CollectionItemSlot",b="data-radix-collection-item",h=(0,u.TL)(m),R=t.forwardRef((e,r)=>{const{scope:o,children:n,...c}=e,s=t.useRef(null),u=(0,a.s)(r,s),d=l(m,o);return t.useEffect(()=>(d.itemMap.set(s,{ref:s,...c}),()=>{d.itemMap.delete(s)})),(0,i.jsx)(h,{[b]:"",ref:u,children:n})});return R.displayName=m,[{Provider:d,Slot:v,ItemSlot:R},function(r){const o=l(e+"CollectionConsumer",r);return t.useCallback(()=>{const e=o.collectionRef.current;if(!e)return[];const r=Array.from(e.querySelectorAll(`[${b}]`));return Array.from(o.itemMap.values()).sort((e,o)=>r.indexOf(e.ref.current)-r.indexOf(o.ref.current))},[o.collectionRef,o.itemMap])},n]}(h),[g,k]=(0,c.A)(h,[y]),[x,C]=g(h),I=t.forwardRef((e,r)=>(0,i.jsx)(R.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,i.jsx)(R.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,i.jsx)(S,{...e,ref:r})})}));I.displayName=h;var S=t.forwardRef((e,r)=>{const{__scopeRovingFocusGroup:o,orientation:c,loop:u=!1,dir:l,currentTabStopId:p,defaultCurrentTabStopId:R,onCurrentTabStopIdChange:y,onEntryFocus:g,preventScrollOnEntryFocus:k=!1,...C}=e,I=t.useRef(null),S=(0,a.s)(r,I),A=v(l),[E,j]=(0,f.i)({prop:p,defaultProp:R??null,onChange:y,caller:h}),[_,T]=t.useState(!1),G=(0,d.c)(g),M=w(o),D=t.useRef(!1),[L,N]=t.useState(0);return t.useEffect(()=>{const e=I.current;if(e)return e.addEventListener(m,G),()=>e.removeEventListener(m,G)},[G]),(0,i.jsx)(x,{scope:o,orientation:c,dir:A,loop:u,currentTabStopId:E,onItemFocus:t.useCallback(e=>j(e),[j]),onItemShiftTab:t.useCallback(()=>T(!0),[]),onFocusableItemAdd:t.useCallback(()=>N(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>N(e=>e-1),[]),children:(0,i.jsx)(s.sG.div,{tabIndex:_||0===L?-1:0,"data-orientation":c,...C,ref:S,style:{outline:"none",...e.style},onMouseDown:(0,n.m)(e.onMouseDown,()=>{D.current=!0}),onFocus:(0,n.m)(e.onFocus,e=>{const r=!D.current;if(e.target===e.currentTarget&&r&&!_){const r=new CustomEvent(m,b);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){const e=M().filter(e=>e.focusable);F([e.find(e=>e.active),e.find(e=>e.id===E),...e].filter(Boolean).map(e=>e.ref.current),k)}}D.current=!1}),onBlur:(0,n.m)(e.onBlur,()=>T(!1))})})}),A="RovingFocusGroupItem",E=t.forwardRef((e,r)=>{const{__scopeRovingFocusGroup:o,focusable:a=!0,active:c=!1,tabStopId:u,children:d,...f}=e,p=(0,l.B)(),v=u||p,m=C(A,o),b=m.currentTabStopId===v,h=w(o),{onFocusableItemAdd:y,onFocusableItemRemove:g,currentTabStopId:k}=m;return t.useEffect(()=>{if(a)return y(),()=>g()},[a,y,g]),(0,i.jsx)(R.ItemSlot,{scope:o,id:v,focusable:a,active:c,children:(0,i.jsx)(s.sG.span,{tabIndex:b?0:-1,"data-orientation":m.orientation,...f,ref:r,onMouseDown:(0,n.m)(e.onMouseDown,e=>{a?m.onItemFocus(v):e.preventDefault()}),onFocus:(0,n.m)(e.onFocus,()=>m.onItemFocus(v)),onKeyDown:(0,n.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;const r=function(e,r,o){const t=function(e,r){return"rtl"!==r?e:"ArrowLeft"===e?"ArrowRight":"ArrowRight"===e?"ArrowLeft":e}(e.key,o);return"vertical"===r&&["ArrowLeft","ArrowRight"].includes(t)||"horizontal"===r&&["ArrowUp","ArrowDown"].includes(t)?void 0:j[t]}(e,m.orientation,m.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)n.reverse();else if("prev"===r||"next"===r){"prev"===r&&n.reverse();const a=n.indexOf(e.currentTarget);n=m.loop?(t=a+1,(o=n).map((e,r)=>o[(t+r)%o.length])):n.slice(a+1)}setTimeout(()=>F(n))}var o,t}),children:"function"==typeof d?d({isCurrentTabStop:b,hasTabStop:null!=k}):d})})});E.displayName=A;var j={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function F(e,r=!1){const o=document.activeElement;for(const t of e){if(t===o)return;if(t.focus({preventScroll:r}),document.activeElement!==o)return}}var _=I,T=E,G=o(54150),M=o(85357),D=o(7971),L="Radio",[N,P]=(0,c.A)(L),[q,K]=N(L),B=t.forwardRef((e,r)=>{const{__scopeRadio:o,name:c,checked:u=!1,required:l,disabled:d,value:f="on",onCheck:p,form:v,...m}=e,[b,h]=t.useState(null),R=(0,a.s)(r,e=>h(e)),w=t.useRef(!1),y=!b||v||!!b.closest("form");return(0,i.jsxs)(q,{scope:o,checked:u,disabled:d,children:[(0,i.jsx)(s.sG.button,{type:"button",role:"radio","aria-checked":u,"data-state":z(u),"data-disabled":d?"":void 0,disabled:d,value:f,...m,ref:R,onClick:(0,n.m)(e.onClick,e=>{u||p?.(),y&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})}),y&&(0,i.jsx)(V,{control:b,bubbles:!w.current,name:c,value:f,checked:u,required:l,disabled:d,form:v,style:{transform:"translateX(-100%)"}})]})});B.displayName=L;var O="RadioIndicator",U=t.forwardRef((e,r)=>{const{__scopeRadio:o,forceMount:t,...n}=e,a=K(O,o);return(0,i.jsx)(D.C,{present:t||a.checked,children:(0,i.jsx)(s.sG.span,{"data-state":z(a.checked),"data-disabled":a.disabled?"":void 0,...n,ref:r})})});U.displayName=O;var V=t.forwardRef(({__scopeRadio:e,control:r,checked:o,bubbles:n=!0,...c},u)=>{const l=t.useRef(null),d=(0,a.s)(l,u),f=(0,M.Z)(o),p=(0,G.X)(r);return t.useEffect(()=>{const e=l.current;if(!e)return;const r=window.HTMLInputElement.prototype,t=Object.getOwnPropertyDescriptor(r,"checked").set;if(f!==o&&t){const r=new Event("click",{bubbles:n});t.call(e,o),e.dispatchEvent(r)}},[f,o,n]),(0,i.jsx)(s.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:o,...c,tabIndex:-1,ref:d,style:{...c.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function z(e){return e?"checked":"unchecked"}V.displayName="RadioBubbleInput";var H=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],X="RadioGroup",[Z,$]=(0,c.A)(X,[k,P]),J=k(),Q=P(),[W,Y]=Z(X),ee=t.forwardRef((e,r)=>{const{__scopeRadioGroup:o,name:t,defaultValue:n,value:a,required:c=!1,disabled:u=!1,orientation:l,dir:d,loop:p=!0,onValueChange:m,...b}=e,h=J(o),R=v(d),[w,y]=(0,f.i)({prop:a,defaultProp:n??null,onChange:m,caller:X});return(0,i.jsx)(W,{scope:o,name:t,required:c,disabled:u,value:w,onValueChange:y,children:(0,i.jsx)(_,{asChild:!0,...h,orientation:l,dir:R,loop:p,children:(0,i.jsx)(s.sG.div,{role:"radiogroup","aria-required":c,"aria-orientation":l,"data-disabled":u?"":void 0,dir:R,...b,ref:r})})})});ee.displayName=X;var re="RadioGroupItem",oe=t.forwardRef((e,r)=>{const{__scopeRadioGroup:o,disabled:c,...s}=e,u=Y(re,o),l=u.disabled||c,d=J(o),f=Q(o),p=t.useRef(null),v=(0,a.s)(r,p),m=u.value===s.value,b=t.useRef(!1);return t.useEffect(()=>{const e=e=>{H.includes(e.key)&&(b.current=!0)},r=()=>b.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",r),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",r)}},[]),(0,i.jsx)(T,{asChild:!0,...d,focusable:!l,active:m,children:(0,i.jsx)(B,{disabled:l,required:u.required,checked:m,...f,...s,name:u.name,ref:v,onCheck:()=>u.onValueChange(s.value),onKeyDown:(0,n.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,n.m)(s.onFocus,()=>{b.current&&p.current?.click()})})})});oe.displayName=re;var te=t.forwardRef((e,r)=>{const{__scopeRadioGroup:o,...t}=e,n=Q(o);return(0,i.jsx)(U,{...n,...t,ref:r})});te.displayName="RadioGroupIndicator";var ne=ee,ae=oe,ce=te},85357:(e,r,o)=>{o.d(r,{Z:()=>n});var t=o(51609);function n(e){const r=t.useRef({value:e,previous:e});return t.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}}}]);