"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[120,1677,2113,2299,2397,3424,5664,6231,8447,8772,9056,9541,9877],{2113:(e,t,s)=>{s.r(t),s.d(t,{default:()=>o});var n=s(76231),l=s(86087),a=s(45111),i=s(10790);const o=(0,l.memo)(({type:e,...t})=>{const{goToNextStep:s,goToPrevStep:o,nextStepDisabled:r,prevStepDisabled:c,enablePluginInstallation:d,isInstalling:p,isLoading:u,currentStep:g}=(0,n.useNewOnboardingData)(),[m,C]=(0,l.useState)(n.steps[g].prevButton),[h,w]=(0,l.useState)(n.steps[g].nextButton),[f,x]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{w("plugins"===g?p?n.steps[g].nextButtonThird:d?n.steps[g].nextButtonSecondary:n.steps[g].nextButton:n.steps[g].nextButton)},[g,d,p]),(0,l.useEffect)(()=>{x(u||p||c||r)},[u,p,c,r]),(0,i.jsxs)("div",{className:`cmplz-modal-footer-btn-wrap cmplz-btn-${e}`,children:[(p||u)&&"next"===e&&(0,i.jsx)(a.default,{name:"loading",color:"next"===e?"white":"blue",size:14}),(0,i.jsx)("button",{type:"button",onClick:()=>{"next"===e?s():"prev"===e&&o()},className:"button "+("prev"===e?"button-default":"button-primary"),disabled:f,...t,children:(0,i.jsx)("span",{children:"next"===e?h:m})})]})})},12397:(e,t,s)=>{s.r(t),s.d(t,{OnboardingModalClose:()=>a});var n=s(76231),l=s(10790);const a=()=>{const{closeModal:e}=(0,n.useNewOnboardingData)();return(0,l.jsx)("button",{type:"button",className:"cmplz-modal-close","data-dismiss":"modal","aria-label":"Close",onClick:()=>e(!0),children:(0,l.jsx)("svg",{"aria-hidden":"true",focusable:"false",role:"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 320 512",height:"18",children:(0,l.jsx)("path",{fill:"#000000",d:"M310.6 361.4c12.5 12.5 12.5 32.75 0 45.25C304.4 412.9 296.2 416 288 416s-16.38-3.125-22.62-9.375L160 301.3L54.63 406.6C48.38 412.9 40.19 416 32 416S15.63 412.9 9.375 406.6c-12.5-12.5-12.5-32.75 0-45.25l105.4-105.4L9.375 150.6c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0L160 210.8l105.4-105.4c12.5-12.5 32.75-12.5 45.25 0s12.5 32.75 0 45.25l-105.4 105.4L310.6 361.4z"})})})}},19541:(e,t,s)=>{s.r(t),s.d(t,{default:()=>l});var n=s(10790);const l=({type:e,className:t="",error:s="",...l})=>{if(e)return"email"===e?(0,n.jsxs)("div",{className:`cmplz-websitescan-input-wrapper ${e}`,children:[(0,n.jsx)("input",{className:`${t} cmplz-websitescan-input`,type:"email",...l}),s&&(0,n.jsx)("span",{className:"cmplz-websitescan-input-invalid",children:s})]}):"checkbox"===e?(0,n.jsx)("div",{className:`cmplz-websitescan-input-wrapper ${e}`,children:(0,n.jsx)("input",{className:`${t} cmplz-websitescan-input`,type:"checkbox",...l})}):null}},20120:(e,t,s)=>{s.r(t),s.d(t,{default:()=>d});var n=s(27723),l=s(86087),a=s(76231),i=s(19541),o=s(4219),r=s(99695),c=s(10790);const d=()=>{const{wscEmail:e,setWscEmail:t,emailError:s,setEmailError:d,setNextStepDisabled:p,setEnableWsc:u,isValidEmail:g,currentStep:m,isContentLoading:C,setIsContentLoading:h}=(0,a.useNewOnboardingData)(),{fields:w,fieldsLoaded:f}=(0,o.default)();return(0,l.useEffect)(()=>{try{if(h(!0),w){const{default:e}=w.find(e=>"cmplz_wsc_email"===e.id);t(e)}else console.log("Fields not loaded")}catch(e){console.log(e)}finally{h(!1)}},[w,f]),(0,c.jsx)("div",{className:`cmplz-modal-content-step ${m}`,children:C?(0,c.jsx)(r.default,{}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("p",{children:(0,n.__)("In the latest release of Complianz, we introduce our newest Website Scan. This scan will not only retrieve services and cookies but also help you configure our plugin and keep you up-to-date if changes are made that might need legal changes.","complianz-gdpr")}),(0,c.jsx)("p",{children:(0,n.__)("To use our newest Website Scan we need to verify your website and confirm your access by email. Register below and get the latest from Complianz!","complianz-gdpr")}),(0,c.jsx)(i.default,{type:"email",value:e,onChange:e=>{const s=e.target.value;t(s);const l=g(s);d(l?"":(0,n.__)("Please enter a valid email address.","complianz-gdpr")),p(!l),u(0!==s.length&&l)},placeholder:(0,n.__)("Your email address","complianz-gdpr"),error:s})]})})}},38772:(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});var n=s(76231),l=s(99695),a=s(10790);const i=({title:e})=>{const{isContentLoading:t}=(0,n.useNewOnboardingData)();return(0,a.jsx)("div",{className:"cmplz-modal-header-branding-title",children:t?(0,a.jsx)(l.default,{lines:"1"}):(0,a.jsx)("p",{className:"cmplz-h4",children:e})})}},48447:(e,t,s)=>{s.r(t),s.d(t,{default:()=>C});var n=s(86087),l=s(4219),a=s(76231),i=s(12397),o=s(38772),r=s(2113),c=s(20120),d=s(82299),p=s(89877),u=s(53424),g=s(91677),m=s(10790);const C=()=>{const{isModalOpen:e,currentStep:t,closeModal:s}=(0,a.useNewOnboardingData)(),{fieldsLoaded:C,getFieldValue:h}=(0,l.default)(),[w,f]=(0,n.useState)(!1);if((0,n.useEffect)(()=>{if(!C)return;const e=h("cmplz_wsc_client_id"),t=h("cmplz_wsc_client_secret"),n=-1!==window.location.href.indexOf("cmplz_force_signup");e&&t?n?f(!0):s():f(!0)},[C,s,h,f]),!e||!w)return null;const{title:x}=a.steps[t];return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("div",{className:"cmplz-modal-backdrop",children:" "}),(0,m.jsxs)("div",{className:"cmplz-modal cmplz-websitescan",children:[(0,m.jsx)("div",{className:"cmplz-modal-header",children:(0,m.jsxs)("div",{className:"cmplz-modal-header-branding",children:[(0,m.jsx)(o.default,{title:x}),(0,m.jsx)(i.OnboardingModalClose,{})]})}),(0,m.jsx)("div",{className:"cmplz-modal-content",children:(0,m.jsxs)(m.Fragment,{children:["welcome"===t&&(0,m.jsx)(c.default,{}),"terms"===t&&(0,m.jsx)(d.default,{}),"newsletter"===t&&(0,m.jsx)(p.default,{}),"plugins"===t&&(0,m.jsx)(u.default,{}),"thankYou"===t&&(0,m.jsx)(g.default,{})]})}),(0,m.jsxs)("div",{className:"cmplz-modal-footer",children:["thankYou"!==t&&(0,m.jsx)(r.default,{type:"prev"}),(0,m.jsx)(r.default,{type:"next"})]})]})]})}},53424:(e,t,s)=>{s.r(t),s.d(t,{default:()=>r});var n=s(86087),l=s(76231),a=s(27723),i=s(95664),o=s(10790);const r=()=>{const{currentStep:e,plugins:t,fetchPlugins:s,setPlugins:r,setEnablePluginInstallation:c,setIsContentLoading:d}=(0,l.useNewOnboardingData)();(0,n.useEffect)(()=>{(async()=>{try{d(!0),await s()}catch(e){console.log(e)}finally{d(!1)}})()},[s]);const p=(e,s)=>{const n=t.map(t=>t.slug===e.slug?{...t,checked:s,toProcess:s}:t);r(n);const l=n.some(e=>e.toProcess);c(l)};return(0,o.jsxs)("div",{className:`cmplz-modal-content-step ${e}`,children:[(0,o.jsx)("p",{children:(0,a.__)("You want more Really Simple Plugins? Select below plugins you'd like to install for free! It only takes 10 seconds..","complianz-gdpr")}),t&&t.map((e,t)=>(0,o.jsx)(i.default,{plugin:e,handleChange:p},e.slug))]})}},76231:(e,t,s)=>{s.r(t),s.d(t,{steps:()=>i,useNewOnboardingData:()=>c});var n=s(9588),l=s(81621),a=s(27723);const i={welcome:{title:(0,a.__)("Welcome to Complianz","complianz-gdpr"),prevButton:(0,a.__)("No, Thanks","complianz-gdpr"),nextButton:(0,a.__)("Continue","complianz-gdpr"),prevButtonGoTo:"newsletter",nextButtonGoTo:"terms"},terms:{title:(0,a.__)("Terms and Conditions","complianz-gdpr"),prevButton:(0,a.__)("Dismiss","complianz-gdpr"),nextButton:(0,a.__)("Continue","complianz-gdpr"),prevButtonGoTo:"newsletter",nextButtonGoTo:"newsletter"},newsletter:{title:(0,a.__)("Get tips and tricks","complianz-gdpr"),prevButton:(0,a.__)("Skip","complianz-gdpr"),nextButton:(0,a.__)("Continue","complianz-gdpr"),prevButtonGoTo:"plugins",nextButtonGoTo:"plugins"},plugins:{title:(0,a.__)("Install quickly for free","complianz-gdpr"),prevButton:(0,a.__)("Skip","complianz-gdpr"),nextButton:(0,a.__)("Continue","complianz-gdpr"),nextButtonSecondary:(0,a.__)("Install","complianz-gdpr"),nextButtonThird:(0,a.__)("Installing ...","complianz-gdpr"),prevButtonGoTo:"thankYou",nextButtonGoTo:"thankYou"},thankYou:{title:(0,a.__)("You’re almost there...","complianz-gdpr"),nextButton:(0,a.__)("Close","complianz-gdpr"),nextButtonGoTo:!1}},o=[{slug:"complianz-terms-conditions",description:(0,a.__)("Missing Terms & Conditions? Generate now","complianz-gdpr"),status:"not-installed",processing:!1},{slug:"really-simple-ssl",description:(0,a.__)("Really Simple Security? Let’s go","complianz-gdpr"),status:"not-installed",processing:!1}],r=async(e,t,s,l)=>{l(e=>({plugins:e.plugins.map(e=>e.slug===s.slug?{...e,status:"processing"}:e)}));const a={slug:s.slug,plugins:e};try{let e="";if("install_plugin"===t){const l=await n.doAction(t,a);if(!l.request_success)throw new Error("API Error: installing plugin.");const i=l.plugins.find(e=>e.slug===s.slug).status||"not-installed";if("not-installed"===i)throw new Error("Error installing plugin.");e=i}const i=await n.doAction("activate_plugin",a);if(!i.request_success)throw new Error("API Error: installing plugin.");const o=i.plugins.find(e=>e.slug===s.slug).status;if("activated"!==o)throw new Error("Error activating plugin.");e=o,l(t=>({plugins:t.plugins.map(t=>t.slug===s.slug?{...t,status:e}:t)}))}catch(e){l({isInstalling:!1}),console.error("Plugin installation error:",e)}},c=(0,l.vt)((e,t)=>({isModalOpen:!0,isOnboardingComplete:!1,currentStep:"welcome",stepProcessing:!1,isLoading:!1,isContentLoading:!0,setIsContentLoading:t=>e({isContentLoading:t}),nextStepDisabled:!1,prevStepDisabled:!1,wscEmail:"",enableWsc:!1,emailError:"",termsAccepted:!1,wscTerms:"",newsletterAccepted:!1,newsletterEmail:"",newsletterTerms:"",fetchError:!1,fetchErrorMessage:"",fetchDoc:async()=>{e({isLoading:!0,fetchError:!1,fetchErrorMessage:""});const s=t().currentStep;let l="terms"===s?"get_wsc_terms":"get_newsletter_terms";const i=await n.doAction(l);i.request_success||e({fetchError:!0,fetchErrorMessage:(0,a.__)("Something went wrong while downloading the document.","complianz-gdpr")});const o=i.doc;o?"terms"===s?e({wscTerms:o,isLoading:!1}):"newsletter"===s&&e({newsletterTerms:o,isLoading:!1}):e({fetchError:!0,fetchErrorMessage:(0,a.__)("Something went wrong while downloading the document.","complianz-gdpr"),isLoading:!1})},suggestedPlugins:o,plugins:[],fetchPlugins:async()=>{try{const t=await n.doAction("get_recommended_plugins_status",{plugins:o});if(!t.request_success)throw new Error("Error fetching.");const s=t.plugins.map(e=>({...e,checked:"activated"===e.status||!1,toProcess:!1}));e({plugins:s})}catch(e){throw new Error("Api error:",e)}},enablePluginInstallation:!1,isInstalling:!1,setWscEmail:t=>e({wscEmail:t}),setEnableWsc:t=>e({enableWsc:t}),setEmailError:t=>e({emailError:t}),setNewsletterEmail:t=>e({newsletterEmail:t}),setNextStepDisabled:t=>e({nextStepDisabled:t}),setPrevStepDisabled:t=>e({prevStepDisabled:t}),setPlugins:t=>e({plugins:t}),setEnablePluginInstallation:t=>e({enablePluginInstallation:t}),closeModal:async s=>{s&&t().skipStep("onboarding");const n=new URL(window.location.href);n.searchParams.delete("websitescan"),window.history.pushState({},"",n.href),e({isModalOpen:!1})},skipStep:async e=>{if(!(await n.doAction("dismiss_wsc_onboarding",{step:e})).request_success)throw new Error("Error fetching.")},goToPrevStep:()=>e(e=>{const s="welcome"===e.currentStep?"newsletter":i[e.currentStep].prevButtonGoTo;if("newsletter"===s||"plugins"===s){const e="newsletter"===s?"websitescan":"newsletter";t().skipStep(e)}return{...e,currentStep:s,stepProcessing:!1}}),goToNextStep:()=>e(async s=>{let l=i[s.currentStep].nextButtonGoTo;e({stepProcessing:!0});try{switch(s.currentStep){case"welcome":0===s.wscEmail.length?(e({enableWsc:!1}),l="newsletter"):e({enableWsc:!0});break;case"terms":let a=t().wscEmail;if(e({termsAccepted:!0,newsletterEmail:a,isLoading:!0}),!(await n.doAction("signup_wsc",{email:a,timestamp:(new Date).getTime(),url:window.location.href})).request_success)throw new Error("Error fetching.");e({isLoading:!1});break;case"newsletter":let i=t().newsletterEmail;if(e({newsletterAccepted:!0,isLoading:!0}),!(await n.doAction("signup_newsletter",{email:i,timestamp:(new Date).getTime(),url:window.location.href})).request_success)throw new Error("Error fetching.");e({isLoading:!1});break;case"plugins":if(!t().enablePluginInstallation)break;const o=t().plugins.filter(e=>e.toProcess);if(o.length<=0)break;const c=t().suggestedPlugins;e({isInstalling:!0});for(const t of o)"not-installed"===t.status?await r(c,"install_plugin",t,e):"installed"===t.status&&await r(c,"activate_plugin",t,e);return void e({isInstalling:!1,stepProcessing:!1,enablePluginInstallation:!1});case"thankYou":e({isOnboardingComplete:!0}),t().closeModal()}e({currentStep:l,stepProcessing:!1})}catch(t){console.error("Error during step transition:",t),e({stepProcessing:!1})}}),isValidEmail:e=>0===e.length||/^[\w.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(e)}))},82299:(e,t,s)=>{s.r(t),s.d(t,{default:()=>d});var n=s(86087),l=s(27723),a=s(76231),i=s(99418),o=s(99695),r=s(89056),c=s(10790);const d=()=>{const{currentStep:e,wscTerms:t,isContentLoading:s,setIsContentLoading:d,fetchError:p,fetchDoc:u}=(0,a.useNewOnboardingData)();(0,n.useEffect)(()=>{(async()=>{try{d(!0),await u()}catch(e){console.log(e)}finally{d(!1)}})()},[u]);const g=i.A.sanitize(t,{ADD_ATTR:["target"]});return(0,c.jsx)("div",{className:`cmplz-modal-content-step ${e}`,children:s?(0,c.jsx)(o.default,{}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("p",{children:(0,l.__)("Great! You're a few minutes away from getting started with the Website Scan. You just need to look over the Terms and Conditions. If you agree, please continue.","complianz-gdpr")}),p&&(0,c.jsx)(r.default,{children:(0,c.jsx)("button",{className:"button button-default",onClick:u,children:(0,l.__)("Try again!","complianz-gdpr")})}),g&&(0,c.jsx)("div",{className:"wrap-terms",dangerouslySetInnerHTML:{__html:g}})]})})}},89056:(e,t,s)=>{s.r(t),s.d(t,{default:()=>a});var n=s(76231),l=s(10790);const a=({children:e})=>{const{fetchErrorMessage:t}=(0,n.useNewOnboardingData)();return(0,l.jsxs)("div",{className:"cmplz-onboarding__error",children:[(0,l.jsx)("p",{children:t}),(0,l.jsx)("div",{children:e})]})}},89877:(e,t,s)=>{s.r(t),s.d(t,{default:()=>p});var n=s(27723),l=s(86087),a=s(76231),i=s(99418),o=s(19541),r=s(99695),c=s(89056),d=s(10790);const p=()=>{const{wscEmail:e,newsletterEmail:t,setNewsletterEmail:s,emailError:p,setEmailError:u,enableWsc:g,isValidEmail:m,setNextStepDisabled:C,currentStep:h,newsletterTerms:w,isContentLoading:f,setIsContentLoading:x,fetchError:L,fetchDoc:_}=(0,a.useNewOnboardingData)();(0,l.useEffect)(()=>{s(e)},[e]),(0,l.useEffect)(()=>{(async()=>{try{x(!0),await _()}catch(e){console.log(e)}finally{x(!1)}})()},[_]);const b=i.A.sanitize(w,{ADD_ATTR:["target"]});return(0,d.jsx)("div",{className:`cmplz-modal-content-step ${h}`,children:f?(0,d.jsx)(r.default,{}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("p",{children:(0,n.__)("We want you to get the most out of Complianz. So over the next week we'll be sending eight tips and tricks to your inbox - be sure to keep a lookout.","complianz-gdpr")}),L&&(0,d.jsx)(c.default,{children:(0,d.jsx)("button",{className:"button button-default",onClick:_,children:(0,n.__)("Try again!","complianz-gdpr")})}),b&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"wrap-terms",dangerouslySetInnerHTML:{__html:b}}),!g&&(0,d.jsx)(o.default,{type:"email",onChange:e=>{const t=e.target.value;s(t);const l=m(t);u(l?"":(0,n.__)("Please enter a valid email address.","complianz-gdpr")),C(!l)},value:t,placeholder:(0,n.__)("Your email address","complianz-gdpr"),error:p})]})]})})}},91677:(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});var n=s(27723),l=s(76231),a=s(10790);const i=()=>{const{currentStep:e,enableWsc:t}=(0,l.useNewOnboardingData)();return(0,a.jsx)("div",{className:`cmplz-modal-content-step ${e}`,children:(0,a.jsxs)("div",{className:"cmplz_thank-you",children:[t?(0,a.jsxs)("svg",{width:"180",height:"162",viewBox:"0 0 180 162",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsxs)("g",{"clip-path":"url(#clip0_5_14)",children:[(0,a.jsx)("path",{d:"M178.421 61.792C178.356 61.7921 178.294 61.7723 178.241 61.7355L90.9063 0.913294C90.6405 0.729062 90.3247 0.630605 90.0013 0.631157C89.6779 0.631708 89.3624 0.731241 89.0973 0.916377L2.392 61.7348C2.32342 61.7829 2.23854 61.8018 2.15603 61.7874C2.07353 61.7729 2.00015 61.7262 1.95204 61.6576C1.90394 61.589 1.88505 61.5042 1.89953 61.4217C1.91401 61.3392 1.96068 61.2658 2.02926 61.2177L88.7346 0.39921C89.1059 0.140058 89.5475 0.000750853 90.0003 6.81078e-06C90.453 -0.000737231 90.8952 0.137116 91.2672 0.395047L178.602 61.217C178.657 61.2555 178.698 61.3105 178.72 61.3742C178.742 61.4378 178.743 61.5067 178.722 61.5709C178.702 61.635 178.662 61.6911 178.608 61.7309C178.554 61.7706 178.488 61.7921 178.421 61.792Z",fill:"#3F3D56"}),(0,a.jsx)("path",{d:"M7.34644 63.948L90.0871 2.62705L173.455 68.3113L94.3502 115.206L51.4029 105.417L7.34644 63.948Z",fill:"#E6E6E6"}),(0,a.jsx)("path",{d:"M55.028 144.171H14.1521C13.9012 144.171 13.6526 144.122 13.4206 144.026C13.1886 143.93 12.9778 143.79 12.8002 143.612C12.6226 143.435 12.4817 143.224 12.3856 142.993C12.2895 142.761 12.24 142.512 12.24 142.261C12.24 142.01 12.2895 141.762 12.3856 141.53C12.4817 141.298 12.6226 141.087 12.8002 140.91C12.9778 140.733 13.1886 140.592 13.4206 140.496C13.6526 140.4 13.9012 140.351 14.1521 140.351H55.028C55.279 140.351 55.5276 140.4 55.7596 140.496C55.9916 140.592 56.2024 140.733 56.38 140.91C56.5576 141.087 56.6985 141.298 56.7946 141.53C56.8907 141.762 56.9402 142.01 56.9402 142.261C56.9402 142.512 56.8907 142.761 56.7946 142.993C56.6985 143.224 56.5576 143.435 56.38 143.612C56.2024 143.79 55.9916 143.93 55.7596 144.026C55.5276 144.122 55.279 144.171 55.028 144.171Z",fill:"#009FFF"}),(0,a.jsx)("path",{d:"M28.8175 135.96H14.1521C13.9012 135.961 13.6526 135.912 13.4206 135.816C13.1886 135.72 12.9778 135.579 12.8002 135.402C12.6226 135.224 12.4817 135.014 12.3856 134.782C12.2895 134.55 12.24 134.302 12.24 134.051C12.24 133.8 12.2895 133.551 12.3856 133.319C12.4817 133.087 12.6226 132.877 12.8002 132.699C12.9778 132.522 13.1886 132.381 13.4206 132.286C13.6526 132.19 13.9012 132.141 14.1521 132.141H28.8175C29.0685 132.141 29.3171 132.19 29.5491 132.286C29.781 132.381 29.9919 132.522 30.1694 132.699C30.347 132.877 30.4879 133.087 30.5841 133.319C30.6802 133.551 30.7297 133.8 30.7297 134.051C30.7297 134.302 30.6802 134.55 30.5841 134.782C30.4879 135.014 30.347 135.224 30.1694 135.402C29.9919 135.579 29.781 135.72 29.5491 135.816C29.3171 135.912 29.0685 135.961 28.8175 135.96Z",fill:"#009FFF"}),(0,a.jsx)("path",{d:"M91.2506 98.103C90.94 98.1033 90.6322 98.0431 90.3446 97.9255L39.3159 76.7522V10.4762C39.3166 9.89015 39.5497 9.32829 39.9641 8.91388C40.3785 8.49948 40.9404 8.26636 41.5264 8.26568H139.421C140.007 8.26636 140.569 8.49948 140.984 8.91388C141.398 9.32829 141.631 9.89015 141.632 10.4762V76.7985L141.536 76.8395L92.1841 97.9152C91.8886 98.0394 91.5712 98.1032 91.2506 98.103Z",fill:"white"}),(0,a.jsx)("path",{d:"M91.2506 98.2607C90.9193 98.2611 90.5911 98.1969 90.2844 98.0717L39.158 76.8577V10.4762C39.1587 9.84827 39.4084 9.24626 39.8524 8.80225C40.2965 8.35824 40.8985 8.10848 41.5264 8.10777H139.421C140.049 8.10848 140.651 8.35824 141.095 8.80225C141.539 9.24626 141.789 9.84827 141.79 10.4762V76.9027L92.246 98.0603C91.9309 98.1929 91.5924 98.261 91.2506 98.2607ZM39.7895 76.4358L90.5252 97.4878C90.9985 97.6798 91.5284 97.6766 91.9993 97.479L141.158 76.4858V10.4762C141.157 10.0157 140.974 9.57427 140.649 9.24866C140.323 8.92306 139.882 8.7399 139.421 8.73935H41.5264C41.0659 8.7399 40.6245 8.92306 40.2989 9.24866C39.9733 9.57427 39.7901 10.0157 39.7895 10.4762L39.7895 76.4358Z",fill:"#3F3D56"}),(0,a.jsx)("path",{d:"M177.789 61.1605H177.726L141.474 76.6405L91.8727 97.8205C91.6789 97.902 91.4709 97.9445 91.2606 97.9456C91.0504 97.9467 90.8419 97.9063 90.6473 97.8268L39.4737 76.5963L2.33049 61.1857L2.27375 61.1605H2.21053C1.62446 61.1611 1.06258 61.3942 0.648164 61.8086C0.233751 62.2231 0.00065029 62.7849 0 63.371V159.371C0.000651124 159.957 0.233751 160.519 0.648164 160.933C1.06258 161.348 1.62446 161.581 2.21053 161.582H177.789C178.376 161.581 178.937 161.348 179.352 160.933C179.766 160.519 179.999 159.957 180 159.371V63.371C179.999 62.7849 179.766 62.2231 179.352 61.8086C178.937 61.3942 178.376 61.1611 177.789 61.1605ZM179.368 159.371C179.368 159.79 179.202 160.191 178.906 160.487C178.61 160.783 178.208 160.95 177.789 160.95H2.21053C1.79181 160.95 1.3903 160.783 1.09423 160.487C0.798155 160.191 0.631746 159.79 0.631579 159.371V63.371C0.632152 62.9627 0.790535 62.5704 1.07361 62.2761C1.35669 61.9819 1.74256 61.8084 2.15054 61.7921L39.4737 77.2784L90.4043 98.411C90.9556 98.6353 91.5734 98.6319 92.1222 98.4015L141.474 77.3257L177.853 61.7921C178.26 61.8096 178.645 61.9836 178.927 62.2777C179.209 62.5717 179.367 62.9633 179.368 63.371V159.371Z",fill:"#3F3D56"}),(0,a.jsx)("path",{d:"M109.564 39.4021C109.32 39.1578 108.913 39.1578 108.658 39.4021L87.9714 60.0891L88.8771 60.9947L109.564 40.3077C109.808 40.0533 109.808 39.6463 109.564 39.4021ZM90.6476 52.8847C90.4034 52.6405 89.9964 52.6405 89.742 52.8847L85.2546 57.3722L86.1602 58.2778L90.6476 53.7904C90.8918 53.5461 90.8918 53.1391 90.6476 52.8847ZM116.3 25.6345C118.254 25.6345 120.096 26.4282 121.439 27.8731C124.034 30.6612 123.79 35.0673 121.093 37.7536L108.882 49.9643C108.638 50.2085 108.638 50.6156 108.882 50.87C109.127 51.1142 109.534 51.1142 109.788 50.87L121.978 38.6796C125.204 35.4539 125.377 30.01 122.151 26.7945C120.533 25.1766 118.417 24.3727 116.3 24.3727C114.184 24.3727 112.057 25.1766 110.439 26.7945L82.558 54.6756L83.4637 55.5813L111.345 27.7001C112.668 26.3671 114.428 25.6345 116.3 25.6345ZM92.4691 40.2466C92.2248 40.0024 91.8178 40.0024 91.5634 40.2466L79.8513 51.9689L80.7569 52.8746L92.4792 41.1523C92.7235 40.908 92.7235 40.501 92.4691 40.2466ZM95.5828 38.0385L108.638 24.9832C110.683 22.9379 113.4 21.8084 116.3 21.8084C118.498 21.8084 120.584 22.4699 122.355 23.6706C122.609 23.8436 122.955 23.8232 123.169 23.5993C123.454 23.3144 123.403 22.8362 123.067 22.6123C121.032 21.2284 118.661 20.5263 116.3 20.5263C113.207 20.5263 110.103 21.7067 107.743 24.0674L94.6772 37.1431C94.433 37.3873 94.433 37.7943 94.6772 38.0487C94.9316 38.2929 95.3386 38.2929 95.5828 38.0385ZM106.674 53.0781C106.43 52.8338 106.023 52.8338 105.769 53.0781L96.0611 62.7856C95.8168 63.0298 95.8168 63.4368 96.0611 63.6912C96.3053 63.9354 96.7123 63.9354 96.9565 63.6912L106.664 53.9837C106.918 53.7293 106.918 53.3325 106.674 53.0781ZM121.276 42.9737L96.0712 68.1888L96.9769 69.0843L122.192 43.8691C122.436 43.6249 122.436 43.2179 122.192 42.9635C121.927 42.7295 121.531 42.7295 121.276 42.9737ZM126.364 25.9397C126.14 25.6039 125.662 25.5429 125.377 25.8278C125.163 26.0415 125.133 26.3875 125.306 26.6419C126.486 28.4022 127.127 30.4679 127.127 32.6455C127.127 35.3318 126.16 37.8655 124.38 39.8498C124.146 40.1042 124.146 40.4908 124.39 40.7351C124.644 40.9895 125.072 40.9895 125.306 40.7249C129.04 36.5732 129.386 30.4679 126.364 25.9397ZM112.668 37.194L116.585 33.2763C116.829 33.0321 116.829 32.6251 116.585 32.3707C116.341 32.1265 115.934 32.1265 115.68 32.3707L111.777 36.2731C111.554 36.4959 111.518 36.8917 111.718 37.1349C111.968 37.4371 112.409 37.4524 112.668 37.194ZM119.363 29.4096C118.498 28.5956 117.399 28.1784 116.29 28.1784C115.181 28.1784 114.011 28.6159 113.136 29.4808L92.8557 49.7812C92.6115 50.0254 92.6115 50.4324 92.8557 50.6868C93.1 50.931 93.507 50.931 93.7614 50.6868L113.95 30.4781C114.469 29.9591 115.14 29.5724 115.873 29.4808C116.87 29.3587 117.837 29.6844 118.539 30.3865C119.139 30.9868 119.475 31.7907 119.475 32.6353C119.475 33.4799 119.139 34.2837 118.539 34.8841L90.6578 62.7652L91.5634 63.6607L119.312 35.9118C121.093 34.1514 121.185 31.1496 119.363 29.4096Z",fill:"#009FFF"}),(0,a.jsx)("path",{d:"M63.3974 52.3149C63.1532 52.5591 63.1532 52.956 63.3974 53.2002L75.8187 65.6215C76.0283 65.8312 76.3591 65.883 76.6084 65.7233C76.9574 65.4984 76.994 65.0263 76.7183 64.7495L64.2837 52.3149C64.0394 52.0707 63.6426 52.0707 63.3984 52.3149H63.3974ZM76.9513 77.3672C77.1955 77.6114 77.5923 77.6114 77.8365 77.3672C78.0808 77.123 78.0808 76.7262 77.8365 76.482L64.4149 63.0502C64.1707 62.8059 63.7739 62.8059 63.5297 63.0502C63.2854 63.2944 63.2854 63.6912 63.5297 63.9354L76.9513 77.3672ZM96.9972 69.0538L77.5008 49.5573L72.1077 44.1642C69.7775 41.8442 66.735 40.674 63.6925 40.674C60.65 40.674 57.5973 41.834 55.2772 44.1642C50.627 48.8145 50.627 56.3546 55.2772 60.9947L70.2354 75.9528L77.0123 82.7298C77.2565 82.974 77.6534 82.974 77.8976 82.7298C77.8976 82.7298 77.8976 82.7196 77.9078 82.7196C77.918 82.7196 77.918 82.7196 77.9281 82.7094L79.4443 81.1933L79.5766 84.9175C79.5766 85.0091 79.5969 85.0905 79.6376 85.1719C79.6682 85.2533 79.7089 85.3347 79.7801 85.3958C80.0243 85.64 80.4212 85.64 80.6654 85.3958L80.6959 85.3653C80.7061 85.3551 80.7162 85.3449 80.7264 85.3347L96.8446 69.2166L96.9972 69.0538ZM88.1241 76.1563L79.8238 67.8561C79.6142 67.6465 79.2835 67.5946 79.0342 67.7543C78.6852 67.9792 78.6486 68.4514 78.9243 68.7281L87.2378 77.0416L85.4774 78.802L61.4732 54.7977C60.883 54.2076 60.5574 53.424 60.5574 52.5795C60.5574 51.7349 60.883 50.9615 61.4732 50.3612C62.0633 49.771 62.8469 49.4454 63.6914 49.4454C64.536 49.4454 65.3094 49.771 65.9097 50.3612L89.914 74.3756L88.1231 76.1563H88.1241ZM93.4561 70.8243L90.2844 67.6526C90.0747 67.443 89.744 67.3911 89.4947 67.5508C89.1457 67.7757 89.1091 68.2479 89.3848 68.5246L92.5698 71.7096L90.7993 73.4802L66.7848 49.4759C65.9301 48.6212 64.8108 48.1938 63.6813 48.1938C62.5518 48.1938 61.4325 48.6212 60.5777 49.4759C58.8682 51.1854 58.8682 53.9633 60.5777 55.683L84.582 79.6873L80.8272 83.4421L80.6949 79.6262C80.6847 79.3617 80.5219 79.1276 80.2777 79.036C80.0335 78.9445 79.7587 78.9953 79.5654 79.1887L77.398 81.3561L72.0558 76.0139L56.1513 60.1094C54.1366 58.0946 53.0274 55.4185 53.0274 52.5795C53.0274 49.7405 54.1762 46.9686 56.2612 44.9417C58.2292 43.0276 60.8769 41.9409 63.6212 41.9256C66.4928 41.9103 69.1924 43.0205 71.2112 45.0495L76.0039 49.8422L95.2155 69.0538L93.4551 70.8243H93.4561ZM57.6176 49.3334C57.7499 49.0892 57.7194 48.784 57.5159 48.5805C57.2208 48.2854 56.712 48.3566 56.5085 48.7331C54.86 51.8163 55.3281 55.7339 57.9229 58.3389L60.4129 60.8288C60.6225 61.0384 60.9532 61.0903 61.2025 60.9306C61.5515 60.7057 61.5881 60.2336 61.3124 59.9568L58.8092 57.4536C56.6214 55.2556 56.2246 51.9384 57.6186 49.3334H57.6176ZM63.6823 44.439C62.3696 44.439 61.0672 44.7544 59.8868 45.3751C59.5103 45.5685 59.4289 46.0773 59.7342 46.3825C59.9377 46.586 60.2328 46.6166 60.4871 46.4843C61.524 45.9328 62.6881 45.658 63.9041 45.6936C65.691 45.7465 67.3801 46.5189 68.6439 47.7827L86.2782 65.417C86.4879 65.6266 86.8186 65.6785 87.0679 65.5188C87.4169 65.2939 87.4535 64.8217 87.1778 64.545L69.4519 46.8191C67.8543 45.2317 65.7683 44.438 63.6823 44.438V44.439Z",fill:"#009FFF"})]}),(0,a.jsx)("defs",{children:(0,a.jsx)("clipPath",{id:"clip0_5_14",children:(0,a.jsx)("rect",{width:"180",height:"161.582",fill:"white"})})})]}):(0,a.jsx)("svg",{width:"220",id:"uuid-198c2dc3-7c1a-4465-a4e3-f3a903a50160",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1637.7 329.2",children:(0,a.jsx)("path",{fill:"#009fff",d:"m803.4,73.7c-21.4,0-43.5,9-53.4,23v-18.1h-63.7v250.6h63.7v-91.6c9.9,14,32,23,53.4,23,49.7,0,83-45.2,83-93.7s-33.3-93.2-83-93.2Zm-16.8,134.7c-21.8,0-36.6-16.8-36.6-40.7s14.4-41.9,36.6-41.9c21,0,36.2,17.7,36.2,41.9s-15.3,40.7-36.2,40.7Zm-181.4-134.7c-29.6,0-47.2,13.1-59.6,29.2-10.3-17.7-26.3-29.2-54.2-29.2-20.9,0-37,6.2-48.9,23.4v-18.5h-63.7v177.1h63.7v-93.2c0-20.5,7-35.3,27.1-35.3s23,13.6,23,37.4v91.2h63.7v-93.2c0-20.5,7-35.3,27.1-35.3s23,13.6,23,37.4v91.2h63.7v-106.6c0-42.4-17.3-75.6-64.9-75.6Zm-336.5,0c-50.9,0-95.3,40.7-95.3,93.2s43.5,93.7,95.7,93.7,95.3-41.5,95.3-94.1-43.9-92.8-95.7-92.8Zm.4,136.4c-22.2,0-33.7-22.6-33.7-43.1s11.5-42.7,33.3-42.7c23,0,33.7,22.6,33.7,42.3s-11.1,43.5-33.3,43.5Zm-171.7.4c-23,0-38.2-21.4-38.2-42.7,0-25.1,15.6-43.5,38.2-43.5,13.1,0,25.1,5.3,35.3,16.4l36.6-34.1c-18.9-22.6-41.9-32.9-71.9-32.9C40.7,73.7,0,116.4,0,167.8s41.5,92.8,97.4,92.8c34.5,0,56.3-11.5,77.6-38.2l-42.7-30c-9.8,11.5-18.5,18.1-34.9,18.1ZM1378.5,73.7c-21,0-39.4,7.8-50.9,23.4v-18.5h-63.7v177.1h63.7v-93.2c0-21.8,10.7-35.3,29.2-35.3s25.1,13.6,25.1,37.4v91.2h63.7v-106.5c-.1-42.4-19.4-75.6-67.1-75.6Zm-218.7,0c-32,0-61.6,4.9-85,18.1l19.7,38.2c18.5-9.5,38.6-15.2,63.3-15.2,18.1,0,26.3,4.1,26.3,16.4v6.2c-57.5,2.5-125.3,13.1-125.3,68.2,0,33.7,30,55,64.1,55,30.8,0,41.9-4.9,61.2-18.5v13.6h63.7v-112.6c-.1-50.5-29.3-69.4-88-69.4Zm24.2,129c-10.3,9-21,12.7-37,12.7-10.3,0-20.5-6.2-20.5-16.4,0-19.7,30.4-25.9,57.5-25.9v29.6Zm-283.2,53h63.7V0h-63.7v255.7Zm651.6-48.7l85.3-128.4h-173.8v49.3h64.9c5.8,0,10.7-.4,10.7-.4,0,0-4.1,4.5-7.4,9.4l-80.1,118.7h175.4v-49.3h-63c-3.6.1-8.9.4-12,.7Zm-570.3-127.6v176.2h63.7V79.2c-10,3.5-20.7,5.5-31.6,5.5s-22-1.9-32.1-5.3Z"})}),(0,a.jsx)("p",{className:"cmplz-h2",children:(0,n.__)("Thanks for joining us!","complianz-gdpr")}),t&&(0,a.jsx)("p",{children:(0,n.__)("We've sent you an email - all you need to do is confirm your email address and you can start using Complianz.","complianz-gdpr")})]})})}},95664:(e,t,s)=>{s.r(t),s.d(t,{default:()=>o}),s(86087);var n=s(45111),l=s(76231),a=s(10790);const i={"not-installed":{label:"Check to Install!",iconColor:"blue",iconName:"info",checked:!1},installed:{label:"Check to Activate!",iconColor:"orange",iconName:"info",checked:!1},activated:{label:"Installed!",iconColor:"green",iconName:"circle-check",checked:!0},processing:{label:"Processing ...",iconColor:"grey",iconName:"loading",checked:!0}},o=({plugin:e,className:t="",handleChange:s,...o})=>{const{isInstalling:r}=(0,l.useNewOnboardingData)();return(0,a.jsxs)("div",{className:`cmplz-websitescan-input-wrapper plugin-checkbox ${e.slug}`,children:[(0,a.jsxs)("label",{children:[(0,a.jsx)("input",{className:`${t} cmplz-websitescan-input`,checked:e.checked,disabled:!!r||i[e.status].checked,onChange:t=>{s(e,t.target.checked)},type:"checkbox",...o}),(0,a.jsx)("span",{className:"checkmark"}),(0,a.jsx)("span",{className:"description",children:e.description})]}),e.status&&(0,a.jsx)(n.default,{name:i[e.status]?.iconName,color:i[e.status]?.iconColor,size:14,tooltip:i[e.status]?.label})]})}}}]);