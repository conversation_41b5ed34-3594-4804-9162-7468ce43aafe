"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[9091,9758],{79758:(e,t,a)=>{a.r(t),a.d(t,{default:()=>g});var o=a(86087),i=a(9588),n=a(4219),s=a(52043),r=a(56427),d=a(99091),l=a(32828),c=a(10790);const g=(0,o.memo)(({type:e="action",style:t="tertiary",label:a,onClick:g,href:m="",target:p="",disabled:u,action:f,field:h,children:C})=>{if(!a&&!C)return null;const b=(h&&h.button_text?h.button_text:a)||C,{fetchFieldsData:_,showSavedSettingsNotice:L}=(0,n.default)(),{setInitialLoadCompleted:k,setProgress:w}=(0,d.UseCookieScanData)(),{setProgressLoaded:x}=(0,l.default)(),{selectedSubMenuItem:v}=(0,s.default)(),[I,y]=(0,o.useState)(!1),D=`button cmplz-button button--${t} button-${e}`,P=async e=>{await i.doAction(h.action,{}).then(e=>{e.success&&(_(v),"reset_settings"===e.id&&(k(!1),w(0),x(!1)),L(e.message))})},S=h&&h.warn?h.warn:"";return"action"===e?(0,c.jsxs)(c.Fragment,{children:[r.__experimentalConfirmDialog&&(0,c.jsx)(r.__experimentalConfirmDialog,{isOpen:I,onConfirm:async()=>{y(!1),await P()},onCancel:()=>{y(!1)},children:S}),(0,c.jsx)("button",{className:D,onClick:async t=>{if("action"!==e||!g)return"action"===e&&f?r.__experimentalConfirmDialog?void(h&&h.warn?y(!0):await P()):void await P():void(window.location.href=h.url);g(t)},disabled:u,children:b})]}):"link"===e?(0,c.jsx)("a",{className:D,href:m,target:p,children:b}):void 0})},99091:(e,t,a)=>{a.r(t),a.d(t,{UseCookieScanData:()=>n});var o=a(81621),i=a(9588);const n=(0,o.vt)((e,t)=>({initialLoadCompleted:!1,setInitialLoadCompleted:t=>e({initialLoadCompleted:t}),iframeLoaded:!1,loading:!1,nextPage:!1,progress:0,cookies:[],lastLoadedIframe:"",setIframeLoaded:t=>e({iframeLoaded:t}),setLastLoadedIframe:t=>e(e=>({lastLoadedIframe:t})),setProgress:t=>e({progress:t}),fetchProgress:()=>(e({loading:!0}),i.doAction("get_scan_progress",{}).then(t=>(e({initialLoadCompleted:!0,loading:!1,nextPage:t.next_page,progress:t.progress,cookies:t.cookies}),t)))}))}}]);