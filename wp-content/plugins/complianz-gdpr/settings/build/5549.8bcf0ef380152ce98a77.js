"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[5549],{979:(t,e,n)=>{n.d(e,{A:()=>o});var r=n(92998),a=n(70551),i=n(94188);function o(t,e){(0,a.A)(2,arguments);var n=(0,i.A)(e);return(0,r.default)(t,-n)}},9411:(t,e,n)=>{n.d(e,{A:()=>i});var r=n(10123),a=n(70551);function i(t){(0,a.A)(1,arguments);var e=(0,r.A)(t),n=e.getUTCDay(),i=(n<1?7:0)+n-1;return e.setUTCDate(e.getUTCDate()-i),e.setUTCHours(0,0,0,0),e}},10123:(t,e,n)=>{n.d(e,{A:()=>i});var r=n(82284),a=n(70551);function i(t){(0,a.A)(1,arguments);var e=Object.prototype.toString.call(t);return t instanceof Date||"object"===(0,r.A)(t)&&"[object Date]"===e?new Date(t.getTime()):"number"==typeof t||"[object Number]"===e?new Date(t):("string"!=typeof t&&"[object String]"!==e||"undefined"==typeof console||(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn((new Error).stack)),new Date(NaN))}},14323:(t,e,n)=>{n.d(e,{A:()=>o});var r=n(94188),a=n(10123),i=n(70551);function o(t,e){return(0,i.A)(2,arguments),function(t,e){(0,i.A)(2,arguments);var n=(0,a.A)(t).getTime(),o=(0,r.A)(e);return new Date(n+o)}(t,-(0,r.A)(e))}},24127:(t,e,n)=>{n.d(e,{A:()=>o});var r=n(10123),a=n(70551),i=n(9411);function o(t){(0,a.A)(1,arguments);var e=(0,r.A)(t),n=e.getUTCFullYear(),o=new Date(0);o.setUTCFullYear(n+1,0,4),o.setUTCHours(0,0,0,0);var u=(0,i.A)(o),d=new Date(0);d.setUTCFullYear(n,0,4),d.setUTCHours(0,0,0,0);var s=(0,i.A)(d);return e.getTime()>=u.getTime()?n+1:e.getTime()>=s.getTime()?n:n-1}},31053:(t,e,n)=>{n.d(e,{A:()=>d});var r=n(10123),a=n(9411),i=n(24127),o=n(70551),u=6048e5;function d(t){(0,o.A)(1,arguments);var e=(0,r.A)(t),n=(0,a.A)(e).getTime()-function(t){(0,o.A)(1,arguments);var e=(0,i.A)(t),n=new Date(0);return n.setUTCFullYear(e,0,4),n.setUTCHours(0,0,0,0),(0,a.A)(n)}(e).getTime();return Math.round(n/u)+1}},31127:(t,e,n)=>{n.r(e),n.d(e,{default:()=>i});var r=n(10123),a=n(70551);function i(t){(0,a.A)(1,arguments);var e=(0,r.A)(t);return e.setHours(0,0,0,0),e}},36014:(t,e,n)=>{n.d(e,{A:()=>r});const r=n(56438).default},41109:(t,e,n)=>{n.d(e,{ef:()=>i,lJ:()=>u,xM:()=>o});var r=["D","DD"],a=["YY","YYYY"];function i(t){return-1!==r.indexOf(t)}function o(t){return-1!==a.indexOf(t)}function u(t,e,n){if("YYYY"===t)throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(e,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if("YY"===t)throw new RangeError("Use `yy` instead of `YY` (in `".concat(e,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if("D"===t)throw new RangeError("Use `d` instead of `D` (in `".concat(e,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if("DD"===t)throw new RangeError("Use `dd` instead of `DD` (in `".concat(e,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}},50464:(t,e,n)=>{n.d(e,{A:()=>d});var r=n(10123),a=n(70551),i=n(89742),o=n(94188),u=n(71858);function d(t,e){var n,d,s,l,c,f,h,m;(0,a.A)(1,arguments);var g=(0,r.A)(t),v=g.getUTCFullYear(),w=(0,u.q)(),b=(0,o.A)(null!==(n=null!==(d=null!==(s=null!==(l=null==e?void 0:e.firstWeekContainsDate)&&void 0!==l?l:null==e||null===(c=e.locale)||void 0===c||null===(f=c.options)||void 0===f?void 0:f.firstWeekContainsDate)&&void 0!==s?s:w.firstWeekContainsDate)&&void 0!==d?d:null===(h=w.locale)||void 0===h||null===(m=h.options)||void 0===m?void 0:m.firstWeekContainsDate)&&void 0!==n?n:1);if(!(b>=1&&b<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var y=new Date(0);y.setUTCFullYear(v+1,0,b),y.setUTCHours(0,0,0,0);var p=(0,i.A)(y,e),T=new Date(0);T.setUTCFullYear(v,0,b),T.setUTCHours(0,0,0,0);var A=(0,i.A)(T,e);return g.getTime()>=p.getTime()?v+1:g.getTime()>=A.getTime()?v:v-1}},56438:(t,e,n)=>{n.r(e),n.d(e,{default:()=>l});var r={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function a(t){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}var i,o={date:a({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:a({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:a({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},u={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function d(t){return function(e,n){var r;if("formatting"===(null!=n&&n.context?String(n.context):"standalone")&&t.formattingValues){var a=t.defaultFormattingWidth||t.defaultWidth,i=null!=n&&n.width?String(n.width):a;r=t.formattingValues[i]||t.formattingValues[a]}else{var o=t.defaultWidth,u=null!=n&&n.width?String(n.width):t.defaultWidth;r=t.values[u]||t.values[o]}return r[t.argumentCallback?t.argumentCallback(e):e]}}function s(t){return function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.width,a=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],i=e.match(a);if(!i)return null;var o,u=i[0],d=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],s=Array.isArray(d)?function(t,e){for(var n=0;n<t.length;n++)if(e(t[n]))return n}(d,function(t){return t.test(u)}):function(t,e){for(var n in t)if(t.hasOwnProperty(n)&&e(t[n]))return n}(d,function(t){return t.test(u)});return o=t.valueCallback?t.valueCallback(s):s,{value:o=n.valueCallback?n.valueCallback(o):o,rest:e.slice(u.length)}}}const l={code:"en-US",formatDistance:function(t,e,n){var a,i=r[t];return a="string"==typeof i?i:1===e?i.one:i.other.replace("{{count}}",e.toString()),null!=n&&n.addSuffix?n.comparison&&n.comparison>0?"in "+a:a+" ago":a},formatLong:o,formatRelative:function(t,e,n,r){return u[t]},localize:{ordinalNumber:function(t,e){var n=Number(t),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:d({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:d({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:function(t){return t-1}}),month:d({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:d({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:d({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(i={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:function(t){return parseInt(t,10)}},function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.match(i.matchPattern);if(!n)return null;var r=n[0],a=t.match(i.parsePattern);if(!a)return null;var o=i.valueCallback?i.valueCallback(a[0]):a[0];return{value:o=e.valueCallback?e.valueCallback(o):o,rest:t.slice(r.length)}}),era:s({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:s({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(t){return t+1}}),month:s({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:s({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:s({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}}},66212:(t,e,n)=>{n.r(e),n.d(e,{default:()=>i});var r=n(10123),a=n(70551);function i(t){(0,a.A)(1,arguments);var e=(0,r.A)(t);return e.setHours(23,59,59,999),e}},67044:(t,e,n)=>{function r(t){var e=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return e.setUTCFullYear(t.getFullYear()),t.getTime()-e.getTime()}n.d(e,{A:()=>r})},70551:(t,e,n)=>{function r(t,e){if(e.length<t)throw new TypeError(t+" argument"+(t>1?"s":"")+" required, but only "+e.length+" present")}n.d(e,{A:()=>r})},71858:(t,e,n)=>{n.d(e,{q:()=>a});var r={};function a(){return r}},72346:(t,e,n)=>{n.r(e),n.d(e,{default:()=>E});var r=n(99768),a=n(14323),i=n(10123),o=n(70551),u=n(31053),d=n(24127),s=n(96894),l=n(50464);function c(t,e){for(var n=t<0?"-":"",r=Math.abs(t).toString();r.length<e;)r="0"+r;return n+r}const f=function(t,e){var n=t.getUTCFullYear(),r=n>0?n:1-n;return c("yy"===e?r%100:r,e.length)},h=function(t,e){var n=t.getUTCMonth();return"M"===e?String(n+1):c(n+1,2)},m=function(t,e){return c(t.getUTCDate(),e.length)},g=function(t,e){return c(t.getUTCHours()%12||12,e.length)},v=function(t,e){return c(t.getUTCHours(),e.length)},w=function(t,e){return c(t.getUTCMinutes(),e.length)},b=function(t,e){return c(t.getUTCSeconds(),e.length)},y=function(t,e){var n=e.length,r=t.getUTCMilliseconds();return c(Math.floor(r*Math.pow(10,n-3)),e.length)};function p(t,e){var n=t>0?"-":"+",r=Math.abs(t),a=Math.floor(r/60),i=r%60;if(0===i)return n+String(a);var o=e||"";return n+String(a)+o+c(i,2)}function T(t,e){return t%60==0?(t>0?"-":"+")+c(Math.abs(t)/60,2):A(t,e)}function A(t,e){var n=e||"",r=t>0?"-":"+",a=Math.abs(t);return r+c(Math.floor(a/60),2)+n+c(a%60,2)}const C={G:function(t,e,n){var r=t.getUTCFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(t,e,n){if("yo"===e){var r=t.getUTCFullYear(),a=r>0?r:1-r;return n.ordinalNumber(a,{unit:"year"})}return f(t,e)},Y:function(t,e,n,r){var a=(0,l.A)(t,r),i=a>0?a:1-a;return"YY"===e?c(i%100,2):"Yo"===e?n.ordinalNumber(i,{unit:"year"}):c(i,e.length)},R:function(t,e){return c((0,d.A)(t),e.length)},u:function(t,e){return c(t.getUTCFullYear(),e.length)},Q:function(t,e,n){var r=Math.ceil((t.getUTCMonth()+1)/3);switch(e){case"Q":return String(r);case"QQ":return c(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(t,e,n){var r=Math.ceil((t.getUTCMonth()+1)/3);switch(e){case"q":return String(r);case"qq":return c(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(t,e,n){var r=t.getUTCMonth();switch(e){case"M":case"MM":return h(t,e);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(t,e,n){var r=t.getUTCMonth();switch(e){case"L":return String(r+1);case"LL":return c(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(t,e,n,r){var a=(0,s.A)(t,r);return"wo"===e?n.ordinalNumber(a,{unit:"week"}):c(a,e.length)},I:function(t,e,n){var r=(0,u.A)(t);return"Io"===e?n.ordinalNumber(r,{unit:"week"}):c(r,e.length)},d:function(t,e,n){return"do"===e?n.ordinalNumber(t.getUTCDate(),{unit:"date"}):m(t,e)},D:function(t,e,n){var r=function(t){(0,o.A)(1,arguments);var e=(0,i.A)(t),n=e.getTime();e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0);var r=n-e.getTime();return Math.floor(r/864e5)+1}(t);return"Do"===e?n.ordinalNumber(r,{unit:"dayOfYear"}):c(r,e.length)},E:function(t,e,n){var r=t.getUTCDay();switch(e){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(t,e,n,r){var a=t.getUTCDay(),i=(a-r.weekStartsOn+8)%7||7;switch(e){case"e":return String(i);case"ee":return c(i,2);case"eo":return n.ordinalNumber(i,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(t,e,n,r){var a=t.getUTCDay(),i=(a-r.weekStartsOn+8)%7||7;switch(e){case"c":return String(i);case"cc":return c(i,e.length);case"co":return n.ordinalNumber(i,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(t,e,n){var r=t.getUTCDay(),a=0===r?7:r;switch(e){case"i":return String(a);case"ii":return c(a,e.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(t,e,n){var r=t.getUTCHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(t,e,n){var r,a=t.getUTCHours();switch(r=12===a?"noon":0===a?"midnight":a/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(t,e,n){var r,a=t.getUTCHours();switch(r=a>=17?"evening":a>=12?"afternoon":a>=4?"morning":"night",e){case"B":case"BB":case"BBB":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(t,e,n){if("ho"===e){var r=t.getUTCHours()%12;return 0===r&&(r=12),n.ordinalNumber(r,{unit:"hour"})}return g(t,e)},H:function(t,e,n){return"Ho"===e?n.ordinalNumber(t.getUTCHours(),{unit:"hour"}):v(t,e)},K:function(t,e,n){var r=t.getUTCHours()%12;return"Ko"===e?n.ordinalNumber(r,{unit:"hour"}):c(r,e.length)},k:function(t,e,n){var r=t.getUTCHours();return 0===r&&(r=24),"ko"===e?n.ordinalNumber(r,{unit:"hour"}):c(r,e.length)},m:function(t,e,n){return"mo"===e?n.ordinalNumber(t.getUTCMinutes(),{unit:"minute"}):w(t,e)},s:function(t,e,n){return"so"===e?n.ordinalNumber(t.getUTCSeconds(),{unit:"second"}):b(t,e)},S:function(t,e){return y(t,e)},X:function(t,e,n,r){var a=(r._originalDate||t).getTimezoneOffset();if(0===a)return"Z";switch(e){case"X":return T(a);case"XXXX":case"XX":return A(a);default:return A(a,":")}},x:function(t,e,n,r){var a=(r._originalDate||t).getTimezoneOffset();switch(e){case"x":return T(a);case"xxxx":case"xx":return A(a);default:return A(a,":")}},O:function(t,e,n,r){var a=(r._originalDate||t).getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+p(a,":");default:return"GMT"+A(a,":")}},z:function(t,e,n,r){var a=(r._originalDate||t).getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+p(a,":");default:return"GMT"+A(a,":")}},t:function(t,e,n,r){var a=r._originalDate||t;return c(Math.floor(a.getTime()/1e3),e.length)},T:function(t,e,n,r){return c((r._originalDate||t).getTime(),e.length)}};var M=n(91788),D=n(67044),k=n(41109),x=n(94188),S=n(71858),U=n(36014),P=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,W=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Y=/^'([^]*?)'?$/,N=/''/g,q=/[a-zA-Z]/;function E(t,e,n){var u,d,s,l,c,f,h,m,g,v,w,b,y,p,T,A,E,O;(0,o.A)(2,arguments);var H=String(e),F=(0,S.q)(),j=null!==(u=null!==(d=null==n?void 0:n.locale)&&void 0!==d?d:F.locale)&&void 0!==u?u:U.A,z=(0,x.A)(null!==(s=null!==(l=null!==(c=null!==(f=null==n?void 0:n.firstWeekContainsDate)&&void 0!==f?f:null==n||null===(h=n.locale)||void 0===h||null===(m=h.options)||void 0===m?void 0:m.firstWeekContainsDate)&&void 0!==c?c:F.firstWeekContainsDate)&&void 0!==l?l:null===(g=F.locale)||void 0===g||null===(v=g.options)||void 0===v?void 0:v.firstWeekContainsDate)&&void 0!==s?s:1);if(!(z>=1&&z<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var L=(0,x.A)(null!==(w=null!==(b=null!==(y=null!==(p=null==n?void 0:n.weekStartsOn)&&void 0!==p?p:null==n||null===(T=n.locale)||void 0===T||null===(A=T.options)||void 0===A?void 0:A.weekStartsOn)&&void 0!==y?y:F.weekStartsOn)&&void 0!==b?b:null===(E=F.locale)||void 0===E||null===(O=E.options)||void 0===O?void 0:O.weekStartsOn)&&void 0!==w?w:0);if(!(L>=0&&L<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!j.localize)throw new RangeError("locale must contain localize property");if(!j.formatLong)throw new RangeError("locale must contain formatLong property");var Q=(0,i.A)(t);if(!(0,r.default)(Q))throw new RangeError("Invalid time value");var G=(0,D.A)(Q),X=(0,a.A)(Q,G),B={firstWeekContainsDate:z,weekStartsOn:L,locale:j,_originalDate:Q};return H.match(W).map(function(t){var e=t[0];return"p"===e||"P"===e?(0,M.A[e])(t,j.formatLong):t}).join("").match(P).map(function(r){if("''"===r)return"'";var a,i,o=r[0];if("'"===o)return(i=(a=r).match(Y))?i[1].replace(N,"'"):a;var u=C[o];if(u)return null!=n&&n.useAdditionalWeekYearTokens||!(0,k.xM)(r)||(0,k.lJ)(r,e,String(t)),null!=n&&n.useAdditionalDayOfYearTokens||!(0,k.ef)(r)||(0,k.lJ)(r,e,String(t)),u(X,r,j.localize,B);if(o.match(q))throw new RangeError("Format string contains an unescaped latin alphabet character `"+o+"`");return r}).join("")}},82284:(t,e,n)=>{function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}n.d(e,{A:()=>r})},89742:(t,e,n)=>{n.d(e,{A:()=>u});var r=n(10123),a=n(70551),i=n(94188),o=n(71858);function u(t,e){var n,u,d,s,l,c,f,h;(0,a.A)(1,arguments);var m=(0,o.q)(),g=(0,i.A)(null!==(n=null!==(u=null!==(d=null!==(s=null==e?void 0:e.weekStartsOn)&&void 0!==s?s:null==e||null===(l=e.locale)||void 0===l||null===(c=l.options)||void 0===c?void 0:c.weekStartsOn)&&void 0!==d?d:m.weekStartsOn)&&void 0!==u?u:null===(f=m.locale)||void 0===f||null===(h=f.options)||void 0===h?void 0:h.weekStartsOn)&&void 0!==n?n:0);if(!(g>=0&&g<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var v=(0,r.A)(t),w=v.getUTCDay(),b=(w<g?7:0)+w-g;return v.setUTCDate(v.getUTCDate()-b),v.setUTCHours(0,0,0,0),v}},91788:(t,e,n)=>{n.d(e,{A:()=>i});var r=function(t,e){switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});default:return e.date({width:"full"})}},a=function(t,e){switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});default:return e.time({width:"full"})}};const i={p:a,P:function(t,e){var n,i=t.match(/(P+)(p+)?/)||[],o=i[1],u=i[2];if(!u)return r(t,e);switch(o){case"P":n=e.dateTime({width:"short"});break;case"PP":n=e.dateTime({width:"medium"});break;case"PPP":n=e.dateTime({width:"long"});break;default:n=e.dateTime({width:"full"})}return n.replace("{{date}}",r(o,e)).replace("{{time}}",a(u,e))}}},92998:(t,e,n)=>{n.r(e),n.d(e,{default:()=>o});var r=n(94188),a=n(10123),i=n(70551);function o(t,e){(0,i.A)(2,arguments);var n=(0,a.A)(t),o=(0,r.A)(e);return isNaN(o)?new Date(NaN):o?(n.setDate(n.getDate()+o),n):n}},94188:(t,e,n)=>{function r(t){if(null===t||!0===t||!1===t)return NaN;var e=Number(t);return isNaN(e)?e:e<0?Math.ceil(e):Math.floor(e)}n.d(e,{A:()=>r})},96894:(t,e,n)=>{n.d(e,{A:()=>l});var r=n(10123),a=n(89742),i=n(50464),o=n(70551),u=n(94188),d=n(71858),s=6048e5;function l(t,e){(0,o.A)(1,arguments);var n=(0,r.A)(t),l=(0,a.A)(n,e).getTime()-function(t,e){var n,r,s,l,c,f,h,m;(0,o.A)(1,arguments);var g=(0,d.q)(),v=(0,u.A)(null!==(n=null!==(r=null!==(s=null!==(l=null==e?void 0:e.firstWeekContainsDate)&&void 0!==l?l:null==e||null===(c=e.locale)||void 0===c||null===(f=c.options)||void 0===f?void 0:f.firstWeekContainsDate)&&void 0!==s?s:g.firstWeekContainsDate)&&void 0!==r?r:null===(h=g.locale)||void 0===h||null===(m=h.options)||void 0===m?void 0:m.firstWeekContainsDate)&&void 0!==n?n:1),w=(0,i.A)(t,e),b=new Date(0);return b.setUTCFullYear(w,0,v),b.setUTCHours(0,0,0,0),(0,a.A)(b,e)}(n,e).getTime();return Math.round(l/s)+1}},99768:(t,e,n)=>{n.r(e),n.d(e,{default:()=>o});var r=n(82284),a=n(70551),i=n(10123);function o(t){if((0,a.A)(1,arguments),!function(t){return(0,a.A)(1,arguments),t instanceof Date||"object"===(0,r.A)(t)&&"[object Date]"===Object.prototype.toString.call(t)}(t)&&"number"!=typeof t)return!1;var e=(0,i.A)(t);return!isNaN(Number(e))}}}]);