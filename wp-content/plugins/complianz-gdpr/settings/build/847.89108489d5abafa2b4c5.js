"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[847,2489,2921,4759,5228,8985,9091,9758],{10800:(e,t,r)=>{r.r(t),r.d(t,{default:()=>z});var s=r(86087),a=r(51609),c=r(9957),n=r(91071),l=r(62133),o=r(81351),i=r(85357),d=r(54150),p=r(12579),u=r(10790),h="Switch",[m,f]=(0,l.A)(h),[b,g]=m(h),_=a.forwardRef((e,t)=>{const{__scopeSwitch:r,name:s,checked:l,defaultChecked:i,required:d,disabled:m,value:f="on",onCheckedChange:g,form:_,...k}=e,[x,y]=a.useState(null),j=(0,n.s)(t,e=>y(e)),w=a.useRef(!1),z=!x||_||!!x.closest("form"),[S,A]=(0,o.i)({prop:l,defaultProp:i??!1,onChange:g,caller:h});return(0,u.jsxs)(b,{scope:r,checked:S,disabled:m,children:[(0,u.jsx)(p.sG.button,{type:"button",role:"switch","aria-checked":S,"aria-required":d,"data-state":C(S),"data-disabled":m?"":void 0,disabled:m,value:f,...k,ref:j,onClick:(0,c.m)(e.onClick,e=>{A(e=>!e),z&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})}),z&&(0,u.jsx)(v,{control:x,bubbles:!w.current,name:s,value:f,checked:S,required:d,disabled:m,form:_,style:{transform:"translateX(-100%)"}})]})});_.displayName=h;var k="SwitchThumb",x=a.forwardRef((e,t)=>{const{__scopeSwitch:r,...s}=e,a=g(k,r);return(0,u.jsx)(p.sG.span,{"data-state":C(a.checked),"data-disabled":a.disabled?"":void 0,...s,ref:t})});x.displayName=k;var v=a.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:s=!0,...c},l)=>{const o=a.useRef(null),p=(0,n.s)(o,l),h=(0,i.Z)(r),m=(0,d.X)(t);return a.useEffect(()=>{const e=o.current;if(!e)return;const t=window.HTMLInputElement.prototype,a=Object.getOwnPropertyDescriptor(t,"checked").set;if(h!==r&&a){const t=new Event("click",{bubbles:s});a.call(e,r),e.dispatchEvent(t)}},[h,r,s]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...c,tabIndex:-1,ref:p,style:{...c.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function C(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var y=_,j=x,w=r(4219);const z=(0,s.memo)(({value:e,onChange:t,required:r,disabled:s,className:a,label:c,id:n})=>{const{getField:l}=(0,w.default)();let o=e;return"0"!==e&&"1"!==e||(o="1"===e),(0,u.jsx)("div",{className:"cmplz-input-group cmplz-switch-group",children:(0,u.jsx)(y,{className:"cmplz-switch-root "+a,checked:o,onCheckedChange:e=>{"banner"===l(n).data_target&&(e=e?"1":"0"),t(e)},disabled:s,required:r,children:(0,u.jsx)(j,{className:"cmplz-switch-thumb"})})})})},25228:(e,t,r)=>{r.r(t),r.d(t,{default:()=>o});var s=r(86087),a=r(21366),c=r(45111),n=r(27723),l=r(10790);const o=(0,s.memo)(({value:e=!1,onChange:t,required:r,defaultValue:s,disabled:o,options:i={},canBeEmpty:d=!0,label:p})=>{if(Array.isArray(i)){let e={};i.map(t=>{e[t.value]=t.label}),i=e}return d?(""===e||!1===e||0===e)&&(e="0",i={0:(0,n.__)("Select an option","complianz-gdpr"),...i}):e||(e=Object.keys(i)[0]),(0,l.jsx)("div",{className:"cmplz-input-group cmplz-select-group",children:(0,l.jsxs)(a.bL,{value:e,defaultValue:s,onValueChange:t,required:r,disabled:o&&!Array.isArray(o),children:[(0,l.jsxs)(a.l9,{className:"cmplz-select-group__trigger",children:[(0,l.jsx)(a.WT,{}),(0,l.jsx)(c.default,{name:"chevron-down"})]}),(0,l.jsxs)(a.UC,{className:"cmplz-select-group__content",position:"popper",children:[(0,l.jsx)(a.PP,{className:"cmplz-select-group__scroll-button",children:(0,l.jsx)(c.default,{name:"chevron-up"})}),(0,l.jsx)(a.LM,{className:"cmplz-select-group__viewport",children:(0,l.jsx)(a.YJ,{children:Object.entries(i).map(([e,t])=>(0,l.jsx)(a.q7,{disabled:Array.isArray(o)&&o.includes(e),className:"cmplz-select-group__item",value:e,children:(0,l.jsx)(a.p4,{children:t})},e))})}),(0,l.jsx)(a.wn,{className:"cmplz-select-group__scroll-button",children:(0,l.jsx)(c.default,{name:"chevron-down"})})]})]})},p)})},32489:(e,t,r)=>{r.r(t),r.d(t,{default:()=>c});var s=r(51609),a=r(10790);const c=(0,s.memo)(({value:e,onChange:t,required:r,disabled:c,id:n,name:l,placeholder:o})=>{const i=n||l,[d,p]=(0,s.useState)("");return(0,s.useEffect)(()=>{p(e||"")},[e]),(0,s.useEffect)(()=>{if(e===d)return;const r=setTimeout(()=>{t(d)},400);return()=>{clearTimeout(r)}},[d]),(0,a.jsx)("div",{className:"cmplz-input-group cmplz-text-input-group",children:(0,a.jsx)("input",{type:"text",id:i,name:l,value:d,onChange:e=>(e=>{p(e)})(e.target.value),required:r,disabled:c,className:"cmplz-text-input-group__input",placeholder:o})})})},32921:(e,t,r)=>{r.r(t),r.d(t,{default:()=>n});var s=r(27723),a=r(44124),c=r(10790);const n=e=>(0,c.jsxs)(c.Fragment,{children:[" ",(0,c.jsx)(a.default,{url:e,target:"_blank",rel:"noopener noreferrer",text:(0,s.__)("For more information, please read this %sarticle%s.","complianz-gdpr")})," "]})},34759:(e,t,r)=>{r.r(t),r.d(t,{default:()=>n});var s=r(81621),a=r(16535),c=r(9588);const n=(0,s.vt)((e,t)=>({integrationsLoaded:!1,fetching:!1,services:[],plugins:[],scripts:[],placeholders:[],blockedScripts:[],setScript:(t,r)=>{e((0,a.Ay)(e=>{if("block_script"===r){let r=e.blockedScripts;if(t.urls){for(const[e,s]of Object.entries(t.urls)){if(!s||0===s.length)continue;let e=!1;for(const[t,a]of Object.entries(r))s===t&&(e=!0);e||(r[s]=s)}e.blockedScripts=r}}const s=e.scripts[r].findIndex(e=>e.id===t.id);-1!==s&&(e.scripts[r][s]=t)}))},fetchIntegrationsData:async()=>{if(t().fetching)return;e({fetching:!0});const{services:r,plugins:s,scripts:a,placeholders:c,blocked_scripts:n}=await l();let o=a;o.block_script&&o.block_script.length>0&&o.block_script.forEach((e,t)=>{e.id=t}),o.add_script&&o.add_script.length>0&&o.add_script.forEach((e,t)=>{e.id=t}),o.whitelist_script&&o.whitelist_script.length>0&&o.whitelist_script.forEach((e,t)=>{e.id=t}),e(()=>({integrationsLoaded:!0,services:r,plugins:s,scripts:o,fetching:!1,placeholders:c,blockedScripts:n}))},addScript:r=>{e({fetching:!0}),t().scripts[r]&&Array.isArray(t().scripts[r])||e((0,a.Ay)(e=>{e.scripts[r]=[]})),e((0,a.Ay)(e=>{e.scripts[r].push({name:"general",id:e.scripts[r].length,enable:!0})}));let s=t().scripts;return c.doAction("update_scripts",{scripts:s}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},saveScript:(r,s)=>{e({fetching:!0}),t().scripts[s]&&Array.isArray(t().scripts[s])||e((0,a.Ay)(e=>{e.scripts[s]=[]})),e((0,a.Ay)(e=>{const t=e.scripts[s].findIndex(e=>e.id===r.id);-1!==t&&(e.scripts[s][t]=r)}));let n=t().scripts;return c.doAction("update_scripts",{scripts:n}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},deleteScript:(r,s)=>{e({fetching:!0}),t().scripts[s]&&Array.isArray(t().scripts[s])||e((0,a.Ay)(e=>{e.scripts[s]=[]})),e((0,a.Ay)(e=>{const t=e.scripts[s].findIndex(e=>e.id===r.id);-1!==t&&e.scripts[s].splice(t,1)}));let n=t().scripts;return c.doAction("update_scripts",{scripts:n}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},updatePluginStatus:async(t,r)=>{e({fetching:!0}),e((0,a.Ay)(e=>{const s=e.plugins.findIndex(e=>e.id===t);-1!==s&&(e.plugins[s].enabled=r)}));const s=await c.doAction("update_plugin_status",{plugin:t,enabled:r}).then(e=>e).catch(e=>{console.error(e)});return e({fetching:!1}),s},updatePlaceholderStatus:async(t,r,s)=>{e({fetching:!0}),s&&e((0,a.Ay)(e=>{const s=e.plugins.findIndex(e=>e.id===t);-1!==s&&(e.plugins[s].placeholder=r?"enabled":"disabled")}));const n=await c.doAction("update_placeholder_status",{id:t,enabled:r}).then(e=>e).catch(e=>{console.error(e)});return e({fetching:!1}),n}})),l=()=>c.doAction("get_integrations_data",{}).then(e=>e).catch(e=>{console.error(e)})},60847:(e,t,r)=>{r.r(t),r.d(t,{default:()=>d}),r(10800);var s=r(32921),a=r(32489),c=r(25228),n=r(27723),l=r(34759),o=r(81366),i=r(10790);const d=e=>{const{setScript:t,fetching:r,placeholders:d}=(0,l.default)(),p=e.script,u=e.type,h=(r,s)=>{let a={...p};a[s]=r,t(a,e.type)};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:[(0,i.jsx)("label",{children:(0,n.__)("Placeholder","complianz-gdpr")}),(0,i.jsx)(o.default,{id:p.id+"placeholder",disabled:r,value:p.enable_placeholder,onChange:e=>h(e,"enable_placeholder"),options:{true:(0,n.__)("Enable placeholder","complianz-gdpr")}})]}),!!p.enable_placeholder&&(0,i.jsxs)(i.Fragment,{children:["block_script"===u&&(0,i.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,i.jsx)(o.default,{id:p.id+"iframe",disabled:r,value:p.iframe||"",onChange:e=>h(e||"","iframe"),options:{true:(0,n.__)("The blocked content is an iframe","complianz-gdpr")}})}),!p.iframe&&(0,i.jsxs)("div",{className:"cmplz-details-row cmplz-details-row",children:[(0,i.jsxs)("p",{children:[(0,n.__)("Enter the div class or ID that should be targeted.","complianz-gdpr"),(0,s.default)("https://complianz.io/integrating-plugins/#placeholder/")]}),(0,i.jsx)(a.default,{disabled:r,value:p.placeholder_class||"",onChange:e=>h(e||"","placeholder_class"),name:"placeholder_class",placeholder:(0,n.__)("Your CSS class","complianz-gdpr")})]}),(0,i.jsx)("div",{className:"cmplz-details-row cmplz-details-row__checkbox",children:(0,i.jsx)(c.default,{disabled:r,value:p.placeholder?p.placeholder:"default",options:d,onChange:e=>h(e||"default","placeholder")})})]})]})}},79758:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var s=r(86087),a=r(9588),c=r(4219),n=r(52043),l=r(56427),o=r(99091),i=r(32828),d=r(10790);const p=(0,s.memo)(({type:e="action",style:t="tertiary",label:r,onClick:p,href:u="",target:h="",disabled:m,action:f,field:b,children:g})=>{if(!r&&!g)return null;const _=(b&&b.button_text?b.button_text:r)||g,{fetchFieldsData:k,showSavedSettingsNotice:x}=(0,c.default)(),{setInitialLoadCompleted:v,setProgress:C}=(0,o.UseCookieScanData)(),{setProgressLoaded:y}=(0,i.default)(),{selectedSubMenuItem:j}=(0,n.default)(),[w,z]=(0,s.useState)(!1),S=`button cmplz-button button--${t} button-${e}`,A=async e=>{await a.doAction(b.action,{}).then(e=>{e.success&&(k(j),"reset_settings"===e.id&&(v(!1),C(0),y(!1)),x(e.message))})},N=b&&b.warn?b.warn:"";return"action"===e?(0,d.jsxs)(d.Fragment,{children:[l.__experimentalConfirmDialog&&(0,d.jsx)(l.__experimentalConfirmDialog,{isOpen:w,onConfirm:async()=>{z(!1),await A()},onCancel:()=>{z(!1)},children:N}),(0,d.jsx)("button",{className:S,onClick:async t=>{if("action"!==e||!p)return"action"===e&&f?l.__experimentalConfirmDialog?void(b&&b.warn?z(!0):await A()):void await A():void(window.location.href=b.url);p(t)},disabled:m,children:_})]}):"link"===e?(0,d.jsx)("a",{className:S,href:u,target:h,children:_}):void 0})},81366:(e,t,r)=>{r.r(t),r.d(t,{default:()=>L});var s=r(51609),a=r(91071),c=r(62133),n=r(9957),l=r(81351),o=r(85357),i=r(54150),d=r(7971),p=r(12579),u=r(10790),h="Checkbox",[m,f]=(0,c.A)(h),[b,g]=m(h);function _(e){const{__scopeCheckbox:t,checked:r,children:a,defaultChecked:c,disabled:n,form:o,name:i,onCheckedChange:d,required:p,value:m="on",internal_do_not_use_render:f}=e,[g,_]=(0,l.i)({prop:r,defaultProp:c??!1,onChange:d,caller:h}),[k,x]=s.useState(null),[v,C]=s.useState(null),y=s.useRef(!1),j=!k||!!o||!!k.closest("form"),w={checked:g,disabled:n,setChecked:_,control:k,setControl:x,name:i,form:o,value:m,hasConsumerStoppedPropagationRef:y,required:p,defaultChecked:!S(c)&&c,isFormControl:j,bubbleInput:v,setBubbleInput:C};return(0,u.jsx)(b,{scope:t,...w,children:z(f)?f(w):a})}var k="CheckboxTrigger",x=s.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...c},l)=>{const{control:o,value:i,disabled:d,checked:h,required:m,setControl:f,setChecked:b,hasConsumerStoppedPropagationRef:_,isFormControl:x,bubbleInput:v}=g(k,e),C=(0,a.s)(l,f),y=s.useRef(h);return s.useEffect(()=>{const e=o?.form;if(e){const t=()=>b(y.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[o,b]),(0,u.jsx)(p.sG.button,{type:"button",role:"checkbox","aria-checked":S(h)?"mixed":h,"aria-required":m,"data-state":A(h),"data-disabled":d?"":void 0,disabled:d,value:i,...c,ref:C,onKeyDown:(0,n.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,n.m)(r,e=>{b(e=>!!S(e)||!e),v&&x&&(_.current=e.isPropagationStopped(),_.current||e.stopPropagation())})})});x.displayName=k;var v=s.forwardRef((e,t)=>{const{__scopeCheckbox:r,name:s,checked:a,defaultChecked:c,required:n,disabled:l,value:o,onCheckedChange:i,form:d,...p}=e;return(0,u.jsx)(_,{__scopeCheckbox:r,checked:a,defaultChecked:c,disabled:l,required:n,onCheckedChange:i,name:s,form:d,value:o,internal_do_not_use_render:({isFormControl:e})=>(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(x,{...p,ref:t,__scopeCheckbox:r}),e&&(0,u.jsx)(w,{__scopeCheckbox:r})]})})});v.displayName=h;var C="CheckboxIndicator",y=s.forwardRef((e,t)=>{const{__scopeCheckbox:r,forceMount:s,...a}=e,c=g(C,r);return(0,u.jsx)(d.C,{present:s||S(c.checked)||!0===c.checked,children:(0,u.jsx)(p.sG.span,{"data-state":A(c.checked),"data-disabled":c.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});y.displayName=C;var j="CheckboxBubbleInput",w=s.forwardRef(({__scopeCheckbox:e,...t},r)=>{const{control:c,hasConsumerStoppedPropagationRef:n,checked:l,defaultChecked:d,required:h,disabled:m,name:f,value:b,form:_,bubbleInput:k,setBubbleInput:x}=g(j,e),v=(0,a.s)(r,x),C=(0,o.Z)(l),y=(0,i.X)(c);s.useEffect(()=>{const e=k;if(!e)return;const t=window.HTMLInputElement.prototype,r=Object.getOwnPropertyDescriptor(t,"checked").set,s=!n.current;if(C!==l&&r){const t=new Event("click",{bubbles:s});e.indeterminate=S(l),r.call(e,!S(l)&&l),e.dispatchEvent(t)}},[k,C,l,n]);const w=s.useRef(!S(l)&&l);return(0,u.jsx)(p.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:d??w.current,required:h,disabled:m,name:f,value:b,form:_,...t,tabIndex:-1,ref:v,style:{...t.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function z(e){return"function"==typeof e}function S(e){return"indeterminate"===e}function A(e){return S(e)?"indeterminate":e?"checked":"unchecked"}w.displayName=j;var N=r(27723),I=r(45111),E=r(86087),P=r(79758);const L=(0,E.memo)(({indeterminate:e,label:t,value:r,id:s,onChange:a,required:c,disabled:n,options:l={}})=>{const[o,i]=(0,E.useState)(!1),[d,p]=(0,E.useState)(!1);let h=r;Array.isArray(h)||(h=""===h?[]:[h]),(0,E.useEffect)(()=>{let e=1===Object.keys(l).length&&"true"===Object.keys(l)[0];i(e)},[]),e&&(r=!0);const m=h;let f=!1;Object.keys(l).length>10&&(f=!0);const b=e=>o?r:m.includes(""+e)||m.includes(parseInt(e)),g=()=>{p(!d)};let _=n&&!Array.isArray(n);return 0===Object.keys(l).length?(0,u.jsx)(u.Fragment,{children:(0,N.__)("No options found","complianz-gdpr")}):(0,u.jsxs)("div",{className:"cmplz-checkbox-group",children:[Object.entries(l).map(([l,i],p)=>(0,u.jsxs)("div",{className:"cmplz-checkbox-group__item"+(!d&&p>9?" cmplz-hidden":""),children:[(0,u.jsx)(v,{className:"cmplz-checkbox-group__checkbox",id:s+"_"+l,checked:b(l),"aria-label":t,disabled:_||Array.isArray(n)&&n.includes(l),required:c,onCheckedChange:e=>((e,t)=>{if(o)a(!r);else{const e=m.includes(""+t)||m.includes(parseInt(t))?m.filter(e=>e!==""+t&&e!==parseInt(t)):[...m,t];a(e)}})(0,l),children:(0,u.jsx)(y,{className:"cmplz-checkbox-group__indicator",children:(0,u.jsx)(I.default,{name:e?"indeterminate":"check",size:14,color:"dark-blue"})})}),(0,u.jsx)("label",{className:"cmplz-checkbox-group__label",htmlFor:s+"_"+l,children:i})]},l)),!d&&f&&(0,u.jsx)(P.default,{onClick:()=>g(),children:(0,N.__)("Show more","complianz-gdpr")}),d&&f&&(0,u.jsx)(P.default,{onClick:()=>g(),children:(0,N.__)("Show less","complianz-gdpr")})]})})},85357:(e,t,r)=>{r.d(t,{Z:()=>a});var s=r(51609);function a(e){const t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},99091:(e,t,r)=>{r.r(t),r.d(t,{UseCookieScanData:()=>c});var s=r(81621),a=r(9588);const c=(0,s.vt)((e,t)=>({initialLoadCompleted:!1,setInitialLoadCompleted:t=>e({initialLoadCompleted:t}),iframeLoaded:!1,loading:!1,nextPage:!1,progress:0,cookies:[],lastLoadedIframe:"",setIframeLoaded:t=>e({iframeLoaded:t}),setLastLoadedIframe:t=>e(e=>({lastLoadedIframe:t})),setProgress:t=>e({progress:t}),fetchProgress:()=>(e({loading:!0}),a.doAction("get_scan_progress",{}).then(t=>(e({initialLoadCompleted:!0,loading:!1,nextPage:t.next_page,progress:t.progress,cookies:t.cookies}),t)))}))}}]);