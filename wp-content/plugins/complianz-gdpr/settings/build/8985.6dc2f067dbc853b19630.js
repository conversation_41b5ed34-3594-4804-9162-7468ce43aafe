"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[8985,9091,9758],{79758:(e,t,n)=>{n.r(t),n.d(t,{default:()=>u});var r=n(86087),o=n(9588),a=n(4219),s=n(52043),c=n(56427),i=n(99091),d=n(32828),l=n(10790);const u=(0,r.memo)(({type:e="action",style:t="tertiary",label:n,onClick:u,href:p="",target:h="",disabled:m,action:f,field:b,children:k})=>{if(!n&&!k)return null;const C=(b&&b.button_text?b.button_text:n)||k,{fetchFieldsData:g,showSavedSettingsNotice:_}=(0,a.default)(),{setInitialLoadCompleted:x,setProgress:v}=(0,i.UseCookieScanData)(),{setProgressLoaded:y}=(0,d.default)(),{selectedSubMenuItem:j}=(0,s.default)(),[w,I]=(0,r.useState)(!1),L=`button cmplz-button button--${t} button-${e}`,S=async e=>{await o.doAction(b.action,{}).then(e=>{e.success&&(g(j),"reset_settings"===e.id&&(x(!1),v(0),y(!1)),_(e.message))})},z=b&&b.warn?b.warn:"";return"action"===e?(0,l.jsxs)(l.Fragment,{children:[c.__experimentalConfirmDialog&&(0,l.jsx)(c.__experimentalConfirmDialog,{isOpen:w,onConfirm:async()=>{I(!1),await S()},onCancel:()=>{I(!1)},children:z}),(0,l.jsx)("button",{className:L,onClick:async t=>{if("action"!==e||!u)return"action"===e&&f?c.__experimentalConfirmDialog?void(b&&b.warn?I(!0):await S()):void await S():void(window.location.href=b.url);u(t)},disabled:m,children:C})]}):"link"===e?(0,l.jsx)("a",{className:L,href:p,target:h,children:C}):void 0})},81366:(e,t,n)=>{n.r(t),n.d(t,{default:()=>R});var r=n(51609),o=n(91071),a=n(62133),s=n(9957),c=n(81351),i=n(85357),d=n(54150),l=n(7971),u=n(12579),p=n(10790),h="Checkbox",[m,f]=(0,a.A)(h),[b,k]=m(h);function C(e){const{__scopeCheckbox:t,checked:n,children:o,defaultChecked:a,disabled:s,form:i,name:d,onCheckedChange:l,required:u,value:m="on",internal_do_not_use_render:f}=e,[k,C]=(0,c.i)({prop:n,defaultProp:a??!1,onChange:l,caller:h}),[g,_]=r.useState(null),[x,v]=r.useState(null),y=r.useRef(!1),j=!g||!!i||!!g.closest("form"),w={checked:k,disabled:s,setChecked:C,control:g,setControl:_,name:d,form:i,value:m,hasConsumerStoppedPropagationRef:y,required:u,defaultChecked:!L(a)&&a,isFormControl:j,bubbleInput:x,setBubbleInput:v};return(0,p.jsx)(b,{scope:t,...w,children:I(f)?f(w):o})}var g="CheckboxTrigger",_=r.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...a},c)=>{const{control:i,value:d,disabled:l,checked:h,required:m,setControl:f,setChecked:b,hasConsumerStoppedPropagationRef:C,isFormControl:_,bubbleInput:x}=k(g,e),v=(0,o.s)(c,f),y=r.useRef(h);return r.useEffect(()=>{const e=i?.form;if(e){const t=()=>b(y.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[i,b]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":L(h)?"mixed":h,"aria-required":m,"data-state":S(h),"data-disabled":l?"":void 0,disabled:l,value:d,...a,ref:v,onKeyDown:(0,s.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,s.m)(n,e=>{b(e=>!!L(e)||!e),x&&_&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})})});_.displayName=g;var x=r.forwardRef((e,t)=>{const{__scopeCheckbox:n,name:r,checked:o,defaultChecked:a,required:s,disabled:c,value:i,onCheckedChange:d,form:l,...u}=e;return(0,p.jsx)(C,{__scopeCheckbox:n,checked:o,defaultChecked:a,disabled:c,required:s,onCheckedChange:d,name:r,form:l,value:i,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(_,{...u,ref:t,__scopeCheckbox:n}),e&&(0,p.jsx)(w,{__scopeCheckbox:n})]})})});x.displayName=h;var v="CheckboxIndicator",y=r.forwardRef((e,t)=>{const{__scopeCheckbox:n,forceMount:r,...o}=e,a=k(v,n);return(0,p.jsx)(l.C,{present:r||L(a.checked)||!0===a.checked,children:(0,p.jsx)(u.sG.span,{"data-state":S(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});y.displayName=v;var j="CheckboxBubbleInput",w=r.forwardRef(({__scopeCheckbox:e,...t},n)=>{const{control:a,hasConsumerStoppedPropagationRef:s,checked:c,defaultChecked:l,required:h,disabled:m,name:f,value:b,form:C,bubbleInput:g,setBubbleInput:_}=k(j,e),x=(0,o.s)(n,_),v=(0,i.Z)(c),y=(0,d.X)(a);r.useEffect(()=>{const e=g;if(!e)return;const t=window.HTMLInputElement.prototype,n=Object.getOwnPropertyDescriptor(t,"checked").set,r=!s.current;if(v!==c&&n){const t=new Event("click",{bubbles:r});e.indeterminate=L(c),n.call(e,!L(c)&&c),e.dispatchEvent(t)}},[g,v,c,s]);const w=r.useRef(!L(c)&&c);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:l??w.current,required:h,disabled:m,name:f,value:b,form:C,...t,tabIndex:-1,ref:x,style:{...t.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function I(e){return"function"==typeof e}function L(e){return"indeterminate"===e}function S(e){return L(e)?"indeterminate":e?"checked":"unchecked"}w.displayName=j;var z=n(27723),N=n(45111),P=n(86087),E=n(79758);const R=(0,P.memo)(({indeterminate:e,label:t,value:n,id:r,onChange:o,required:a,disabled:s,options:c={}})=>{const[i,d]=(0,P.useState)(!1),[l,u]=(0,P.useState)(!1);let h=n;Array.isArray(h)||(h=""===h?[]:[h]),(0,P.useEffect)(()=>{let e=1===Object.keys(c).length&&"true"===Object.keys(c)[0];d(e)},[]),e&&(n=!0);const m=h;let f=!1;Object.keys(c).length>10&&(f=!0);const b=e=>i?n:m.includes(""+e)||m.includes(parseInt(e)),k=()=>{u(!l)};let C=s&&!Array.isArray(s);return 0===Object.keys(c).length?(0,p.jsx)(p.Fragment,{children:(0,z.__)("No options found","complianz-gdpr")}):(0,p.jsxs)("div",{className:"cmplz-checkbox-group",children:[Object.entries(c).map(([c,d],u)=>(0,p.jsxs)("div",{className:"cmplz-checkbox-group__item"+(!l&&u>9?" cmplz-hidden":""),children:[(0,p.jsx)(x,{className:"cmplz-checkbox-group__checkbox",id:r+"_"+c,checked:b(c),"aria-label":t,disabled:C||Array.isArray(s)&&s.includes(c),required:a,onCheckedChange:e=>((e,t)=>{if(i)o(!n);else{const e=m.includes(""+t)||m.includes(parseInt(t))?m.filter(e=>e!==""+t&&e!==parseInt(t)):[...m,t];o(e)}})(0,c),children:(0,p.jsx)(y,{className:"cmplz-checkbox-group__indicator",children:(0,p.jsx)(N.default,{name:e?"indeterminate":"check",size:14,color:"dark-blue"})})}),(0,p.jsx)("label",{className:"cmplz-checkbox-group__label",htmlFor:r+"_"+c,children:d})]},c)),!l&&f&&(0,p.jsx)(E.default,{onClick:()=>k(),children:(0,z.__)("Show more","complianz-gdpr")}),l&&f&&(0,p.jsx)(E.default,{onClick:()=>k(),children:(0,z.__)("Show less","complianz-gdpr")})]})})},85357:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(51609);function o(e){const t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},99091:(e,t,n)=>{n.r(t),n.d(t,{UseCookieScanData:()=>a});var r=n(81621),o=n(9588);const a=(0,r.vt)((e,t)=>({initialLoadCompleted:!1,setInitialLoadCompleted:t=>e({initialLoadCompleted:t}),iframeLoaded:!1,loading:!1,nextPage:!1,progress:0,cookies:[],lastLoadedIframe:"",setIframeLoaded:t=>e({iframeLoaded:t}),setLastLoadedIframe:t=>e(e=>({lastLoadedIframe:t})),setProgress:t=>e({progress:t}),fetchProgress:()=>(e({loading:!0}),o.doAction("get_scan_progress",{}).then(t=>(e({initialLoadCompleted:!0,loading:!1,nextPage:t.next_page,progress:t.progress,cookies:t.cookies}),t)))}))}}]);