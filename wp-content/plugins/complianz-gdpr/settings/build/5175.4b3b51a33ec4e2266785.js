"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[1629,2010,5175,6946],{45175:(e,t,s)=>{s.r(t),s.d(t,{default:()=>c});var i=s(27723),l=s(46946),a=s(4219),n=s(86087),d=s(81629),r=s(10790);const c=(0,n.memo)(e=>{const{updateField:t,setChangedField:s}=(0,a.default)(),{documentsLoaded:c,fetchData:o}=(0,d.default)();(0,n.useEffect)(()=>{c||o()},[]);let m=e.field,p=m.value;return Array.isArray(p)||(p=[]),(0,r.jsxs)("div",{className:"components-base-control cmplz-processor",children:[(0,r.jsx)("div",{children:(0,r.jsx)("button",{onClick:()=>(()=>{let l=e.field.value;Array.isArray(l)||(l=[]);let a={},n=[...l];a.name=(0,i.__)("New processor","complianz-gdpr"),n.push(a),t(m.id,n),s(m.id,n)})(),className:"button button-default",children:(0,i.__)("Add new Processors & Service Providers","complianz-gdpr")})}),(0,r.jsx)("div",{className:"cmplz-panel__list",children:p.map((t,s)=>(0,r.jsx)(l.default,{field:e.field,index:s,processor:t},s))})]})})},46946:(e,t,s)=>{s.r(t),s.d(t,{default:()=>m});var i=s(27723),l=s(45111),a=s(52010),n=s(4219),d=s(86087),r=s(81629),c=s(52043),o=s(10790);const m=(0,d.memo)(e=>{const{updateField:t,setChangedField:s,saveFields:m}=(0,n.default)(),{documentsLoaded:p,documents:u}=(0,r.default)(),{selectedMainMenuItem:g}=(0,c.default)(),[_,h]=wp.element.useState(e.processor.name?e.processor.name:""),[f,v]=wp.element.useState(e.processor.purpose?e.processor.purpose:""),[x,j]=wp.element.useState(e.processor.country?e.processor.country:""),[z,y]=wp.element.useState(e.processor.data?e.processor.data:""),N=(i,l)=>{let a=[...e.field.value];Array.isArray(a)||(a=[]);let n={...a[e.index]};n[l]=i,a[e.index]=n,t(e.field.id,a),s(e.field.id,a)};(0,d.useEffect)(()=>{const e=setTimeout(()=>{N(_,"name")},500);return()=>{clearTimeout(e)}},[_]),(0,d.useEffect)(()=>{const e=setTimeout(()=>{N(z,"data")},500);return()=>{clearTimeout(e)}},[z]),(0,d.useEffect)(()=>{const e=setTimeout(()=>{N(x,"country")},500);return()=>{clearTimeout(e)}},[x]),(0,d.useEffect)(()=>{const e=setTimeout(()=>{N(f,"purpose")},500);return()=>{clearTimeout(e)}},[f]);let w=p?[...u]:[];w.push({id:-1,title:(0,i.__)("A Processing Agreement outside Complianz Privacy Suite","complianz-gdpr"),region:"",service:"",date:""});let b={...e.processor};return b.processing_agreement||(b.processing_agreement=0),(0,o.jsx)(o.Fragment,{children:(0,o.jsx)(a.default,{summary:_,details:(a=>(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{className:"cmplz-details-row",children:[(0,o.jsx)("label",{children:(0,i.__)("Name","complianz-gdpr")}),(0,o.jsx)("input",{onChange:e=>h(e.target.value),type:"text",placeholder:(0,i.__)("Name","complianz-gdpr"),value:_})]}),(0,o.jsxs)("div",{className:"cmplz-details-row",children:[(0,o.jsx)("label",{children:(0,i.__)("Country","complianz-gdpr")}),(0,o.jsx)("input",{onChange:e=>j(e.target.value),type:"text",placeholder:(0,i.__)("Country","complianz-gdpr"),value:x})]}),(0,o.jsxs)("div",{className:"cmplz-details-row",children:[(0,o.jsx)("label",{children:(0,i.__)("Purpose","complianz-gdpr")}),(0,o.jsx)("input",{onChange:e=>v(e.target.value),type:"text",placeholder:(0,i.__)("Purpose","complianz-gdpr"),value:f})]}),(0,o.jsxs)("div",{className:"cmplz-details-row",children:[(0,o.jsx)("label",{children:(0,i.__)("Data","complianz-gdpr")}),(0,o.jsx)("input",{onChange:e=>y(e.target.value),type:"text",placeholder:(0,i.__)("Data","complianz-gdpr"),value:z})]}),(0,o.jsxs)("div",{className:"cmplz-details-row",children:[(0,o.jsx)("label",{children:(0,i.__)("Processing Agreement","complianz-gdpr")}),p&&(0,o.jsxs)("select",{onChange:e=>N(e.target.value,"processing_agreement"),value:a.processing_agreement,children:[(0,o.jsx)("option",{value:"0",children:(0,i.__)("Select an option","complianz-gdpr")}),w.map((e,t)=>(0,o.jsx)("option",{value:e.id,children:e.title},t))]}),!p&&(0,o.jsxs)("div",{className:"cmplz-documents-loader",children:[(0,o.jsx)("div",{children:(0,o.jsx)(l.default,{name:"loading",color:"grey"})}),(0,o.jsx)("div",{children:(0,i.__)("Loading...","complianz-gdpr")})]})]}),(0,o.jsx)("div",{className:"cmplz-details-row__buttons",children:(0,o.jsx)("button",{className:"button button-default cmplz-reset-button",onClick:i=>(async()=>{let i=e.field.value;Array.isArray(i)||(i=[]);let l=[...i];l.hasOwnProperty(e.index)&&l.splice(e.index,1),t(e.field.id,l),s(e.field.id,l),await m(g,!1,!1)})(),children:(0,i.__)("Delete","complianz-gdpr")})})]}))(b)})})})},52010:(e,t,s)=>{s.r(t),s.d(t,{default:()=>n});var i=s(45111),l=s(86087),a=s(10790);const n=e=>{const[t,s]=(0,l.useState)(!1);return(0,a.jsx)("div",{className:"cmplz-panel__list__item",style:e.style?e.style:{},children:(0,a.jsxs)("details",{open:t,children:[(0,a.jsxs)("summary",{onClick:e=>(e=>{e.preventDefault(),s(!t)})(e),children:[e.icon&&(0,a.jsx)(i.default,{name:e.icon}),(0,a.jsx)("h5",{className:"cmplz-panel__list__item__title",children:e.summary}),(0,a.jsx)("div",{className:"cmplz-panel__list__item__comment",children:e.comment}),(0,a.jsx)("div",{className:"cmplz-panel__list__item__icons",children:e.icons}),(0,a.jsx)(i.default,{name:"chevron-down",size:18})]}),(0,a.jsx)("div",{className:"cmplz-panel__list__item__details",children:t&&e.details})]})})}},81629:(e,t,s)=>{s.r(t),s.d(t,{default:()=>d});var i=s(81621),l=s(16535),a=s(9588),n=s(73710);const d=(0,i.vt)((e,t)=>({documentsLoaded:!1,region:"",fileName:"",serviceName:"",fetching:!1,updating:!1,loadingFields:!1,documents:[],regions:[],fields:[],editDocumentId:!1,resetEditDocumentId:t=>{e({editDocumentId:!1,region:"",serviceName:""})},editDocument:async t=>{e({updating:!0}),await a.doAction("load_processing_agreement",{id:t}).then(t=>{e({fields:t.fields,region:t.region,serviceName:t.serviceName,updating:!1,fileName:t.file_name})}).catch(e=>{console.error(e)}),e({editDocumentId:t})},setRegion:t=>{e({region:t})},setServiceName:t=>{e({serviceName:t})},updateField:(s,i)=>{let a=!1,d=!1;e((0,l.Ay)(e=>{e.fields.forEach(function(e,t){e.id===s&&(d=t,a=!0)}),!1!==d&&(e.fields[d].value=i)}));let r=(0,n.updateFieldsListWithConditions)(t().fields);e({fields:r})},save:async(s,i)=>{e({updating:!0});let l=t().editDocumentId;await a.doAction("save_processing_agreement",{fields:t().fields,region:s,serviceName:i,post_id:l}).then(t=>(e({updating:!1}),t)).catch(e=>{console.error(e)}),t().fetchData()},deleteDocuments:async s=>{let i=t().documents.filter(e=>s.includes(e.id));e(e=>({documents:e.documents.filter(e=>!s.includes(e.id))}));let l={};l.documents=i,await a.doAction("delete_processing_agreement",l).then(e=>e).catch(e=>{console.error(e)})},fetchData:async()=>{if(t().fetching)return;e({fetching:!0});const{documents:s,regions:i}=await a.doAction("get_processing_agreements",{}).then(e=>e).catch(e=>{console.error(e)});e(()=>({documentsLoaded:!0,documents:s,regions:i,fetching:!1}))},fetchFields:async t=>{let s={region:t};e({loadingFields:!0});const{fields:i}=await a.doAction("get_processing_agreement_fields",s).then(e=>e).catch(e=>{console.error(e)});let l=(0,n.updateFieldsListWithConditions)(i);e(e=>({fields:l,loadingFields:!1}))}}))}}]);