"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[9582],{99582:(s,l,e)=>{e.r(l),e.d(l,{default:()=>r});var a=e(27723),c=e(86087),t=e(32828),i=e(10790);const r=()=>{const{setFilter:s,filter:l,fetchFilter:e,notices:r,progressLoaded:n}=(0,t.default)();(0,c.useEffect)(()=>{e()},[]);let m=0,p=0;return m=n?r.length:0,p=r.filter(function(s){return"completed"!==s.status}).length,(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("h3",{className:"cmplz-grid-title cmplz-h4",children:(0,a.__)("Progress","complianz-gdpr")}),(0,i.jsx)("div",{className:"cmplz-grid-item-controls",children:(0,i.jsxs)("div",{className:"cmplz-task-switcher-container cmplz-active-filter-"+l,children:[(0,i.jsxs)("a",{href:"#",className:"cmplz-task-switcher cmplz-all-tasks",onClick:()=>s("all"),"data-filter":"all",children:[(0,a.__)("All tasks","complianz-gdpr"),(0,i.jsxs)("span",{className:"rsssl_task_count",children:["(",m,")"]})]}),(0,i.jsxs)("a",{href:"#",className:"cmplz-task-switcher cmplz-remaining-tasks",onClick:()=>s("remaining"),"data-filter":"remaining",children:[(0,a.__)("Remaining tasks","complianz-gdpr"),(0,i.jsxs)("span",{className:"rsssl_task_count",children:["(",p,")"]})]})]})})]})}}}]);