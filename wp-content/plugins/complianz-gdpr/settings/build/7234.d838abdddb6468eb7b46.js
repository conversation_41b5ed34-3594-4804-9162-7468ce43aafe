"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[4098,7234,8985,9091,9758],{54098:(e,t,n)=>{n.r(t),n.d(t,{default:()=>s});var o=n(81621),a=n(9588);const s=(0,o.vt)((e,t)=>({documentsLoaded:!1,fetching:!1,generating:!1,documents:[],downloadUrl:"",regions:[],fields:[],deleteDocuments:async n=>{let o=t().documents.filter(e=>n.includes(e.id));e(e=>({documents:e.documents.filter(e=>!n.includes(e.id))}));let s={};s.documents=o,await a.doAction("delete_proof_of_consent_documents",s).then(e=>e).catch(e=>{console.error(e)})},generateProofOfConsent:async()=>{e({generating:!0}),await a.doAction("generate_proof_of_consent",{}).then(e=>e).catch(e=>{console.error(e)}),await t().fetchData(),e({generating:!1})},fetchData:async()=>{if(t().fetching)return;e({fetching:!0});const{documents:n,regions:o,download_url:s}=await a.doAction("get_proof_of_consent_documents",{}).then(e=>e).catch(e=>{console.error(e)});e(e=>({documentsLoaded:!0,documents:n,regions:o,downloadUrl:s,fetching:!1}))}}))},79758:(e,t,n)=>{n.r(t),n.d(t,{default:()=>u});var o=n(86087),a=n(9588),s=n(4219),r=n(52043),c=n(56427),i=n(99091),l=n(32828),d=n(10790);const u=(0,o.memo)(({type:e="action",style:t="tertiary",label:n,onClick:u,href:p="",target:m="",disabled:h,action:f,field:g,children:b})=>{if(!n&&!b)return null;const _=(g&&g.button_text?g.button_text:n)||b,{fetchFieldsData:k,showSavedSettingsNotice:C}=(0,s.default)(),{setInitialLoadCompleted:x,setProgress:v}=(0,i.UseCookieScanData)(),{setProgressLoaded:w}=(0,l.default)(),{selectedSubMenuItem:y}=(0,r.default)(),[j,z]=(0,o.useState)(!1),L=`button cmplz-button button--${t} button-${e}`,S=async e=>{await a.doAction(g.action,{}).then(e=>{e.success&&(k(y),"reset_settings"===e.id&&(x(!1),v(0),w(!1)),C(e.message))})},P=g&&g.warn?g.warn:"";return"action"===e?(0,d.jsxs)(d.Fragment,{children:[c.__experimentalConfirmDialog&&(0,d.jsx)(c.__experimentalConfirmDialog,{isOpen:j,onConfirm:async()=>{z(!1),await S()},onCancel:()=>{z(!1)},children:P}),(0,d.jsx)("button",{className:L,onClick:async t=>{if("action"!==e||!u)return"action"===e&&f?c.__experimentalConfirmDialog?void(g&&g.warn?z(!0):await S()):void await S():void(window.location.href=g.url);u(t)},disabled:h,children:_})]}):"link"===e?(0,d.jsx)("a",{className:L,href:p,target:m,children:_}):void 0})},81366:(e,t,n)=>{n.r(t),n.d(t,{default:()=>R});var o=n(51609),a=n(91071),s=n(62133),r=n(9957),c=n(81351),i=n(85357),l=n(54150),d=n(7971),u=n(12579),p=n(10790),m="Checkbox",[h,f]=(0,s.A)(m),[g,b]=h(m);function _(e){const{__scopeCheckbox:t,checked:n,children:a,defaultChecked:s,disabled:r,form:i,name:l,onCheckedChange:d,required:u,value:h="on",internal_do_not_use_render:f}=e,[b,_]=(0,c.i)({prop:n,defaultProp:s??!1,onChange:d,caller:m}),[k,C]=o.useState(null),[x,v]=o.useState(null),w=o.useRef(!1),y=!k||!!i||!!k.closest("form"),j={checked:b,disabled:r,setChecked:_,control:k,setControl:C,name:l,form:i,value:h,hasConsumerStoppedPropagationRef:w,required:u,defaultChecked:!L(s)&&s,isFormControl:y,bubbleInput:x,setBubbleInput:v};return(0,p.jsx)(g,{scope:t,...j,children:z(f)?f(j):a})}var k="CheckboxTrigger",C=o.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...s},c)=>{const{control:i,value:l,disabled:d,checked:m,required:h,setControl:f,setChecked:g,hasConsumerStoppedPropagationRef:_,isFormControl:C,bubbleInput:x}=b(k,e),v=(0,a.s)(c,f),w=o.useRef(m);return o.useEffect(()=>{const e=i?.form;if(e){const t=()=>g(w.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[i,g]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":L(m)?"mixed":m,"aria-required":h,"data-state":S(m),"data-disabled":d?"":void 0,disabled:d,value:l,...s,ref:v,onKeyDown:(0,r.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,r.m)(n,e=>{g(e=>!!L(e)||!e),x&&C&&(_.current=e.isPropagationStopped(),_.current||e.stopPropagation())})})});C.displayName=k;var x=o.forwardRef((e,t)=>{const{__scopeCheckbox:n,name:o,checked:a,defaultChecked:s,required:r,disabled:c,value:i,onCheckedChange:l,form:d,...u}=e;return(0,p.jsx)(_,{__scopeCheckbox:n,checked:a,defaultChecked:s,disabled:c,required:r,onCheckedChange:l,name:o,form:d,value:i,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(C,{...u,ref:t,__scopeCheckbox:n}),e&&(0,p.jsx)(j,{__scopeCheckbox:n})]})})});x.displayName=m;var v="CheckboxIndicator",w=o.forwardRef((e,t)=>{const{__scopeCheckbox:n,forceMount:o,...a}=e,s=b(v,n);return(0,p.jsx)(d.C,{present:o||L(s.checked)||!0===s.checked,children:(0,p.jsx)(u.sG.span,{"data-state":S(s.checked),"data-disabled":s.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=v;var y="CheckboxBubbleInput",j=o.forwardRef(({__scopeCheckbox:e,...t},n)=>{const{control:s,hasConsumerStoppedPropagationRef:r,checked:c,defaultChecked:d,required:m,disabled:h,name:f,value:g,form:_,bubbleInput:k,setBubbleInput:C}=b(y,e),x=(0,a.s)(n,C),v=(0,i.Z)(c),w=(0,l.X)(s);o.useEffect(()=>{const e=k;if(!e)return;const t=window.HTMLInputElement.prototype,n=Object.getOwnPropertyDescriptor(t,"checked").set,o=!r.current;if(v!==c&&n){const t=new Event("click",{bubbles:o});e.indeterminate=L(c),n.call(e,!L(c)&&c),e.dispatchEvent(t)}},[k,v,c,r]);const j=o.useRef(!L(c)&&c);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:d??j.current,required:m,disabled:h,name:f,value:g,form:_,...t,tabIndex:-1,ref:x,style:{...t.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function z(e){return"function"==typeof e}function L(e){return"indeterminate"===e}function S(e){return L(e)?"indeterminate":e?"checked":"unchecked"}j.displayName=y;var P=n(27723),D=n(45111),N=n(86087),I=n(79758);const R=(0,N.memo)(({indeterminate:e,label:t,value:n,id:o,onChange:a,required:s,disabled:r,options:c={}})=>{const[i,l]=(0,N.useState)(!1),[d,u]=(0,N.useState)(!1);let m=n;Array.isArray(m)||(m=""===m?[]:[m]),(0,N.useEffect)(()=>{let e=1===Object.keys(c).length&&"true"===Object.keys(c)[0];l(e)},[]),e&&(n=!0);const h=m;let f=!1;Object.keys(c).length>10&&(f=!0);const g=e=>i?n:h.includes(""+e)||h.includes(parseInt(e)),b=()=>{u(!d)};let _=r&&!Array.isArray(r);return 0===Object.keys(c).length?(0,p.jsx)(p.Fragment,{children:(0,P.__)("No options found","complianz-gdpr")}):(0,p.jsxs)("div",{className:"cmplz-checkbox-group",children:[Object.entries(c).map(([c,l],u)=>(0,p.jsxs)("div",{className:"cmplz-checkbox-group__item"+(!d&&u>9?" cmplz-hidden":""),children:[(0,p.jsx)(x,{className:"cmplz-checkbox-group__checkbox",id:o+"_"+c,checked:g(c),"aria-label":t,disabled:_||Array.isArray(r)&&r.includes(c),required:s,onCheckedChange:e=>((e,t)=>{if(i)a(!n);else{const e=h.includes(""+t)||h.includes(parseInt(t))?h.filter(e=>e!==""+t&&e!==parseInt(t)):[...h,t];a(e)}})(0,c),children:(0,p.jsx)(w,{className:"cmplz-checkbox-group__indicator",children:(0,p.jsx)(D.default,{name:e?"indeterminate":"check",size:14,color:"dark-blue"})})}),(0,p.jsx)("label",{className:"cmplz-checkbox-group__label",htmlFor:o+"_"+c,children:l})]},c)),!d&&f&&(0,p.jsx)(I.default,{onClick:()=>b(),children:(0,P.__)("Show more","complianz-gdpr")}),d&&f&&(0,p.jsx)(I.default,{onClick:()=>b(),children:(0,P.__)("Show less","complianz-gdpr")})]})})},85357:(e,t,n)=>{n.d(t,{Z:()=>a});var o=n(51609);function a(e){const t=o.useRef({value:e,previous:e});return o.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},97234:(e,t,n)=>{n.r(t),n.d(t,{default:()=>i});var o=n(86087),a=n(81366),s=n(27723),r=n(54098),c=n(10790);const i=(0,o.memo)(()=>{const{documents:e,downloadUrl:t,deleteDocuments:i,documentsLoaded:l,fetchData:d}=(0,r.default)(),[u,p]=(0,o.useState)(""),[m,h]=(0,o.useState)([]),[f,g]=(0,o.useState)({}),[b,_]=(0,o.useState)(!1),[k,C]=(0,o.useState)(!1),[x,v]=(0,o.useState)(null);(0,o.useEffect)(()=>{n.e(3757).then(n.bind(n,83757)).then(({default:e})=>{v(()=>e)})},[]),(0,o.useEffect)(()=>{l||d()},[l]);const w=async()=>{let n=e.filter(e=>m.includes(e.id));h([]);const o=async()=>{if(n.length>0){const e=n.shift(),a=t+"/"+e.file;p(!0);try{let t=new XMLHttpRequest;t.responseType="blob",t.open("get",a,!0),t.send(),t.onreadystatechange=function(){if(4===this.readyState&&200===this.status){let t=window.URL.createObjectURL(this.response),n=window.document.createElement("a");n.setAttribute("href",t),n.setAttribute("download",e.file),window.document.body.appendChild(n),n.click(),setTimeout(function(){window.URL.revokeObjectURL(t)},6e4)}},await o()}catch(e){console.error(e),p(!1)}}};await o(),p(!1)},y=e=>(e.sort((e,t)=>e.file<t.file?-1:e.file>t.file?1:0),e),j=[{name:(0,c.jsx)(a.default,{options:{true:""},indeterminate:b,value:k,onChange:t=>(t=>{if(t){C(!0);let t=f.currentPage?f.currentPage:1,n=y(e).slice(10*(t-1),10*t);h(n.map(e=>e.id))}else C(!1),h([]);_(!1)})(t)}),selector:e=>e.selectControl,grow:1,minWidth:"50px"},{name:(0,s.__)("Document","complianz-gdpr"),selector:e=>e.file,sortable:!0,grow:5},{name:(0,s.__)("Region","complianz-gdpr"),selector:e=>(0,c.jsx)("img",{alt:"region",width:"20px",height:"20px",src:cmplz_settings.plugin_url+"assets/images/"+e.region+".svg"}),sortable:!0,grow:2,right:!0},{name:(0,s.__)("Consent","complianz-gdpr"),selector:e=>e.consent,sortable:!0,grow:2,right:!0},{name:(0,s.__)("Date","complianz-gdpr"),selector:e=>e.time,sortable:!0,grow:4,right:!0}];let z=[...e];z=y(z);let L=[];return z.forEach(t=>{let n={...t};n.selectControl=(0,c.jsx)(a.default,{value:m.includes(n.id),options:{true:""},onChange:t=>((t,n)=>{let o=[...m];t?o.includes(n)||(o.push(n),h(o)):(o=[...m.filter(e=>e!==n)],h(o));let a=f.currentPage?f.currentPage:1,s=y(e).slice(10*(a-1),10*a),r=!0,c=!1;s.forEach(e=>{o.includes(e.id)?c=!0:r=!1}),r?(C(!0),_(!1)):c?(C(!1),_(!0)):_(!1)})(!m.includes(n.id),n.id)}),L.push(n)}),(0,c.jsxs)(c.Fragment,{children:[m.length>0&&(0,c.jsxs)("div",{className:"cmplz-selected-document",children:[m.length>1&&(0,s.__)("%s items selected","complianz-gdpr").replace("%s",m.length),1===m.length&&(0,s.__)("1 item selected","complianz-gdpr"),(0,c.jsxs)("div",{className:"cmplz-selected-document-controls",children:[(0,c.jsx)("button",{disabled:u,className:"button button-default cmplz-btn-reset",onClick:()=>w(),children:(0,s.__)("Download Proof of Consent","complianz-gdpr")}),(0,c.jsx)("button",{className:"button button-default cmplz-reset-button",onClick:()=>(async e=>{h([]),await i(e)})(m),children:(0,s.__)("Delete","complianz-gdpr")})]})]}),x&&(0,c.jsx)(c.Fragment,{children:(0,c.jsx)(x,{className:"cmplz-data-table",columns:j,data:L,dense:!0,pagination:!0,paginationPerPage:10,onChangePage:e=>{g({...f,currentPage:e})},paginationState:f,noDataComponent:(0,c.jsx)("div",{className:"cmplz-no-documents",children:(0,s.__)("No documents","complianz-gdpr")}),persistTableHead:!0,theme:"really-simple-plugins",customStyles:{headCells:{style:{paddingLeft:"0",paddingRight:"0"}},cells:{style:{paddingLeft:"0",paddingRight:"0"}}}})})]})})},99091:(e,t,n)=>{n.r(t),n.d(t,{UseCookieScanData:()=>s});var o=n(81621),a=n(9588);const s=(0,o.vt)((e,t)=>({initialLoadCompleted:!1,setInitialLoadCompleted:t=>e({initialLoadCompleted:t}),iframeLoaded:!1,loading:!1,nextPage:!1,progress:0,cookies:[],lastLoadedIframe:"",setIframeLoaded:t=>e({iframeLoaded:t}),setLastLoadedIframe:t=>e(e=>({lastLoadedIframe:t})),setProgress:t=>e({progress:t}),fetchProgress:()=>(e({loading:!0}),a.doAction("get_scan_progress",{}).then(t=>(e({initialLoadCompleted:!0,loading:!1,nextPage:t.next_page,progress:t.progress,cookies:t.cookies}),t)))}))}}]);