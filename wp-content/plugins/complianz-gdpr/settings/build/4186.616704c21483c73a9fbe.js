"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[4186,7338],{57338:(n,i,l)=>{l.r(i),l.d(i,{default:()=>g});var a=l(81621),t=l(9588),e=l(27723);const g=(0,a.vt)((n,i)=>({error:!1,dataLoaded:!1,pluginData:[],updatePluginData:(l,a)=>{let t=i().pluginData;t.forEach(function(n,i){n.slug===l&&(t[i]=a)}),n(n=>({dataLoaded:!0,pluginData:t}))},getPluginData:n=>i().pluginData.filter(i=>i.slug===n)[0],fetchOtherPluginsData:async()=>{const{pluginData:i,error:l}=await t.doAction("otherpluginsdata").then(n=>{let i=[];i=n.plugins;let l=n.error;return l||i.forEach(function(n,l){i[l].pluginActionNice=u(n.pluginAction)}),{pluginData:i,error:l}});n(n=>({dataLoaded:!0,pluginData:i,error:l}))},pluginActions:(n,l,a)=>{a&&a.preventDefault();let e={};e.slug=n,e.pluginAction=l;let g=i().getPluginData(n);"download"===l?g.pluginAction="downloading":"activate"===l&&(g.pluginAction="activating"),g.pluginActionNice=u(g.pluginAction),i().updatePluginData(n,g),"installed"!==l&&"upgrade-to-premium"!==l&&t.doAction("plugin_actions",e).then(l=>{g=l,i().updatePluginData(n,g),i().pluginActions(n,g.pluginAction)})}})),u=n=>({download:(0,e.__)("Install","complianz-gdpr"),activate:(0,e.__)("Activate","complianz-gdpr"),activating:(0,e.__)("Activating...","complianz-gdpr"),downloading:(0,e.__)("Downloading...","complianz-gdpr"),"upgrade-to-premium":(0,e.__)("Downloading...","complianz-gdpr")}[n])},64186:(n,i,l)=>{l.r(i),l.d(i,{default:()=>p});var a=l(86087),t=l(27723),e=l(99695),g=l(57338),u=l(10790);const p=()=>{const{dataLoaded:n,pluginData:i,pluginActions:l,fetchOtherPluginsData:p,error:c}=(0,g.default)();return(0,a.useEffect)(()=>{n||p()},[]),!n||c?(0,u.jsx)(e.default,{lines:"3",error:c}):(0,u.jsx)("div",{className:"cmplz-other-plugins-container",children:i.map((n,i)=>((n,i)=>(0,u.jsxs)("div",{className:"cmplz-suggested-plugin cmplz-"+n.slug,children:[(0,u.jsx)("img",{className:"cmplz-suggested-plugin-img",src:cmplz_settings.plugin_url+"/upgrade/img/"+n.image}),(0,u.jsxs)("div",{className:"cmplz-suggested-plugin-group",children:[(0,u.jsx)("div",{className:"cmplz-suggested-plugin-group-title",children:n.title}),(0,u.jsx)("div",{className:"cmplz-suggested-plugin-group-desc",children:n.summary})]}),(0,u.jsx)("div",{className:"cmplz-suggested-plugin-desc-long",children:n.description}),(0,u.jsx)("div",{children:(0,u.jsxs)("div",{className:"cmplz-other-plugin-status",children:["upgrade-to-premium"===n.pluginAction&&(0,u.jsx)("a",{type:"button",className:"button-secondary cmplz-install-plugin",target:"_blank",href:n.upgrade_url,rel:"noopener noreferrer",children:(0,t.__)("Upgrade","complianz-gdpr")}),"upgrade-to-premium"!==n.pluginAction&&"installed"!==n.pluginAction&&(0,u.jsx)("button",{type:"button",className:"button-secondary cmplz-install-plugin",onClick:i=>l(n.slug,n.pluginAction,i),children:n.pluginActionNice}),"installed"===n.pluginAction&&(0,t.__)("Installed","complianz-gdpr")]})})]},i))(n,i))})}}}]);