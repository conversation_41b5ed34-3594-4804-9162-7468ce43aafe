"use strict";(globalThis.webpackChunkcomplianz_gdpr=globalThis.webpackChunkcomplianz_gdpr||[]).push([[93,800,2921,4759],{9957:(e,t,n)=>{function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}n.d(t,{m:()=>r})},10800:(e,t,n)=>{n.r(t),n.d(t,{default:()=>j});var r=n(86087),o=n(51609),s=n(9957),i=n(91071),c=n(62133),a=n(81351),l=n(85357),d=n(54150),u=n(12579),p=n(10790),h="Switch",[f,g]=(0,c.A)(h),[m,b]=f(h),v=o.forwardRef((e,t)=>{const{__scopeSwitch:n,name:r,checked:c,defaultChecked:l,required:d,disabled:f,value:g="on",onCheckedChange:b,form:v,...y}=e,[_,S]=o.useState(null),k=(0,i.s)(t,e=>S(e)),C=o.useRef(!1),j=!_||v||!!_.closest("form"),[A,N]=(0,a.i)({prop:c,defaultProp:l??!1,onChange:b,caller:h});return(0,p.jsxs)(m,{scope:n,checked:A,disabled:f,children:[(0,p.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":A,"aria-required":d,"data-state":x(A),"data-disabled":f?"":void 0,disabled:f,value:g,...y,ref:k,onClick:(0,s.m)(e.onClick,e=>{N(e=>!e),j&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),j&&(0,p.jsx)(w,{control:_,bubbles:!C.current,name:r,value:g,checked:A,required:d,disabled:f,form:v,style:{transform:"translateX(-100%)"}})]})});v.displayName=h;var y="SwitchThumb",_=o.forwardRef((e,t)=>{const{__scopeSwitch:n,...r}=e,o=b(y,n);return(0,p.jsx)(u.sG.span,{"data-state":x(o.checked),"data-disabled":o.disabled?"":void 0,...r,ref:t})});_.displayName=y;var w=o.forwardRef(({__scopeSwitch:e,control:t,checked:n,bubbles:r=!0,...s},c)=>{const a=o.useRef(null),u=(0,i.s)(a,c),h=(0,l.Z)(n),f=(0,d.X)(t);return o.useEffect(()=>{const e=a.current;if(!e)return;const t=window.HTMLInputElement.prototype,o=Object.getOwnPropertyDescriptor(t,"checked").set;if(h!==n&&o){const t=new Event("click",{bubbles:r});o.call(e,n),e.dispatchEvent(t)}},[h,n,r]),(0,p.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...s,tabIndex:-1,ref:u,style:{...s.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}w.displayName="SwitchBubbleInput";var S=v,k=_,C=n(4219);const j=(0,r.memo)(({value:e,onChange:t,required:n,disabled:r,className:o,label:s,id:i})=>{const{getField:c}=(0,C.default)();let a=e;return"0"!==e&&"1"!==e||(a="1"===e),(0,p.jsx)("div",{className:"cmplz-input-group cmplz-switch-group",children:(0,p.jsx)(S,{className:"cmplz-switch-root "+o,checked:a,onCheckedChange:e=>{"banner"===c(i).data_target&&(e=e?"1":"0"),t(e)},disabled:r,required:n,children:(0,p.jsx)(k,{className:"cmplz-switch-thumb"})})})})},12579:(e,t,n)=>{n.d(t,{hO:()=>a,sG:()=>c});var r=n(51609),o=n(75795),s=n(33362),i=n(10790),c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{const n=(0,s.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{const{asChild:o,...s}=e,c=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(c,{...s,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function a(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},32921:(e,t,n)=>{n.r(t),n.d(t,{default:()=>i});var r=n(27723),o=n(44124),s=n(10790);const i=e=>(0,s.jsxs)(s.Fragment,{children:[" ",(0,s.jsx)(o.default,{url:e,target:"_blank",rel:"noopener noreferrer",text:(0,r.__)("For more information, please read this %sarticle%s.","complianz-gdpr")})," "]})},33362:(e,t,n)=>{n.d(t,{TL:()=>i});var r=n(51609),o=n(91071),s=n(10790);function i(e){const t=c(e),n=r.forwardRef((e,n)=>{const{children:o,...i}=e,c=r.Children.toArray(o),a=c.find(l);if(a){const e=a.props.children,o=c.map(t=>t===a?r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null:t);return(0,s.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,s.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}function c(e){const t=r.forwardRef((e,t)=>{const{children:n,...s}=e;if(r.isValidElement(n)){const e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}(n),i=function(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...e)=>{const t=s(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...s}:"className"===r&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}(s,n.props);return n.type!==r.Fragment&&(i.ref=t?(0,o.t)(t,e):e),r.cloneElement(n,i)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var a=Symbol("radix.slottable");function l(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},34759:(e,t,n)=>{n.r(t),n.d(t,{default:()=>i});var r=n(81621),o=n(16535),s=n(9588);const i=(0,r.vt)((e,t)=>({integrationsLoaded:!1,fetching:!1,services:[],plugins:[],scripts:[],placeholders:[],blockedScripts:[],setScript:(t,n)=>{e((0,o.Ay)(e=>{if("block_script"===n){let n=e.blockedScripts;if(t.urls){for(const[e,r]of Object.entries(t.urls)){if(!r||0===r.length)continue;let e=!1;for(const[t,o]of Object.entries(n))r===t&&(e=!0);e||(n[r]=r)}e.blockedScripts=n}}const r=e.scripts[n].findIndex(e=>e.id===t.id);-1!==r&&(e.scripts[n][r]=t)}))},fetchIntegrationsData:async()=>{if(t().fetching)return;e({fetching:!0});const{services:n,plugins:r,scripts:o,placeholders:s,blocked_scripts:i}=await c();let a=o;a.block_script&&a.block_script.length>0&&a.block_script.forEach((e,t)=>{e.id=t}),a.add_script&&a.add_script.length>0&&a.add_script.forEach((e,t)=>{e.id=t}),a.whitelist_script&&a.whitelist_script.length>0&&a.whitelist_script.forEach((e,t)=>{e.id=t}),e(()=>({integrationsLoaded:!0,services:n,plugins:r,scripts:a,fetching:!1,placeholders:s,blockedScripts:i}))},addScript:n=>{e({fetching:!0}),t().scripts[n]&&Array.isArray(t().scripts[n])||e((0,o.Ay)(e=>{e.scripts[n]=[]})),e((0,o.Ay)(e=>{e.scripts[n].push({name:"general",id:e.scripts[n].length,enable:!0})}));let r=t().scripts;return s.doAction("update_scripts",{scripts:r}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},saveScript:(n,r)=>{e({fetching:!0}),t().scripts[r]&&Array.isArray(t().scripts[r])||e((0,o.Ay)(e=>{e.scripts[r]=[]})),e((0,o.Ay)(e=>{const t=e.scripts[r].findIndex(e=>e.id===n.id);-1!==t&&(e.scripts[r][t]=n)}));let i=t().scripts;return s.doAction("update_scripts",{scripts:i}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},deleteScript:(n,r)=>{e({fetching:!0}),t().scripts[r]&&Array.isArray(t().scripts[r])||e((0,o.Ay)(e=>{e.scripts[r]=[]})),e((0,o.Ay)(e=>{const t=e.scripts[r].findIndex(e=>e.id===n.id);-1!==t&&e.scripts[r].splice(t,1)}));let i=t().scripts;return s.doAction("update_scripts",{scripts:i}).then(t=>(e({fetching:!1}),t)).catch(e=>{console.error(e)})},updatePluginStatus:async(t,n)=>{e({fetching:!0}),e((0,o.Ay)(e=>{const r=e.plugins.findIndex(e=>e.id===t);-1!==r&&(e.plugins[r].enabled=n)}));const r=await s.doAction("update_plugin_status",{plugin:t,enabled:n}).then(e=>e).catch(e=>{console.error(e)});return e({fetching:!1}),r},updatePlaceholderStatus:async(t,n,r)=>{e({fetching:!0}),r&&e((0,o.Ay)(e=>{const r=e.plugins.findIndex(e=>e.id===t);-1!==r&&(e.plugins[r].placeholder=n?"enabled":"disabled")}));const i=await s.doAction("update_placeholder_status",{id:t,enabled:n}).then(e=>e).catch(e=>{console.error(e)});return e({fetching:!1}),i}})),c=()=>s.doAction("get_integrations_data",{}).then(e=>e).catch(e=>{console.error(e)})},54150:(e,t,n)=>{n.d(t,{X:()=>s});var r=n(51609),o=n(88200);function s(e){const[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const t=new ResizeObserver(t=>{if(!Array.isArray(t))return;if(!t.length)return;const r=t[0];let o,s;if("borderBoxSize"in r){const e=r.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,s=t.blockSize}else o=e.offsetWidth,s=e.offsetHeight;n({width:o,height:s})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},60093:(e,t,n)=>{n.r(t),n.d(t,{default:()=>d});var r=n(34759),o=n(86087),s=n(27723),i=n(4219),c=n(32921),a=n(10800),l=n(10790);const d=(0,o.memo)(()=>{const{updatePlaceholderStatus:e,fetching:t,updatePluginStatus:d,integrationsLoaded:u,plugins:p,fetchIntegrationsData:h}=(0,r.default)(),[f,g]=(0,o.useState)(""),[m,b]=(0,o.useState)(!1),[v,y]=(0,o.useState)(""),{getFieldValue:_}=(0,i.default)(),[w,x]=(0,o.useState)(null);(0,o.useEffect)(()=>{n.e(3757).then(n.bind(n,83757)).then(({default:e})=>{x(()=>e)})},[]),(0,o.useEffect)(()=>{u||h(),u&&(1==_("safe_mode")?(y((0,s.__)("Safe Mode enabled. To manage integrations, disable Safe Mode under Tools - Support.","complianz-gdpr")),b(!0)):0===p.length&&(y((0,s.__)("No active plugins detected in the integrations list.","complianz-gdpr")),b(!0)))},[u]),(0,o.useEffect)(()=>{},[p]);const S=[{name:(0,s.__)("Plugin","complianz-gdpr"),selector:e=>e.label,sortable:!0,grow:5},{name:(0,s.__)("Placeholder","complianz-gdpr"),selector:e=>e.placeholderControl,sortable:!0,sortFunction:(e,t)=>{const n=e.placeholder,r=t.placeholder;return n>r?1:r>n?-1:0},grow:2},{name:(0,s.__)("Status","complianz-gdpr"),selector:e=>e.enabledControl,sortable:!0,sortFunction:(e,t)=>{const n=e.enabled,r=t.enabled;return n>r?1:r>n?-1:0},grow:1,right:!0}];let k=p.filter(e=>e.label.toLowerCase().includes(f.toLowerCase()));k.sort((e,t)=>e.label<t.label?-1:e.label>t.label?1:0);let C=[];return k.forEach(n=>{let r={...n};r.enabledControl=(0,l.jsx)(a.default,{disabled:t,className:"cmplz-switch-input-tiny",value:n.enabled,onChange:e=>(async(e,t)=>{await d(e.id,t),await h()})(n,e)}),r.placeholderControl=(0,l.jsx)(l.Fragment,{children:"none"!==n.placeholder&&(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(a.default,{className:"cmplz-switch-input-tiny",disabled:"none"===n.placeholder||t,value:"enabled"===n.placeholder,onChange:t=>(async(t,n)=>{await e(t.id,n,!0)})(n,t)})})}),C.push(r)}),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("p",{children:[(0,s.__)("Below you will find the plugins currently detected and integrated with Complianz. Most plugins work by default, but you can also add a plugin to the script center or add it to the integration list.","complianz-gdpr"),(0,c.default)("https://complianz.io/developers-guide-for-third-party-integrations"),(0,s.__)("Enabled plugins will be blocked on the front-end of your website until the user has given consent (opt-in), or after the user has revoked consent (opt-out). When possible a placeholder is activated. You can also disable or configure the placeholder to your liking.","complianz-gdpr"),(0,c.default)("https://complianz.io/blocking-recaptcha-manually/")]}),(0,l.jsx)("div",{className:"cmplz-table-header",children:p.length>5&&(0,l.jsx)("input",{type:"text",placeholder:(0,s.__)("Search","complianz-gdpr"),value:f,onChange:e=>g(e.target.value)})}),(m||0===k.length)&&(0,l.jsx)("div",{className:"cmplz-settings-overlay",children:(0,l.jsx)("div",{className:"cmplz-settings-overlay-message",children:v})}),0===C.length&&(0,l.jsx)(l.Fragment,{}),!m&&C.length>0&&w&&(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(w,{columns:S,data:C,dense:!0,pagination:!0,paginationPerPage:5,noDataComponent:(0,l.jsx)("div",{className:"cmplz-no-documents",children:(0,s.__)("No plugins","complianz-gdpr")}),persistTableHead:!0,theme:"really-simple-plugins",customStyles:{headCells:{style:{paddingLeft:"0",paddingRight:"0"}},cells:{style:{paddingLeft:"0",paddingRight:"0"}}}})})]})})},62133:(e,t,n)=>{n.d(t,{A:()=>s});var r=n(51609),o=n(10790);function s(e,t=[]){let n=[];const s=()=>{const t=n.map(e=>r.createContext(e));return function(n){const o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return s.scopeName=e,[function(t,s){const i=r.createContext(s),c=n.length;n=[...n,s];const a=t=>{const{scope:n,children:s,...a}=t,l=n?.[e]?.[c]||i,d=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(l.Provider,{value:d,children:s})};return a.displayName=t+"Provider",[a,function(n,o){const a=o?.[e]?.[c]||i,l=r.useContext(a);if(l)return l;if(void 0!==s)return s;throw new Error(`\`${n}\` must be used within \`${t}\``)}]},i(s,...t)]}function i(...e){const t=e[0];if(1===e.length)return t;const n=()=>{const n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){const o=n.reduce((t,{useScope:n,scopeName:r})=>({...t,...n(e)[`__scope${r}`]}),{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}},81351:(e,t,n)=>{var r;n.d(t,{i:()=>c});var o=n(51609),s=n(88200),i=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||s.N;function c({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[s,c,a]=function({defaultProp:e,onChange:t}){const[n,r]=o.useState(e),s=o.useRef(n),c=o.useRef(t);return i(()=>{c.current=t},[t]),o.useEffect(()=>{s.current!==n&&(c.current?.(n),s.current=n)},[n,s]),[n,r,c]}({defaultProp:t,onChange:n}),l=void 0!==e,d=l?e:s;{const t=o.useRef(void 0!==e);o.useEffect(()=>{const e=t.current;if(e!==l){const t=e?"controlled":"uncontrolled",n=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${t} to ${n}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,r])}const u=o.useCallback(t=>{if(l){const n=function(e){return"function"==typeof e}(t)?t(e):t;n!==e&&a.current?.(n)}else c(t)},[l,e,c,a]);return[d,u]}Symbol("RADIX:SYNC_STATE")},85357:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(51609);function o(e){const t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},88200:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(51609),o=globalThis?.document?r.useLayoutEffect:()=>{}},91071:(e,t,n)=>{n.d(t,{s:()=>i,t:()=>s});var r=n(51609);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let n=!1;const r=e.map(e=>{const r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){const n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function i(...e){return r.useCallback(s(...e),e)}}}]);