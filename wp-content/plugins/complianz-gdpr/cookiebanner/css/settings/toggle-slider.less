.cmplz-cookiebanner .cmplz-categories .cmplz-category {
  .cmplz-banner-checkbox {
	position: relative;
	input.cmplz-consent-checkbox {
	  &:focus + .cmplz-label:before {
		//box-shadow:0 0 0 3px rgba(0, 119, 200, 0.50);
		box-shadow:0 0 0 2px #245fcc
		//rgb(51 51 51) 0px 0px 0px 3px
	  }
	  opacity:0;
	  margin: 0;
	  margin-top: -10px;
	  cursor: pointer;
	  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
	  filter: alpha(opacity=0);
	  -moz-opacity: 0;
	  -khtml-opacity: 0;
	  position: absolute;
	  z-index: 1;
	  top: 0px;
	  left: 0px;
	  width: 40px;
	  height: 20px;

	  &:checked + .cmplz-label {
		&::before {
		  display:block;
		  background-color: var( --cmplz_slider_active_color );
		  content: "";
		  padding-left: 6px;
		}

		&:after {
		  left: 14px;
		}
	  }
	}

	.cmplz-label {
	  position: relative;
	  padding-left: 30px;
	  margin: 0;

	  &:before, &:after {
		box-sizing: border-box;
		position: absolute;
		-webkit-border-radius: 10px;
		-moz-border-radius: 10px;
		border-radius: 10px;
		transition: background-color 0.3s, left 0.3s;
	  }

	  &:before {
		display:block;
		content: "";
		color: #fff;
		box-sizing: border-box;
		font-family: 'FontAwesome', sans-serif;
		padding-left: 23px;
		font-size: 12px;
		line-height: 20px;
		background-color: var( --cmplz_slider_inactive_color );
		left: 0px;
		top: -7px;
		height: 15px;
		width: 28px;
		-webkit-border-radius: 10px;
		-moz-border-radius: 10px;
		border-radius: 10px;
	  }

	  &:after {
		display:block;
		content: "";
		letter-spacing: 20px;
		background: var( --cmplz_slider_bullet_color );
		left: 4px;
		top: -5px;
		height: 11px;
		width: 11px;
	  }
	}
  }
}
