.cmplz-cookiebanner .tcf {
  --cmplz_bottom_banner_breakpoint: 768px;
  display: block;
}
/*Do not preprocess the below */
/**
* Hack to hide banner header on smaller screens on safari only.
 */
/*Do not preprocess the below */
@media (max-width:425px) {
	@media not all and (min-resolution:.001dpcm)
	{ @supports (-webkit-appearance:none) {
		.cmplz-cookiebanner .cmplz-header {
			display:none;
		}
	}
	}
}
.cmplz-cookiebanner .optin .cmplz-categories.cmplz-tcf {
  display: block;
}
.cmplz-cookiebanner .optout .cmplz-categories.cmplz-tcf {
  display: none;
}
.cmplz-cookiebanner .cmplz-categories:not(.cmplz-tcf),
.cmplz-cookiebanner .cmplz-manage-third-parties,
.cmplz-cookiebanner .cmplz-save-preferences,
.cmplz-cookiebanner .cmplz-view-preferences,
.cmplz-cookiebanner .cmplz-manage-options {
  display: none;
}
.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-category-header .cmplz-title {
  justify-self: left;
  grid-column-start: 1;
}
.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-category-header .cmplz-always-active {
  justify-self: right;
}
.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-category-header .cmplz-description {
  padding: 0;
}
.cmplz-cookiebanner .cmplz-links.cmplz-information {
  display: flex;
}
.cmplz-cookiebanner .cmplz-links.cmplz-information .cmplz-link.cmplz-read-more-purposes.tcf {
  display: block;
}
.cmplz-cookiebanner .cmplz-buttons .cmplz-btn.cmplz-manage-options.tcf {
  background-color: var(--cmplz_button_settings_background_color);
  border: 1px solid var(--cmplz_button_settings_border_color);
  color: var(--cmplz_button_settings_text_color);
}
.cmplz-cookiebanner .cmplz-buttons a.cmplz-btn.cmplz-manage-options.tcf {
  display: flex;
}
.cmplz-cookiebanner .cmplz-categories .cmplz-category .cmplz-category-header .cmplz-category-title {
  margin: 0 0 5px 0;
}
@media (min-width: 1024px) {
  .cmplz-cookiebanner.cmplz-bottom .cmplz-body > div {
    width: 49%;
  }
}
@media (min-width: 768px) {
  .cmplz-cookiebanner .cmplz-categories {
    height: 280px;
  }
}
.cmplz-cookiebanner.cmplz-bottom .cmplz-buttons {
  flex-direction: column;
}
/**
 * load CSS styles for the vendor page
 */
.cmplz_tcf_legitimate_interest_checkbox {
  display: none;
}
.cmplz-tcf-type-description {
  margin: 20px;
  display: none;
}
#cmplz-document .cmplz-tcf-rm:after {
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/></svg>') no-repeat;
}
#cmplz-document .cmplz-tcf-rl:after {
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0z" fill="none"/><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/></svg>') no-repeat;
}
#cmplz-document .cmplz-tcf-toggle,
#cmplz-document .cmplz-tcf-toggle-vendor {
  text-decoration: none;
  border-bottom: 0;
}
#cmplz-document .cmplz-tcf-toggle:after,
#cmplz-document .cmplz-tcf-toggle-vendor:after {
  content: '';
  display: inline-block;
  height: 15px;
  width: 15px;
  background-size: cover;
}
#cmplz-document .cmplz-tcf-expl-header {
  margin-top: 15px;
}
.cmplz-tcf-category-expl p,
.cmplz-tcf-category-expl a {
  font-size: 13px;
  color: #333;
}
.cmplz-tcf-category-expl a {
  text-decoration: underline;
}
.cmplz-tcf-category-expl .cmplz-tcf-banner-marketing-purposes,
.cmplz-tcf-category-expl .cmplz-tcf-banner-statistics-purposes {
  max-width: fit-content;
}
.cmplz-tcf-template {
  display: none;
}
.cmplz-tcf-buttons {
  margin-top: 20px;
}
.cmplz-tcf-buttons button {
  margin-right: 20px;
  margin-bottom: 20px;
}
.cmplz-tcf-container {
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;
}
.cmplz-tcf-container .cmplz-tcf-vendor-container {
  margin-bottom: 20px;
}
.cmplz-tcf-container .cmplz-tcf-vendor-container.cmplz-vendortype-tcf .cmplz-vendortype-label {
  display: none;
}
.cmplz-tcf-container .cmplz-tcf-vendor-container label {
  display: flex;
  align-items: center;
  gap: 10px;
}
.cmplz-tcf-container .cmplz-tcf-vendor-container label .cmplz-vendortype-label {
  background-color: #333;
  color: white;
  font-size: 12px;
  padding: 5px 10px;
  border-radius: 3px;
}
.cmplz-tcf-container .cmplz-tcf-checkbox-container input {
  margin-right: 5px;
}
.cmplz-tcf-container .cmplz-tcf-checkbox-container.cmplz-vendortype-ac .cmplz-tcf-info .cmplz-tcf-info-content:not(:first-child) {
  display: none;
}
.cmplz-tcf-container .cmplz-tcf-checkbox-container .cmplz-tcf-links {
  display: flex;
}
.cmplz-tcf-container .cmplz-tcf-checkbox-container .cmplz-tcf-links .cmplz-tcf-policy-url {
  margin-left: 30px;
  min-width: 125px;
}
.cmplz-tcf-container .cmplz-tcf-checkbox-container .cmplz-tcf-links .cmplz-tcf-showinfo {
  margin-left: 20px;
}
.cmplz-tcf-container .cmplz-tcf-checkbox-container .cmplz-tcf-info {
  display: none;
}
.cmplz-tcf-container .cmplz-tcf-checkbox-container .cmplz-tcf-info .cmplz-tcf-info-content {
  line-height: 33px;
  margin-left: 30px;
  display: flex;
}
.cmplz-tcf-container .cmplz-tcf-checkbox-container .cmplz-tcf-info .cmplz-tcf-info-content .retention_seconds,
.cmplz-tcf-container .cmplz-tcf-checkbox-container .cmplz-tcf-info .cmplz-tcf-info-content .retention_days,
.cmplz-tcf-container .cmplz-tcf-checkbox-container .cmplz-tcf-info .cmplz-tcf-info-content .session-storage,
.cmplz-tcf-container .cmplz-tcf-checkbox-container .cmplz-tcf-info .cmplz-tcf-info-content .non-cookie-storage-inactive,
.cmplz-tcf-container .cmplz-tcf-checkbox-container .cmplz-tcf-info .cmplz-tcf-info-content .non-cookie-storage-active,
.cmplz-tcf-container .cmplz-tcf-checkbox-container .cmplz-tcf-info .cmplz-tcf-info-content .non-cookie-refresh-inactive,
.cmplz-tcf-container .cmplz-tcf-checkbox-container .cmplz-tcf-info .cmplz-tcf-info-content .non-cookie-refresh-active {
  display: none;
}
.cmplz-tcf-container .cmplz-tcf-checkbox-container .cmplz-tcf-info .cmplz-tcf-info-content .cmplz-tcf-header {
  min-width: 125px;
}
.cmplz-tcf-container .cmplz-tcf-checkbox-container .cmplz-tcf-info .cmplz-tcf-info-content .cmplz-tcf-description {
  display: flex;
  flex-wrap: wrap;
}
.cmplz-tcf-container .cmplz-tcf-checkbox-container .cmplz-tcf-info .cmplz-tcf-info-content .cmplz-tcf-description input {
  margin-left: 15px;
}
/*# sourceMappingURL=tcf.css.map */
