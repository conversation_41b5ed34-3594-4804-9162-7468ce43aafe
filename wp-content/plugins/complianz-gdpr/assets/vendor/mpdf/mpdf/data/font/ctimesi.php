<?php

$cw = array(
	chr(0) => 250, chr(1) => 250, chr(2) => 250, chr(3) => 250, chr(4) => 250, chr(5) => 250, chr(6) => 250, chr(7) => 250, chr(8) => 250, chr(9) => 250, chr(10) => 250, chr(11) => 250, chr(12) => 250, chr(13) => 250, chr(14) => 250, chr(15) => 250, chr(16) => 250, chr(17) => 250, chr(18) => 250, chr(19) => 250, chr(20) => 250, chr(21) => 250,
	chr(22) => 250, chr(23) => 250, chr(24) => 250, chr(25) => 250, chr(26) => 250, chr(27) => 250, chr(28) => 250, chr(29) => 250, chr(30) => 250, chr(31) => 250, ' ' => 250, '!' => 333, '"' => 420, '#' => 500, '$' => 500, '%' => 833, '&' => 778, '\'' => 214, '(' => 333, ')' => 333, '*' => 500, '+' => 675,
	',' => 250, '-' => 333, '.' => 250, '/' => 278, '0' => 500, '1' => 500, '2' => 500, '3' => 500, '4' => 500, '5' => 500, '6' => 500, '7' => 500, '8' => 500, '9' => 500, ':' => 333, ';' => 333, '<' => 675, '=' => 675, '>' => 675, '?' => 500, '@' => 920, 'A' => 611,
	'B' => 611, 'C' => 667, 'D' => 722, 'E' => 611, 'F' => 611, 'G' => 722, 'H' => 722, 'I' => 333, 'J' => 444, 'K' => 667, 'L' => 556, 'M' => 833, 'N' => 667, 'O' => 722, 'P' => 611, 'Q' => 722, 'R' => 611, 'S' => 500, 'T' => 556, 'U' => 722, 'V' => 611, 'W' => 833,
	'X' => 611, 'Y' => 556, 'Z' => 556, '[' => 389, '\\' => 278, ']' => 389, '^' => 422, '_' => 500, '`' => 333, 'a' => 500, 'b' => 500, 'c' => 444, 'd' => 500, 'e' => 444, 'f' => 278, 'g' => 500, 'h' => 500, 'i' => 278, 'j' => 278, 'k' => 444, 'l' => 278, 'm' => 722,
	'n' => 500, 'o' => 500, 'p' => 500, 'q' => 500, 'r' => 389, 's' => 389, 't' => 278, 'u' => 500, 'v' => 444, 'w' => 667, 'x' => 444, 'y' => 444, 'z' => 389, '{' => 400, '|' => 275, '}' => 400, '~' => 541, chr(127) => 350, chr(128) => 500, chr(129) => 350, chr(130) => 333, chr(131) => 500,
	chr(132) => 556, chr(133) => 889, chr(134) => 500, chr(135) => 500, chr(136) => 333, chr(137) => 1000, chr(138) => 500, chr(139) => 333, chr(140) => 944, chr(141) => 350, chr(142) => 556, chr(143) => 350, chr(144) => 350, chr(145) => 333, chr(146) => 333, chr(147) => 556, chr(148) => 556, chr(149) => 350, chr(150) => 500, chr(151) => 889, chr(152) => 333, chr(153) => 980,
	chr(154) => 389, chr(155) => 333, chr(156) => 667, chr(157) => 350, chr(158) => 389, chr(159) => 556, chr(160) => 250, chr(161) => 389, chr(162) => 500, chr(163) => 500, chr(164) => 500, chr(165) => 500, chr(166) => 275, chr(167) => 500, chr(168) => 333, chr(169) => 760, chr(170) => 276, chr(171) => 500, chr(172) => 675, chr(173) => 333, chr(174) => 760, chr(175) => 333,
	chr(176) => 400, chr(177) => 675, chr(178) => 300, chr(179) => 300, chr(180) => 333, chr(181) => 500, chr(182) => 523, chr(183) => 250, chr(184) => 333, chr(185) => 300, chr(186) => 310, chr(187) => 500, chr(188) => 750, chr(189) => 750, chr(190) => 750, chr(191) => 500, chr(192) => 611, chr(193) => 611, chr(194) => 611, chr(195) => 611, chr(196) => 611, chr(197) => 611,
	chr(198) => 889, chr(199) => 667, chr(200) => 611, chr(201) => 611, chr(202) => 611, chr(203) => 611, chr(204) => 333, chr(205) => 333, chr(206) => 333, chr(207) => 333, chr(208) => 722, chr(209) => 667, chr(210) => 722, chr(211) => 722, chr(212) => 722, chr(213) => 722, chr(214) => 722, chr(215) => 675, chr(216) => 722, chr(217) => 722, chr(218) => 722, chr(219) => 722,
	chr(220) => 722, chr(221) => 556, chr(222) => 611, chr(223) => 500, chr(224) => 500, chr(225) => 500, chr(226) => 500, chr(227) => 500, chr(228) => 500, chr(229) => 500, chr(230) => 667, chr(231) => 444, chr(232) => 444, chr(233) => 444, chr(234) => 444, chr(235) => 444, chr(236) => 278, chr(237) => 278, chr(238) => 278, chr(239) => 278, chr(240) => 500, chr(241) => 500,
	chr(242) => 500, chr(243) => 500, chr(244) => 500, chr(245) => 500, chr(246) => 500, chr(247) => 675, chr(248) => 500, chr(249) => 500, chr(250) => 500, chr(251) => 500, chr(252) => 500, chr(253) => 444, chr(254) => 500, chr(255) => 444);

//$desc=array('Ascent'=>683,'Descent'=>-217,'CapHeight'=>653,'FontBBox'=>'[-169 -217 1010 883]');
$desc = array('Flags' => 96, 'FontBBox' => '[-169 -217 1010 883]', 'ItalicAngle' => -15.5, 'Ascent' => 883, 'Descent' => -217, 'Leading' => 0, 'CapHeight' => 653, 'XHeight' => 441, 'StemV' => 76, 'StemH' => 32, 'AvgWidth' => 491, 'MaxWidth' => 1000, 'MissingWidth' => 491);
$up = -100;
$ut = 50;
$kerninfo = array(chr(49) => array(chr(49) => -74,), chr(65) => array(chr(84) => -37, chr(86) => -49, chr(87) => -37, chr(89) => -55, chr(118) => -55, chr(119) => -55, chr(121) => -55, chr(146) => -37,), chr(70) => array(chr(44) => -128, chr(46) => -128, chr(65) => -128,), chr(76) => array(chr(84) => -20, chr(86) => -37, chr(87) => -37, chr(89) => -20, chr(121) => -29, chr(146) => -37,), chr(80) => array(chr(44) => -128, chr(46) => -128, chr(65) => -128,), chr(82) => array(chr(84) => 0, chr(86) => -18, chr(87) => -18, chr(89) => -18, chr(121) => -18,), chr(84) => array(chr(44) => -74, chr(46) => -74, chr(58) => -55, chr(65) => -74, chr(79) => -18, chr(97) => -91, chr(99) => -91, chr(101) => -91, chr(105) => -55, chr(111) => -91, chr(114) => -55, chr(115) => -91, chr(117) => -55, chr(119) => -74, chr(121) => -74,), chr(86) => array(chr(44) => -128, chr(46) => -128, chr(58) => -64, chr(65) => -74, chr(79) => -29, chr(97) => -110, chr(101) => -110, chr(105) => -74, chr(111) => -110, chr(114) => -74, chr(117) => -74, chr(121) => -91,), chr(87) => array(chr(44) => -91, chr(46) => -91, chr(58) => -64, chr(65) => -69, chr(97) => -91, chr(101) => -91, chr(105) => -55, chr(111) => -91, chr(114) => -55, chr(117) => -55, chr(121) => -91,), chr(89) => array(chr(44) => -91, chr(46) => -91, chr(58) => -64, chr(65) => -69, chr(97) => -91, chr(101) => -91, chr(105) => -74, chr(111) => -91, chr(112) => -91, chr(113) => -110, chr(117) => -91, chr(118) => -91,), chr(102) => array(chr(146) => 91,), chr(114) => array(chr(44) => -110, chr(46) => -110, chr(99) => -37, chr(100) => -37, chr(101) => -37, chr(103) => -37, chr(104) => -18, chr(111) => -37, chr(113) => -37, chr(114) => 0, chr(116) => 0, chr(117) => 0, chr(118) => 0, chr(119) => 0, chr(120) => 0, chr(121) => 0, chr(146) => 37,), chr(118) => array(chr(44) => -74, chr(46) => -74,), chr(119) => array(chr(44) => -74, chr(46) => -74,), chr(121) => array(chr(44) => -55, chr(46) => -55,), chr(145) => array(chr(145) => -110,), chr(146) => array(chr(115) => -128, chr(116) => -110, chr(146) => -110,),);
