.cmplz {
	.cmplz {
		&-radio-group {
			&__item {
				display: flex;
				gap: var(--rsp-spacing-xs);
				background-color: transparent;
				align-items: center;

				button {
					all: unset;
					display: flex;
					align-content: center;
					align-items: center;
					justify-content: center;
					border-radius: 50%;
					width: 16px;
					height: 16px;
					aspect-ratio: 1 / 1;
					border: 1px solid var(--rsp-input-border-color);
					transition: background-color 0.2s ease-in-out;

					&:hover {
						background-color: var(--rsp-blue-faded);
					}

					&:focus {
						box-shadow: 0 0 0 3px var(--rsp-background-color), 0 0 0 5px var(--rsp-dark-blue);
					}
					&:disabled {
						background-color: var(--rsp-grey-300);
						cursor: not-allowed;
						& + label {
							cursor: not-allowed;
						}
					}
				}
			}

			&__indicator {
				background: var(--rsp-dark-blue);
				width: 10px;
				height: 10px;
				aspect-ratio: 1 / 1;
				border-radius: 50%;
			}
		}

		&-radio-label {
			font-size: var(--rsp-fs-300);
			color: var(--rsp-text-color-light);
		}
	}

}
