#complianz {
	.components-form-file-upload {
		button {
			color: var(--rsp-text-color-light);
			border: 1px solid var(--rsp-input-border-color);
			background: var(--rsp-grey-200);
			padding: 0 var(--rsp-spacing-xs);
			min-height: 30px;
			height: min-content;
		}
	}

	input[type="checkbox"].indeterminate {
		background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect x="10" y="45" width="80" height="10" fill="currentColor"/></svg>') no-repeat center center;
	}

	div[class^="cmplz-wizard-"] { // starts with selector
		.cmplz-parent-label {
			padding-bottom: var(--rsp-spacing-m);

			label {
				font-size: var(--rsp-fs-500);
			}
		}

		.cmplz-required {
			font-size: var(--rsp-fs-100);
			color: var(--rsp-text-color-light);
			font-weight: 400;
		}


		//to align tooltips and premium label
		.cmplz-label-container {
			display: flex;
			gap: 10px;
			font-weight: 600;
			font-size: var(--rsp-fs-300);
			color: var(--rsp-text-color);

			&.cmplz-parent {
				font-size: var(--rsp-fs-500);
			}
		}

		.cmplz-create-document {
			padding: 10px 0;
			display: flex;
			flex-direction: row;
			align-items: center;

			.cmplz-icon {
				margin-right: 10px;
			}

			input {
				width: 50%;
				min-width: 200px;
				margin-right: 10px;
			}
		}

		.components-flex {
			.components-flex-item label {
				margin: 0;
			}

			.components-input-control__container {
				max-width: max-content;
			}
		}

		.components-base-control__field {
			margin-bottom: 0;
			display: flex;
			flex-flow: column;
		}

		.cmplz_multicheckbox {
			.components-base-control__field {
				display: block;
			}
		}

		.cmplz-text-control {
			-webkit-box-pack: justify;
			justify-content: space-between;

			&__field {
				display: flex;
			}
		}

		.components-toggle-control {
			.components-base-control__field, .components-flex {
				display: flex;
				flex-direction: row-reverse;
				align-items: center;
				justify-content: space-between;
				width: 100%;

				.components-form-toggle {
					margin-right: 0;

				}
			}
		}

		.components-form-toggle.is-checked .components-form-toggle__track {
			background-color: var(--rsp-green);
		}

		.cmplz-grid-item {
			.cmplz-grid-item-content {
				margin-block: var(--rsp-spacing-l);
				display: flex;
				flex-direction: column;
				gap: var(--rsp-spacing-l);
				padding-inline: 0;
				//overflow: hidden; => causes cut off for last element.

			}
		}

		.cmplz-field-wrap {
			display: flex;
		}

		.cmplz-field-wrap, .cmplz-settings-block-intro {
			//adding display flex here causes settings-block-intro with html to break.
			//becuse of this the cmplz-field-wrap has this property separately (see above)
			flex-direction: column;
			gap: var(--rsp-spacing-xs);
			padding-inline: var(--rsp-spacing-l);
			//double wrapped fields, like processing agreements create
			& > .cmplz-field-wrap {
				padding-left: 0;
			}
		}

		// inputs in row with label
		.cmplz-field-wrap {
			&.cmplz-checkbox,
			&.cmplz-banner-reset-button,
			&.cmplz-copy-multisite,
			&.cmplz-export,
			&.cmplz-button {
				display: flex;
				align-items: center;
				flex-direction: row;
				justify-content: space-between;
				gap: var(--rsp-spacing-s);
				flex-wrap: wrap;
				.cmplz-label-container {
					width: fit-content;
				}
			}
		}

		.button {
			display: flex;
			align-items: center;
			flex-direction: row;
			justify-content: space-between;
			gap: var(--rsp-spacing-s);
			flex-wrap: wrap;
		}

		.cmplz-field-button, .cmplz-export, .cmplz-import {
			display: flex;
			justify-content: space-between;
			flex-wrap: wrap;

			.cmplz-export-container {

			}

			&-form {
				display: flex;
			}
		}
	}

	.cmplz-field-wrap.cmplz-error {
		input,
		textarea,
		select {
			box-shadow: 0 0 0 2px var(--rsp-red-faded);
		}
	}

	.cmplz-error-text {
		display: flex;
		gap: var(--rsp-spacing-xxs);

		p {
			color: var(--rsp-red);
			font-size: var(--rsp-fs-200);
			font-weight: 400;
			font-style: italic;
		}
	}

	.cmplz-highlight {
		background-color: transparent;
		border-left: 4px solid var(--rsp-green);
		-o-animation: fadeIt 3s ease-in-out;
		animation: fadeIt 3s ease-in-out;
	}

	.cmplz-comment {
		font-style: italic;
	}

	@-o-keyframes fadeIt {
		0% {
			background-color: transparent;
		}
		30% {
			background-color: var(--rsp-green-faded);
		}
		100% {
			border-right: 0;
			background-color: inherit;
		}
	}
	@keyframes fadeIt {
		0% {
			background-color: transparent;
			border-right: 0;
		}
		30% {
			background-color: var(--rsp-green-faded);
		}
		100% {
			background-color: inherit;
		}
	}
}

//switch padding to top, to prevent tasks from getting stuck to text field
.cmplz-license {
	.cmplz-processing {
		//give the container an opacity
		opacity: 0.3;
	}

	.cmplz-task-element {
		padding-top: var(--rsp-spacing-s);
		padding-bottom: 0;
	}

	.cmplz-license-field {
		display: flex;
	}
}
