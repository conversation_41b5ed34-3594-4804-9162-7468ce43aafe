# Copyright (C) 2025 Complianz
# This file is distributed under the same license as the Complianz | GDPR/CCPA Cookie Consent plugin.
msgid ""
msgstr ""
"Project-Id-Version: Complianz | GDPR/CCPA Cookie Consent 7.4.2\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/trunk\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-07-14T16:09:05+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: complianz-gdpr\n"

#. Plugin Name of the plugin
#: complianz-gpdr.php
msgid "Complianz | GDPR/CCPA Cookie Consent"
msgstr ""

#. Plugin URI of the plugin
#: complianz-gpdr.php
msgid "https://www.wordpress.org/plugins/complianz-gdpr"
msgstr ""

#. Description of the plugin
#: complianz-gpdr.php
msgid "Complianz Privacy Suite for GDPR, CaCPA, DSVGO, AVG with a conditional cookie warning and customized cookie policy"
msgstr ""

#. Author of the plugin
#: complianz-gpdr.php
msgid "Complianz"
msgstr ""

#. Author URI of the plugin
#: complianz-gpdr.php
msgid "https://www.complianz.io"
msgstr ""

#: class-admin.php:158
msgid "It is highly recommended that you back up your data before updating to the Beta version. Beta versions are not intended for production environments or critical systems. They are best suited for users who are willing to explore new features and provide feedback."
msgstr ""

#: class-admin.php:207
#: cookiebanner/settings.php:613
#: settings/config/menu.php:244
#: settings/config/menu.php:306
#: settings/config/menu.php:322
#: settings/settings.php:346
#: settings/settings.php:347
msgid "Settings"
msgstr ""

#: class-admin.php:214
#: settings/config/menu.php:277
#: settings/config/menu.php:281
#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Support"
msgstr ""

#: class-admin.php:220
#: settings/settings.php:369
msgid "Upgrade to premium"
msgstr ""

#: class-admin.php:326
#: functions.php:1994
#: websitescan/class-wsc-scanner.php:1165
#: settings/build/1699.617faf04cf422977e5ed.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/6644.b133f6e44de4c0c58899.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9406.cf81ca916c1e2e336278.js:1
#: settings/build/9567.1bb4efce88343dfca145.js:1
msgid "Read more"
msgstr ""

#: class-admin.php:330
#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/2113.4da962fd404fcc001b7d.js:1
#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/2397.128a313ab0db7310b523.js:1
#: settings/build/3424.88f5ecf06f0e26d47eac.js:1
#: settings/build/5664.09a5d870cc30fa151cad.js:1
#: settings/build/6231.f9d17a5a1d14848fe3a8.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/8772.785d9c0620aa8b9946a2.js:1
#: settings/build/9056.7a23f9081f6350bd3057.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "Dismiss"
msgstr ""

#: class-document.php:481
#: documents/class-document.php:542
msgid "Region %s not activated for %s."
msgstr ""

#: class-document.php:590
#: documents/class-document.php:659
msgid "Annex"
msgstr ""

#: class-document.php:720
#: documents/class-document.php:785
msgid "(See paragraph %s)"
msgstr ""

#: class-document.php:726
#: documents/class-document.php:791
msgid "(See annex %s)"
msgstr ""

#: class-document.php:768
#: class-document.php:776
#: documents/class-document.php:821
#: documents/class-document.php:828
msgctxt "if phonenumber is entered, this string is part of the sentence \"you may contact %s, via %s or by telephone via %s\""
msgid "or by telephone on %s"
msgstr ""

#: class-document.php:858
#: documents/class-document.php:902
msgid "and"
msgstr ""

#: class-document.php:962
#: documents/admin-class-documents.php:1031
msgid "This website uses the Privacy Suite for WordPress by Complianz to collect and record Browser and Device-based Consent. For this functionality, your IP address is anonymized and stored in our database."
msgstr ""

#: class-document.php:964
#: documents/admin-class-documents.php:1033
msgid "This service does not process any personally identifiable information and does not share any data with the service provider."
msgstr ""

#: class-document.php:967
#: documents/admin-class-documents.php:1036
msgid "For more information, see the Complianz %sPrivacy Statement%s."
msgstr ""

#: class-document.php:1095
#: documents/class-document.php:1110
msgctxt "Subject in notification email"
msgid "Your legal documents on %s need to be updated."
msgstr ""

#: class-document.php:1102
#: documents/class-document.php:1117
msgctxt "notification email"
msgid "Your legal documents on %s have not been updated in 12 months. Please log in and run the %swizard%s in the Complianz plugin to check if everything is up to date."
msgstr ""

#: class-document.php:1106
#: documents/class-document.php:1121
msgid "This message was generated by Complianz GDPR/CCPA."
msgstr ""

#: class-document.php:1133
#: documents/class-document.php:1148
msgctxt "cookie policy"
msgid "You have loaded the Cookie Policy without javascript support."
msgstr ""

#: class-document.php:1134
#: documents/class-document.php:1149
msgctxt "cookie policy"
msgid "On AMP, you can use the manage consent button on the bottom of the page."
msgstr ""

#: class-document.php:1176
#: documents/class-document.php:1180
msgid "Click to accept marketing cookies"
msgstr ""

#: class-document.php:1246
#: documents/admin-class-documents.php:809
msgid "Legal Document"
msgstr ""

#: class-document.php:1262
#: documents/admin-class-documents.php:832
msgid "Document status"
msgstr ""

#: class-document.php:1284
#: documents/admin-class-documents.php:854
#: gutenberg/build/index.js:1
msgid "Synchronize document with Complianz"
msgstr ""

#: class-document.php:1288
#: documents/admin-class-documents.php:858
#: gutenberg/build/index.js:1
msgid "Edit document and stop synchronization"
msgstr ""

#: class-document.php:1537
#: class-document.php:1623
msgid "Update pages"
msgstr ""

#: class-document.php:1573
#: settings/build/3430.888f2122ddc42c0265c5.js:1
msgid "The pages marked with X should be added to your website. You can create these pages with a shortcode, a Gutenberg block or use the below \"Create missing pages\" button."
msgstr ""

#: class-document.php:1575
#: settings/build/3430.888f2122ddc42c0265c5.js:1
msgid "All necessary pages have been created already. You can update the page titles here if you want, then click the \"Update pages\" button."
msgstr ""

#: class-document.php:1581
#: settings/build/3430.888f2122ddc42c0265c5.js:1
msgid "You haven't selected any legal documents to create."
msgstr ""

#: class-document.php:1581
#: settings/build/3430.888f2122ddc42c0265c5.js:1
msgid "You can continue to the next step."
msgstr ""

#: class-document.php:1614
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9567.1bb4efce88343dfca145.js:1
msgid "Click to copy the document shortcode"
msgstr ""

#: class-document.php:1621
#: settings/build/3430.888f2122ddc42c0265c5.js:1
msgid "Create missing pages"
msgstr ""

#: class-document.php:1649
#: class-document.php:1713
msgid "Your WordPress version does not support the functions needed for this step. You can upgrade to the latest WordPress version, or add the pages manually to a menu."
msgstr ""

#: class-document.php:1682
#: class-document.php:1743
#: class-document.php:1763
#: settings/build/2302.a1257a2ff8404e29dc9f.js:1
#: settings/build/3737.d7bfee35ff013bc096fb.js:1
#: settings/build/5193.f173371ca89b7cfd1dc7.js:1
#: settings/build/9709.a24b2a1fe24c4ad466ea.js:1
msgid "Select a menu"
msgstr ""

#: class-document.php:2356
#: class-document.php:2400
#: documents/class-document.php:1456
#: documents/class-document.php:1500
msgid "Click to accept the cookies for this service"
msgstr ""

#: class-field.php:620
msgid "This field is required. Please complete the question before continuing"
msgstr ""

#: class-field.php:943
#: settings/build/847.89108489d5abafa2b4c5.js:1
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
#: settings/build/3068.896ccf52a406c91528e3.js:1
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
#: settings/build/7234.d838abdddb6468eb7b46.js:1
#: settings/build/8033.8caf3a2047bae05d7bc7.js:1
#: settings/build/8550.b17ddca5f7d4e170622e.js:1
#: settings/build/8985.6dc2f067dbc853b19630.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "No options found"
msgstr ""

#: class-field.php:946
msgid "More options"
msgstr ""

#: class-field.php:946
msgid "Less options"
msgstr ""

#: class-field.php:1022
msgid "Generated by Complianz"
msgstr ""

#: class-field.php:1024
msgid "Generate a comprehensive and legally validated %s"
msgstr ""

#: class-field.php:1029
#: settings/build/8358.de96f9467392c01f0c5f.js:1
msgid "Link to custom page"
msgstr ""

#: class-field.php:1030
msgid "Custom URL"
msgstr ""

#: class-field.php:1031
msgid "No document"
msgstr ""

#: class-field.php:1146
msgid "None selected"
msgstr ""

#: class-field.php:1509
#: class-field.php:1589
#: settings/build/1370.9dc9c94c443870d44ab9.js:1
#: settings/build/3549.e5ba19d1953def146594.js:1
#: settings/build/6009.498d0da1c09d09915322.js:1
msgid "Top"
msgstr ""

#: class-field.php:1514
#: class-field.php:1594
#: settings/build/1370.9dc9c94c443870d44ab9.js:1
#: settings/build/3549.e5ba19d1953def146594.js:1
#: settings/build/6009.498d0da1c09d09915322.js:1
msgid "Right"
msgstr ""

#: class-field.php:1519
#: class-field.php:1599
#: cookiebanner/settings.php:270
#: settings/build/1370.9dc9c94c443870d44ab9.js:1
#: settings/build/3549.e5ba19d1953def146594.js:1
#: settings/build/6009.498d0da1c09d09915322.js:1
msgid "Bottom"
msgstr ""

#: class-field.php:1524
#: class-field.php:1604
#: settings/build/1370.9dc9c94c443870d44ab9.js:1
#: settings/build/3549.e5ba19d1953def146594.js:1
#: settings/build/6009.498d0da1c09d09915322.js:1
msgid "Left"
msgstr ""

#: class-field.php:1848
msgid "Choose an option"
msgstr ""

#: class-field.php:1947
msgid "Choose file"
msgstr ""

#: class-field.php:1953
#: settings/config/fields/tools/data.php:28
#: settings/build/3785.e377b210085f5b6810ca.js:1
msgid "Import"
msgstr ""

#: class-field.php:1955
msgid "No file chosen"
msgstr ""

#: class-field.php:1974
#: class-field.php:2230
#: class-field.php:2341
#: class-field.php:2555
#: class-field.php:2960
#: class-field.php:3070
#: class-wizard.php:114
#: class-wizard.php:741
#: settings/build/11.e9c71a310cc4e025c93b.js:1
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/2980.92a8207f9d739ea56809.js:1
#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6281.81436365488010af52e4.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Save"
msgstr ""

#: class-field.php:1995
#: class-field.php:2403
#: class-field.php:2766
#: class-field.php:3013
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
msgid "Add new"
msgstr ""

#: class-field.php:2005
msgid "Description"
msgstr ""

#: class-field.php:2017
#: class-field.php:2234
#: class-field.php:2345
#: class-field.php:2556
#: class-field.php:2961
#: class-field.php:3071
msgid "Remove"
msgstr ""

#: class-field.php:2047
msgid "Add new cookie"
msgstr ""

#: class-field.php:2062
msgid "Cookies in %s"
msgstr ""

#: class-field.php:2062
msgid "Services in %s"
msgstr ""

#: class-field.php:2110
msgid "Add new service"
msgstr ""

#: class-field.php:2159
msgid "Name of the %s with whom you share the data"
msgstr ""

#: class-field.php:2170
msgid "Select the Processing Agreement you made with this %s, or %screate one%s"
msgstr ""

#: class-field.php:2179
msgid "No agreement selected"
msgstr ""

#: class-field.php:2182
#: settings/build/5175.4b3b51a33ec4e2266785.js:1
#: settings/build/6946.d959054c40656aabd21c.js:1
msgid "A Processing Agreement outside Complianz Privacy Suite"
msgstr ""

#: class-field.php:2193
msgid "%s country"
msgstr ""

#: class-field.php:2205
#: class-field.php:2319
#: settings/config/menu.php:78
#: settings/build/2111.847b0d94c7100f73f709.js:1
#: settings/build/3582.f939c1a1c9321a2e16ab.js:1
#: settings/build/5175.4b3b51a33ec4e2266785.js:1
#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/6946.d959054c40656aabd21c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "Purpose"
msgstr ""

#: class-field.php:2217
#: class-field.php:2329
msgid "What type of data is shared"
msgstr ""

#: class-field.php:2241
#: class-field.php:2351
#: class-field.php:2422
#: class-field.php:2560
#: class-field.php:2784
#: class-field.php:2965
#: class-field.php:3021
#: class-field.php:3075
msgid "New entry"
msgstr ""

#: class-field.php:2255
msgid "Add new %s"
msgstr ""

#: class-field.php:2294
msgid "Name of the Third Party with whom you share the data"
msgstr ""

#: class-field.php:2306
msgid "Third Party country"
msgstr ""

#: class-field.php:2361
#: settings/build/3582.f939c1a1c9321a2e16ab.js:1
msgid "Add new Third Party"
msgstr ""

#: class-field.php:2383
#: class-field.php:2744
#: class-field.php:2998
#: integrations/admin/integrations.php:235
#: integrations/admin/integrations.php:248
#: integrations/admin/integrations.php:263
msgid "Example"
msgstr ""

#: class-field.php:2439
#: class-field.php:2802
#: class-field.php:3033
#: DNSMPD/class-DNSMPD.php:273
#: DNSMPD/class-DNSMPD.php:275
#: templates/cookiepolicy/cookies_row.php:2
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/2111.847b0d94c7100f73f709.js:1
#: settings/build/3582.f939c1a1c9321a2e16ab.js:1
#: settings/build/5175.4b3b51a33ec4e2266785.js:1
#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/6946.d959054c40656aabd21c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Name"
msgstr ""

#: class-field.php:2473
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "This script contains an async attribute."
msgstr ""

#: class-field.php:2490
#: class-field.php:2839
#: gutenberg/build/index.js:1
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/1356.47a194349e5cee442354.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Category"
msgstr ""

#: class-field.php:2493
#: class-field.php:2842
#: cookiebanner/class-cookiebanner.php:840
#: cookiebanner/settings.php:944
#: cookiebanner/settings.php:945
#: cookiebanner/settings.php:946
#: settings/config/menu.php:112
#: settings/config/menu.php:438
#: settings/config/menu.php:449
#: gutenberg/build/index.js:1
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/1356.47a194349e5cee442354.js:1
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
#: settings/build/3518.c39c508126fbf4701fb6.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Statistics"
msgstr ""

#: class-field.php:2504
#: class-field.php:2853
#: cookiebanner/class-cookiebanner.php:844
#: cookiebanner/settings.php:1001
#: cookiebanner/settings.php:1002
#: cookiebanner/settings.php:1003
#: gutenberg/build/index.js:1
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/1356.47a194349e5cee442354.js:1
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Marketing"
msgstr ""

#: class-field.php:2517
#: class-field.php:2866
#: gutenberg/build/index.js:1
#: settings/build/93.3f39c6fe7156b09aa236.js:1
#: settings/build/847.89108489d5abafa2b4c5.js:1
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/5023.e939e70845602ba08cf1.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Placeholder"
msgstr ""

#: class-field.php:2520
#: class-field.php:2869
#: settings/build/847.89108489d5abafa2b4c5.js:1
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Enable placeholder"
msgstr ""

#: class-field.php:2536
#: class-field.php:2902
#: settings/build/847.89108489d5abafa2b4c5.js:1
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Enter the div class or ID that should be targeted."
msgstr ""

#: class-field.php:2812
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/4078.107fceffa2f236d7a108.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "URLs that should be blocked before consent."
msgstr ""

#: class-field.php:2885
#: settings/build/847.89108489d5abafa2b4c5.js:1
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "The blocked content is an iframe"
msgstr ""

#: class-field.php:2921
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Dependency"
msgstr ""

#: class-field.php:2925
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/8033.8caf3a2047bae05d7bc7.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Enable dependency"
msgstr ""

#: class-field.php:2948
msgid "No dependency"
msgstr ""

#: class-field.php:2954
msgid "waits for %s"
msgstr ""

#: class-field.php:3043
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/4078.107fceffa2f236d7a108.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "URLs that should be whitelisted."
msgstr ""

#: class-field.php:3114
msgid "No site logo configured."
msgstr ""

#: class-field.php:3147
#: settings/build/1439.dd2d909d2719f92b7192.js:1
#: settings/build/3068.896ccf52a406c91528e3.js:1
#: settings/build/8550.b17ddca5f7d4e170622e.js:1
msgid "Edit"
msgstr ""

#: class-review.php:83
msgid "Hi, you have been using Complianz | GDPR cookie consent for a month now, awesome! If you have a moment, please consider leaving a review on WordPress.org to spread the word. We greatly appreciate it! If you have any questions or feedback, leave us a %smessage%s."
msgstr ""

#: class-review.php:87
msgid "Leave a review"
msgstr ""

#: class-review.php:90
msgid "Maybe later"
msgstr ""

#: class-review.php:93
msgid "Don't show again"
msgstr ""

#: class-wizard.php:51
msgid "Great, the main wizard is completed. This means the general data is already in the system, and you can continue with the next question. This will start a new, empty document."
msgstr ""

#: class-wizard.php:56
msgid "The wizard isn't completed yet. If you have answered all required questions, you just need to click 'finish' to complete it. In the wizard some general data is entered which is needed for this document. %sPlease complete the wizard first%s."
msgstr ""

#: class-wizard.php:110
msgid "Not all required fields are completed yet. Please check the steps to complete all required questions"
msgstr ""

#: class-wizard.php:114
#: settings/build/2058.366ca8932d06dc54d28d.js:1
msgid "Click '%s' to complete the configuration. You can come back to change your configuration at any time."
msgstr ""

#: class-wizard.php:118
msgid "The cookie banner and the cookie blocker are now ready to be enabled."
msgstr ""

#: class-wizard.php:119
#: settings/build/2058.366ca8932d06dc54d28d.js:1
msgid "Please check your website after finishing the wizard to verify that your configuration is working properly."
msgstr ""

#: class-wizard.php:121
msgid "Your site does not require a cookie banner. If you think you need a cookie banner, please review your wizard settings."
msgstr ""

#: class-wizard.php:126
#: cookiebanner/admin/cookiebanner.php:121
#: cookiebanner/settings.php:62
msgid "We use technologies like cookies to store and/or access device information. We do this to improve browsing experience and to show (non-) personalized ads. Consenting to these technologies will allow us to process data such as browsing behavior or unique IDs on this site. Not consenting or withdrawing consent, may adversely affect certain features and functions."
msgstr ""

#: class-wizard.php:516
msgid "The wizard is currently being edited by %s"
msgstr ""

#: class-wizard.php:518
msgid "If this user stops editing, the lock will expire after %s minutes."
msgstr ""

#: class-wizard.php:612
#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "The Wizard"
msgstr ""

#: class-wizard.php:708
#: settings/build/11.e9c71a310cc4e025c93b.js:1
#: settings/build/2980.92a8207f9d739ea56809.js:1
#: settings/build/6281.81436365488010af52e4.js:1
#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "Previous"
msgstr ""

#: class-wizard.php:712
#: settings/build/11.e9c71a310cc4e025c93b.js:1
msgid "Save and Continue"
msgstr ""

#: class-wizard.php:723
msgid "View document"
msgstr ""

#: class-wizard.php:724
#: settings/config/menu.php:172
#: settings/build/11.e9c71a310cc4e025c93b.js:1
#: settings/build/2058.366ca8932d06dc54d28d.js:1
#: settings/build/2980.92a8207f9d739ea56809.js:1
#: settings/build/6281.81436365488010af52e4.js:1
msgid "Finish"
msgstr ""

#: class-wizard.php:733
msgid "Save and Style Cookie Banner"
msgstr ""

#: complianz-gpdr.php:46
msgid "Complianz GDPR cannot be activated. The plugin requires PHP 7.2 or higher"
msgstr ""

#: complianz-gpdr.php:57
msgid "Complianz GDPR cannot be activated. The plugin requires WordPress 4.9 or higher"
msgstr ""

#: config/config-i18n.php:11
#: integrations/plugins/elementor-pro/elementor-pro.php:82
#: settings/config/fields/wizard/consent.php:173
#: settings/config/fields/wizard/consent.php:196
#: settings/config/fields/wizard/consent.php:231
#: settings/config/fields/wizard/consent.php:256
#: settings/config/fields/wizard/consent.php:281
#: settings/config/fields/wizard/consent.php:305
msgid "Yes"
msgstr ""

#: config/config-i18n.php:12
#: integrations/plugins/elementor-pro/elementor-pro.php:83
#: settings/config/fields/wizard/consent.php:40
#: settings/config/fields/wizard/consent.php:174
#: settings/config/fields/wizard/consent.php:197
#: settings/config/fields/wizard/consent.php:232
#: settings/config/fields/wizard/consent.php:257
#: settings/config/fields/wizard/consent.php:282
#: settings/config/fields/wizard/consent.php:306
#: settings/config/fields/wizard/consent.php:400
#: settings/config/fields/wizard/services.php:89
msgid "No"
msgstr ""

#: config/config-i18n.php:13
msgid "I obtain permission from the person concerned"
msgstr ""

#: config/config-i18n.php:14
msgid "It is necessary for the execution of an agreement with the person concerned"
msgstr ""

#: config/config-i18n.php:15
msgid "I am obligated by law"
msgstr ""

#: config/config-i18n.php:16
msgid "It is necessary to fulfill a task concerning public law"
msgstr ""

#: config/config-i18n.php:17
msgid "It is necessary for my own legitimate interest, and that interest outweighs the interest of the person concerned"
msgstr ""

#: config/config-i18n.php:18
msgid "It is necessary to protect the life or physical safety of a person"
msgstr ""

#: config/config-i18n.php:19
msgid "It is necessary to carry out studies by a research body, ensuring, whenever possible, the anonymization of personal data"
msgstr ""

#: config/config-i18n.php:20
msgid "It is necessary for the regular exercise of rights in judicial, administrative or arbitration proceedings"
msgstr ""

#: config/config-i18n.php:21
msgid "It is necessary for the protection of health, exclusively, in a procedure performed by health professionals, health services or health authority"
msgstr ""

#: config/config-i18n.php:23
#: settings/config/fields/tools/placeholders.php:108
#: gutenberg/build/index.js:1
msgid "Default"
msgstr ""

#: config/config-i18n.php:24
msgid "English"
msgstr ""

#: config/config-i18n.php:25
msgid "Danish"
msgstr ""

#: config/config-i18n.php:26
msgid "German"
msgstr ""

#: config/config-i18n.php:27
msgid "Greek"
msgstr ""

#: config/config-i18n.php:28
msgid "Spanish"
msgstr ""

#: config/config-i18n.php:29
msgid "Estonian"
msgstr ""

#: config/config-i18n.php:30
msgid "French"
msgstr ""

#: config/config-i18n.php:31
msgid "Italian"
msgstr ""

#: config/config-i18n.php:32
msgid "Dutch"
msgstr ""

#: config/config-i18n.php:33
msgid "Norwegian"
msgstr ""

#: config/config-i18n.php:34
msgid "Swedish"
msgstr ""

#: config/config-i18n.php:35
msgid "Afghanistan"
msgstr ""

#: config/config-i18n.php:36
msgid "Aland Islands"
msgstr ""

#: config/config-i18n.php:37
msgid "Albania"
msgstr ""

#: config/config-i18n.php:38
msgid "Algeria"
msgstr ""

#: config/config-i18n.php:39
msgid "American Samoa"
msgstr ""

#: config/config-i18n.php:40
msgid "Andorra"
msgstr ""

#: config/config-i18n.php:41
msgid "Angola"
msgstr ""

#: config/config-i18n.php:42
msgid "Anguilla"
msgstr ""

#: config/config-i18n.php:43
msgid "Antarctica"
msgstr ""

#: config/config-i18n.php:44
msgid "Antigua And Barbuda"
msgstr ""

#: config/config-i18n.php:45
msgid "Argentina"
msgstr ""

#: config/config-i18n.php:46
msgid "Armenia"
msgstr ""

#: config/config-i18n.php:47
msgid "Aruba"
msgstr ""

#: config/config-i18n.php:48
#: config/config-i18n.php:306
msgid "Australia"
msgstr ""

#: config/config-i18n.php:49
msgid "Austria"
msgstr ""

#: config/config-i18n.php:50
msgid "Azerbaijan"
msgstr ""

#: config/config-i18n.php:51
msgid "Bahamas"
msgstr ""

#: config/config-i18n.php:52
msgid "Bahrain"
msgstr ""

#: config/config-i18n.php:53
msgid "Bangladesh"
msgstr ""

#: config/config-i18n.php:54
msgid "Barbados"
msgstr ""

#: config/config-i18n.php:55
msgid "Belarus"
msgstr ""

#: config/config-i18n.php:56
msgid "Belgium"
msgstr ""

#: config/config-i18n.php:57
msgid "Belize"
msgstr ""

#: config/config-i18n.php:58
msgid "Benin"
msgstr ""

#: config/config-i18n.php:59
msgid "Bermuda"
msgstr ""

#: config/config-i18n.php:60
msgid "Bhutan"
msgstr ""

#: config/config-i18n.php:61
msgid "Bolivia"
msgstr ""

#: config/config-i18n.php:62
msgid "Bosnia And Herzegovina"
msgstr ""

#: config/config-i18n.php:63
msgid "Botswana"
msgstr ""

#: config/config-i18n.php:64
msgid "Bouvet Island"
msgstr ""

#: config/config-i18n.php:65
#: config/config-i18n.php:311
msgid "Brazil"
msgstr ""

#: config/config-i18n.php:66
msgid "British Indian Ocean Territory"
msgstr ""

#: config/config-i18n.php:67
msgid "Brunei Darussalam"
msgstr ""

#: config/config-i18n.php:68
msgid "Bulgaria"
msgstr ""

#: config/config-i18n.php:69
msgid "Burkina Faso"
msgstr ""

#: config/config-i18n.php:70
msgid "Burundi"
msgstr ""

#: config/config-i18n.php:71
msgid "Cambodia"
msgstr ""

#: config/config-i18n.php:72
msgid "Cameroon"
msgstr ""

#: config/config-i18n.php:73
#: config/config-i18n.php:299
msgid "Canada"
msgstr ""

#: config/config-i18n.php:74
msgid "Cape Verde"
msgstr ""

#: config/config-i18n.php:75
msgid "Cayman Islands"
msgstr ""

#: config/config-i18n.php:76
msgid "Central African Republic"
msgstr ""

#: config/config-i18n.php:77
msgid "Chad"
msgstr ""

#: config/config-i18n.php:78
msgid "Chile"
msgstr ""

#: config/config-i18n.php:79
msgid "China"
msgstr ""

#: config/config-i18n.php:80
msgid "Christmas Island"
msgstr ""

#: config/config-i18n.php:81
msgid "Cocos (Keeling) Islands"
msgstr ""

#: config/config-i18n.php:82
msgid "Colombia"
msgstr ""

#: config/config-i18n.php:83
msgid "Comoros"
msgstr ""

#: config/config-i18n.php:84
msgid "Congo"
msgstr ""

#: config/config-i18n.php:85
msgid "Congo, Democratic Republic"
msgstr ""

#: config/config-i18n.php:86
msgid "Cook Islands"
msgstr ""

#: config/config-i18n.php:87
msgid "Costa Rica"
msgstr ""

#: config/config-i18n.php:88
msgid "Cote D'Ivoire"
msgstr ""

#: config/config-i18n.php:89
msgid "Croatia"
msgstr ""

#: config/config-i18n.php:90
msgid "Cuba"
msgstr ""

#: config/config-i18n.php:91
msgid "Cyprus"
msgstr ""

#: config/config-i18n.php:92
msgid "Czech Republic"
msgstr ""

#: config/config-i18n.php:93
msgid "Denmark"
msgstr ""

#: config/config-i18n.php:94
msgid "Djibouti"
msgstr ""

#: config/config-i18n.php:95
msgid "Dominica"
msgstr ""

#: config/config-i18n.php:96
msgid "Dominican Republic"
msgstr ""

#: config/config-i18n.php:97
msgid "Ecuador"
msgstr ""

#: config/config-i18n.php:98
msgid "Egypt"
msgstr ""

#: config/config-i18n.php:99
msgid "El Salvador"
msgstr ""

#: config/config-i18n.php:100
msgid "Equatorial Guinea"
msgstr ""

#: config/config-i18n.php:101
msgid "Eritrea"
msgstr ""

#: config/config-i18n.php:102
msgid "Estonia"
msgstr ""

#: config/config-i18n.php:103
msgid "Ethiopia"
msgstr ""

#: config/config-i18n.php:104
msgid "Falkland Islands (Malvinas)"
msgstr ""

#: config/config-i18n.php:105
msgid "Faroe Islands"
msgstr ""

#: config/config-i18n.php:106
msgid "Fiji"
msgstr ""

#: config/config-i18n.php:107
msgid "Finland"
msgstr ""

#: config/config-i18n.php:108
msgid "France"
msgstr ""

#: config/config-i18n.php:109
msgid "French Guiana"
msgstr ""

#: config/config-i18n.php:110
msgid "French Polynesia"
msgstr ""

#: config/config-i18n.php:111
msgid "French Southern Territories"
msgstr ""

#: config/config-i18n.php:112
msgid "Gabon"
msgstr ""

#: config/config-i18n.php:113
msgid "Gambia"
msgstr ""

#: config/config-i18n.php:114
msgid "Georgia"
msgstr ""

#: config/config-i18n.php:115
msgid "Germany"
msgstr ""

#: config/config-i18n.php:116
msgid "Ghana"
msgstr ""

#: config/config-i18n.php:117
msgid "Gibraltar"
msgstr ""

#: config/config-i18n.php:118
msgid "Greece"
msgstr ""

#: config/config-i18n.php:119
msgid "Greenland"
msgstr ""

#: config/config-i18n.php:120
msgid "Grenada"
msgstr ""

#: config/config-i18n.php:121
msgid "Guadeloupe"
msgstr ""

#: config/config-i18n.php:122
msgid "Guam"
msgstr ""

#: config/config-i18n.php:123
msgid "Guatemala"
msgstr ""

#: config/config-i18n.php:124
msgid "Guernsey"
msgstr ""

#: config/config-i18n.php:125
msgid "Guinea"
msgstr ""

#: config/config-i18n.php:126
msgid "Guinea-Bissau"
msgstr ""

#: config/config-i18n.php:127
msgid "Guyana"
msgstr ""

#: config/config-i18n.php:128
msgid "Haiti"
msgstr ""

#: config/config-i18n.php:129
msgid "Heard Island & Mcdonald Islands"
msgstr ""

#: config/config-i18n.php:130
msgid "Holy See (Vatican City State)"
msgstr ""

#: config/config-i18n.php:131
msgid "Honduras"
msgstr ""

#: config/config-i18n.php:132
msgid "Hong Kong"
msgstr ""

#: config/config-i18n.php:133
msgid "Hungary"
msgstr ""

#: config/config-i18n.php:134
msgid "Iceland"
msgstr ""

#: config/config-i18n.php:135
msgid "India"
msgstr ""

#: config/config-i18n.php:136
msgid "Indonesia"
msgstr ""

#: config/config-i18n.php:137
msgid "Iran, Islamic Republic Of"
msgstr ""

#: config/config-i18n.php:138
msgid "Iraq"
msgstr ""

#: config/config-i18n.php:139
msgid "Ireland"
msgstr ""

#: config/config-i18n.php:140
msgid "Isle Of Man"
msgstr ""

#: config/config-i18n.php:141
msgid "Israel"
msgstr ""

#: config/config-i18n.php:142
msgid "Italy"
msgstr ""

#: config/config-i18n.php:143
msgid "Jamaica"
msgstr ""

#: config/config-i18n.php:144
msgid "Japan"
msgstr ""

#: config/config-i18n.php:145
msgid "Jersey"
msgstr ""

#: config/config-i18n.php:146
msgid "Jordan"
msgstr ""

#: config/config-i18n.php:147
msgid "Kazakhstan"
msgstr ""

#: config/config-i18n.php:148
msgid "Kenya"
msgstr ""

#: config/config-i18n.php:149
msgid "Kiribati"
msgstr ""

#: config/config-i18n.php:150
msgid "Korea"
msgstr ""

#: config/config-i18n.php:151
msgid "Kuwait"
msgstr ""

#: config/config-i18n.php:152
msgid "Kyrgyzstan"
msgstr ""

#: config/config-i18n.php:153
msgid "Lao People's Democratic Republic"
msgstr ""

#: config/config-i18n.php:154
msgid "Latvia"
msgstr ""

#: config/config-i18n.php:155
msgid "Lebanon"
msgstr ""

#: config/config-i18n.php:156
msgid "Lesotho"
msgstr ""

#: config/config-i18n.php:157
msgid "Liberia"
msgstr ""

#: config/config-i18n.php:158
msgid "Libyan Arab Jamahiriya"
msgstr ""

#: config/config-i18n.php:159
msgid "Liechtenstein"
msgstr ""

#: config/config-i18n.php:160
msgid "Lithuania"
msgstr ""

#: config/config-i18n.php:161
msgid "Luxembourg"
msgstr ""

#: config/config-i18n.php:162
msgid "Macao"
msgstr ""

#: config/config-i18n.php:163
msgid "North Macedonia"
msgstr ""

#: config/config-i18n.php:164
msgid "Madagascar"
msgstr ""

#: config/config-i18n.php:165
msgid "Malawi"
msgstr ""

#: config/config-i18n.php:166
msgid "Malaysia"
msgstr ""

#: config/config-i18n.php:167
msgid "Maldives"
msgstr ""

#: config/config-i18n.php:168
msgid "Mali"
msgstr ""

#: config/config-i18n.php:169
msgid "Malta"
msgstr ""

#: config/config-i18n.php:170
msgid "Marshall Islands"
msgstr ""

#: config/config-i18n.php:171
msgid "Martinique"
msgstr ""

#: config/config-i18n.php:172
msgid "Mauritania"
msgstr ""

#: config/config-i18n.php:173
msgid "Mauritius"
msgstr ""

#: config/config-i18n.php:174
msgid "Mayotte"
msgstr ""

#: config/config-i18n.php:175
msgid "Mexico"
msgstr ""

#: config/config-i18n.php:176
msgid "Micronesia, Federated States Of"
msgstr ""

#: config/config-i18n.php:177
msgid "Moldova"
msgstr ""

#: config/config-i18n.php:178
msgid "Monaco"
msgstr ""

#: config/config-i18n.php:179
msgid "Mongolia"
msgstr ""

#: config/config-i18n.php:180
msgid "Montenegro"
msgstr ""

#: config/config-i18n.php:181
msgid "Montserrat"
msgstr ""

#: config/config-i18n.php:182
msgid "Morocco"
msgstr ""

#: config/config-i18n.php:183
msgid "Mozambique"
msgstr ""

#: config/config-i18n.php:184
msgid "Myanmar"
msgstr ""

#: config/config-i18n.php:185
msgid "Namibia"
msgstr ""

#: config/config-i18n.php:186
msgid "Nauru"
msgstr ""

#: config/config-i18n.php:187
msgid "Nepal"
msgstr ""

#: config/config-i18n.php:188
msgid "Netherlands"
msgstr ""

#: config/config-i18n.php:189
msgid "Netherlands Antilles"
msgstr ""

#: config/config-i18n.php:190
msgid "New Caledonia"
msgstr ""

#: config/config-i18n.php:191
msgid "New Zealand"
msgstr ""

#: config/config-i18n.php:192
msgid "Nicaragua"
msgstr ""

#: config/config-i18n.php:193
msgid "Niger"
msgstr ""

#: config/config-i18n.php:194
msgid "Nigeria"
msgstr ""

#: config/config-i18n.php:195
msgid "Niue"
msgstr ""

#: config/config-i18n.php:196
msgid "Norfolk Island"
msgstr ""

#: config/config-i18n.php:197
msgid "Northern Mariana Islands"
msgstr ""

#: config/config-i18n.php:198
msgid "Norway"
msgstr ""

#: config/config-i18n.php:199
msgid "Oman"
msgstr ""

#: config/config-i18n.php:200
msgid "Pakistan"
msgstr ""

#: config/config-i18n.php:201
msgid "Palau"
msgstr ""

#: config/config-i18n.php:202
msgid "Palestinian Territory, Occupied"
msgstr ""

#: config/config-i18n.php:203
msgid "Panama"
msgstr ""

#: config/config-i18n.php:204
msgid "Papua New Guinea"
msgstr ""

#: config/config-i18n.php:205
msgid "Paraguay"
msgstr ""

#: config/config-i18n.php:206
msgid "Peru"
msgstr ""

#: config/config-i18n.php:207
msgid "Philippines"
msgstr ""

#: config/config-i18n.php:208
msgid "Pitcairn"
msgstr ""

#: config/config-i18n.php:209
msgid "Poland"
msgstr ""

#: config/config-i18n.php:210
msgid "Portugal"
msgstr ""

#: config/config-i18n.php:211
msgid "Puerto Rico"
msgstr ""

#: config/config-i18n.php:212
msgid "Qatar"
msgstr ""

#: config/config-i18n.php:213
msgid "Reunion"
msgstr ""

#: config/config-i18n.php:214
msgid "Romania"
msgstr ""

#: config/config-i18n.php:215
msgid "Russian Federation"
msgstr ""

#: config/config-i18n.php:216
msgid "Rwanda"
msgstr ""

#: config/config-i18n.php:217
msgid "Saint Barthelemy"
msgstr ""

#: config/config-i18n.php:218
msgid "Saint Helena"
msgstr ""

#: config/config-i18n.php:219
msgid "Saint Kitts And Nevis"
msgstr ""

#: config/config-i18n.php:220
msgid "Saint Lucia"
msgstr ""

#: config/config-i18n.php:221
msgid "Saint Martin"
msgstr ""

#: config/config-i18n.php:222
msgid "Saint Pierre And Miquelon"
msgstr ""

#: config/config-i18n.php:223
msgid "Saint Vincent And Grenadines"
msgstr ""

#: config/config-i18n.php:224
msgid "Samoa"
msgstr ""

#: config/config-i18n.php:225
msgid "San Marino"
msgstr ""

#: config/config-i18n.php:226
msgid "Sao Tome And Principe"
msgstr ""

#: config/config-i18n.php:227
msgid "Saudi Arabia"
msgstr ""

#: config/config-i18n.php:228
msgid "Senegal"
msgstr ""

#: config/config-i18n.php:229
msgid "Serbia"
msgstr ""

#: config/config-i18n.php:230
msgid "Seychelles"
msgstr ""

#: config/config-i18n.php:231
msgid "Sierra Leone"
msgstr ""

#: config/config-i18n.php:232
msgid "Singapore"
msgstr ""

#: config/config-i18n.php:233
msgid "Slovakia"
msgstr ""

#: config/config-i18n.php:234
msgid "Slovenia"
msgstr ""

#: config/config-i18n.php:235
msgid "Solomon Islands"
msgstr ""

#: config/config-i18n.php:236
msgid "Somalia"
msgstr ""

#: config/config-i18n.php:237
#: config/config-i18n.php:308
msgid "South Africa"
msgstr ""

#: config/config-i18n.php:238
msgid "South Georgia And Sandwich Isl."
msgstr ""

#: config/config-i18n.php:239
msgid "Spain"
msgstr ""

#: config/config-i18n.php:240
msgid "Sri Lanka"
msgstr ""

#: config/config-i18n.php:241
msgid "Sudan"
msgstr ""

#: config/config-i18n.php:242
msgid "Suriname"
msgstr ""

#: config/config-i18n.php:243
msgid "Svalbard And Jan Mayen"
msgstr ""

#: config/config-i18n.php:244
msgid "Swaziland"
msgstr ""

#: config/config-i18n.php:245
msgid "Sweden"
msgstr ""

#: config/config-i18n.php:246
msgid "Switzerland"
msgstr ""

#: config/config-i18n.php:247
msgid "Syrian Arab Republic"
msgstr ""

#: config/config-i18n.php:248
msgid "Taiwan"
msgstr ""

#: config/config-i18n.php:249
msgid "Tajikistan"
msgstr ""

#: config/config-i18n.php:250
msgid "Tanzania"
msgstr ""

#: config/config-i18n.php:251
msgid "Thailand"
msgstr ""

#: config/config-i18n.php:252
msgid "Timor-Leste"
msgstr ""

#: config/config-i18n.php:253
msgid "Togo"
msgstr ""

#: config/config-i18n.php:254
msgid "Tokelau"
msgstr ""

#: config/config-i18n.php:255
msgid "Tonga"
msgstr ""

#: config/config-i18n.php:256
msgid "Trinidad And Tobago"
msgstr ""

#: config/config-i18n.php:257
msgid "Tunisia"
msgstr ""

#: config/config-i18n.php:258
msgid "Turkey"
msgstr ""

#: config/config-i18n.php:259
msgid "Turkmenistan"
msgstr ""

#: config/config-i18n.php:260
msgid "Turks And Caicos Islands"
msgstr ""

#: config/config-i18n.php:261
msgid "Tuvalu"
msgstr ""

#: config/config-i18n.php:262
msgid "Uganda"
msgstr ""

#: config/config-i18n.php:263
msgid "Ukraine"
msgstr ""

#: config/config-i18n.php:264
msgid "United Arab Emirates"
msgstr ""

#: config/config-i18n.php:265
#: config/config-i18n.php:303
msgid "United Kingdom"
msgstr ""

#: config/config-i18n.php:266
#: config/config-i18n.php:291
#: config/config-i18n.php:297
msgid "United States"
msgstr ""

#: config/config-i18n.php:267
msgid "United States Outlying Islands"
msgstr ""

#: config/config-i18n.php:268
msgid "Uruguay"
msgstr ""

#: config/config-i18n.php:269
msgid "Uzbekistan"
msgstr ""

#: config/config-i18n.php:270
msgid "Vanuatu"
msgstr ""

#: config/config-i18n.php:271
msgid "Venezuela"
msgstr ""

#: config/config-i18n.php:272
msgid "Viet Nam"
msgstr ""

#: config/config-i18n.php:273
msgid "Virgin Islands, British"
msgstr ""

#: config/config-i18n.php:274
msgid "Virgin Islands, U.S."
msgstr ""

#: config/config-i18n.php:275
msgid "Wallis And Futuna"
msgstr ""

#: config/config-i18n.php:276
msgid "Western Sahara"
msgstr ""

#: config/config-i18n.php:277
msgid "Yemen"
msgstr ""

#: config/config-i18n.php:278
msgid "Zambia"
msgstr ""

#: config/config-i18n.php:279
msgid "Zimbabwe"
msgstr ""

#: config/config-i18n.php:280
msgid "California (CPRA)"
msgstr ""

#: config/config-i18n.php:281
msgid "Colorado (CPA)"
msgstr ""

#: config/config-i18n.php:282
msgid "Connecticut (CTDPA)"
msgstr ""

#: config/config-i18n.php:283
msgid "Montana (MCDPA)"
msgstr ""

#: config/config-i18n.php:284
msgid "Nevada (NRS 603A)"
msgstr ""

#: config/config-i18n.php:285
msgid "Oregon (OCPA)"
msgstr ""

#: config/config-i18n.php:286
msgid "Texas (TDPSA)"
msgstr ""

#: config/config-i18n.php:287
msgid "Utah (UCPA)"
msgstr ""

#: config/config-i18n.php:288
msgid "Virginia (CDPA)"
msgstr ""

#: config/config-i18n.php:289
msgid "European Union (GDPR)"
msgstr ""

#: config/config-i18n.php:290
msgid "United Kingdom (UK-GDPR, PECR, Data Protection Act)"
msgstr ""

#: config/config-i18n.php:292
msgid "Canada (PIPEDA)"
msgstr ""

#: config/config-i18n.php:293
msgid "Australia (Privacy Act 1988)"
msgstr ""

#: config/config-i18n.php:294
msgid "South Africa (POPIA)"
msgstr ""

#: config/config-i18n.php:295
msgid "Brazil (LGPD)"
msgstr ""

#: config/config-i18n.php:296
msgid "US"
msgstr ""

#: config/config-i18n.php:298
msgid "CA"
msgstr ""

#: config/config-i18n.php:300
msgid "EU"
msgstr ""

#: config/config-i18n.php:301
msgid "European Union"
msgstr ""

#: config/config-i18n.php:302
msgid "UK"
msgstr ""

#: config/config-i18n.php:304
msgid "UK-GDPR"
msgstr ""

#: config/config-i18n.php:305
msgid "AU"
msgstr ""

#: config/config-i18n.php:307
msgid "ZA"
msgstr ""

#: config/config-i18n.php:309
msgid "POPIA"
msgstr ""

#: config/config-i18n.php:310
msgid "BR"
msgstr ""

#: config/config-i18n.php:312
msgid "LGPD"
msgstr ""

#: config/config-i18n.php:313
msgid "Contact - Through phone, mail, email and/or webforms"
msgstr ""

#: config/config-i18n.php:314
msgid "Payments"
msgstr ""

#: config/config-i18n.php:315
msgid "Registering an account"
msgstr ""

#: config/config-i18n.php:316
msgid "Newsletters"
msgstr ""

#: config/config-i18n.php:317
msgid "To support services or products that a customer wants to buy or has purchased"
msgstr ""

#: config/config-i18n.php:318
msgid "To be able to comply with legal obligations"
msgstr ""

#: config/config-i18n.php:319
msgid "Compiling and analyzing statistics for website improvement."
msgstr ""

#: config/config-i18n.php:320
msgid "To be able to offer personalized products and services"
msgstr ""

#: config/config-i18n.php:321
msgid "To sell or share data with a third party"
msgstr ""

#: config/config-i18n.php:322
msgid "Deliveries"
msgstr ""

#: config/config-i18n.php:323
msgid "A first and last name"
msgstr ""

#: config/config-i18n.php:324
msgid "Account name or alias"
msgstr ""

#: config/config-i18n.php:325
msgid "A home or other physical address, including street name and name of a city or town"
msgstr ""

#: config/config-i18n.php:326
msgid "An email address"
msgstr ""

#: config/config-i18n.php:327
msgid "A telephone number"
msgstr ""

#: config/config-i18n.php:328
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
msgid "IP Address"
msgstr ""

#: config/config-i18n.php:329
msgid "Internet activity information, including, but not limited to, browsing history, search history, and information regarding a consumer's interaction with an Internet Web site, application, or advertisement"
msgstr ""

#: config/config-i18n.php:330
msgid "Geolocation data"
msgstr ""

#: config/config-i18n.php:331
msgid "Marital status"
msgstr ""

#: config/config-i18n.php:332
msgid "Date of birth"
msgstr ""

#: config/config-i18n.php:333
msgid "Sex"
msgstr ""

#: config/config-i18n.php:334
msgid "Photos"
msgstr ""

#: config/config-i18n.php:335
msgid "Social Media accounts"
msgstr ""

#: config/config-i18n.php:336
msgid "A social security number"
msgstr ""

#: config/config-i18n.php:337
msgid "A signature"
msgstr ""

#: config/config-i18n.php:338
msgid "Physical characteristics or description"
msgstr ""

#: config/config-i18n.php:339
msgid "Passport number"
msgstr ""

#: config/config-i18n.php:340
msgid "Driver's license"
msgstr ""

#: config/config-i18n.php:341
msgid "State identification card number"
msgstr ""

#: config/config-i18n.php:342
msgid "Insurance policy number"
msgstr ""

#: config/config-i18n.php:343
msgid "Education information"
msgstr ""

#: config/config-i18n.php:344
msgid "Professional or employment-related information"
msgstr ""

#: config/config-i18n.php:345
msgid "Employment history"
msgstr ""

#: config/config-i18n.php:346
msgid "Financial information such as bank account number or credit card number"
msgstr ""

#: config/config-i18n.php:347
msgid "Medical information"
msgstr ""

#: config/config-i18n.php:348
msgid "Health insurance information"
msgstr ""

#: config/config-i18n.php:349
msgid "Commercial information, including records of personal property, products or services purchased, obtained, or considered"
msgstr ""

#: config/config-i18n.php:350
msgid "Biometric information"
msgstr ""

#: config/config-i18n.php:351
msgid "Audio, electronic, visual, thermal, olfactory, or similar information"
msgstr ""

#: config/config-i18n.php:352
msgid "a first and last name"
msgstr ""

#: config/config-i18n.php:353
msgid "a home or other physical address including street name and name of a city or town"
msgstr ""

#: config/config-i18n.php:354
msgid "an email address from the child"
msgstr ""

#: config/config-i18n.php:355
msgid "an email address from the parent or guardian"
msgstr ""

#: config/config-i18n.php:356
msgid "a telephone number"
msgstr ""

#: config/config-i18n.php:357
msgid "a Social Security number"
msgstr ""

#: config/config-i18n.php:358
msgid "an identifier that permits the physical or online contacting of a child"
msgstr ""

#: config/config-i18n.php:359
msgid "other information concerning the child or the parents, combined with an identifier as described above"
msgstr ""

#: config/documents/cookie-policy-au.php:122
#: config/documents/cookie-policy-br.php:102
#: config/documents/cookie-policy-ca.php:103
#: config/documents/cookie-policy-eu.php:118
#: config/documents/cookie-policy-us.php:137
#: config/documents/cookie-policy-za.php:98
msgid "%s is located in the United States."
msgid_plural "%s are located in the United States."
msgstr[0] ""
msgstr[1] ""

#: config/documents/cookie-policy-au.php:175
#: config/documents/cookie-policy-br.php:205
#: config/documents/cookie-policy-ca.php:192
#: config/documents/cookie-policy-eu.php:211
#: config/documents/cookie-policy-za.php:189
msgctxt "Legal document cookie policy"
msgid "Website:"
msgstr ""

#: config/documents/cookie-policy-au.php:176
#: config/documents/cookie-policy-br.php:206
#: config/documents/cookie-policy-ca.php:193
#: config/documents/cookie-policy-eu.php:212
#: config/documents/cookie-policy-za.php:190
msgctxt "Legal document cookie policy"
msgid "Email:"
msgstr ""

#: config/documents/cookie-policy-br.php:6
msgctxt "Legal document cookie policy"
msgid "This Cookie Policy was last updated on %s and applies to citizens and legal permanent residents of Brazil."
msgstr ""

#: config/documents/cookie-policy-br.php:9
#: config/documents/cookie-policy-ca.php:13
#: config/documents/cookie-policy-eu.php:9
#: config/documents/cookie-policy-za.php:9
msgctxt "Legal document cookie policy:paragraph title"
msgid "Introduction"
msgstr ""

#: config/documents/cookie-policy-br.php:10
#: config/documents/cookie-policy-ca.php:14
#: config/documents/cookie-policy-eu.php:10
#: config/documents/cookie-policy-za.php:10
msgctxt "Legal document cookie policy"
msgid "Our website, %s (hereinafter: \"the website\") uses cookies and other related technologies (for convenience all technologies are referred to as \"cookies\"). Cookies are also placed by third parties we have engaged. In the document below we inform you about the use of cookies on our website."
msgstr ""

#: config/documents/cookie-policy-br.php:13
#: config/documents/cookie-policy-ca.php:18
#: config/documents/cookie-policy-eu.php:13
#: config/documents/cookie-policy-za.php:13
msgctxt "Legal document cookie policy:paragraph title"
msgid "What are cookies?"
msgstr ""

#: config/documents/cookie-policy-br.php:14
#: config/documents/cookie-policy-ca.php:19
#: config/documents/cookie-policy-eu.php:14
#: config/documents/cookie-policy-za.php:14
msgctxt "Legal document cookie policy"
msgid "A cookie is a small simple file that is sent along with pages of this website and stored by your browser on the hard drive of your computer or another device. The information stored therein may be returned to our servers or to the servers of the relevant third parties during a subsequent visit."
msgstr ""

#: config/documents/cookie-policy-br.php:17
#: config/documents/cookie-policy-ca.php:22
#: config/documents/cookie-policy-eu.php:17
#: config/documents/cookie-policy-za.php:17
msgctxt "Legal document cookie policy:paragraph title"
msgid "What are scripts?"
msgstr ""

#: config/documents/cookie-policy-br.php:18
#: config/documents/cookie-policy-ca.php:23
#: config/documents/cookie-policy-eu.php:18
#: config/documents/cookie-policy-za.php:18
msgctxt "Legal document cookie policy"
msgid "A script is a piece of program code that is used to make our website function properly and interactively. This code is executed on our server or on your device."
msgstr ""

#: config/documents/cookie-policy-br.php:21
#: config/documents/cookie-policy-ca.php:26
#: config/documents/cookie-policy-eu.php:21
#: config/documents/cookie-policy-za.php:21
msgctxt "Legal document cookie policy:paragraph title"
msgid "What is a web beacon?"
msgstr ""

#: config/documents/cookie-policy-br.php:22
#: config/documents/cookie-policy-ca.php:27
#: config/documents/cookie-policy-eu.php:22
#: config/documents/cookie-policy-za.php:22
msgctxt "Legal document cookie policy"
msgid "A web beacon (or a pixel tag) is a small, invisible piece of text or image on a website that is used to monitor traffic on a website. In order to do this, various data about you is stored using web beacons."
msgstr ""

#: config/documents/cookie-policy-br.php:27
#: config/documents/cookie-policy-ca.php:36
#: config/documents/cookie-policy-eu.php:27
#: config/documents/cookie-policy-za.php:27
msgctxt "Legal document cookie policy:paragraph title"
msgid "Cookies"
msgstr ""

#: config/documents/cookie-policy-br.php:31
#: config/documents/cookie-policy-ca.php:39
#: config/documents/cookie-policy-eu.php:31
#: config/documents/cookie-policy-za.php:31
msgctxt "Legal document cookie policy:paragraph title"
msgid "Technical or functional cookies"
msgstr ""

#: config/documents/cookie-policy-br.php:32
#: config/documents/cookie-policy-eu.php:32
#: config/documents/cookie-policy-za.php:32
msgctxt "Legal document cookie policy"
msgid "Some cookies ensure that certain parts of the website work properly and that your user preferences remain known. By placing functional cookies, we make it easier for you to visit our website. This way, you do not need to repeatedly enter the same information when visiting our website and, for example, the items remain in your shopping cart until you have paid. We may place these cookies without your consent."
msgstr ""

#: config/documents/cookie-policy-br.php:36
#: config/documents/cookie-policy-br.php:44
#: config/documents/cookie-policy-ca.php:44
#: config/documents/cookie-policy-eu.php:36
#: config/documents/cookie-policy-eu.php:44
#: config/documents/cookie-policy-za.php:36
#: config/documents/cookie-policy-za.php:44
msgctxt "Legal document cookie policy:paragraph title"
msgid "Statistics cookies"
msgstr ""

#: config/documents/cookie-policy-br.php:37
#: config/documents/cookie-policy-ca.php:45
#: config/documents/cookie-policy-eu.php:37
#: config/documents/cookie-policy-za.php:37
msgctxt "Legal document cookie policy"
msgid "We use statistics cookies to optimize the website experience for our users. With these statistics cookies we get insights in the usage of our website."
msgstr ""

#: config/documents/cookie-policy-br.php:38
#: config/documents/cookie-policy-eu.php:38
#: config/documents/cookie-policy-za.php:38
msgctxt "Legal document cookie policy"
msgid "We ask your permission to place statistics cookies."
msgstr ""

#: config/documents/cookie-policy-br.php:45
#: config/documents/cookie-policy-eu.php:45
#: config/documents/cookie-policy-za.php:45
msgctxt "Legal document cookie policy"
msgid "Because statistics are being tracked anonymously, no permission is asked to place statistics cookies."
msgstr ""

#: config/documents/cookie-policy-br.php:53
#: config/documents/cookie-policy-br.php:62
#: config/documents/cookie-policy-ca.php:52
#: config/documents/cookie-policy-ca.php:61
#: config/documents/cookie-policy-eu.php:53
#: config/documents/cookie-policy-eu.php:62
#: config/documents/cookie-policy-za.php:52
#: config/documents/cookie-policy-za.php:61
msgctxt "Legal document cookie policy:paragraph title"
msgid "Advertising cookies"
msgstr ""

#: config/documents/cookie-policy-br.php:54
#: config/documents/cookie-policy-ca.php:53
#: config/documents/cookie-policy-eu.php:54
#: config/documents/cookie-policy-za.php:53
msgctxt "Legal document cookie policy"
msgid "On this website we use advertising cookies, enabling us to personalize the advertisements for you, and we (and third parties) gain insights into the campaign results. This happens based on a profile we create based on your click and surfing on and outside %s. With these cookies you, as website visitor are linked to a unique ID, so you do not see the same ad more than once for example."
msgstr ""

#: config/documents/cookie-policy-br.php:63
#: config/documents/cookie-policy-ca.php:62
#: config/documents/cookie-policy-eu.php:63
#: config/documents/cookie-policy-za.php:62
msgctxt "Legal document cookie policy"
msgid "On this website we use advertising cookies, enabling us to gain insights into the campaign results. This happens based on a profile we create based on your behavior on %s. With these cookies you, as website visitor, are linked to a unique ID but these cookies will not profile your behavior and interests to serve personalized ads."
msgstr ""

#: config/documents/cookie-policy-br.php:71
#: config/documents/cookie-policy-ca.php:77
#: config/documents/cookie-policy-eu.php:87
#: config/documents/cookie-policy-za.php:75
msgctxt "cookie policy"
msgid "Marketing/Tracking cookies"
msgstr ""

#: config/documents/cookie-policy-br.php:72
#: config/documents/cookie-policy-ca.php:78
#: config/documents/cookie-policy-eu.php:88
#: config/documents/cookie-policy-za.php:76
msgctxt "cookie policy"
msgid "Marketing/Tracking cookies are cookies or any other form of local storage, used to create user profiles to display advertising or to track the user on this website or across several websites for similar marketing purposes."
msgstr ""

#: config/documents/cookie-policy-br.php:80
#: config/documents/cookie-policy-eu.php:96
#: config/documents/cookie-policy-za.php:70
msgctxt "Legal document cookie policy"
msgid "Because these cookies are marked as tracking cookies, we ask your permission to place these."
msgstr ""

#: config/documents/cookie-policy-br.php:85
#: config/documents/cookie-policy-br.php:93
#: config/documents/cookie-policy-ca.php:86
#: config/documents/cookie-policy-ca.php:94
#: config/documents/cookie-policy-eu.php:101
#: config/documents/cookie-policy-eu.php:109
#: config/documents/cookie-policy-us.php:120
#: config/documents/cookie-policy-za.php:81
#: config/documents/cookie-policy-za.php:89
msgctxt "Legal document cookie policy:paragraph title"
msgid "Social media"
msgstr ""

#: config/documents/cookie-policy-br.php:86
#: config/documents/cookie-policy-ca.php:87
#: config/documents/cookie-policy-eu.php:102
#: config/documents/cookie-policy-us.php:121
#: config/documents/cookie-policy-za.php:82
msgctxt "Legal document cookie policy"
msgid "On our website, we have included content to promote web pages (e.g. “like”, “pin”) or share (e.g. “tweet”) on social networks. This content is embedded with code derived from third parties and places cookies. This content might store and process certain information for personalized advertising."
msgstr ""

#: config/documents/cookie-policy-br.php:94
#: config/documents/cookie-policy-ca.php:95
#: config/documents/cookie-policy-eu.php:110
#: config/documents/cookie-policy-za.php:90
msgctxt "Legal document cookie policy"
msgid "On our website, we have included content from %s to promote web pages (e.g. “like”, “pin”) or share (e.g. “tweet”) on social networks like %s. This content is embedded with code derived from %s and places cookies. This content might store and process certain information for personalized advertising."
msgstr ""

#: config/documents/cookie-policy-br.php:102
#: config/documents/cookie-policy-br.php:110
#: config/documents/cookie-policy-ca.php:103
#: config/documents/cookie-policy-ca.php:111
#: config/documents/cookie-policy-eu.php:118
#: config/documents/cookie-policy-eu.php:126
#: config/documents/cookie-policy-us.php:137
#: config/documents/cookie-policy-us.php:145
#: config/documents/cookie-policy-za.php:98
#: config/documents/cookie-policy-za.php:106
msgid "Please read the privacy statement of these social networks (which can change regularly) to read what they do with your (personal) data which they process using these cookies. The data that is retrieved is anonymized as much as possible."
msgstr ""

#: config/documents/cookie-policy-br.php:118
#: config/documents/cookie-policy-ca.php:119
#: config/documents/cookie-policy-eu.php:134
#: config/documents/cookie-policy-za.php:114
msgctxt "Legal document cookie policy:paragraph title"
msgid "Placed cookies"
msgstr ""

#: config/documents/cookie-policy-br.php:123
#: config/documents/cookie-policy-br.php:132
#: config/documents/cookie-policy-ca.php:124
#: config/documents/cookie-policy-ca.php:131
#: config/documents/cookie-policy-eu.php:139
#: config/documents/cookie-policy-eu.php:148
#: config/documents/cookie-policy-za.php:119
#: config/documents/cookie-policy-za.php:128
msgctxt "Legal document cookie policy:paragraph title"
msgid "Consent"
msgstr ""

#: config/documents/cookie-policy-br.php:124
#: config/documents/cookie-policy-ca.php:132
#: config/documents/cookie-policy-eu.php:140
#: config/documents/cookie-policy-za.php:120
msgctxt "Legal document cookie policy"
msgid "When you visit our website for the first time, we will show you a pop-up with an explanation about cookies. As soon as you click on \"%s\", you consent to us using all cookies and plug-ins as described in the pop-up and this Cookie Policy. You can disable the use of cookies via your browser, but please note that our website may no longer work properly."
msgstr ""

#: config/documents/cookie-policy-br.php:133
#: config/documents/cookie-policy-eu.php:149
#: config/documents/cookie-policy-za.php:129
msgctxt "Legal document cookie policy"
msgid "When you visit our website for the first time, we will show you a pop-up with an explanation about cookies. As soon as you click on \"%s\", you consent to us using the categories of cookies and plug-ins you selected in the pop-up, as described in this Cookie Policy. You can disable the use of cookies via your browser, but please note that our website may no longer work properly."
msgstr ""

#: config/documents/cookie-policy-br.php:141
#: config/documents/cookie-policy-ca.php:139
#: config/documents/cookie-policy-eu.php:157
#: config/documents/cookie-policy-za.php:138
msgctxt "Legal document cookie policy:paragraph title"
msgid "Manage your consent settings"
msgstr ""

#: config/documents/cookie-policy-br.php:147
#: config/documents/cookie-policy-ca.php:146
#: config/documents/cookie-policy-eu.php:165
#: config/documents/cookie-policy-za.php:145
msgctxt "Legal document cookie policy:paragraph title"
msgid "Vendors"
msgstr ""

#: config/documents/cookie-policy-br.php:154
#: config/documents/cookie-policy-ca.php:157
#: config/documents/cookie-policy-eu.php:172
msgctxt "Legal document cookie policy:paragraph title"
msgid "Enabling/disabling and deleting cookies"
msgstr ""

#: config/documents/cookie-policy-br.php:155
#: config/documents/cookie-policy-eu.php:173
msgctxt "Legal document cookie policy"
msgid "You can use your internet browser to automatically or manually delete cookies. You can also specify that certain cookies may not be placed. Another option is to change the settings of your internet browser so that you receive a message each time a cookie is placed. For more information about these options, please refer to the instructions in the Help section of your browser."
msgstr ""

#: config/documents/cookie-policy-br.php:159
#: config/documents/cookie-policy-ca.php:163
#: config/documents/cookie-policy-eu.php:177
#: config/documents/cookie-policy-us.php:191
#: config/documents/cookie-policy-za.php:156
msgctxt "Legal document cookie policy"
msgid "Please note that our website may not work properly if all cookies are disabled. If you do delete the cookies in your browser, they will be placed again after your consent when you visit our website again."
msgstr ""

#: config/documents/cookie-policy-br.php:163
#: config/documents/cookie-policy-ca.php:167
#: config/documents/cookie-policy-eu.php:181
#: config/documents/cookie-policy-za.php:161
msgctxt "Legal document cookie policy:paragraph title"
msgid "Your rights with respect to personal data"
msgstr ""

#: config/documents/cookie-policy-br.php:165
#: config/documents/cookie-policy-ca.php:170
#: config/documents/cookie-policy-eu.php:183
#: config/documents/cookie-policy-za.php:163
msgctxt "Legal document cookie policy"
msgid "You have the following rights with respect to your personal data:"
msgstr ""

#: config/documents/cookie-policy-br.php:172
msgctxt "Legal document"
msgid "confirmation of the existence of processing;"
msgstr ""

#: config/documents/cookie-policy-br.php:173
msgctxt "Legal document cookie policy"
msgid "access to data;"
msgstr ""

#: config/documents/cookie-policy-br.php:174
msgctxt "Legal document cookie policy"
msgid "correction of incomplete, inaccurate, or outdated data;"
msgstr ""

#: config/documents/cookie-policy-br.php:175
msgctxt "Legal document cookie policy"
msgid "anonymization, blocking, or deletion of unnecessary, excessive, or processed data in disagreement with the provisions of the General Law for the Protection of Personal Data (LGPD);"
msgstr ""

#: config/documents/cookie-policy-br.php:176
msgctxt "Legal document cookie policy"
msgid "portability of data to another service or product provider, upon express request, in accordance with the regulations of the national authority, observing commercial and industrial secrets;"
msgstr ""

#: config/documents/cookie-policy-br.php:177
msgctxt "Legal document cookie policy"
msgid "deletion of personal data processed with the consent of the holder, except in the cases provided for in art. 16 of the General Law for the Protection of Personal Data (LGPD);"
msgstr ""

#: config/documents/cookie-policy-br.php:178
msgctxt "Legal document cookie policy"
msgid "information on public and private entities with which the controller shared data;"
msgstr ""

#: config/documents/cookie-policy-br.php:179
msgctxt "Legal document cookie policy"
msgid "information on the possibility of not providing consent and on the consequences of denial."
msgstr ""

#: config/documents/cookie-policy-br.php:184
msgctxt "Legal document cookie policy"
msgid "To exercise these rights, please contact us. Please refer to the contact details at the bottom of this Cookie Policy. If you have a complaint about how we handle your data, we would like to hear from you, but you also have the right to submit a complaint to the National Data Protection Authority (ANPD):"
msgstr ""

#: config/documents/cookie-policy-br.php:194
msgctxt "Legal document"
msgid "Website"
msgstr ""

#: config/documents/cookie-policy-br.php:198
#: config/documents/cookie-policy-ca.php:184
#: config/documents/cookie-policy-eu.php:204
#: config/documents/cookie-policy-za.php:182
msgctxt "Legal document cookie policy:paragraph title"
msgid "Contact details"
msgstr ""

#: config/documents/cookie-policy-br.php:199
#: config/documents/cookie-policy-ca.php:185
#: config/documents/cookie-policy-eu.php:205
#: config/documents/cookie-policy-za.php:183
msgctxt "Legal document cookie policy"
msgid "For questions and/or comments about our Cookie Policy and this statement, please contact us by using the following contact details:"
msgstr ""

#: config/documents/cookie-policy-br.php:211
#: config/documents/cookie-policy-ca.php:198
#: config/documents/cookie-policy-eu.php:217
#: config/documents/cookie-policy-za.php:195
msgctxt "Legal document cookie policy"
msgid "This Cookie Policy was synchronized with %scookiedatabase.org%s on %s."
msgstr ""

#: config/documents/cookie-policy-ca.php:10
msgctxt "Legal document cookie policy"
msgid "This page was last changed on %s, last checked on %s and applies to citizens and legal permanent residents of Canada."
msgstr ""

#: config/documents/cookie-policy-ca.php:31
msgctxt "Legal document cookie policy:paragraph title"
msgid "Third parties"
msgstr ""

#: config/documents/cookie-policy-ca.php:32
msgctxt "Legal document cookie policy"
msgid "We have made agreements about the use of cookies with other companies that place cookies. However, we cannot guarantee that these third parties handle your personal data in a reliable or secure manner. Parties such as Google are to be considered as independent data controllers. We recommend that you read the privacy statements of these companies."
msgstr ""

#: config/documents/cookie-policy-ca.php:40
msgctxt "Legal document cookie policy"
msgid "Some cookies ensure that certain parts of the website work properly and that your user preferences remain known. By placing functional cookies, we make it easier for you to visit our website. This way, you do not need to repeatedly enter the same information when visiting our website and, for example, the items remain in your shopping cart until you have paid."
msgstr ""

#: config/documents/cookie-policy-ca.php:70
msgctxt "Legal document cookie policy"
msgid "You can object to the tracking by these cookies by clicking the \"Manage Consent\" button."
msgstr ""

#: config/documents/cookie-policy-ca.php:125
msgctxt "Legal document cookie policy"
msgid "When you visit our website for the first time, we will show you a pop-up with an explanation about cookies. You do have the right to opt-out and to object against the further use of non-functional cookies."
msgstr ""

#: config/documents/cookie-policy-ca.php:143
msgctxt "Legal document cookie policy:paragraph title"
msgid "You can also disable the use of cookies via your browser, but please note that our website may no longer work properly."
msgstr ""

#: config/documents/cookie-policy-ca.php:158
msgctxt "Legal document cookie policy"
msgid "You can use your internet browser to automatically or manually delete cookies. You can also specify that certain cookies may not be placed. Another option is to change the settings of your internet browser so that you receive a message each time a cookie is placed. For more information about these options, please refer to the instructions in the Help section of your browser. Or you can indicate your preferences on the following page: %syouradchoices.ca%s"
msgstr ""

#: config/documents/cookie-policy-ca.php:172
msgctxt "Legal document cookie policy"
msgid "you may submit a request for access to the data we process about you;"
msgstr ""

#: config/documents/cookie-policy-ca.php:173
msgctxt "Legal document cookie policy"
msgid "you may object to the processing;"
msgstr ""

#: config/documents/cookie-policy-ca.php:174
msgctxt "Legal document cookie policy"
msgid "you may request an overview, in a commonly used format, of the data we process about you;"
msgstr ""

#: config/documents/cookie-policy-ca.php:175
msgctxt "Legal document cookie policy"
msgid "you may request correction or deletion of the data if it is incorrect or not or no longer relevant. Where appropriate, the amended information shall be transmitted to third parties having access to the information in question."
msgstr ""

#: config/documents/cookie-policy-ca.php:176
msgctxt "Legal document cookie policy"
msgid "You have the right to withdraw consent at any time, subject to legal or contractual restrictions and reasonable notice. You will be informed of the implications of such withdrawal."
msgstr ""

#: config/documents/cookie-policy-ca.php:177
msgctxt "Legal document cookie policy"
msgid "You have the right to address a challenge concerning non-compliance with PIPEDA to our organization and, if the issue is not resolved, to the Office of the Privacy Commissioner of Canada."
msgstr ""

#: config/documents/cookie-policy-ca.php:179
msgctxt "Legal document cookie policy"
msgid "To exercise these rights, please contact us. Please refer to the contact details at the bottom of this Cookie Policy. If you have a complaint about how we handle your data, we would like to hear from you."
msgstr ""

#: config/documents/cookie-policy-eu.php:6
msgctxt "Legal document cookie policy"
msgid "This Cookie Policy was last updated on %s and applies to citizens and legal permanent residents of the European Economic Area and Switzerland."
msgstr ""

#: config/documents/cookie-policy-eu.php:190
#: config/documents/cookie-policy-za.php:169
msgctxt "Legal document cookie policy"
msgid "You have the right to know why your personal data is needed, what will happen to it, and how long it will be retained for."
msgstr ""

#: config/documents/cookie-policy-eu.php:191
#: config/documents/cookie-policy-za.php:170
msgctxt "Legal document cookie policy"
msgid "Right of access: You have the right to access your personal data that is known to us."
msgstr ""

#: config/documents/cookie-policy-eu.php:192
#: config/documents/cookie-policy-za.php:171
msgctxt "Legal document cookie policy"
msgid "Right to rectification: you have the right to supplement, correct, have deleted or blocked your personal data whenever you wish."
msgstr ""

#: config/documents/cookie-policy-eu.php:193
#: config/documents/cookie-policy-za.php:172
msgctxt "Legal document cookie policy"
msgid "If you give us your consent to process your data, you have the right to revoke that consent and to have your personal data deleted."
msgstr ""

#: config/documents/cookie-policy-eu.php:194
msgctxt "Legal document cookie policy"
msgid "Right to transfer your data: you have the right to request all your personal data from the controller and transfer it in its entirety to another controller."
msgstr ""

#: config/documents/cookie-policy-eu.php:195
#: config/documents/cookie-policy-za.php:173
msgctxt "Legal document cookie policy"
msgid "Right to object: you may object to the processing of your data. We comply with this, unless there are justified grounds for processing."
msgstr ""

#: config/documents/cookie-policy-eu.php:200
msgctxt "Legal document cookie policy"
msgid "To exercise these rights, please contact us. Please refer to the contact details at the bottom of this Cookie Policy. If you have a complaint about how we handle your data, we would like to hear from you, but you also have the right to submit a complaint to the supervisory authority (the Data Protection Authority)."
msgstr ""

#: config/documents/cookie-policy-us.php:129
msgid "On our website, we have included content from %s to promote web pages (e.g. “like”, “pin”) or share (e.g. “tweet”) on social networks like %s. This content is embedded with code derived from %s and places cookies. This content might store and process certain information for personalized advertising."
msgstr ""

#: config/documents/cookie-policy-za.php:6
msgctxt "Legal document cookie policy"
msgid "This Cookie Policy was last updated on %s and applies to citizens and legal permanent residents of South Africa."
msgstr ""

#: config/documents/cookie-policy-za.php:178
msgctxt "Legal document cookie policy"
msgid "To exercise these rights, please contact us. Please refer to the contact details at the bottom of this Cookie Policy. If you have a complaint about how we handle your data, we would like to hear from you, but you also have the right to submit a complaint to the Information Regulator South Africa: P.O Box 31533, Braamfontein, Johannesburg, 2017 Complaints email: <EMAIL>"
msgstr ""

#: config/documents/documents.php:6
#: settings/config/fields/wizard/general.php:136
#: gutenberg/build/index.js:1
msgid "Cookie Policy"
msgstr ""

#: config/documents/documents.php:10
#: integrations/plugins/gravity-forms.php:147
#: integrations/plugins/gravity-forms.php:186
#: settings/config/fields-notices.php:129
#: settings/config/fields-notices.php:136
#: settings/config/fields/wizard/general.php:151
#: settings/config/menu.php:467
#: gutenberg/build/index.js:1
msgid "Privacy Statement"
msgstr ""

#: config/documents/documents.php:14
msgid "Children's statement"
msgstr ""

#: config/documents/documents.php:18
msgid "Impressum"
msgstr ""

#: config/documents/documents.php:22
#: settings/config/fields/wizard/general.php:196
#: settings/config/menu.php:46
#: gutenberg/build/index.js:1
msgid "Disclaimer"
msgstr ""

#: config/documents/documents.php:33
msgid "Cookie Policy (EU)"
msgstr ""

#: config/documents/documents.php:41
msgid "Privacy Statement (EU)"
msgstr ""

#: config/documents/documents.php:59
msgid "Privacy Statement (US)"
msgstr ""

#: config/documents/documents.php:70
msgid "Cookie Policy (UK)"
msgstr ""

#: config/documents/documents.php:78
msgid "Privacy Statement (UK)"
msgstr ""

#: config/documents/documents.php:88
msgid "Cookie Policy (CA)"
msgstr ""

#: config/documents/documents.php:96
msgid "Privacy Statement (CA)"
msgstr ""

#: config/documents/documents.php:114
msgid "Privacy Statement (AU)"
msgstr ""

#: config/documents/documents.php:124
msgid "Cookie Policy (ZA)"
msgstr ""

#: config/documents/documents.php:132
msgid "Privacy Statement (ZA)"
msgstr ""

#: config/documents/documents.php:142
msgid "Cookie Policy (BR)"
msgstr ""

#: config/documents/documents.php:150
msgid "Privacy Statement (BR)"
msgstr ""

#: config/documents/documents.php:160
#: settings/config/fields/wizard/general.php:171
#: settings/config/menu.php:40
msgid "Imprint"
msgstr ""

#: config/warnings.php:226
msgid "Your PHP version is lower than the recommended PHP version. Some features are not available. Support for this PHP version will be dropped soon."
msgstr ""

#: config/warnings.php:233
msgid "Migrate.js, which allowed a smooth upgrade to 6.0, has been deprecated."
msgstr ""

#: config/warnings.php:256
msgid "Do Not Track and Global Privacy Control are respected."
msgstr ""

#: config/warnings.php:263
msgid "Please check if your REST API is loading correctly. Your site currently is using the slower Ajax fallback method to load the settings."
msgstr ""

#: config/warnings.php:272
msgid "You have currently selected an informal language, which will result in informal use of language on the legal documents. If you prefer the formal style, you can activate this in the general settings."
msgstr ""

#: config/warnings.php:282
msgid "Google Fonts requires your attention."
msgstr ""

#: config/warnings.php:282
msgid "We have added additional support and recommend reviewing your settings."
msgstr ""

#: config/warnings.php:282
msgid "Please read this %sarticle%s to read our position on self-hosting Google Fonts and Privacy by Design."
msgstr ""

#: config/warnings.php:292
msgid "No cookie changes have been detected."
msgstr ""

#: config/warnings.php:293
msgid "Cookie changes have been detected."
msgstr ""

#: config/warnings.php:293
msgid "Please review your cookies for changes."
msgstr ""

#: config/warnings.php:300
msgid "Last site scan completed on %s."
msgstr ""

#: config/warnings.php:301
msgid "No site scan has been completed yet."
msgstr ""

#: config/warnings.php:311
msgid "All required pages have been generated."
msgstr ""

#: config/warnings.php:312
msgid "Not all required pages have been generated."
msgstr ""

#: config/warnings.php:318
msgid "Harden your website and quickly detect vulnerabilities with Really Simple Security"
msgstr ""

#: config/warnings.php:328
msgid "Google Analytics is being used, but is not configured in Complianz."
msgstr ""

#: config/warnings.php:337
msgid "Google Tag Manager is being used, but is not configured in Complianz."
msgstr ""

#: config/warnings.php:346
msgid "Matomo is being used, but is not configured in Complianz."
msgstr ""

#: config/warnings.php:353
msgid "Your documents have not been updated in the past 12 months. Run the wizard to check your settings."
msgstr ""

#: config/warnings.php:361
msgid "You have cookies with incomplete descriptions."
msgstr ""

#: config/warnings.php:362
msgid "Enable the cookiedatabase.org API for automatic descriptions, or add these manually."
msgstr ""

#: config/warnings.php:372
msgid "You have a duplicate implementation of your statistics tool on your site."
msgstr ""

#: config/warnings.php:373
msgid "After the issue has been resolved, please re-run a scan to clear this message."
msgstr ""

#: config/warnings.php:384
msgid "JavaScript errors are detected on the front-end of your site. This may break the consent banner functionality."
msgstr ""

#: config/warnings.php:385
msgid "Last error in the console:"
msgstr ""

#: config/warnings.php:397
msgid "Your site requires a consent banner, which has been enabled."
msgstr ""

#: config/warnings.php:398
msgid "Your site is not configured to show a consent banner at the moment."
msgstr ""

#: config/warnings.php:409
msgid "Pretty permalinks are not enabled on your site. This can cause issues with the REST API, used by Complianz."
msgstr ""

#: config/warnings.php:419
msgid "Your uploads folder is not writable. Complianz needs this folder to save the consent banner CSS."
msgstr ""

#: config/warnings.php:430
msgid "We see you have enabled Google Maps as a service, but we can't find an integration. You can integrate manually if needed."
msgstr ""

#: config/warnings.php:438
msgid "We have detected the %s plugin on your website."
msgstr ""

#: config/warnings.php:438
msgid "As Complianz handles all the functionality this plugin provides, you should disable this plugin to prevent unexpected behaviour."
msgstr ""

#: config/warnings.php:445
msgid "Are you showing ads on your site? Consider implementing TCF."
msgstr ""

#: config/warnings.php:452
msgid "Create a Privacy Statement and other Legal Documents with Complianz."
msgstr ""

#: config/warnings.php:460
msgid "Legal compliance for webshops."
msgstr ""

#: config/warnings.php:468
msgid "Learn more about Google Consent Mode V2."
msgstr ""

#: config/warnings.php:476
msgid "Are you targeting multiple regions?"
msgstr ""

#: config/warnings.php:483
msgid "You might see a notice about Complianz when debugging your WordPress website. Although not a breaking issue, pleasee know we're attending to this issue."
msgstr ""

#: cookie/class-cookie.php:364
#: cookie/class-cookie.php:425
msgid "%s days"
msgstr ""

#: cookie/class-sync.php:106
#: cookie/class-sync.php:404
msgid "You haven't accepted the usage of the cookiedatabase.org API. To automatically complete your cookie descriptions, please choose yes."
msgstr ""

#: cookie/class-sync.php:120
#: cookie/class-sync.php:419
msgid "A request is already running. Please be patient until the current request finishes"
msgstr ""

#: cookie/class-sync.php:166
#: cookie/class-sync.php:462
msgid "Could not connect to cookiedatabase.org"
msgstr ""

#: cookie/class-sync.php:683
msgid "New cookie"
msgstr ""

#: cookiebanner/admin/cookiebanner.php:137
#: cookiebanner/admin/cookiebanner.php:263
msgid "Banner A"
msgstr ""

#: cookiebanner/admin/cookiebanner.php:143
msgid "Banner B"
msgstr ""

#: cookiebanner/admin/cookiebanner.php:286
#: cookiebanner/admin/cookiebanner.php:287
#: settings/config/menu.php:179
#: settings/build/3971.42c75f410294426ba63f.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8525.433647e550a4b16270cb.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Consent Banner"
msgstr ""

#: cookiebanner/admin/cookiebanner.php:304
msgid "Cookiebanner"
msgstr ""

#: cookiebanner/admin/cookiebanner.php:323
msgid "Disable Complianz on this page"
msgstr ""

#: cookiebanner/admin/cookiebanner.php:328
msgid "On a cookie policy, the banner will be minimized by default"
msgstr ""

#: cookiebanner/class-banner-loader.php:840
#: functions.php:1997
msgid "Purpose pending investigation"
msgstr ""

#: cookiebanner/class-banner-loader.php:1144
msgid "%s at %s"
msgstr ""

#: cookiebanner/class-banner-loader.php:1164
msgid "(not synced yet)"
msgstr ""

#: cookiebanner/class-cookiebanner.php:338
#: cookiebanner/settings.php:134
#: cookiebanner/settings.php:745
#: cookiebanner/settings.php:746
msgid "Manage consent"
msgstr ""

#: cookiebanner/class-cookiebanner.php:833
#: cookiebanner/settings.php:885
#: cookiebanner/settings.php:886
#: cookiebanner/settings.php:887
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
msgid "Functional"
msgstr ""

#: cookiebanner/class-cookiebanner.php:836
#: cookiebanner/settings.php:909
#: cookiebanner/settings.php:910
#: cookiebanner/settings.php:911
#: gutenberg/build/index.js:1
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
msgid "Preferences"
msgstr ""

#: cookiebanner/class-cookiebanner.php:849
msgid "Do Not Track"
msgstr ""

#: cookiebanner/class-cookiebanner.php:850
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
msgid "No Choice"
msgstr ""

#: cookiebanner/class-cookiebanner.php:853
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
msgid "No Warning"
msgstr ""

#: cookiebanner/class-cookiebanner.php:1294
msgctxt "as in: click to accept statistics cookies"
msgid "statistics"
msgstr ""

#: cookiebanner/class-cookiebanner.php:1294
msgctxt "as in: click to accept marketing cookies"
msgid "marketing"
msgstr ""

#: cookiebanner/class-cookiebanner.php:1303
msgid "Click button to enable {service}"
msgstr ""

#: cookiebanner/settings.php:72
msgid "To provide the best experiences, we use technologies like cookies to store and/or access device information. Consenting to these technologies will allow us to process data such as browsing behavior or unique IDs on this site. Not consenting or withdrawing consent, may adversely affect certain features and functions."
msgstr ""

#: cookiebanner/settings.php:75
msgid "Categories in France"
msgstr ""

#: cookiebanner/settings.php:76
msgid "Due to the French CNIL guidelines we suggest using the Accept - Deny - View preferences template. For more information, read about the CNIL updated privacy guidelines in this %sarticle%s."
msgstr ""

#: cookiebanner/settings.php:95
msgid "Consent banner title"
msgstr ""

#: cookiebanner/settings.php:96
msgid "Descriptive title of the cookiebanner"
msgstr ""

#: cookiebanner/settings.php:97
msgid "For internal use only"
msgstr ""

#: cookiebanner/settings.php:111
msgid "Accept - Deny - View Preferences"
msgstr ""

#: cookiebanner/settings.php:112
msgid "Accept - Deny - Save Preferences"
msgstr ""

#: cookiebanner/settings.php:113
msgid "Accept - Deny"
msgstr ""

#: cookiebanner/settings.php:115
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
msgid "Categories"
msgstr ""

#: cookiebanner/settings.php:116
msgid "With categories, you can let users choose which category of cookies they want to accept."
msgstr ""

#: cookiebanner/settings.php:117
msgid "Depending on your settings and cookies you use, there can be two or three categories. With Tag Manager you can use more, custom categories."
msgstr ""

#: cookiebanner/settings.php:135
msgid "Manage consent display options"
msgstr ""

#: cookiebanner/settings.php:136
msgid "Select how the manage consent text should appear."
msgstr ""

#: cookiebanner/settings.php:138
msgid "Hover on Desktop - Hide on Mobile (Default)"
msgstr ""

#: cookiebanner/settings.php:139
msgid "Hover on Desktop - Show on Mobile"
msgstr ""

#: cookiebanner/settings.php:140
msgid "Show everywhere"
msgstr ""

#: cookiebanner/settings.php:141
msgid "Hide everywhere"
msgstr ""

#: cookiebanner/settings.php:157
msgid "Disable consent banner"
msgstr ""

#: cookiebanner/settings.php:166
#: cookiebanner/settings.php:169
msgid "Default consent banner"
msgstr ""

#: cookiebanner/settings.php:170
msgid "When enabled, this is the consent banner that is used for all visitors. Enabling it will disable this setting on the current default banner. Disabling it will enable randomly a different default banner."
msgstr ""

#: cookiebanner/settings.php:187
msgid "Hide preview"
msgstr ""

#: cookiebanner/settings.php:196
msgid "Reset to default values"
msgstr ""

#: cookiebanner/settings.php:197
#: settings/config/fields/tools/data.php:37
#: settings/build/6449.6de76ea494992ea5f41f.js:1
msgid "Reset"
msgstr ""

#: cookiebanner/settings.php:200
msgid "Reset the consent banner"
msgstr ""

#: cookiebanner/settings.php:201
msgid "If you want to start from the default values, you can use the reset button."
msgstr ""

#: cookiebanner/settings.php:201
msgid "Texts will also get reset."
msgstr ""

#: cookiebanner/settings.php:218
msgid "Dismiss on scroll"
msgstr ""

#: cookiebanner/settings.php:219
msgid "When dismiss on scroll is enabled, the consent banner will be dismissed as soon as the user scrolls."
msgstr ""

#: cookiebanner/settings.php:234
msgid "Dismiss on time out"
msgstr ""

#: cookiebanner/settings.php:235
msgid "When dismiss on time out is enabled, the consent banner will be dismissed after 10 seconds, or the time you choose below."
msgstr ""

#: cookiebanner/settings.php:250
msgid "Timeout in seconds"
msgstr ""

#: cookiebanner/settings.php:267
msgid "Position"
msgstr ""

#: cookiebanner/settings.php:269
msgid "Center"
msgstr ""

#: cookiebanner/settings.php:271
msgid "Bottom left"
msgstr ""

#: cookiebanner/settings.php:272
msgid "Bottom right"
msgstr ""

#: cookiebanner/settings.php:282
msgid "Animation"
msgstr ""

#: cookiebanner/settings.php:284
#: settings/config/fields/wizard/general.php:37
#: settings/config/fields/wizard/general.php:156
#: settings/config/fields/wizard/general.php:176
#: settings/config/fields/wizard/general.php:194
msgid "None"
msgstr ""

#: cookiebanner/settings.php:285
msgid "Fade"
msgstr ""

#: cookiebanner/settings.php:286
msgid "Slide"
msgstr ""

#: cookiebanner/settings.php:299
msgid "Width of the banner in pixels"
msgstr ""

#: cookiebanner/settings.php:314
msgid "Checkbox style"
msgstr ""

#: cookiebanner/settings.php:315
msgid "This style is for the checkboxes on the consent banner, as well as on your policy for managing consent."
msgstr ""

#: cookiebanner/settings.php:317
msgid "Classic"
msgstr ""

#: cookiebanner/settings.php:318
msgid "Slider"
msgstr ""

#: cookiebanner/settings.php:335
msgid "Legal document links on banner"
msgstr ""

#: cookiebanner/settings.php:336
msgid "On the consent banner the generated documents are shown. The title is based on the actual post title."
msgstr ""

#: cookiebanner/settings.php:343
msgid "You can upload your own logo, hide it, or use the site logo."
msgstr ""

#: cookiebanner/settings.php:344
msgid "The site logo is the default logo set in your theme's site identity."
msgstr ""

#: cookiebanner/settings.php:346
msgid "Logo"
msgstr ""

#: cookiebanner/settings.php:348
msgid "Hide"
msgstr ""

#: cookiebanner/settings.php:349
msgid "Use Site Logo"
msgstr ""

#: cookiebanner/settings.php:350
msgid "Use \"Powered by Complianz\""
msgstr ""

#: cookiebanner/settings.php:351
msgid "Upload Custom Logo"
msgstr ""

#: cookiebanner/settings.php:368
msgid "Close button"
msgstr ""

#: cookiebanner/settings.php:369
msgid "If enabled, a close icon will be shown on your consent banner."
msgstr ""

#: cookiebanner/settings.php:379
msgid "Box shadow"
msgstr ""

#: cookiebanner/settings.php:388
msgid "Box shadow on header and footer"
msgstr ""

#: cookiebanner/settings.php:397
msgid "Show as soft cookie wall"
msgstr ""

#: cookiebanner/settings.php:398
msgid "Unlike the regular cookie wall, the soft cookie wall doesn't block visitors that do not deny consent or dismiss the banner. It will however forces a choice before visitors enter your website."
msgstr ""

#: cookiebanner/settings.php:399
msgid "Read more about %sthe soft cookie wall%s."
msgstr ""

#: cookiebanner/settings.php:402
msgid "Soft cookie wall"
msgstr ""

#: cookiebanner/settings.php:403
msgid "Read more about our privacy-friendly cookie wall."
msgstr ""

#: cookiebanner/settings.php:426
msgid "Border radius banner"
msgstr ""

#: cookiebanner/settings.php:440
msgid "Border width banner"
msgstr ""

#: cookiebanner/settings.php:455
msgid "Border radius buttons"
msgstr ""

#: cookiebanner/settings.php:464
msgid "Font size"
msgstr ""

#: cookiebanner/settings.php:475
#: functions.php:726
#: settings/config/menu.php:21
#: settings/config/menu.php:183
#: settings/config/menu.php:196
#: settings/config/menu.php:249
#: settings/config/menu.php:253
#: settings/config/menu.php:332
#: settings/config/menu.php:403
#: settings/config/menu.php:421
#: settings/config/menu.php:442
#: settings/build/1249.6de9f730cac4c47b6e71.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "General"
msgstr ""

#: cookiebanner/settings.php:476
#: cookiebanner/settings.php:484
#: cookiebanner/settings.php:529
#: cookiebanner/settings.php:562
#: cookiebanner/settings.php:589
#: cookiebanner/settings.php:622
msgid "Background"
msgstr ""

#: cookiebanner/settings.php:488
#: cookiebanner/settings.php:566
#: cookiebanner/settings.php:593
#: cookiebanner/settings.php:626
msgid "Border"
msgstr ""

#: cookiebanner/settings.php:498
#: cookiebanner/settings.php:570
#: cookiebanner/settings.php:597
#: cookiebanner/settings.php:630
msgid "Text"
msgstr ""

#: cookiebanner/settings.php:506
msgid "Color"
msgstr ""

#: cookiebanner/settings.php:510
msgid "Hyperlink"
msgstr ""

#: cookiebanner/settings.php:520
#: settings/config/menu.php:200
msgid "Toggles"
msgstr ""

#: cookiebanner/settings.php:533
msgid "Bullet"
msgstr ""

#: cookiebanner/settings.php:537
msgid "Inactive"
msgstr ""

#: cookiebanner/settings.php:553
#: cookiebanner/settings.php:766
#: cookiebanner/settings.php:768
#: cookiebanner/settings.php:782
#: cookiebanner/settings.php:784
#: integrations/plugins/gravity-forms.php:130
#: integrations/plugins/gravity-forms.php:135
#: integrations/plugins/gravity-forms.php:136
msgid "Accept"
msgstr ""

#: cookiebanner/settings.php:580
#: cookiebanner/settings.php:800
#: cookiebanner/settings.php:802
msgid "Deny"
msgstr ""

#: cookiebanner/settings.php:647
msgid "Disable width auto correction"
msgstr ""

#: cookiebanner/settings.php:655
msgid "This will disable a back-end javascript to keep the banner width aligned with other elements."
msgstr ""

#: cookiebanner/settings.php:663
msgid "Use Custom CSS"
msgstr ""

#: cookiebanner/settings.php:665
msgid "Custom CSS is not recommended for publishers that use TCF and additional frameworks due to strict guidelines."
msgstr ""

#: cookiebanner/settings.php:675
#: settings/config/fields/tools/documents.php:41
#: settings/config/menu.php:216
msgid "Custom CSS"
msgstr ""

#: cookiebanner/settings.php:676
#: settings/config/fields/tools/documents.php:42
msgid "You can add additional custom CSS here. For tips and CSS lessons, check out our documentation."
msgstr ""

#: cookiebanner/settings.php:747
msgid "Text on the manage consent tab"
msgstr ""

#: cookiebanner/settings.php:748
msgid "The tab will show after the visitor interacted with the banner, and can be used to make the consent banner reappear."
msgstr ""

#: cookiebanner/settings.php:756
msgid "Header"
msgstr ""

#: cookiebanner/settings.php:757
#: cookiebanner/settings.php:758
msgid "Manage Consent"
msgstr ""

#: cookiebanner/settings.php:767
#: cookiebanner/settings.php:783
msgid "Accept button"
msgstr ""

#: cookiebanner/settings.php:801
#: cookiebanner/settings.php:805
msgid "Deny button"
msgstr ""

#: cookiebanner/settings.php:806
msgid "This button will reject all cookies except necessary cookies, and dismisses the consent banner."
msgstr ""

#: cookiebanner/settings.php:821
#: cookiebanner/settings.php:822
#: cookiebanner/settings.php:823
msgid "View preferences"
msgstr ""

#: cookiebanner/settings.php:844
#: cookiebanner/settings.php:845
#: cookiebanner/settings.php:846
msgid "Save preferences"
msgstr ""

#: cookiebanner/settings.php:868
#: cookiebanner/settings.php:1044
msgid "Cookie message"
msgstr ""

#: cookiebanner/settings.php:897
msgid "The technical storage or access is strictly necessary for the legitimate purpose of enabling the use of a specific service explicitly requested by the subscriber or user, or for the sole purpose of carrying out the transmission of a communication over an electronic communications network."
msgstr ""

#: cookiebanner/settings.php:901
msgid "Functional description"
msgstr ""

#: cookiebanner/settings.php:926
msgid "The technical storage or access is necessary for the legitimate purpose of storing preferences that are not requested by the subscriber or user."
msgstr ""

#: cookiebanner/settings.php:930
msgid "Preferences description"
msgstr ""

#: cookiebanner/settings.php:954
msgid "The technical storage or access that is used exclusively for statistical purposes."
msgstr ""

#: cookiebanner/settings.php:955
msgid "Statistics description"
msgstr ""

#: cookiebanner/settings.php:977
msgid "The technical storage or access that is used exclusively for anonymous statistical purposes. Without a subpoena, voluntary compliance on the part of your Internet Service Provider, or additional records from a third party, information stored or retrieved for this purpose alone cannot usually be used to identify you."
msgstr ""

#: cookiebanner/settings.php:981
msgid "Anonymous statistics description"
msgstr ""

#: cookiebanner/settings.php:1018
msgid "The technical storage or access is required to create user profiles to send advertising, or to track the user on a website or across several websites for similar marketing purposes."
msgstr ""

#: cookiebanner/settings.php:1022
msgid "Marketing description"
msgstr ""

#: cookiebanner/templates/cookiebanner.php:11
msgid "Close dialog"
msgstr ""

#: cookiebanner/templates/cookiebanner.php:35
msgid "Always active"
msgstr ""

#: cookiebanner/templates/cookiebanner.php:120
msgid "Manage options"
msgstr ""

#: cookiebanner/templates/cookiebanner.php:121
msgid "Manage services"
msgstr ""

#: cookiebanner/templates/cookiebanner.php:122
msgid "Manage %s vendors"
msgstr ""

#: cookiebanner/templates/cookiebanner.php:123
msgid "Read more about these purposes"
msgstr ""

#: DNSMPD/class-admin-DNSMPD.php:139
msgid "You have open data requests."
msgstr ""

#: DNSMPD/class-admin-DNSMPD.php:139
msgid "Please check the data requests <a href=\"%s\">overview page</a>."
msgstr ""

#: DNSMPD/class-admin-DNSMPD.php:362
#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
#: settings/build/3068.896ccf52a406c91528e3.js:1
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
#: settings/build/7234.d838abdddb6468eb7b46.js:1
#: settings/build/8550.b17ddca5f7d4e170622e.js:1
msgid "Date"
msgstr ""

#: DNSMPD/class-DNSMPD.php:68
msgid "Global opt-out"
msgstr ""

#: DNSMPD/class-DNSMPD.php:69
msgid "Global opt-out from selling and sharing my personal information and limiting the use or disclosure of sensitive personal information."
msgstr ""

#: DNSMPD/class-DNSMPD.php:73
msgid "Do not sell my info"
msgstr ""

#: DNSMPD/class-DNSMPD.php:74
msgid "Do not sell my personal information for cross-context behavioral advertising"
msgstr ""

#: DNSMPD/class-DNSMPD.php:78
msgid "Limit sensitive data"
msgstr ""

#: DNSMPD/class-DNSMPD.php:79
msgid "Limit the use of my sensitive personal information"
msgstr ""

#: DNSMPD/class-DNSMPD.php:83
#: DNSMPD/class-DNSMPD.php:84
msgid "Request for access"
msgstr ""

#: DNSMPD/class-DNSMPD.php:88
#: DNSMPD/class-DNSMPD.php:89
msgid "Right to be Forgotten"
msgstr ""

#: DNSMPD/class-DNSMPD.php:93
#: DNSMPD/class-DNSMPD.php:94
msgid "Right to Data Portability"
msgstr ""

#: DNSMPD/class-DNSMPD.php:126
msgid "You have received a new data request on %s"
msgstr ""

#: DNSMPD/class-DNSMPD.php:127
msgid "Please check the data request on %s"
msgstr ""

#: DNSMPD/class-DNSMPD.php:194
msgid "Sorry, it looks like you're a bot"
msgstr ""

#: DNSMPD/class-DNSMPD.php:199
#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "Please enter a valid email address."
msgstr ""

#: DNSMPD/class-DNSMPD.php:204
msgid "Please enter your name"
msgstr ""

#: DNSMPD/class-DNSMPD.php:209
msgid "That's a long name you got there. Please try to shorten the name."
msgstr ""

#: DNSMPD/class-DNSMPD.php:245
msgid "Your request has been processed successfully!"
msgstr ""

#: DNSMPD/class-DNSMPD.php:247
msgid "Your request could not be processed. A request is already in progress for this email address or the form is not complete."
msgstr ""

#: DNSMPD/class-DNSMPD.php:276
msgid "Your name"
msgstr ""

#: DNSMPD/class-DNSMPD.php:279
#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
msgid "Email"
msgstr ""

#: DNSMPD/class-DNSMPD.php:293
#: settings/build/2827.bb78b0624a7550aee9be.js:1
msgid "Send"
msgstr ""

#: documents/admin-class-documents.php:309
#: settings/settings.php:639
msgid "Terms & Conditions"
msgstr ""

#: documents/class-document.php:2104
msgid "No title"
msgstr ""

#: functions.php:349
msgid "Based on your Analytics configuration you should fire Analytics on event cmplz_functional."
msgstr ""

#: functions.php:351
msgid "Based on your Analytics configuration you should fire Analytics on event cmplz_statistics."
msgstr ""

#: functions.php:425
msgid "Not found"
msgstr ""

#: functions.php:491
#: settings/build/5016.86c12b6af899e8605bab.js:1
msgid "Revoke"
msgstr ""

#: functions.php:523
#: functions.php:526
msgid "Current status: %s"
msgstr ""

#: functions.php:524
msgid "Accepted"
msgstr ""

#: functions.php:527
msgid "Denied"
msgstr ""

#: functions.php:1072
msgid "the page source"
msgstr ""

#: functions.php:1075
msgid "%s on line %s of %s"
msgstr ""

#: functions.php:1416
#: settings/build/11.e9c71a310cc4e025c93b.js:1
#: settings/build/4186.616704c21483c73a9fbe.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/4644.ce465c5d9733b80acf30.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9487.e9838335195a59707dfe.js:1
#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Upgrade"
msgstr ""

#: functions.php:1965
msgid "Miscellaneous"
msgstr ""

#: functions.php:1972
#: functions.php:1982
msgid "This data is not shared with third parties."
msgstr ""

#: functions.php:1974
#: functions.php:1979
msgid "For more information, please read the %s%s Privacy Statement%s."
msgstr ""

#: functions.php:1984
msgid "Sharing of data is pending investigation"
msgstr ""

#: functions.php:1987
msgctxt "Legal document cookie policy"
msgid "We use %s for %s."
msgstr ""

#: functions.php:2469
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
msgid "Opt-in"
msgstr ""

#: functions.php:2471
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
msgid "Opt-out"
msgstr ""

#: functions.php:2473
msgid "All consent types"
msgstr ""

#: functions.php:2833
msgid "In September 2023 the Quebec bill 64 will be enforced in Canada. In order to keep your site compliant, %sopt-in must be implemented for Canada%s. Please Navigate to the %sWizard%s and enable opt-in for Canada."
msgstr ""

#: functions.php:2834
msgid "Please be aware that this will activate opt-in for Canada, altering the banner and blocking non-functional scripts and cookies prior to consent. Please check the front-end of your site after activating opt-in."
msgstr ""

#: integrations/admin/integrations.php:362
#: settings/config/menu.php:135
#: websitescan/class-wsc-scanner.php:1243
#: websitescan/class-wsc-scanner.php:1252
#: websitescan/class-wsc-scanner.php:1261
msgid "Advertising"
msgstr ""

#: integrations/admin/integrations.php:428
msgid "We have enabled integrations for plugins and services, please double-check your configuration."
msgstr ""

#: integrations/plugins/beehive.php:31
msgid "You have selected you anonymize IP addresses. This setting is now enabled in Beehive."
msgstr ""

#: integrations/plugins/beehive.php:35
msgid "You have selected you do not share data with third-party networks. Display advertising is now disabled in Beehive."
msgstr ""

#: integrations/plugins/beehive.php:41
#: integrations/plugins/caos-host-analytics-local.php:88
#: integrations/plugins/google-analytics-dashboard-for-wp.php:71
#: integrations/plugins/google-tagmanager-for-wordpress.php:38
#: integrations/plugins/monsterinsights.php:61
#: integrations/plugins/woocommerce-google-analytics-integration.php:78
#: integrations/plugins/woocommerce-google-analytics-pro.php:91
msgid "Statistics plugin detected"
msgstr ""

#: integrations/plugins/beehive.php:42
#: integrations/plugins/caos-host-analytics-local.php:89
#: integrations/plugins/google-analytics-dashboard-for-wp.php:72
#: integrations/plugins/monsterinsights.php:62
#: integrations/plugins/woocommerce-google-analytics-integration.php:79
#: integrations/plugins/woocommerce-google-analytics-pro.php:92
msgid "You use %s, which means the answer to this question should be Google Analytics."
msgstr ""

#: integrations/plugins/burst-statistics.php:20
msgid "Burst Statistics will be configured automatically."
msgstr ""

#: integrations/plugins/contact-form-7.php:93
msgid "Click to accept marketing cookies and enable this form"
msgstr ""

#: integrations/plugins/contact-form-7.php:96
msgid "Please accept marketing cookies to enable this form"
msgstr ""

#: integrations/plugins/contact-form-7.php:189
#: integrations/plugins/wpforms.php:60
msgid "To submit this form, you need to accept our %sPrivacy Statement%s"
msgstr ""

#: integrations/plugins/contact-form-7.php:271
msgid "Due to continuous breaking changes in Contact Form 7 we are dropping the CF7 integration as of CF7 5.4. We have concluded that the only viable solution is for Contact Form 7 to integrate with the WP Consent API."
msgstr ""

#: integrations/plugins/disable-and-remove-google-fonts.php:11
#: integrations/plugins/embed-google-fonts.php:9
#: integrations/plugins/local-google-fonts.php:10
#: integrations/plugins/olympus-google-fonts.php:10
#: integrations/plugins/omgf.php:9
#: integrations/plugins/use-any-font.php:10
#: settings/config/fields/wizard/services.php:93
msgid "Self-hosting Google Fonts"
msgstr ""

#: integrations/plugins/disable-and-remove-google-fonts.php:12
#: integrations/plugins/embed-google-fonts.php:10
#: integrations/plugins/local-google-fonts.php:11
#: integrations/plugins/olympus-google-fonts.php:11
#: integrations/plugins/omgf.php:10
#: integrations/plugins/use-any-font.php:11
msgid "You have %s installed. We recommend saying 'Yes' to self-hosting Google Fonts"
msgstr ""

#: integrations/plugins/elementor-pro/elementor-pro.php:49
msgid "Generated template"
msgstr ""

#: integrations/plugins/elementor-pro/elementor-pro.php:50
msgid "A legal hub template has been generated. Click the more info link to edit it."
msgstr ""

#: integrations/plugins/elementor-pro/elementor-pro.php:56
msgid "Legal Hub"
msgstr ""

#: integrations/plugins/elementor-pro/elementor-pro.php:57
msgid "If you choose to create your Legal Hub with Elementor Pro we will import our default template."
msgstr ""

#: integrations/plugins/elementor-pro/elementor-pro.php:79
msgid "Do you want to create a Legal Hub with Elementor Pro?"
msgstr ""

#: integrations/plugins/forminator-addon-class-v2.php:21
#: integrations/plugins/forminator-addon-class.php:27
msgid "Integrate Forminator with Complianz Privacy Suite"
msgstr ""

#: integrations/plugins/forminator-addon-class-v2.php:24
#: integrations/plugins/forminator-addon-class.php:30
msgid "Sorry but we failed to activate the Complianz integration"
msgstr ""

#: integrations/plugins/forminator-addon-class-v2.php:27
#: integrations/plugins/forminator-addon-class.php:33
msgid "Sorry but we failed to deactivate the Complianz integration, please try again"
msgstr ""

#: integrations/plugins/forminator-addon-class-v2.php:30
#: integrations/plugins/forminator-addon-class.php:36
msgid "Sorry, we failed to update settings, please check your form and try again"
msgstr ""

#: integrations/plugins/forminator.php:15
msgid "Please accept cookies so we can validate your request with reCaptcha, and submit this form"
msgstr ""

#: integrations/plugins/forminator.php:76
msgid "Yes, I agree with the %sPrivacy Statement%s"
msgstr ""

#: integrations/plugins/forminator.php:80
#: integrations/plugins/happyforms.php:129
msgid "Privacy"
msgstr ""

#: integrations/plugins/google-analytics-dashboard-for-wp.php:53
#: integrations/plugins/google-tagmanager-for-wordpress.php:22
msgid "You have selected you anonymize IP addresses. This setting is now enabled in %s."
msgstr ""

#: integrations/plugins/google-analytics-dashboard-for-wp.php:57
msgid "You have selected you do not share data with third-party networks. Display advertising is now disabled in %s."
msgstr ""

#: integrations/plugins/google-site-kit.php:26
msgid "Because you're using %s, you can choose which plugin should insert the relevant snippet. If you want to use Google Consent Mode, you can only use the default, advanced mode. You can read more about configuring SiteKit and the different Consent Mode below."
msgstr ""

#: integrations/plugins/google-tagmanager-for-wordpress.php:25
msgid "You have selected you do not share data with third-party networks. Remarketing is now disabled in %s."
msgstr ""

#: integrations/plugins/google-tagmanager-for-wordpress.php:39
msgid "You use %s, which means the answer to this question should be Google Tag Manager."
msgstr ""

#: integrations/plugins/gravity-forms.php:52
msgid "Click to accept reCaptcha validation."
msgstr ""

#: integrations/plugins/gravity-forms.php:108
msgid "To submit this form, you need to accept our Privacy Statement"
msgstr ""

#: integrations/plugins/happyforms.php:123
msgid "Yes, I agree with the %sprivacy statement%s"
msgstr ""

#: integrations/plugins/monsterinsights.php:45
msgid "You have selected you anonymize IP addresses. This setting is now enabled in MonsterInsights."
msgstr ""

#: integrations/plugins/monsterinsights.php:49
msgid "You have selected you do not share data with third-party networks. Demographics is now disabled in MonsterInsights."
msgstr ""

#: integrations/plugins/wp-google-map-plugin.php:65
msgid "The WP Maps Pro integration has changed with the help of the authors of WP Maps, please check your implementation."
msgstr ""

#: mailer/class-mail.php:25
msgid "This email is part of the Complianz Notification System"
msgstr ""

#: mailer/class-mail.php:26
msgid "Notification by Complianz"
msgstr ""

#: mailer/class-mail.php:27
msgid "Learn more about our features!"
msgstr ""

#: mailer/class-mail.php:28
msgid "This email was sent to"
msgstr ""

#: mailer/class-mail.php:29
msgid "What now?"
msgstr ""

#: mailer/class-mail.php:30
msgid "I didn't change any settings in the plugin."
msgstr ""

#: mailer/class-mail.php:32
msgid "You have enabled a feature on %s. We think it's important to let you know a little bit more about this feature so you can use it without worries."
msgstr ""

#: mailer/class-mail.php:51
msgid "Email address not valid"
msgstr ""

#: mailer/class-mail.php:53
msgid "Complianz - Notification Test"
msgstr ""

#: mailer/class-mail.php:54
msgid "This email is confirmation that any email notices are likely to reach your inbox."
msgstr ""

#: mailer/class-mail.php:58
msgid "About notifications"
msgstr ""

#: mailer/class-mail.php:59
msgid "Email notifications are only sent for important updates, changes or when certain features are enabled."
msgstr ""

#: mailer/class-mail.php:65
msgid "Email sent! Please check your mail"
msgstr ""

#: mailer/class-mail.php:69
msgid "Email could not be sent."
msgstr ""

#: mailer/class-mail.php:71
#: upgrade/upgrade-to-pro.php:372
#: upgrade/upgrade-to-pro.php:373
msgid "An error occurred:"
msgstr ""

#: mailer/class-mail.php:127
msgid "Learn more"
msgstr ""

#: placeholders/class-placeholders.php:64
msgid "Report for:"
msgstr ""

#: proof-of-consent/class-proof-of-consent.php:64
msgid "Select a region"
msgstr ""

#: proof-of-consent/class-proof-of-consent.php:284
msgid "Cookie consent settings"
msgstr ""

#: proof-of-consent/class-proof-of-consent.php:285
#: settings/config/menu.php:397
#: settings/config/menu.php:408
#: settings/build/3192.927ccc5a39908a2c5eac.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8525.433647e550a4b16270cb.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9567.1bb4efce88343dfca145.js:1
msgid "Proof of Consent"
msgstr ""

#: proof-of-consent/class-proof-of-consent.php:286
msgid ""
"This document was generated to show efforts made to comply with privacy legislation.\n"
"                            This document will contain the Cookie Policy and the cookie consent settings to proof consent\n"
"                            for the time and region specified below. For more information about this document, please go\n"
"                            to %shttps://complianz.io/consent%s."
msgstr ""

#: rest-api/rest-api.php:191
msgctxt "cookie policy"
msgid "We have received a privacy signal from your browser. For this reason we have set your privacy settings on this website to strictly necessary. If you want to have full functionality, please consider excluding %s from your privacy settings."
msgstr ""

#: settings/config/blocks.php:48
msgid "Tips & Tricks"
msgstr ""

#: settings/config/blocks.php:60
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/7907.6fefe9abf9598f028248.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Other Plugins"
msgstr ""

#: settings/config/blocks.php:61
msgid "A help text"
msgstr ""

#: settings/config/disable-fields-filter.php:19
msgid "With Matomo cookieless tracking, configuration by Complianz is required."
msgstr ""

#: settings/config/fields-notices.php:18
msgid "Prefilled field"
msgstr ""

#: settings/config/fields-notices.php:19
msgid "Some answers have been been pre-filled by our system detection. Please check before continuing"
msgstr ""

#: settings/config/fields-notices.php:20
msgid "Please make sure you remove your current implementation to prevent double statistics tracking."
msgstr ""

#: settings/config/fields-notices.php:30
msgid "Detected statistics"
msgstr ""

#: settings/config/fields-notices.php:31
msgid "The site scan detected %s on your site, which means the answer to this question should be %s."
msgstr ""

#: settings/config/fields-notices.php:44
msgid "Detected social media"
msgstr ""

#: settings/config/fields-notices.php:45
msgid "The scan found social media buttons or widgets for %s on your site, which means the answer should be yes"
msgstr ""

#: settings/config/fields-notices.php:54
msgid "Detected forms"
msgstr ""

#: settings/config/fields-notices.php:55
msgid "The scan found forms on your site, which means answer should probably include \"contact\"."
msgstr ""

#: settings/config/fields-notices.php:68
msgid "Detected third-party services"
msgstr ""

#: settings/config/fields-notices.php:69
msgid "The scan found third-party services on your website: %s, this means the answer should be yes."
msgstr ""

#: settings/config/fields-notices.php:78
msgid "Selling personal data"
msgstr ""

#: settings/config/fields-notices.php:79
msgid "The site scan detected cookies from services that share data with Third Parties. According to US privacy laws, your website is considered to sell personal data if it collects and shares any personal data in return for money or services. This includes a service like Google Analytics."
msgstr ""

#: settings/config/fields-notices.php:87
msgid "Divi detected"
msgstr ""

#: settings/config/fields-notices.php:88
msgid "Your site uses Divi. If you use reCAPTCHA on your site, you may need to disable the reCAPTCHA integration in Complianz. "
msgstr ""

#: settings/config/fields-notices.php:97
msgid "Third-party cookies"
msgstr ""

#: settings/config/fields-notices.php:98
msgid "The site scan detected cookies from services which share data with Third Parties. If these cookies were also used in the past 12 months, you should at least select the option 'Internet activity...'"
msgstr ""

#: settings/config/fields-notices.php:107
msgid "You're using Google Tag Manager. This means you need to configure Tag Manager to use the below categories."
msgstr ""

#: settings/config/fields-notices.php:116
msgid "Using categories is mandatory"
msgstr ""

#: settings/config/fields-notices.php:117
msgid "Categories are mandatory for your statistics configuration."
msgstr ""

#: settings/config/fields-notices.php:130
#: settings/config/fields-notices.php:137
msgid "It is recommended to select a Privacy Statement."
msgstr ""

#: settings/config/fields-notices.php:130
msgid "The link to the Privacy Statement is used in the consent banner and in your Cookie Policy."
msgstr ""

#: settings/config/fields-notices.php:137
msgid "The link to the Privacy Statement is used in your Cookie Policy."
msgstr ""

#: settings/config/fields-notices.php:149
msgid "First-party marketing cookies"
msgstr ""

#: settings/config/fields-notices.php:150
msgid "You use plugins which place first-party marketing cookies. Complianz cannot only block such cookies if the plugin conforms to the WP Consent API, or you have enabled Consent Per Service"
msgstr ""

#: settings/config/fields-notices.php:159
msgid "Sensitive & personal data"
msgstr ""

#: settings/config/fields-notices.php:160
msgid "You have selected options that indicate your site processes sensitive, personal data. You should select 'Yes'"
msgstr ""

#: settings/config/fields-notices.php:168
msgid "TCF not possible with custom Cookie Policy"
msgstr ""

#: settings/config/fields-notices.php:169
msgid "You have chosen a custom Cookie Policy. The TCF option is disabled as it can only be used in combination with the Cookie Policy generated by Complianz."
msgstr ""

#: settings/config/fields-notices.php:177
msgid "TCF enabled"
msgstr ""

#: settings/config/fields-notices.php:178
msgid "You have enabled TCF. This option can only be used in combination with the Cookie Policy generated by Complianz."
msgstr ""

#: settings/config/fields-notices.php:186
msgid "TCF enabled: Review customization guidelines"
msgstr ""

#: settings/config/fields-notices.php:187
msgid ""
"You have enabled TCF. Please check the do's and don'ts regarding customizations:\n"
"\t\t                     <a href='https://complianz.io/customizing-the-tcf-banner/?utm_source=tipstricks&utm_medium=plugin&utm_campaign=articles&utm_id=66&utm_content=tcf' target='_blank'>Read more</a>"
msgstr ""

#: settings/config/fields/general-settings/settings.php:13
msgid "You can use GEO IP to enable the warning only for countries with a cookie law, or which you target"
msgstr ""

#: settings/config/fields/general-settings/settings.php:16
msgid "Enable GEO IP"
msgstr ""

#: settings/config/fields/general-settings/settings.php:18
msgid "If enabled, the cookie warning will not show for countries without a cookie law, and will adjust consent management depending on supported privacy laws"
msgstr ""

#: settings/config/fields/general-settings/settings.php:28
msgid "Do you consent to the use of the cookiedatabase.org API?"
msgstr ""

#: settings/config/fields/general-settings/settings.php:29
msgid "Without the API, you will have to manually describe all found cookies, their purpose, function, service and service types. "
msgstr ""

#: settings/config/fields/general-settings/settings.php:33
msgid "Complianz provides your Cookie Policy with comprehensive cookie descriptions, supplied by cookiedatabase.org. We connect to this open-source database using an external API, which sends the results of the cookiescan (a list of found cookies, used plugins and your domain) to cookiedatabase.org, for the sole purpose of providing you with accurate descriptions and keeping them up-to-date on a regular basis."
msgstr ""

#: settings/config/fields/general-settings/settings.php:52
msgid "Do you want to hyperlink cookie names so visitors can find more information on Cookiedatabase.org?"
msgstr ""

#: settings/config/fields/general-settings/settings.php:53
msgid "These links will be added with HTML attributes so it won't hurt SEO."
msgstr ""

#: settings/config/fields/general-settings/settings.php:63
msgid "Notifications by email"
msgstr ""

#: settings/config/fields/general-settings/settings.php:64
msgid "Get notified of important updates, changes, and settings."
msgstr ""

#: settings/config/fields/general-settings/settings.php:73
msgid "Email address"
msgstr ""

#: settings/config/fields/general-settings/settings.php:86
msgid "Disable notifications"
msgstr ""

#: settings/config/fields/general-settings/settings.php:87
msgid "Disable all plus ones and warnings on your dashboard."
msgstr ""

#: settings/config/fields/general-settings/settings.php:96
msgid "Restart plugin tour"
msgstr ""

#: settings/config/fields/general-settings/settings.php:97
#: settings/build/626.fe1c387a5a23ee649985.js:1
#: settings/build/8708.19bd0a9a3ec0adec8ee8.js:1
msgid "Start"
msgstr ""

#: settings/config/fields/general-settings/settings.php:105
msgid "Consent banner expiration in days"
msgstr ""

#: settings/config/fields/general-settings/settings.php:113
msgid "Disable the monthly site scan, subsequent sync with cookiedatabase.org and compliance reporting."
msgstr ""

#: settings/config/fields/general-settings/settings.php:121
msgid "Enable cookie blocker for ajax loaded content"
msgstr ""

#: settings/config/fields/general-settings/settings.php:124
msgid "When content is loaded with ajax, for example with a load more button or lightbox, this option could help blocking the service correctly"
msgstr ""

#: settings/config/fields/general-settings/settings.php:133
msgid "Set cookiebanner cookies on the root domain"
msgstr ""

#: settings/config/fields/general-settings/settings.php:136
msgid "Cookies on Root Domain"
msgstr ""

#: settings/config/fields/general-settings/settings.php:137
msgid "This is useful if you have a multisite, or several sites as subdomains on a main site"
msgstr ""

#: settings/config/fields/general-settings/settings.php:146
msgid "Domain to set the cookies on"
msgstr ""

#: settings/config/fields/general-settings/settings.php:149
msgid "Cookie Domain"
msgstr ""

#: settings/config/fields/general-settings/settings.php:150
msgid "This should be your main, root domain."
msgstr ""

#: settings/config/fields/integrations/services.php:13
msgid "Developers and custom implementations"
msgstr ""

#: settings/config/fields/integrations/services.php:14
msgid "Complianz is built to be easily configured, with automatic background processes, integrations and a wizard for specific questions. But this is WordPress, a million different configurations sometimes ask for custom implementations. We have collected many custom implementations from our contributors, and have written an article for anyone who wants to make their own integration."
msgstr ""

#: settings/config/fields/tools/ab-testing.php:20
msgid "Enable consent statistics"
msgstr ""

#: settings/config/fields/tools/ab-testing.php:21
msgid "If enabled, the plugin will visualize stored records of consent."
msgstr ""

#: settings/config/fields/tools/ab-testing.php:31
msgid "Enable A/B testing"
msgstr ""

#: settings/config/fields/tools/ab-testing.php:36
msgid "If enabled, the plugin will track which consent banner has the best conversion rate."
msgstr ""

#: settings/config/fields/tools/ab-testing.php:52
msgid "Duration in days of the A/B testing period"
msgstr ""

#: settings/config/fields/tools/data.php:14
msgid "Export settings"
msgstr ""

#: settings/config/fields/tools/data.php:15
#: settings/build/5848.5d28f6e94d788d9d1401.js:1
msgid "Export"
msgstr ""

#: settings/config/fields/tools/data.php:16
msgid "You can use this to export your settings to another site"
msgstr ""

#: settings/config/fields/tools/data.php:23
msgid "You can use this to import your settings from another site"
msgstr ""

#: settings/config/fields/tools/data.php:34
msgid "Are you sure? This will remove all Complianz data."
msgstr ""

#: settings/config/fields/tools/data.php:38
msgid "This will reset all settings to defaults. All data in the Complianz plugin will be deleted!"
msgstr ""

#: settings/config/fields/tools/data.php:45
msgid "Clear all data from Complianz on uninstall"
msgstr ""

#: settings/config/fields/tools/data.php:46
msgid "Enabling this option will delete all your settings, and the Complianz tables when you deactivate and remove Complianz."
msgstr ""

#: settings/config/fields/tools/datarequests.php:20
msgid "View export options"
msgstr ""

#: settings/config/fields/tools/datarequests.php:21
msgid "Export personal data"
msgstr ""

#: settings/config/fields/tools/datarequests.php:29
msgid "View erase options"
msgstr ""

#: settings/config/fields/tools/datarequests.php:30
msgid "Erase personal data"
msgstr ""

#: settings/config/fields/tools/datarequests.php:37
msgid "Export Data Requests"
msgstr ""

#: settings/config/fields/tools/datarequests.php:44
msgid "Notification sender email address"
msgstr ""

#: settings/config/fields/tools/datarequests.php:46
msgid "When emails are sent, you can choose the sender email address here. Please note that it should have this website's domain as sender domain, otherwise the server might block the email from being sent."
msgstr ""

#: settings/config/fields/tools/datarequests.php:49
msgid "Responding to data requests"
msgstr ""

#: settings/config/fields/tools/datarequests.php:50
msgid "You have an open data requests ready for response? Get started here."
msgstr ""

#: settings/config/fields/tools/datarequests.php:59
msgid "Notification email subject"
msgstr ""

#: settings/config/fields/tools/datarequests.php:60
msgid "We have received your request"
msgstr ""

#: settings/config/fields/tools/datarequests.php:61
msgid "Subject used for Data Request email notifications."
msgstr ""

#: settings/config/fields/tools/datarequests.php:74
msgid "Notification email content"
msgstr ""

#: settings/config/fields/tools/datarequests.php:75
msgid "Hi {name}"
msgstr ""

#: settings/config/fields/tools/datarequests.php:76
msgid "We have received your request on {blogname}. Depending on the specific request and legal obligations we might follow-up."
msgstr ""

#: settings/config/fields/tools/datarequests.php:77
msgctxt "email signature"
msgid "Kind regards,"
msgstr ""

#: settings/config/fields/tools/datarequests.php:79
msgid "Email content used for Data Request email notifications."
msgstr ""

#: settings/config/fields/tools/documents.php:12
msgid "Use document CSS by Complianz"
msgstr ""

#: settings/config/fields/tools/documents.php:14
msgid "Disable to let your theme take over."
msgstr ""

#: settings/config/fields/tools/documents.php:21
msgid "Enable custom document CSS"
msgstr ""

#: settings/config/fields/tools/documents.php:23
msgid "Enable if you want to add custom CSS for the documents"
msgstr ""

#: settings/config/fields/tools/documents.php:30
msgid "Custom document CSS"
msgstr ""

#: settings/config/fields/tools/placeholders.php:13
msgid "Disable placeholder insertion"
msgstr ""

#: settings/config/fields/tools/placeholders.php:15
msgid "If you experience styling issues with videos or iFrames you can disable the placeholder insertion, which in some themes can conflict with theme styling."
msgstr ""

#: settings/config/fields/tools/placeholders.php:30
#: settings/config/fields/tools/placeholders.php:35
#: settings/config/fields/tools/placeholders.php:54
#: settings/config/fields/tools/placeholders.php:59
msgid "Blocked content text"
msgstr ""

#: settings/config/fields/tools/placeholders.php:31
msgid "Click to accept %s cookies and enable this content"
msgstr ""

#: settings/config/fields/tools/placeholders.php:32
#: settings/config/fields/tools/placeholders.php:56
#: settings/config/fields/tools/placeholders.php:81
msgid "The blocked content text appears when for example a YouTube video is embedded."
msgstr ""

#: settings/config/fields/tools/placeholders.php:36
msgid "Do not change or translate the {category} string."
msgstr ""

#: settings/config/fields/tools/placeholders.php:36
#: settings/config/fields/tools/placeholders.php:60
msgid "You may remove it if you want."
msgstr ""

#: settings/config/fields/tools/placeholders.php:36
msgid "It will be replaced with the name of the category that is blocked."
msgstr ""

#: settings/config/fields/tools/placeholders.php:55
msgid "Click 'I agree' to enable %s"
msgstr ""

#: settings/config/fields/tools/placeholders.php:60
msgid "Do not change or translate the {service} string."
msgstr ""

#: settings/config/fields/tools/placeholders.php:60
msgid "It will be replaced with the name of the service that is blocked."
msgstr ""

#: settings/config/fields/tools/placeholders.php:79
msgid "Text on 'I agree' button"
msgstr ""

#: settings/config/fields/tools/placeholders.php:80
msgid "I agree"
msgstr ""

#: settings/config/fields/tools/placeholders.php:102
#: settings/config/fields/tools/placeholders.php:126
msgid "Custom placeholders"
msgstr ""

#: settings/config/fields/tools/placeholders.php:103
msgid "Choose the style that best complements your website's design."
msgstr ""

#: settings/config/fields/tools/placeholders.php:109
msgid "Light"
msgstr ""

#: settings/config/fields/tools/placeholders.php:110
msgid "Full Color"
msgstr ""

#: settings/config/fields/tools/placeholders.php:111
msgid "Dark Mode"
msgstr ""

#: settings/config/fields/tools/placeholders.php:112
#: settings/config/fields/tools/placeholders.php:160
msgid "Custom"
msgstr ""

#: settings/config/fields/tools/placeholders.php:121
msgid "Placeholder style"
msgstr ""

#: settings/config/fields/tools/placeholders.php:123
msgid "You can choose your favorite placeholder style here."
msgstr ""

#: settings/config/fields/tools/placeholders.php:127
msgid "You can change your placeholders manually or use Premium to do it for you."
msgstr ""

#: settings/config/fields/tools/placeholders.php:162
msgid "Google Maps placeholder ratio"
msgstr ""

#: settings/config/fields/tools/placeholders.php:164
msgid "Select the optimal placeholder ratio for your site."
msgstr ""

#: settings/config/fields/tools/placeholders.php:174
msgid "Custom ratio for Google Maps"
msgstr ""

#: settings/config/fields/tools/placeholders.php:175
msgid "If you select custom, you need to add your custom image to your site."
msgstr ""

#: settings/config/fields/tools/processing-agreements.php:16
msgid "About Processing Agreements"
msgstr ""

#: settings/config/fields/tools/processing-agreements.php:17
msgid "To learn what Processing Agreements are and what you need them for, please read the below article"
msgstr ""

#: settings/config/fields/tools/processing-agreements.php:42
msgid "About Data Breach Reports"
msgstr ""

#: settings/config/fields/tools/processing-agreements.php:43
msgid "To learn what Data Breach Reports are and what you need them for, please read the below article"
msgstr ""

#: settings/config/fields/tools/proof-of-consent.php:62
msgid "Export Records of Consent"
msgstr ""

#: settings/config/fields/tools/proof-of-consent.php:65
msgid "What are records of consent?"
msgstr ""

#: settings/config/fields/tools/proof-of-consent.php:66
msgid "Records of Consent are required in certain circumstances, you can read our article about dealing with records of consent and why it is needed."
msgstr ""

#: settings/config/fields/tools/security.php:12
#: settings/settings.php:651
msgid "Lightweight plugin. Heavyweight security features."
msgstr ""

#: settings/config/fields/tools/security.php:14
#: settings/settings.php:652
msgid "Leverage your SSL certificate to the fullest, with health checks, security headers, hardening, vulnerability detection and more."
msgstr ""

#: settings/config/fields/tools/security.php:20
msgid "5+ million websites are secured with Really Simple Security"
msgstr ""

#: settings/config/fields/tools/support.php:18
msgid "Enable safe mode"
msgstr ""

#: settings/config/fields/tools/support.php:21
msgid "Safe Mode"
msgstr ""

#: settings/config/fields/tools/support.php:22
msgid "When safe mode is enabled, all integrations will be disabled temporarily, please read the instructions to debug the issue or ask support if needed."
msgstr ""

#: settings/config/fields/tools/support.php:31
msgid "Possible relevant errors"
msgstr ""

#: settings/config/fields/tools/support.php:39
msgid "System Status"
msgstr ""

#: settings/config/fields/tools/support.php:40
#: settings/build/2980.92a8207f9d739ea56809.js:1
#: settings/build/4575.34faeb333c9031b5299f.js:1
#: settings/build/7161.b7d2c4b248f286201398.js:1
msgid "Download"
msgstr ""

#: settings/config/fields/tools/support.php:50
msgid "Support form"
msgstr ""

#: settings/config/fields/wizard/consent.php:16
#: settings/config/menu.php:105
#: websitescan/class-wsc-settings.php:166
msgid "Website Scan"
msgstr ""

#: settings/config/fields/wizard/consent.php:17
msgid "If you want to clear all cookies from the plugin, you can do so here. If you want to start with a clean slate, you might need to clear your browsercache, to make sure all cookies are removed from your browser as well."
msgstr ""

#: settings/config/fields/wizard/consent.php:30
msgid "Do you compile statistics of this website?"
msgstr ""

#: settings/config/fields/wizard/consent.php:32
msgid "Yes, and Google Tag Manager fires this script"
msgstr ""

#: settings/config/fields/wizard/consent.php:33
msgid "Yes, and Matomo Tag Manager fires this script"
msgstr ""

#: settings/config/fields/wizard/consent.php:34
msgid "Yes, with Google Analytics"
msgstr ""

#: settings/config/fields/wizard/consent.php:35
msgid "Yes, with Matomo"
msgstr ""

#: settings/config/fields/wizard/consent.php:36
msgid "Yes, with Clicky"
msgstr ""

#: settings/config/fields/wizard/consent.php:37
msgid "Yes, with Yandex"
msgstr ""

#: settings/config/fields/wizard/consent.php:38
msgid "Yes, with Clarity"
msgstr ""

#: settings/config/fields/wizard/consent.php:39
msgid "Yes, but not with any of the above services"
msgstr ""

#: settings/config/fields/wizard/consent.php:50
#: settings/config/fields/wizard/consent.php:80
msgid "Does the following apply to your website?"
msgstr ""

#: settings/config/fields/wizard/consent.php:51
msgid "When checking all three checkboxes, we will set statistics to anonymous. Based on your region, statistics might be set before consent."
msgstr ""

#: settings/config/fields/wizard/consent.php:53
msgid "By design, IP anonymization is always enabled for GA4 properties."
msgstr ""

#: settings/config/fields/wizard/consent.php:55
#: settings/config/fields/wizard/consent.php:82
msgid "I have accepted the Google data processing amendment"
msgstr ""

#: settings/config/fields/wizard/consent.php:56
#: settings/config/fields/wizard/consent.php:83
msgid "Google is not allowed to use this data for other Google services"
msgstr ""

#: settings/config/fields/wizard/consent.php:57
msgid "IP addresses are anonymized or let Complianz do this for me."
msgstr ""

#: settings/config/fields/wizard/consent.php:67
msgid "Anonymized IP"
msgstr ""

#: settings/config/fields/wizard/consent.php:68
msgid "If you select the option that IP addresses are anonymized, and let Complianz handle the statistics, Complianz will ensure that ip addresses are anonymized by default, unless consent is given for statistics."
msgstr ""

#: settings/config/fields/wizard/consent.php:84
msgid "Acquiring IP-addresses is blocked"
msgstr ""

#: settings/config/fields/wizard/consent.php:89
msgid "You can configure Google Tag Manager for Complianz, and, if applicable, adjust configuration for Google Analytics for GDPR and other opt-in based privacy laws."
msgstr ""

#: settings/config/fields/wizard/consent.php:106
msgid "Do you want to use cookieless tracking with Matomo?"
msgstr ""

#: settings/config/fields/wizard/consent.php:111
msgid "Learn more about using cookieless tracking with Matomo."
msgstr ""

#: settings/config/fields/wizard/consent.php:126
msgid "In some countries, like Germany, Austria, Belgium or Spain, consent is required for statistics, even if the data is anonymized."
msgstr ""

#: settings/config/fields/wizard/consent.php:128
msgid "Do you want to ask consent for statistics?"
msgstr ""

#: settings/config/fields/wizard/consent.php:150
msgid "Controlling your statistics script"
msgstr ""

#: settings/config/fields/wizard/consent.php:151
#: settings/config/fields/wizard/consent.php:160
#: settings/config/menu.php:237
msgid "Script Center"
msgstr ""

#: settings/config/fields/wizard/consent.php:161
msgid "Below you can choose to implement your statistics script with Complianz."
msgstr ""

#: settings/config/fields/wizard/consent.php:162
msgid "We will add the needed snippets and control consent at the same time."
msgstr ""

#: settings/config/fields/wizard/consent.php:171
msgid "Do you want Complianz to add %s to your website?"
msgstr ""

#: settings/config/fields/wizard/consent.php:182
msgid "It's recommended to let Complianz handle the statistics script. This way, the plugin can detect if it needs to be hooked into the cookie consent code or not. But if you have set it up yourself and don't want to change this, you can choose to do so."
msgstr ""

#: settings/config/fields/wizard/consent.php:193
msgid "Do you want to enable Google Consent Mode V2?"
msgstr ""

#: settings/config/fields/wizard/consent.php:194
msgid "Please read this %sarticle%s to make sure Consent Mode is working as expected."
msgstr ""

#: settings/config/fields/wizard/consent.php:210
msgid "Enabling this feature means all Google tags will be handled by Google Consent Mode. You will need to read the integration manual below, and double-check to see if it works for your setup."
msgstr ""

#: settings/config/fields/wizard/consent.php:223
#: settings/config/fields/wizard/consent.php:226
msgid "Do you want to block all Google Tags before consent?"
msgstr ""

#: settings/config/fields/wizard/consent.php:227
msgid "By default Consent Mode is enabled in Advanced Mode. This means tags are loaded before consent, but depending on user preferences selects the appropriate tracking mechanisms, e.g. cookieless tracking or cookies, automatically. If you answer Yes, Complianz will only apply Consent Mode after consent."
msgstr ""

#: settings/config/fields/wizard/consent.php:253
msgid "Do you want to set a URL passthrough parameter"
msgstr ""

#: settings/config/fields/wizard/consent.php:254
msgid "This can improve conversion accuracy, but can contain personal data like a client ID."
msgstr ""

#: settings/config/fields/wizard/consent.php:278
msgid "Deny cookies when advertising is rejected?"
msgstr ""

#: settings/config/fields/wizard/consent.php:279
msgid "When enabled, cookies will no longer be set when ad_storage is denied and identifiers in network requests will be redacted."
msgstr ""

#: settings/config/fields/wizard/consent.php:303
msgid "Will you be using our Tag Manager template?"
msgstr ""

#: settings/config/fields/wizard/consent.php:319
msgid "Configuring Consent Mode & Tag Manager"
msgstr ""

#: settings/config/fields/wizard/consent.php:320
msgid "You can choose the official Consent Mode template by Complianz from the template gallery, or use local initialization."
msgstr ""

#: settings/config/fields/wizard/consent.php:332
msgid "Google Tag - Statistics"
msgstr ""

#: settings/config/fields/wizard/consent.php:335
msgid "Tracking ID"
msgstr ""

#: settings/config/fields/wizard/consent.php:336
msgid "If you add the ID for your Statistics tool here, Complianz will configure your site for statistics tracking."
msgstr ""

#: settings/config/fields/wizard/consent.php:346
msgid "For the Google Analytics tracking ID, log in and click Admin and copy the tracking ID."
msgstr ""

#: settings/config/fields/wizard/consent.php:355
msgid "Additional Google Tags - Statistics"
msgstr ""

#: settings/config/fields/wizard/consent.php:364
#: settings/config/fields/wizard/consent.php:447
msgid "You can add additional tags, comma separated."
msgstr ""

#: settings/config/fields/wizard/consent.php:373
msgid "Please enter your GTM container ID"
msgstr ""

#: settings/config/fields/wizard/consent.php:376
#: settings/config/fields/wizard/consent.php:469
#: settings/config/fields/wizard/consent.php:491
msgid "Configuration by Complianz"
msgstr ""

#: settings/config/fields/wizard/consent.php:377
#: settings/config/fields/wizard/consent.php:492
msgid "If you add the ID for your statistics tool here, Complianz will configure your site for statistics tracking."
msgstr ""

#: settings/config/fields/wizard/consent.php:386
msgid "For the Google Tag Manager code, log in and you will immediatly see your container codes. The one next to your website name is the code you will need to fill in here, the container ID."
msgstr ""

#: settings/config/fields/wizard/consent.php:397
msgid "Do you want to force the script in the header?"
msgstr ""

#: settings/config/fields/wizard/consent.php:399
msgid "Yes - (Experimental)"
msgstr ""

#: settings/config/fields/wizard/consent.php:402
msgid "It's possible that forcing this script in the header breaks configurations and integrations with other plugins."
msgstr ""

#: settings/config/fields/wizard/consent.php:421
msgid "Google Tag - Marketing or Advertising"
msgstr ""

#: settings/config/fields/wizard/consent.php:422
msgid "This will be fired on marketing consent."
msgstr ""

#: settings/config/fields/wizard/consent.php:423
msgid "Optional."
msgstr ""

#: settings/config/fields/wizard/consent.php:439
msgid "Additional Google Tags - Marketing or Advertising"
msgstr ""

#: settings/config/fields/wizard/consent.php:459
#: settings/config/fields/wizard/consent.php:506
msgid "Enter the URL of Matomo"
msgstr ""

#: settings/config/fields/wizard/consent.php:470
#: settings/config/fields/wizard/consent.php:517
msgid "The URL depends on your configuration of Matomo."
msgstr ""

#: settings/config/fields/wizard/consent.php:478
msgid "Enter your Matomo site ID"
msgstr ""

#: settings/config/fields/wizard/consent.php:516
#: settings/config/fields/wizard/consent.php:555
msgid "Configuration"
msgstr ""

#: settings/config/fields/wizard/consent.php:529
msgid "Enter your Matomo container ID"
msgstr ""

#: settings/config/fields/wizard/consent.php:545
msgid "Enter your Clicky site ID"
msgstr ""

#: settings/config/fields/wizard/consent.php:556
msgid "Because Clicky always sets a so-called unique identifier cookie, consent for statistics is always required."
msgstr ""

#: settings/config/fields/wizard/consent.php:567
msgid "Enter your Yandex ID"
msgstr ""

#: settings/config/fields/wizard/consent.php:583
msgid "Enter your Clarity project ID"
msgstr ""

#: settings/config/fields/wizard/consent.php:600
msgid "Do you want to enable the Yandex ecommerce datalayer?"
msgstr ""

#: settings/config/fields/wizard/cookiedatabase.php:12
msgid "Connect with Cookiedatabase.org"
msgstr ""

#: settings/config/fields/wizard/documents.php:13
msgid "Create documents"
msgstr ""

#: settings/config/fields/wizard/documents.php:20
msgid "Yes, redirect based on GEO IP"
msgstr ""

#: settings/config/fields/wizard/documents.php:21
msgid "No, choose a menu per document"
msgstr ""

#: settings/config/fields/wizard/documents.php:30
msgid "GEO IP based redirect is available in Premium"
msgstr ""

#: settings/config/fields/wizard/documents.php:31
msgid "Use a region redirect on the relevant documents"
msgstr ""

#: settings/config/fields/wizard/finish.php:34
msgid "Show Consent Banner"
msgstr ""

#: settings/config/fields/wizard/finish.php:35
msgid "If you enable this setting, a consent banner will be enabled, if needed."
msgstr ""

#: settings/config/fields/wizard/finish.php:36
msgid "You can always enable and disable the Consent Banner when styling the Consent Banner, under Consent Banner settings."
msgstr ""

#: settings/config/fields/wizard/finish.php:54
msgid "Enable cookie and script blocker"
msgstr ""

#: settings/config/fields/wizard/finish.php:55
msgid "The Cookie Blocker will, among others, block any tracking and third-party scripts configured by the wizard, automatic configuration or our script center."
msgstr ""

#: settings/config/fields/wizard/finish.php:58
msgid "Using Safe Mode"
msgstr ""

#: settings/config/fields/wizard/finish.php:59
msgid "If the Cookie Blocker causes an issue, you can enable Safe Mode under settings. Disabling Safe Mode will activate the Cookie Blocker."
msgstr ""

#: settings/config/fields/wizard/general.php:12
msgid "Which region(s) do you target with your website?"
msgstr ""

#: settings/config/fields/wizard/general.php:18
msgid "Which privacy law or guideline do you want to use as the default for your worldwide visitors?"
msgstr ""

#: settings/config/fields/wizard/general.php:22
msgid "Which region(s) do I target?"
msgstr ""

#: settings/config/fields/wizard/general.php:23
msgid "You don’t need to configure your website for ‘accidental’ visitors. Only choose the regions your website is intended for."
msgstr ""

#: settings/config/fields/wizard/general.php:29
msgid "If you want to dynamically apply privacy laws based on the visitor's location, consider upgrading to the premium version, which allows you to apply a privacy law specific for that region."
msgstr ""

#: settings/config/fields/wizard/general.php:36
msgid "Which banner do you want to display in other regions?"
msgstr ""

#: settings/config/fields/wizard/general.php:51
msgid "Do you target visitors from Germany, Austria, Belgium and/or Spain?"
msgstr ""

#: settings/config/fields/wizard/general.php:67
msgid "Do you target visitors from Jersey or Guernsey?"
msgstr ""

#: settings/config/fields/wizard/general.php:83
msgid "Do you target visitors from Quebec?"
msgstr ""

#: settings/config/fields/wizard/general.php:86
msgid "This will apply an opt-in mechanism for all visitors from Canada, as required by Quebec bill 64."
msgstr ""

#: settings/config/fields/wizard/general.php:106
msgid "Do you specifically target visitors from these states?"
msgstr ""

#: settings/config/fields/wizard/general.php:107
msgid "There are some laws that only apply to one or more states and are described separately if needed."
msgstr ""

#: settings/config/fields/wizard/general.php:121
msgid "Does your site have visitors with log-in access to a restricted area of the website?"
msgstr ""

#: settings/config/fields/wizard/general.php:122
msgid "If so, the scan will be extended to the wp-admin part of your site. "
msgstr ""

#: settings/config/fields/wizard/general.php:132
#: settings/config/fields/wizard/general.php:153
#: settings/config/fields/wizard/general.php:173
msgid "Generated with Complianz"
msgstr ""

#: settings/config/fields/wizard/general.php:133
#: settings/config/fields/wizard/general.php:154
#: settings/config/fields/wizard/general.php:174
#: settings/config/fields/wizard/general.php:192
msgid "Existing page"
msgstr ""

#: settings/config/fields/wizard/general.php:138
msgid "A Cookie Policy is required to inform your visitors about the way cookies and similar techniques are used on your website."
msgstr ""

#: settings/config/fields/wizard/general.php:159
msgid "A Privacy Statement is required to inform your visitors about the way you deal with the privacy of website visitors. A link to this document is placed on your consent banner."
msgstr ""

#: settings/config/fields/wizard/general.php:179
msgid "An Imprint provides general contact information about the organization behind this website and might be required in your region."
msgstr ""

#: settings/config/fields/wizard/general.php:191
msgid "Generated"
msgstr ""

#: settings/config/fields/wizard/general.php:198
msgid "A Disclaimer is commonly used to exclude or limit liability or to make statements about the content of the website. Having a Disclaimer is not legally required."
msgstr ""

#: settings/config/fields/wizard/general.php:205
msgid "Name or company name"
msgstr ""

#: settings/config/fields/wizard/general.php:206
msgid "Who is the owner of the website?"
msgstr ""

#: settings/config/fields/wizard/general.php:221
msgid "Address, City and Zipcode"
msgstr ""

#: settings/config/fields/wizard/general.php:224
msgid "What is your address?"
msgstr ""

#: settings/config/fields/wizard/general.php:242
msgid "What is your country?"
msgstr ""

#: settings/config/fields/wizard/general.php:244
msgid "This setting is automatically selected based on your WordPress language setting."
msgstr ""

#: settings/config/fields/wizard/general.php:251
msgid "The email address will be obfuscated on the front-end to prevent spidering."
msgstr ""

#: settings/config/fields/wizard/general.php:252
msgid "What is the email address your visitors can use to contact you about privacy issues?"
msgstr ""

#: settings/config/fields/wizard/general.php:269
msgid "Phone number:"
msgstr ""

#: settings/config/fields/wizard/general.php:270
msgid "What is the telephone number your visitors can use to contact you about privacy issues?"
msgstr ""

#: settings/config/fields/wizard/general.php:295
msgid "Person who is accountable for the organization’s policies and practices and to whom complaints or inquiries can be forwarded."
msgstr ""

#: settings/config/fields/wizard/general.php:310
msgid "What is the address where complaints or inquiries can be forwarded?"
msgstr ""

#: settings/config/fields/wizard/general.php:328
msgid "Indicate for what purpose personal data is processed via your website:"
msgstr ""

#: settings/config/fields/wizard/general.php:343
msgid "Extend Proof of Consent with Records of Consent"
msgstr ""

#: settings/config/fields/wizard/general.php:345
msgid "Do you want to enable Records of Consent?"
msgstr ""

#: settings/config/fields/wizard/general.php:352
#: settings/config/menu.php:415
#: settings/config/menu.php:429
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/6644.b133f6e44de4c0c58899.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Records of Consent"
msgstr ""

#: settings/config/fields/wizard/general.php:353
msgid "Enabling this option will extend our Proof of Consent method with user consent registration."
msgstr ""

#: settings/config/fields/wizard/general.php:354
msgid "This option is recommended in combination with TCF and will store consent data in your database."
msgstr ""

#: settings/config/fields/wizard/general.php:364
#: settings/config/fields/wizard/general.php:366
msgid "Do you want to enable Data Request Forms?"
msgstr ""

#: settings/config/fields/wizard/general.php:373
msgid "What are data request forms?"
msgstr ""

#: settings/config/fields/wizard/general.php:374
msgid "This will enable Data Requests Forms for your Privacy Statements."
msgstr ""

#: settings/config/fields/wizard/general.php:384
msgid "Respect Do Not Track and Global Privacy Control?"
msgstr ""

#: settings/config/fields/wizard/general.php:385
msgid "If you enable this option, Complianz will not show the consent banner to users that enabled a ‘Do Not Track’ or 'Global Privacy Control' setting in their browsers and their default consent status is set to ‘denied’."
msgstr ""

#: settings/config/fields/wizard/general.php:394
msgid "Does your website contain or process sensitive (personal) information?"
msgstr ""

#: settings/config/fields/wizard/general.php:402
msgid "Sensitive personal information is considered data that is very likely to have a greater impact on Privacy. For example medical, religious or legal information."
msgstr ""

#: settings/config/fields/wizard/plugins.php:10
msgid "Enabled integrations"
msgstr ""

#: settings/config/fields/wizard/purposes.php:28
msgid "Specify the types of data you collect"
msgstr ""

#: settings/config/fields/wizard/services.php:15
msgid "Do you want to use 'Consent per Service'?"
msgstr ""

#: settings/config/fields/wizard/services.php:16
msgid "The default configuration is 'Consent per Category'. This is currently compliant with your selected regions."
msgstr ""

#: settings/config/fields/wizard/services.php:17
msgid "For a granular approach you can enable 'consent per service', a unique way to control cookies real-time."
msgstr ""

#: settings/config/fields/wizard/services.php:20
msgid "Cookie Shredder"
msgstr ""

#: settings/config/fields/wizard/services.php:21
msgid "This feature includes real-time cookie removal with the CookieShredder."
msgstr ""

#: settings/config/fields/wizard/services.php:21
msgid "This could break website functionality."
msgstr ""

#: settings/config/fields/wizard/services.php:34
msgid "Does your website use third-party services?"
msgstr ""

#: settings/config/fields/wizard/services.php:35
msgid "e.g. services like Google Fonts, Maps or reCAPTCHA usually place cookies."
msgstr ""

#: settings/config/fields/wizard/services.php:44
msgid "Select the types of third-party services you use on your site."
msgstr ""

#: settings/config/fields/wizard/services.php:45
msgid "Checking services here will add the associated cookies to your Cookie Policy, and block the service until consent is given (opt-in), or after consent is revoked (opt-out)."
msgstr ""

#: settings/config/fields/wizard/services.php:49
#: settings/config/menu.php:313
#: settings/build/3971.42c75f410294426ba63f.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Placeholders"
msgstr ""

#: settings/config/fields/wizard/services.php:50
msgid "When possible a placeholder is activated. You can also disable or configure the placeholder to your liking. You can disable services and placeholders under Integrations."
msgstr ""

#: settings/config/fields/wizard/services.php:74
msgid "Do you want to block reCAPTCHA before consent, and when consent is revoked?"
msgstr ""

#: settings/config/fields/wizard/services.php:77
msgid "Blocking reCaptcha"
msgstr ""

#: settings/config/fields/wizard/services.php:78
msgid "If you choose to block reCAPTCHA, please make sure you add a placeholder to your forms."
msgstr ""

#: settings/config/fields/wizard/services.php:88
msgid "Yes (recommended)"
msgstr ""

#: settings/config/fields/wizard/services.php:94
msgid "Your site uses Google Fonts. For best privacy compliance, we recommend to self host Google Fonts. To self host, follow the instructions in the below link."
msgstr ""

#: settings/config/fields/wizard/services.php:97
msgid "If you choose 'No', Complianz will block all known Google Fonts sources."
msgstr ""

#: settings/config/fields/wizard/services.php:97
msgid "Please read this %sarticle%s why self-hosting Google Fonts is recommended."
msgstr ""

#: settings/config/fields/wizard/services.php:104
msgid "Will you self-host Google Fonts?"
msgstr ""

#: settings/config/fields/wizard/services.php:125
#: settings/config/fields/wizard/services.php:129
msgid "Did you enable the consent module in your HubSpot account?"
msgstr ""

#: settings/config/fields/wizard/services.php:128
msgid "Integrating Hubspot"
msgstr ""

#: settings/config/fields/wizard/services.php:142
msgid "Is Hotjar configured in a privacy-friendly way?"
msgstr ""

#: settings/config/fields/wizard/services.php:145
msgid "Integrating Hotjar"
msgstr ""

#: settings/config/fields/wizard/services.php:146
msgid "You can configure Hotjar privacy-friendly, if you do this, no consent is required for Hotjar."
msgstr ""

#: settings/config/fields/wizard/services.php:165
msgid "Does your website contain embedded social media content, like buttons, timelines, videos or pixels?"
msgstr ""

#: settings/config/fields/wizard/services.php:166
msgid "Content from social media is mostly embedded through iFrames. These often place third party (tracking) cookies, so must be blocked based on visitor consent. If your website only contains buttons or links to a social media profile on an external page you can answer No."
msgstr ""

#: settings/config/fields/wizard/services.php:181
msgid "Select which social media are used on the website."
msgstr ""

#: settings/config/fields/wizard/services.php:182
msgid "Checking services here will add the associated cookies to your Cookie Policy, and block the service until consent is given (opt-in), or after consent is revoked (opt-out)"
msgstr ""

#: settings/config/fields/wizard/services.php:192
msgid "You have stated that you don't use third-party services. Do you use plugins that might set marketing cookies?"
msgstr ""

#: settings/config/fields/wizard/services.php:193
msgid "Complianz cannot automatically block first-party marketing cookies unless these plugins conform to the WP Consent API. Look for any possible integrations on our website if you're not sure. When you answer 'No' to this question, the marketing category will be removed."
msgstr ""

#: settings/config/fields/wizard/services.php:211
msgid "If you show advertising on your website, or place scripts for advertising purposes e.g. Google Shopping or remarketing, please answer with Yes."
msgstr ""

#: settings/config/fields/wizard/services.php:212
msgid "Does your website contain scripts for advertising purposes?"
msgstr ""

#: settings/config/fields/wizard/services.php:223
msgid "If you're using AdSense, AdManager or AdMob, please choose Google CMP Certified Consent Management, for other advertising products that don't use Google you can only use TCF."
msgstr ""

#: settings/config/fields/wizard/services.php:226
msgid "Don't use an additional framework."
msgstr ""

#: settings/config/fields/wizard/services.php:227
msgid "Enable TCF, without support for Google Advertising Products."
msgstr ""

#: settings/config/fields/wizard/services.php:228
msgid "Enable TCF & Google CMP Certified Consent Management with support for Google Advertising Products"
msgstr ""

#: settings/config/fields/wizard/services.php:231
msgid "Choose the appropriate frameworks needed for your configuration."
msgstr ""

#: settings/config/fields/wizard/services.php:232
msgid "Google Advertising Products requires Google CMP Certified Consent Management. If you don't show ads, but use Google Advertising Products with Google Consent Mode, an additional framework is not required. Please be aware that this consent banner has additional guidelines and restricts customization."
msgstr ""

#: settings/config/fields/wizard/services.php:322
msgid "Does your website use WordPress comments?"
msgstr ""

#: settings/config/fields/wizard/services.php:339
msgid "Do you want to disable the storage of personal data by the WP comments function and the checkbox?"
msgstr ""

#: settings/config/fields/wizard/services.php:342
msgid "WordPress comments"
msgstr ""

#: settings/config/fields/wizard/services.php:343
msgid "If you enable this, WordPress will not store personal data with comments and you won't need a consent checkbox for the comment form. The consent box will not be displayed."
msgstr ""

#: settings/config/menu.php:11
#: settings/settings.php:58
#: settings/settings.php:320
#: settings/settings.php:321
#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "Dashboard"
msgstr ""

#: settings/config/menu.php:16
#: settings/settings.php:329
#: settings/settings.php:330
msgid "Wizard"
msgstr ""

#: settings/config/menu.php:25
msgid "Visitors"
msgstr ""

#: settings/config/menu.php:26
msgid "The Complianz wizard will guide you through the necessary steps to configure your website for privacy legislation around the world. We designed the wizard to be comprehensible, without making concessions in legal compliance."
msgstr ""

#: settings/config/menu.php:30
#: settings/config/menu.php:153
#: settings/config/menu.php:157
#: settings/config/menu.php:328
#: settings/build/1249.6de9f730cac4c47b6e71.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Documents"
msgstr ""

#: settings/config/menu.php:31
msgid "Here you can select which legal documents you want to generate with Complianz. You can also use existing legal documents."
msgstr ""

#: settings/config/menu.php:35
msgid "Website information"
msgstr ""

#: settings/config/menu.php:36
msgid "We need some information to be able to generate your documents and configure your consent banner."
msgstr ""

#: settings/config/menu.php:41
msgid "We need some information to be able to generate your Imprint. Not all fields are required."
msgstr ""

#: settings/config/menu.php:47
msgid "As you have selected the Disclaimer to be generated, please fill out the questions below."
msgstr ""

#: settings/config/menu.php:52
msgid "Financial incentives"
msgstr ""

#: settings/config/menu.php:58
msgid "Children's Privacy Policy"
msgstr ""

#: settings/config/menu.php:59
#: settings/config/menu.php:66
msgid "In one ore more regions your selected, you need to specify if you target children."
msgstr ""

#: settings/config/menu.php:65
msgid "Children: Purposes"
msgstr ""

#: settings/config/menu.php:72
msgid "Data Protection Officer"
msgstr ""

#: settings/config/menu.php:83
msgid "Details per Purpose"
msgstr ""

#: settings/config/menu.php:88
msgid "Sharing of Data"
msgstr ""

#: settings/config/menu.php:94
msgid "Security & Consent"
msgstr ""

#: settings/config/menu.php:101
#: settings/build/7234.d838abdddb6468eb7b46.js:1
msgid "Consent"
msgstr ""

#: settings/config/menu.php:106
msgid "Complianz will scan several pages of your website for first-party cookies and known third-party scripts. The scan will be recurring monthly to keep you up-to-date!"
msgstr ""

#: settings/config/menu.php:106
msgid "For more information, %sread our 5 tips%s about the site scan."
msgstr ""

#: settings/config/menu.php:113
msgid "Below you can choose to implement your statistics tooling with Complianz. We will add the needed snippets and control consent at the same time"
msgstr ""

#: settings/config/menu.php:118
msgid "Statistics configuration"
msgstr ""

#: settings/config/menu.php:119
msgid "If you choose Complianz to handle your statistics implementation, please delete the current implementation."
msgstr ""

#: settings/config/menu.php:124
#: settings/config/menu.php:227
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
msgid "Services"
msgstr ""

#: settings/config/menu.php:128
#: settings/config/menu.php:232
msgid "Plugins"
msgstr ""

#: settings/config/menu.php:129
msgid "We have detected the below plugins."
msgstr ""

#: settings/config/menu.php:129
msgid "We have enabled the integrations and possible placeholders."
msgstr ""

#: settings/config/menu.php:129
msgid "To change these settings, please visit the script center."
msgstr ""

#: settings/config/menu.php:136
msgid "The below questions will help you configure a vendor list of your choosing. Only vendors that adhere to the purposes and special features you configure will be able to serve ads."
msgstr ""

#: settings/config/menu.php:143
msgid "Complianz provides your Cookie Policy with comprehensive cookie descriptions, supplied by cookiedatabase.org."
msgstr ""

#: settings/config/menu.php:144
msgid "We connect to this open-source database using an external API, which sends the results of the cookiescan (a list of found cookies, used plugins and your domain) to cookiedatabase.org, for the sole purpose of providing you with accurate descriptions and keeping them up-to-date on a regular basis."
msgstr ""

#: settings/config/menu.php:158
msgid "Generate your documents, then you can add them to your menu directly or do it manually after the wizard is finished."
msgstr ""

#: settings/config/menu.php:163
msgid "Link to menu"
msgstr ""

#: settings/config/menu.php:164
msgid "It's possible to use region redirect when GEO IP is enabled, and you have multiple policies and statements."
msgstr ""

#: settings/config/menu.php:184
msgid "These are the main options to customize your consent banner. To go even further you can use our documentation on complianz.io for CSS Lessons, or even start from scratch and create your own with just HTML and CSS."
msgstr ""

#: settings/config/menu.php:188
msgid "Appearance"
msgstr ""

#: settings/config/menu.php:192
msgid "Colors"
msgstr ""

#: settings/config/menu.php:204
msgid "Buttons"
msgstr ""

#: settings/config/menu.php:210
msgid "Texts"
msgstr ""

#: settings/config/menu.php:211
msgid "Here you can edit the texts on your banner."
msgstr ""

#: settings/config/menu.php:223
#: settings/settings.php:338
#: settings/settings.php:339
#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "Integrations"
msgstr ""

#: settings/config/menu.php:254
msgid "Missing any settings? We have moved settings to Tools, available in the menu."
msgstr ""

#: settings/config/menu.php:273
#: settings/settings.php:354
#: settings/settings.php:355
#: settings/build/3518.c39c508126fbf4701fb6.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Tools"
msgstr ""

#: settings/config/menu.php:282
msgid "You will be redirected to our support form, with the needed information, automatically."
msgstr ""

#: settings/config/menu.php:283
msgid "If you encounter issues, you can also go to the <a href=\"%s\">support</a> form directly."
msgstr ""

#: settings/config/menu.php:286
msgid "Get premium support with %sComplianz GDPR Premium%s"
msgstr ""

#: settings/config/menu.php:290
msgid "Debugging"
msgstr ""

#: settings/config/menu.php:296
#: settings/config/menu.php:301
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/6644.b133f6e44de4c0c58899.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Data Requests"
msgstr ""

#: settings/config/menu.php:317
msgid "Placeholder Style"
msgstr ""

#: settings/config/menu.php:336
msgid "Document CSS"
msgstr ""

#: settings/config/menu.php:343
msgid "Multisite options"
msgstr ""

#: settings/config/menu.php:348
#: settings/config/menu.php:363
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/6644.b133f6e44de4c0c58899.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Processing Agreements"
msgstr ""

#: settings/config/menu.php:354
msgid "Create Processing Agreements"
msgstr ""

#: settings/config/menu.php:356
msgid "Here you can create and upload processing agreements. These are necessary when you allow other third parties to process your data."
msgstr ""

#: settings/config/menu.php:359
msgid "Create Processing Agreements with %sComplianz GDPR Premium%s"
msgstr ""

#: settings/config/menu.php:367
#: websitescan/class-wsc-settings.php:170
msgid "View and manage Processing Agreements with %sComplianz GDPR Premium%s"
msgstr ""

#: settings/config/menu.php:373
#: settings/config/menu.php:388
msgid "Data Breach Reports"
msgstr ""

#: settings/config/menu.php:379
msgid "Create Data Breach Reports"
msgstr ""

#: settings/config/menu.php:381
msgid "Do you think your data might have been compromised? Did you experience a security incident or are not sure who had access to personal data for a period of time? Create a data breach report below to see what you need to do."
msgstr ""

#: settings/config/menu.php:384
msgid "Create Data Breach Reports with %sComplianz GDPR Premium%s"
msgstr ""

#: settings/config/menu.php:391
msgid "View and manage Data Breach Reports with %sComplianz GDPR Premium%s"
msgstr ""

#: settings/config/menu.php:425
#: settings/config/menu.php:432
#: settings/config/menu.php:452
msgid "View and manage Records of Consent with %sComplianz GDPR Premium%s"
msgstr ""

#: settings/config/menu.php:458
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/6644.b133f6e44de4c0c58899.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Security"
msgstr ""

#: settings/config/menu.php:463
msgid "Improve Security"
msgstr ""

#: settings/config/menu.php:468
msgid "Below text is meant for your Privacy Statement, and is created by using Really Simple Security. In Complianz Premium the text will be automatically added to the Privacy Statement."
msgstr ""

#: settings/config/menu.php:475
#: settings/config/menu.php:479
#: settings/config/menu.php:490
#: settings/build/2111.847b0d94c7100f73f709.js:1
#: settings/build/3582.f939c1a1c9321a2e16ab.js:1
#: settings/build/5175.4b3b51a33ec4e2266785.js:1
#: settings/build/6946.d959054c40656aabd21c.js:1
msgid "Data"
msgstr ""

#: settings/config/menu.php:486
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/6644.b133f6e44de4c0c58899.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Multisite"
msgstr ""

#: settings/settings.php:594
msgid "Data successfully cleared"
msgstr ""

#: settings/settings.php:640
msgid "Configure your own Terms and Conditions."
msgstr ""

#: settings/settings.php:641
msgid "A simple, but in-depth wizard will configure a Terms and Conditions page for your website or for those of your clients."
msgstr ""

#: templates/cookiepolicy/cookies_row.php:6
#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "Expiration"
msgstr ""

#: templates/cookiepolicy/cookies_row.php:10
msgid "Function"
msgstr ""

#: templates/cookiepolicy/services.php:9
msgctxt "cookie policy"
msgid "Usage"
msgstr ""

#: templates/cookiepolicy/services.php:13
msgctxt "Legal document cookie policy"
msgid "Sharing data"
msgstr ""

#: upgrade/upgrade-to-pro.php:81
msgid "Checking if plugin folder exists..."
msgstr ""

#: upgrade/upgrade-to-pro.php:82
msgid "Able to create destination folder"
msgstr ""

#: upgrade/upgrade-to-pro.php:83
msgid "Destination folder already exists"
msgstr ""

#: upgrade/upgrade-to-pro.php:88
msgid "Validating license..."
msgstr ""

#: upgrade/upgrade-to-pro.php:89
msgid "License valid"
msgstr ""

#: upgrade/upgrade-to-pro.php:90
msgid "License invalid"
msgstr ""

#: upgrade/upgrade-to-pro.php:95
msgid "Retrieving package information..."
msgstr ""

#: upgrade/upgrade-to-pro.php:96
msgid "Package information retrieved"
msgstr ""

#: upgrade/upgrade-to-pro.php:97
msgid "Failed to gather package information"
msgstr ""

#: upgrade/upgrade-to-pro.php:102
msgid "Installing plugin..."
msgstr ""

#: upgrade/upgrade-to-pro.php:103
msgid "Plugin installed"
msgstr ""

#: upgrade/upgrade-to-pro.php:104
msgid "Failed to install plugin"
msgstr ""

#: upgrade/upgrade-to-pro.php:109
msgid "Activating plugin..."
msgstr ""

#: upgrade/upgrade-to-pro.php:110
msgid "Plugin activated"
msgstr ""

#: upgrade/upgrade-to-pro.php:111
msgid "Failed to activate plugin"
msgstr ""

#: upgrade/upgrade-to-pro.php:144
msgid "Self-hosted and privacy-friendly analytics tool."
msgstr ""

#: upgrade/upgrade-to-pro.php:146
#: upgrade/upgrade-to-pro.php:159
#: upgrade/upgrade-to-pro.php:176
#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/2113.4da962fd404fcc001b7d.js:1
#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/2397.128a313ab0db7310b523.js:1
#: settings/build/3424.88f5ecf06f0e26d47eac.js:1
#: settings/build/4186.616704c21483c73a9fbe.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/5664.09a5d870cc30fa151cad.js:1
#: settings/build/6231.f9d17a5a1d14848fe3a8.js:1
#: settings/build/7338.18ed6f787e1915edb1be.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/8772.785d9c0620aa8b9946a2.js:1
#: settings/build/9056.7a23f9081f6350bd3057.js:1
#: settings/build/9487.e9838335195a59707dfe.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "Install"
msgstr ""

#: upgrade/upgrade-to-pro.php:161
msgid "Configure your Cookie Notice, Cookie Consent and Cookie Policy with our Wizard and Site Scan. Supports GDPR, DSGVO, TTDSG, LGPD, POPIA, RGPD, CCPA and PIPEDA."
msgstr ""

#: upgrade/upgrade-to-pro.php:174
msgid "SSL & Security"
msgstr ""

#: upgrade/upgrade-to-pro.php:178
msgid "Really Simple SSL & Security - Lightweight plugin, heavyweight features."
msgstr ""

#: upgrade/upgrade-to-pro.php:190
#: settings/build/4186.616704c21483c73a9fbe.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9487.e9838335195a59707dfe.js:1
msgid "Installed"
msgstr ""

#: upgrade/upgrade-to-pro.php:233
msgid "Installation finished"
msgstr ""

#: upgrade/upgrade-to-pro.php:331
msgid "Recommended by Really Simple Plugins"
msgstr ""

#: upgrade/upgrade-to-pro.php:356
#: settings/build/9487.e9838335195a59707dfe.js:1
msgid "Installing"
msgstr ""

#: upgrade/upgrade-to-pro.php:367
msgid "Visit Dashboard"
msgstr ""

#: upgrade/upgrade-to-pro.php:370
#: settings/build/1027.680330fc1970e47f24df.js:1
#: settings/build/2980.92a8207f9d739ea56809.js:1
#: settings/build/6281.81436365488010af52e4.js:1
msgid "Cancel"
msgstr ""

#: upgrade/upgrade-to-pro.php:372
msgid "Install %sManually%s."
msgstr ""

#: upgrade/upgrade-to-pro.php:373
msgid "Check your %slicense%s."
msgstr ""

#: upgrade/upgrade-to-pro.php:450
msgid "Could not rename folder!"
msgstr ""

#: upgrade/upgrade-to-pro.php:550
#: upgrade/upgrade-to-pro.php:582
msgid "An error occurred, please try again."
msgstr ""

#: upgrade/upgrade-to-pro.php:558
msgid "Your license key expired on %s."
msgstr ""

#: upgrade/upgrade-to-pro.php:564
msgid "Your license key has been disabled."
msgstr ""

#: upgrade/upgrade-to-pro.php:567
msgid "Missing license."
msgstr ""

#: upgrade/upgrade-to-pro.php:570
msgid "Invalid license."
msgstr ""

#: upgrade/upgrade-to-pro.php:573
msgid "Your license is not active for this URL."
msgstr ""

#: upgrade/upgrade-to-pro.php:576
msgid "This appears to be an invalid license key for this plugin."
msgstr ""

#: upgrade/upgrade-to-pro.php:579
msgid "Your license key has reached its activation limit."
msgstr ""

#: websitescan/class-wsc-notices.php:33
msgid "E-mail mismatch"
msgstr ""

#: websitescan/class-wsc-notices.php:34
msgid "The e-mail that you are authenticating does not match the e-mail stored in your settings currently. Please clear the e-mail, save, then enter your e-mail address again."
msgstr ""

#: websitescan/class-wsc-notices.php:42
msgid "Missing token"
msgstr ""

#: websitescan/class-wsc-notices.php:43
msgid "The token is missing from the URL which you are using to authenticate."
msgstr ""

#: websitescan/class-wsc-notices.php:51
msgid "Authentication failed"
msgstr ""

#: websitescan/class-wsc-notices.php:52
msgid "The authentication of your e-mail address failed. Please try again later."
msgstr ""

#: websitescan/class-wsc-notices.php:61
msgid "Token not retrieved"
msgstr ""

#: websitescan/class-wsc-notices.php:62
msgid "The token for the api could not be retrieved."
msgstr ""

#: websitescan/class-wsc-notices.php:70
msgid "E-mail verification not sent"
msgstr ""

#: websitescan/class-wsc-notices.php:71
msgid "The e-mail to verify your e-mail address could not be sent. Please check your e-mail address or try again later."
msgstr ""

#: websitescan/class-wsc-notices.php:79
#: websitescan/class-wsc-notices.php:88
msgid "Try our new Website Scan!"
msgstr ""

#: websitescan/class-wsc-notices.php:80
#: websitescan/class-wsc-notices.php:89
#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
msgid "In the latest release of Complianz, we introduce our newest Website Scan. This scan will not only retrieve services and cookies but also help you configure our plugin and keep you up-to-date if changes are made that might need legal changes."
msgstr ""

#: websitescan/class-wsc-notices.php:97
msgid "Check your email!"
msgstr ""

#: websitescan/class-wsc-notices.php:98
msgid "Your authentication is still on pending, check your emails for a confirmation."
msgstr ""

#: websitescan/class-wsc-notices.php:129
msgid "You have a new feature! To enable the new and improved Website Scan you need to authenticate your website."
msgstr ""

#. translators: %s: website name.
#: websitescan/class-wsc-scanner.php:990
msgid "[Complianz] Compliance report for %s"
msgstr ""

#. translators: %1$s: site url, %2$s: website name.
#: websitescan/class-wsc-scanner.php:1063
msgid "This compliance report was sent from your site <a href=\"%1$s\" target=\"_blank\">%2$s</a> by Complianz"
msgstr ""

#: websitescan/class-wsc-scanner.php:1067
msgid "You can find the most important takeaways below:"
msgstr ""

#. translators: %1$s: website name.
#: websitescan/class-wsc-scanner.php:1076
msgid "Are you no longer the website administrator? <a href=\"%1$s\" target=\"_blank\">Click here</a> to dismiss notifications."
msgstr ""

#: websitescan/class-wsc-scanner.php:1214
#: websitescan/class-wsc-scanner.php:1223
#: websitescan/class-wsc-scanner.php:1232
msgid "Consent mode"
msgstr ""

#: websitescan/class-wsc-scanner.php:1215
#: websitescan/class-wsc-scanner.php:1224
#: websitescan/class-wsc-scanner.php:1233
msgid "We have found {technology} on your site and recommend <a target=\"_blank\" href=\"{admin_url}\">enabling</a> Google Consent Mode V2 to optimize your analytics implementation."
msgstr ""

#: websitescan/class-wsc-scanner.php:1244
#: websitescan/class-wsc-scanner.php:1253
#: websitescan/class-wsc-scanner.php:1262
msgid "If you’re showing ads on your website it’s likely you need a Google certified CMP to make sure your ads are shown correctly. With Complianz you can <a target=\"_blank\" href=\"{admin_url}\">enable TCF</a> with our Google certified CMP."
msgstr ""

#: websitescan/class-wsc-scanner.php:1272
#: websitescan/class-wsc-scanner.php:1280
#: websitescan/class-wsc-scanner.php:1288
msgid "Privacy laws"
msgstr ""

#: websitescan/class-wsc-scanner.php:1273
#: websitescan/class-wsc-scanner.php:1281
#: websitescan/class-wsc-scanner.php:1289
msgid "On websites with multiple languages you need to consider that your visitors might be from various regions, and therefore different privacy laws might apply. Double-check and see if you’re complying with all relevant regions."
msgstr ""

#: websitescan/class-wsc-scanner.php:1298
#: websitescan/class-wsc-scanner.php:1306
#: websitescan/class-wsc-scanner.php:1314
msgid "Collecting data"
msgstr ""

#: websitescan/class-wsc-scanner.php:1299
#: websitescan/class-wsc-scanner.php:1307
#: websitescan/class-wsc-scanner.php:1315
msgid "We found a privacy statement! Please double-check if your privacy statement has all the needed elements expected from a privacy statement. Make sure you also allow for data request forms and records of consent to support your privacy statement."
msgstr ""

#: websitescan/class-wsc-scanner.php:1324
#: websitescan/class-wsc-scanner.php:1333
#: websitescan/class-wsc-scanner.php:1342
msgid "Privacy statement"
msgstr ""

#: websitescan/class-wsc-scanner.php:1325
#: websitescan/class-wsc-scanner.php:1334
#: websitescan/class-wsc-scanner.php:1343
msgid "We didn’t find a privacy statement. Please <a target=\"_blank\" href=\"{admin_url}\">add a privacy statement</a> that has all the needed elements expected from a privacy statement. Make sure you allow for DSR and Consent Records that respect the data minimization principle as well."
msgstr ""

#: websitescan/class-wsc-scanner.php:1353
#: websitescan/class-wsc-scanner.php:1361
#: websitescan/class-wsc-scanner.php:1369
msgid "WooCommerce"
msgstr ""

#: websitescan/class-wsc-scanner.php:1354
#: websitescan/class-wsc-scanner.php:1362
#: websitescan/class-wsc-scanner.php:1370
msgid "When selling with WooCommerce, compliance with privacy laws and customer rights is essential. Complianz simplifies this by generating required documents and managing privacy obligations effectively."
msgstr ""

#: websitescan/class-wsc-scanner.php:1379
#: websitescan/class-wsc-scanner.php:1387
#: websitescan/class-wsc-scanner.php:1395
msgid "Funding choices"
msgstr ""

#: websitescan/class-wsc-scanner.php:1380
#: websitescan/class-wsc-scanner.php:1388
#: websitescan/class-wsc-scanner.php:1396
msgid "Are you using the Google consent banner for advertising and Complianz for everything else? Why not remove one banner by combining everything with our Google Certified CMP that enablesd TCF and Consent Mode for Google products, without the need of multiple consent banners."
msgstr ""

#: websitescan/class-wsc-settings.php:167
msgid "Here you can manage your credentials. If you don’t want to use the Website Scan, you can reset it. A token will be created to verify your website. After creating your credentials, please make sure to check your email for a confirmation."
msgstr ""

#: websitescan/class-wsc-settings.php:203
msgid "E-mail address"
msgstr ""

#: websitescan/class-wsc-settings.php:212
msgid "Client ID"
msgstr ""

#: websitescan/class-wsc-settings.php:227
msgid "Client Secret"
msgstr ""

#: gutenberg/build/index.js:1
msgid "Legal document - Complianz"
msgstr ""

#: gutenberg/build/index.js:1
msgid "Select a document"
msgstr ""

#: gutenberg/build/index.js:1
#: settings/build/5175.4b3b51a33ec4e2266785.js:1
#: settings/build/6946.d959054c40656aabd21c.js:1
#: settings/build/8358.de96f9467392c01f0c5f.js:1
msgid "Loading..."
msgstr ""

#: gutenberg/build/index.js:1
msgid "No documents found. Please finish the Complianz Privacy Suite wizard to generate documents"
msgstr ""

#: gutenberg/build/index.js:1
msgid "Document settings"
msgstr ""

#: gutenberg/build/index.js:1
msgid "Document sync status"
msgstr ""

#: gutenberg/build/index.js:1
#: settings/build/5023.e939e70845602ba08cf1.js:1
#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "Service"
msgstr ""

#: gutenberg/build/index.js:1
msgid "You do not have sufficient permissions to edit this block."
msgstr ""

#: gutenberg/build/index.js:1
msgid "You can add custom HTML to create your own placeholder. This placeholder is visible before consent."
msgstr ""

#: gutenberg/build/index.js:1
msgid "You can add custom HTML that requires consent. In the right-side bar you will find the options for this custom block. For instructions, please go to complianz.io/gutenberg for more information."
msgstr ""

#: settings/build/11.e9c71a310cc4e025c93b.js:1
#: settings/build/2302.a1257a2ff8404e29dc9f.js:1
#: settings/build/3737.d7bfee35ff013bc096fb.js:1
#: settings/build/5193.f173371ca89b7cfd1dc7.js:1
#: settings/build/7771.c248b4fe81dc93792714.js:1
#: settings/build/9709.a24b2a1fe24c4ad466ea.js:1
msgid "Saving menu..."
msgstr ""

#: settings/build/11.e9c71a310cc4e025c93b.js:1
#: settings/build/2302.a1257a2ff8404e29dc9f.js:1
#: settings/build/3737.d7bfee35ff013bc096fb.js:1
#: settings/build/5193.f173371ca89b7cfd1dc7.js:1
#: settings/build/7771.c248b4fe81dc93792714.js:1
#: settings/build/9709.a24b2a1fe24c4ad466ea.js:1
msgid "Menu saved"
msgstr ""

#: settings/build/11.e9c71a310cc4e025c93b.js:1
#: settings/build/2302.a1257a2ff8404e29dc9f.js:1
#: settings/build/3737.d7bfee35ff013bc096fb.js:1
#: settings/build/5193.f173371ca89b7cfd1dc7.js:1
#: settings/build/7771.c248b4fe81dc93792714.js:1
#: settings/build/9709.a24b2a1fe24c4ad466ea.js:1
#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Something went wrong"
msgstr ""

#: settings/build/11.e9c71a310cc4e025c93b.js:1
#: settings/build/2302.a1257a2ff8404e29dc9f.js:1
#: settings/build/3737.d7bfee35ff013bc096fb.js:1
#: settings/build/5193.f173371ca89b7cfd1dc7.js:1
#: settings/build/7771.c248b4fe81dc93792714.js:1
#: settings/build/9709.a24b2a1fe24c4ad466ea.js:1
msgid "Settings have not been changed"
msgstr ""

#: settings/build/11.e9c71a310cc4e025c93b.js:1
#: settings/build/625.f81554ba7eb76dbbf0b0.js:1
#: settings/build/3407.51c452236e685dff3ecf.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/5035.51b29bb58017f7b65b0b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9684.e4903645d19905c0d455.js:1
msgid "More info"
msgstr ""

#: settings/build/11.e9c71a310cc4e025c93b.js:1
#: settings/build/4644.ce465c5d9733b80acf30.js:1
msgid "Learn more about %sPremium%s"
msgstr ""

#: settings/build/11.e9c71a310cc4e025c93b.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/4644.ce465c5d9733b80acf30.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Instructions"
msgstr ""

#: settings/build/11.e9c71a310cc4e025c93b.js:1
#: settings/build/4644.ce465c5d9733b80acf30.js:1
msgid "Check license"
msgstr ""

#: settings/build/11.e9c71a310cc4e025c93b.js:1
msgid "Get Premium"
msgstr ""

#: settings/build/11.e9c71a310cc4e025c93b.js:1
#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/2113.4da962fd404fcc001b7d.js:1
#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/2397.128a313ab0db7310b523.js:1
#: settings/build/3424.88f5ecf06f0e26d47eac.js:1
#: settings/build/5664.09a5d870cc30fa151cad.js:1
#: settings/build/6231.f9d17a5a1d14848fe3a8.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/8772.785d9c0620aa8b9946a2.js:1
#: settings/build/9056.7a23f9081f6350bd3057.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "Continue"
msgstr ""

#: settings/build/11.e9c71a310cc4e025c93b.js:1
msgid "Notifications"
msgstr ""

#: settings/build/11.e9c71a310cc4e025c93b.js:1
msgid "Expand all"
msgstr ""

#: settings/build/11.e9c71a310cc4e025c93b.js:1
msgid "Collapse all"
msgstr ""

#: settings/build/17.1d5f385a5620b230bdb0.js:1
#: settings/build/2980.92a8207f9d739ea56809.js:1
msgid "This wizard is intended to provide a general guide to a possible data breach."
msgstr ""

#: settings/build/17.1d5f385a5620b230bdb0.js:1
#: settings/build/2980.92a8207f9d739ea56809.js:1
msgid "Specialist legal advice should be sought about your specific circumstances."
msgstr ""

#: settings/build/17.1d5f385a5620b230bdb0.js:1
#: settings/build/2980.92a8207f9d739ea56809.js:1
msgid "Specialist legal advice required"
msgstr ""

#: settings/build/17.1d5f385a5620b230bdb0.js:1
#: settings/build/2980.92a8207f9d739ea56809.js:1
msgid "Your dataleak report:"
msgstr ""

#: settings/build/93.3f39c6fe7156b09aa236.js:1
#: settings/build/847.89108489d5abafa2b4c5.js:1
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/2921.a3c2c5e4ef574d459817.js:1
#: settings/build/4078.107fceffa2f236d7a108.js:1
#: settings/build/5023.e939e70845602ba08cf1.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "For more information, please read this %sarticle%s."
msgstr ""

#: settings/build/93.3f39c6fe7156b09aa236.js:1
#: settings/build/679.e5f71cd6da9fc981b109.js:1
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/5023.e939e70845602ba08cf1.js:1
msgid "Safe Mode enabled. To manage integrations, disable Safe Mode under Tools - Support."
msgstr ""

#: settings/build/93.3f39c6fe7156b09aa236.js:1
msgid "No active plugins detected in the integrations list."
msgstr ""

#: settings/build/93.3f39c6fe7156b09aa236.js:1
msgid "Plugin"
msgstr ""

#: settings/build/93.3f39c6fe7156b09aa236.js:1
#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
#: settings/build/5023.e939e70845602ba08cf1.js:1
msgid "Status"
msgstr ""

#: settings/build/93.3f39c6fe7156b09aa236.js:1
msgid "Below you will find the plugins currently detected and integrated with Complianz. Most plugins work by default, but you can also add a plugin to the script center or add it to the integration list."
msgstr ""

#: settings/build/93.3f39c6fe7156b09aa236.js:1
msgid "Enabled plugins will be blocked on the front-end of your website until the user has given consent (opt-in), or after the user has revoked consent (opt-out). When possible a placeholder is activated. You can also disable or configure the placeholder to your liking."
msgstr ""

#: settings/build/93.3f39c6fe7156b09aa236.js:1
#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
#: settings/build/3068.896ccf52a406c91528e3.js:1
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
#: settings/build/5023.e939e70845602ba08cf1.js:1
#: settings/build/8550.b17ddca5f7d4e170622e.js:1
msgid "Search"
msgstr ""

#: settings/build/93.3f39c6fe7156b09aa236.js:1
msgid "No plugins"
msgstr ""

#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
msgid "To use our newest Website Scan we need to verify your website and confirm your access by email. Register below and get the latest from Complianz!"
msgstr ""

#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "Your email address"
msgstr ""

#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/2113.4da962fd404fcc001b7d.js:1
#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/2397.128a313ab0db7310b523.js:1
#: settings/build/3424.88f5ecf06f0e26d47eac.js:1
#: settings/build/5664.09a5d870cc30fa151cad.js:1
#: settings/build/6231.f9d17a5a1d14848fe3a8.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/8525.433647e550a4b16270cb.js:1
#: settings/build/8772.785d9c0620aa8b9946a2.js:1
#: settings/build/9056.7a23f9081f6350bd3057.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "Welcome to Complianz"
msgstr ""

#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/2113.4da962fd404fcc001b7d.js:1
#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/2397.128a313ab0db7310b523.js:1
#: settings/build/3424.88f5ecf06f0e26d47eac.js:1
#: settings/build/5664.09a5d870cc30fa151cad.js:1
#: settings/build/6231.f9d17a5a1d14848fe3a8.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/8772.785d9c0620aa8b9946a2.js:1
#: settings/build/9056.7a23f9081f6350bd3057.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "No, Thanks"
msgstr ""

#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/2113.4da962fd404fcc001b7d.js:1
#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/2397.128a313ab0db7310b523.js:1
#: settings/build/3424.88f5ecf06f0e26d47eac.js:1
#: settings/build/5664.09a5d870cc30fa151cad.js:1
#: settings/build/6231.f9d17a5a1d14848fe3a8.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/8772.785d9c0620aa8b9946a2.js:1
#: settings/build/9056.7a23f9081f6350bd3057.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "Terms and Conditions"
msgstr ""

#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/2113.4da962fd404fcc001b7d.js:1
#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/2397.128a313ab0db7310b523.js:1
#: settings/build/3424.88f5ecf06f0e26d47eac.js:1
#: settings/build/5664.09a5d870cc30fa151cad.js:1
#: settings/build/6231.f9d17a5a1d14848fe3a8.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/8772.785d9c0620aa8b9946a2.js:1
#: settings/build/9056.7a23f9081f6350bd3057.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "Get tips and tricks"
msgstr ""

#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/2113.4da962fd404fcc001b7d.js:1
#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/2397.128a313ab0db7310b523.js:1
#: settings/build/3424.88f5ecf06f0e26d47eac.js:1
#: settings/build/5664.09a5d870cc30fa151cad.js:1
#: settings/build/6231.f9d17a5a1d14848fe3a8.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/8772.785d9c0620aa8b9946a2.js:1
#: settings/build/9056.7a23f9081f6350bd3057.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "Skip"
msgstr ""

#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/2113.4da962fd404fcc001b7d.js:1
#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/2397.128a313ab0db7310b523.js:1
#: settings/build/3424.88f5ecf06f0e26d47eac.js:1
#: settings/build/5664.09a5d870cc30fa151cad.js:1
#: settings/build/6231.f9d17a5a1d14848fe3a8.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/8772.785d9c0620aa8b9946a2.js:1
#: settings/build/9056.7a23f9081f6350bd3057.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "Install quickly for free"
msgstr ""

#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/2113.4da962fd404fcc001b7d.js:1
#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/2397.128a313ab0db7310b523.js:1
#: settings/build/3424.88f5ecf06f0e26d47eac.js:1
#: settings/build/5664.09a5d870cc30fa151cad.js:1
#: settings/build/6231.f9d17a5a1d14848fe3a8.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/8772.785d9c0620aa8b9946a2.js:1
#: settings/build/9056.7a23f9081f6350bd3057.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "Installing ..."
msgstr ""

#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/2113.4da962fd404fcc001b7d.js:1
#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/2397.128a313ab0db7310b523.js:1
#: settings/build/3424.88f5ecf06f0e26d47eac.js:1
#: settings/build/5664.09a5d870cc30fa151cad.js:1
#: settings/build/6231.f9d17a5a1d14848fe3a8.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/8772.785d9c0620aa8b9946a2.js:1
#: settings/build/9056.7a23f9081f6350bd3057.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "You’re almost there..."
msgstr ""

#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/2113.4da962fd404fcc001b7d.js:1
#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/2397.128a313ab0db7310b523.js:1
#: settings/build/3424.88f5ecf06f0e26d47eac.js:1
#: settings/build/5664.09a5d870cc30fa151cad.js:1
#: settings/build/6231.f9d17a5a1d14848fe3a8.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/8772.785d9c0620aa8b9946a2.js:1
#: settings/build/9056.7a23f9081f6350bd3057.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "Close"
msgstr ""

#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/2113.4da962fd404fcc001b7d.js:1
#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/2397.128a313ab0db7310b523.js:1
#: settings/build/3424.88f5ecf06f0e26d47eac.js:1
#: settings/build/5664.09a5d870cc30fa151cad.js:1
#: settings/build/6231.f9d17a5a1d14848fe3a8.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/8772.785d9c0620aa8b9946a2.js:1
#: settings/build/9056.7a23f9081f6350bd3057.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "Missing Terms & Conditions? Generate now"
msgstr ""

#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/2113.4da962fd404fcc001b7d.js:1
#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/2397.128a313ab0db7310b523.js:1
#: settings/build/3424.88f5ecf06f0e26d47eac.js:1
#: settings/build/5664.09a5d870cc30fa151cad.js:1
#: settings/build/6231.f9d17a5a1d14848fe3a8.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/8772.785d9c0620aa8b9946a2.js:1
#: settings/build/9056.7a23f9081f6350bd3057.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "Really Simple Security? Let’s go"
msgstr ""

#: settings/build/120.39062aaeb197ef27b347.js:1
#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/2113.4da962fd404fcc001b7d.js:1
#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/2397.128a313ab0db7310b523.js:1
#: settings/build/3424.88f5ecf06f0e26d47eac.js:1
#: settings/build/5664.09a5d870cc30fa151cad.js:1
#: settings/build/6231.f9d17a5a1d14848fe3a8.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/8772.785d9c0620aa8b9946a2.js:1
#: settings/build/9056.7a23f9081f6350bd3057.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "Something went wrong while downloading the document."
msgstr ""

#: settings/build/277.39654c18f2c50b5a2f3c.js:1
#: settings/build/847.89108489d5abafa2b4c5.js:1
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/1249.6de9f730cac4c47b6e71.js:1
#: settings/build/2980.92a8207f9d739ea56809.js:1
#: settings/build/3192.927ccc5a39908a2c5eac.js:1
#: settings/build/3518.c39c508126fbf4701fb6.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/5175.4b3b51a33ec4e2266785.js:1
#: settings/build/5228.3af41d894b237b867567.js:1
#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6281.81436365488010af52e4.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/6946.d959054c40656aabd21c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
#: settings/build/8033.8caf3a2047bae05d7bc7.js:1
#: settings/build/8217.c00283a5ed4d60b3ceb1.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9567.1bb4efce88343dfca145.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Select an option"
msgstr ""

#: settings/build/277.39654c18f2c50b5a2f3c.js:1
msgid "No logo found. Please add a logo in the customizer."
msgstr ""

#: settings/build/277.39654c18f2c50b5a2f3c.js:1
msgid "Select a logo"
msgstr ""

#: settings/build/277.39654c18f2c50b5a2f3c.js:1
msgid "Set logo"
msgstr ""

#: settings/build/293.d5a0682eefcea58ff65c.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "View all"
msgstr ""

#: settings/build/348.688edc514d6d0bc0e1a6.js:1
msgid "The conversion graph shows the ratio for the different choices users have. When a user has made a choice, this will be counted as either a converted user, or a not converted. If no choice is made, the user will be listed in the \"No choice\" category."
msgstr ""

#: settings/build/348.688edc514d6d0bc0e1a6.js:1
msgid "As you have enabled geoip, there are several regions in which a banner is shown, in different ways. In regions apart from %s no banner is shown at all."
msgstr ""

#: settings/build/348.688edc514d6d0bc0e1a6.js:1
msgid "Banners in different regions"
msgstr ""

#: settings/build/348.688edc514d6d0bc0e1a6.js:1
msgid "The consent banner with the best results has been enabled as default banner."
msgstr ""

#: settings/build/348.688edc514d6d0bc0e1a6.js:1
msgid "A/B testing is disabled. Previously made progress is saved."
msgstr ""

#: settings/build/348.688edc514d6d0bc0e1a6.js:1
msgid "A/B is enabled and will end in %s days."
msgstr ""

#: settings/build/348.688edc514d6d0bc0e1a6.js:1
msgid "A/B is enabled and will end in 1 day."
msgstr ""

#: settings/build/348.688edc514d6d0bc0e1a6.js:1
msgid "A/B is enabled and will end today."
msgstr ""

#: settings/build/348.688edc514d6d0bc0e1a6.js:1
msgid "The A/B tracking period has ended, the best performer will be enabled on the next scheduled check."
msgstr ""

#: settings/build/626.fe1c387a5a23ee649985.js:1
msgid "You are using an ad blocker. This will prevent most cookies from being placed. Please run the scan without an adblocker enabled."
msgstr ""

#: settings/build/626.fe1c387a5a23ee649985.js:1
msgid "Ad Blocker detected."
msgstr ""

#: settings/build/626.fe1c387a5a23ee649985.js:1
msgid "Your browser has the Do Not Track or Global Privacy Control setting enabled."
msgstr ""

#: settings/build/626.fe1c387a5a23ee649985.js:1
msgid "This will prevent most cookies from being placed."
msgstr ""

#: settings/build/626.fe1c387a5a23ee649985.js:1
msgid "Please run the scan with these browser options disabled."
msgstr ""

#: settings/build/626.fe1c387a5a23ee649985.js:1
msgid "DNT or GPC enabled."
msgstr ""

#: settings/build/626.fe1c387a5a23ee649985.js:1
msgid "No cookies found on your domain yet."
msgstr ""

#: settings/build/626.fe1c387a5a23ee649985.js:1
msgid "The scan found 1 cookie on your domain."
msgstr ""

#: settings/build/626.fe1c387a5a23ee649985.js:1
msgid "The scan found %s cookies on your domain."
msgstr ""

#: settings/build/626.fe1c387a5a23ee649985.js:1
msgid "Continue the wizard to categorize cookies and configure consent."
msgstr ""

#: settings/build/626.fe1c387a5a23ee649985.js:1
msgid "Scanning, %s complete."
msgstr ""

#: settings/build/626.fe1c387a5a23ee649985.js:1
msgid "Advanced Scan Unavailable"
msgstr ""

#: settings/build/626.fe1c387a5a23ee649985.js:1
msgid "Scan"
msgstr ""

#: settings/build/626.fe1c387a5a23ee649985.js:1
msgid "Clear Cookies"
msgstr ""

#: settings/build/847.89108489d5abafa2b4c5.js:1
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Your CSS class"
msgstr ""

#: settings/build/847.89108489d5abafa2b4c5.js:1
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
#: settings/build/3068.896ccf52a406c91528e3.js:1
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
#: settings/build/7234.d838abdddb6468eb7b46.js:1
#: settings/build/8033.8caf3a2047bae05d7bc7.js:1
#: settings/build/8550.b17ddca5f7d4e170622e.js:1
#: settings/build/8985.6dc2f067dbc853b19630.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Show more"
msgstr ""

#: settings/build/847.89108489d5abafa2b4c5.js:1
#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
#: settings/build/3068.896ccf52a406c91528e3.js:1
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
#: settings/build/7234.d838abdddb6468eb7b46.js:1
#: settings/build/8033.8caf3a2047bae05d7bc7.js:1
#: settings/build/8550.b17ddca5f7d4e170622e.js:1
#: settings/build/8985.6dc2f067dbc853b19630.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Show less"
msgstr ""

#: settings/build/880.ed38458988fd4dab2969.js:1
#: settings/build/3430.888f2122ddc42c0265c5.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9567.1bb4efce88343dfca145.js:1
msgid "Copied shortcode"
msgstr ""

#: settings/build/1027.680330fc1970e47f24df.js:1
msgid "Are you sure?"
msgstr ""

#: settings/build/1027.680330fc1970e47f24df.js:1
msgid "Confirm"
msgstr ""

#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/8033.8caf3a2047bae05d7bc7.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "waits for: "
msgstr ""

#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/8033.8caf3a2047bae05d7bc7.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Empty URL"
msgstr ""

#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/8033.8caf3a2047bae05d7bc7.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Add a URL to create a dependency between two URLs"
msgstr ""

#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
#: settings/build/2111.847b0d94c7100f73f709.js:1
#: settings/build/3068.896ccf52a406c91528e3.js:1
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
#: settings/build/3582.f939c1a1c9321a2e16ab.js:1
#: settings/build/5175.4b3b51a33ec4e2266785.js:1
#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/6946.d959054c40656aabd21c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
#: settings/build/7234.d838abdddb6468eb7b46.js:1
#: settings/build/8550.b17ddca5f7d4e170622e.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Delete"
msgstr ""

#: settings/build/1137.d41a8b71772cb6b6b503.js:1
#: settings/build/9713.32a7de6fe52bec35e49c.js:1
#: settings/build/9736.18721ac0f927e9e78fe6.js:1
msgid "Write your JavaScript without wrapping it in script tags."
msgstr ""

#: settings/build/1137.d41a8b71772cb6b6b503.js:1
msgid "The script center should be used to add and block third-party scripts and iFrames before consent is given, or when consent is revoked. For example Hotjar and embedded video’s."
msgstr ""

#: settings/build/1137.d41a8b71772cb6b6b503.js:1
msgid "Add a third-party script"
msgstr ""

#: settings/build/1137.d41a8b71772cb6b6b503.js:1
msgid "Block a script, iframe or plugin"
msgstr ""

#: settings/build/1137.d41a8b71772cb6b6b503.js:1
msgid ""
"Whitelist a script, iframe or plugin\n"
""
msgstr ""

#: settings/build/1361.386c742185a30f4ae804.js:1
msgid "Show"
msgstr ""

#: settings/build/1370.9dc9c94c443870d44ab9.js:1
msgid "Unlink values"
msgstr ""

#: settings/build/1370.9dc9c94c443870d44ab9.js:1
msgid "Link values together"
msgstr ""

#: settings/build/1439.dd2d909d2719f92b7192.js:1
msgid "Help"
msgstr ""

#: settings/build/1439.dd2d909d2719f92b7192.js:1
msgid "Ignore"
msgstr ""

#: settings/build/1439.dd2d909d2719f92b7192.js:1
msgid "Fix"
msgstr ""

#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
msgid "Resolved"
msgstr ""

#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
msgid "Open"
msgstr ""

#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
#: settings/build/2980.92a8207f9d739ea56809.js:1
#: settings/build/3068.896ccf52a406c91528e3.js:1
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
#: settings/build/6281.81436365488010af52e4.js:1
#: settings/build/7234.d838abdddb6468eb7b46.js:1
#: settings/build/8550.b17ddca5f7d4e170622e.js:1
msgid "Region"
msgstr ""

#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
msgid "Data Request"
msgstr ""

#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
msgid "All"
msgstr ""

#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
#: settings/build/3068.896ccf52a406c91528e3.js:1
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
#: settings/build/7234.d838abdddb6468eb7b46.js:1
#: settings/build/8550.b17ddca5f7d4e170622e.js:1
msgid "%s items selected"
msgstr ""

#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
#: settings/build/3068.896ccf52a406c91528e3.js:1
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
#: settings/build/7234.d838abdddb6468eb7b46.js:1
#: settings/build/8550.b17ddca5f7d4e170622e.js:1
msgid "1 item selected"
msgstr ""

#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
msgid "Mark as resolved"
msgstr ""

#: settings/build/1624.01b1c5264a5e8bef49f1.js:1
#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
msgid "No records"
msgstr ""

#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
msgid "Thanks for joining us!"
msgstr ""

#: settings/build/1677.515fe447bb45467918d1.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
msgid "We've sent you an email - all you need to do is confirm your email address and you can start using Complianz."
msgstr ""

#: settings/build/2058.366ca8932d06dc54d28d.js:1
msgid "The consent banner and cookie blocker are required on your website."
msgstr ""

#: settings/build/2058.366ca8932d06dc54d28d.js:1
msgid "You can enable them both here, then you should check your website if your configuration is working properly."
msgstr ""

#: settings/build/2058.366ca8932d06dc54d28d.js:1
msgid "Please read the below article to debug any issues while in safe mode. Safe mode is available under settings."
msgstr ""

#: settings/build/2058.366ca8932d06dc54d28d.js:1
msgid "You will find tips and tricks on your dashboard after you have configured your consent banner."
msgstr ""

#: settings/build/2058.366ca8932d06dc54d28d.js:1
msgid "A consent banner is required"
msgstr ""

#: settings/build/2058.366ca8932d06dc54d28d.js:1
msgid "Your site does not require a consent banner. If you think you need a consent banner, please review your wizard settings."
msgstr ""

#: settings/build/2058.366ca8932d06dc54d28d.js:1
msgid "A consent banner is not required"
msgstr ""

#: settings/build/2058.366ca8932d06dc54d28d.js:1
msgid "Almost there!"
msgstr ""

#: settings/build/2058.366ca8932d06dc54d28d.js:1
msgid "There are %s questions that are required to complete the wizard."
msgstr ""

#: settings/build/2058.366ca8932d06dc54d28d.js:1
msgid "The consent banner and the cookie blocker are now ready to be enabled."
msgstr ""

#: settings/build/2058.366ca8932d06dc54d28d.js:1
msgid "Not all required fields are completed yet."
msgstr ""

#: settings/build/2058.366ca8932d06dc54d28d.js:1
msgid "Please check the wizard to complete all required questions."
msgstr ""

#: settings/build/2058.366ca8932d06dc54d28d.js:1
msgid "The following required fields have not been completed:"
msgstr ""

#: settings/build/2058.366ca8932d06dc54d28d.js:1
msgid "Go to question"
msgstr ""

#: settings/build/2111.847b0d94c7100f73f709.js:1
#: settings/build/3582.f939c1a1c9321a2e16ab.js:1
#: settings/build/5175.4b3b51a33ec4e2266785.js:1
#: settings/build/6946.d959054c40656aabd21c.js:1
msgid "Country"
msgstr ""

#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
msgid "Great! You're a few minutes away from getting started with the Website Scan. You just need to look over the Terms and Conditions. If you agree, please continue."
msgstr ""

#: settings/build/2299.54f393cb725af3df69f0.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "Try again!"
msgstr ""

#: settings/build/2302.a1257a2ff8404e29dc9f.js:1
msgid "No menus found."
msgstr ""

#: settings/build/2302.a1257a2ff8404e29dc9f.js:1
msgid "No menus were found. Skip this step, or create a menu first."
msgstr ""

#: settings/build/2302.a1257a2ff8404e29dc9f.js:1
msgid "Pages not included in a menu"
msgstr ""

#: settings/build/2302.a1257a2ff8404e29dc9f.js:1
msgid "The generated document %s has not been assigned to a menu yet, you can do this now, or skip this step and do it later."
msgstr ""

#: settings/build/2302.a1257a2ff8404e29dc9f.js:1
msgid "Not all generated documents have been assigned to a menu yet, you can do this now, or skip this step and do it later."
msgstr ""

#: settings/build/2302.a1257a2ff8404e29dc9f.js:1
msgid "All pages generated!"
msgstr ""

#: settings/build/2302.a1257a2ff8404e29dc9f.js:1
msgid "Great! All your generated documents have been assigned to a menu, so you can skip this step."
msgstr ""

#: settings/build/2827.bb78b0624a7550aee9be.js:1
msgid "Type your question here"
msgstr ""

#: settings/build/2980.92a8207f9d739ea56809.js:1
#: settings/build/6281.81436365488010af52e4.js:1
msgid "Incomplete"
msgstr ""

#: settings/build/2980.92a8207f9d739ea56809.js:1
#: settings/build/6281.81436365488010af52e4.js:1
msgid "The wizard has not been completed yet, but this field requires information from the wizard. Please complete the wizard first."
msgstr ""

#: settings/build/2980.92a8207f9d739ea56809.js:1
msgid "Create Data Breach report"
msgstr ""

#: settings/build/2980.92a8207f9d739ea56809.js:1
#: settings/build/6281.81436365488010af52e4.js:1
#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "Next"
msgstr ""

#: settings/build/2980.92a8207f9d739ea56809.js:1
#: settings/build/6281.81436365488010af52e4.js:1
msgid "Create"
msgstr ""

#: settings/build/2980.92a8207f9d739ea56809.js:1
msgid "Exit"
msgstr ""

#: settings/build/3068.896ccf52a406c91528e3.js:1
#: settings/build/7234.d838abdddb6468eb7b46.js:1
#: settings/build/8550.b17ddca5f7d4e170622e.js:1
msgid "Document"
msgstr ""

#: settings/build/3068.896ccf52a406c91528e3.js:1
msgid "Download Processing Agreement"
msgstr ""

#: settings/build/3068.896ccf52a406c91528e3.js:1
#: settings/build/7234.d838abdddb6468eb7b46.js:1
#: settings/build/8550.b17ddca5f7d4e170622e.js:1
msgid "No documents"
msgstr ""

#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
msgid "Other"
msgstr ""

#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
msgid "User ID"
msgstr ""

#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
msgid "DNT/GPC"
msgstr ""

#: settings/build/3078.85c3947c0da1a8cdc7c7.js:1
#: settings/build/7234.d838abdddb6468eb7b46.js:1
msgid "Download Proof of Consent"
msgstr ""

#: settings/build/3192.927ccc5a39908a2c5eac.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9567.1bb4efce88343dfca145.js:1
msgid "Other documents"
msgstr ""

#: settings/build/3192.927ccc5a39908a2c5eac.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/5175.4b3b51a33ec4e2266785.js:1
#: settings/build/6946.d959054c40656aabd21c.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9567.1bb4efce88343dfca145.js:1
msgid "Processing Agreement"
msgstr ""

#: settings/build/3192.927ccc5a39908a2c5eac.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9567.1bb4efce88343dfca145.js:1
msgid "Data Breach"
msgstr ""

#: settings/build/3192.927ccc5a39908a2c5eac.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8217.c00283a5ed4d60b3ceb1.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9567.1bb4efce88343dfca145.js:1
msgid "Download file"
msgstr ""

#: settings/build/3192.927ccc5a39908a2c5eac.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8217.c00283a5ed4d60b3ceb1.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9567.1bb4efce88343dfca145.js:1
msgid "Go to overview"
msgstr ""

#: settings/build/3192.927ccc5a39908a2c5eac.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8217.c00283a5ed4d60b3ceb1.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9567.1bb4efce88343dfca145.js:1
msgid "Create new"
msgstr ""

#: settings/build/3254.3156353cae27b67e8c76.js:1
msgid "We found %s active plugin integrations"
msgstr ""

#: settings/build/3254.3156353cae27b67e8c76.js:1
msgid "We found %s active service integrations"
msgstr ""

#: settings/build/3370.b0a99ca16b07a3094f38.js:1
msgid "To view possible script conflicts on your site, set the SCRIPT_DEBUG constant in your wp-config.php, or install the plugin WP Debugging"
msgstr ""

#: settings/build/3370.b0a99ca16b07a3094f38.js:1
msgid "Debugging enabled:"
msgstr ""

#: settings/build/3370.b0a99ca16b07a3094f38.js:1
msgid "No script errors detected"
msgstr ""

#: settings/build/3407.51c452236e685dff3ecf.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/5035.51b29bb58017f7b65b0b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9684.e4903645d19905c0d455.js:1
msgid "Re-started test"
msgstr ""

#: settings/build/3407.51c452236e685dff3ecf.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/5035.51b29bb58017f7b65b0b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9684.e4903645d19905c0d455.js:1
msgid "Re-check"
msgstr ""

#: settings/build/3407.51c452236e685dff3ecf.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/5035.51b29bb58017f7b65b0b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9684.e4903645d19905c0d455.js:1
msgid "View"
msgstr ""

#: settings/build/3407.51c452236e685dff3ecf.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Not all fields have been entered, or you have not clicked the \"finish\" button yet."
msgstr ""

#: settings/build/3407.51c452236e685dff3ecf.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Consent Management is activated on your site."
msgstr ""

#: settings/build/3407.51c452236e685dff3ecf.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "You still have 1 task open."
msgstr ""

#: settings/build/3407.51c452236e685dff3ecf.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "You still have %s tasks open."
msgstr ""

#: settings/build/3407.51c452236e685dff3ecf.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Well done! Your website is ready for your selected regions."
msgstr ""

#: settings/build/3424.88f5ecf06f0e26d47eac.js:1
#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
msgid "You want more Really Simple Plugins? Select below plugins you'd like to install for free! It only takes 10 seconds.."
msgstr ""

#: settings/build/3430.888f2122ddc42c0265c5.js:1
msgid "No required documents"
msgstr ""

#: settings/build/3430.888f2122ddc42c0265c5.js:1
msgid "Documents updated!"
msgstr ""

#: settings/build/3430.888f2122ddc42c0265c5.js:1
msgid "Update"
msgstr ""

#: settings/build/3582.f939c1a1c9321a2e16ab.js:1
msgid "New Third Party"
msgstr ""

#: settings/build/3785.e377b210085f5b6810ca.js:1
#: settings/build/6281.81436365488010af52e4.js:1
msgid "You can only upload .json files"
msgstr ""

#: settings/build/3785.e377b210085f5b6810ca.js:1
#: settings/build/6281.81436365488010af52e4.js:1
msgid "Incorrect extension"
msgstr ""

#: settings/build/3785.e377b210085f5b6810ca.js:1
#: settings/build/6281.81436365488010af52e4.js:1
msgid "Select file"
msgstr ""

#: settings/build/3785.e377b210085f5b6810ca.js:1
#: settings/build/6281.81436365488010af52e4.js:1
msgid "Settings imported"
msgstr ""

#: settings/build/3971.42c75f410294426ba63f.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Continue Wizard"
msgstr ""

#: settings/build/3971.42c75f410294426ba63f.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Cookie Blocker"
msgstr ""

#: settings/build/3990.4f2c03aad14e23760979.js:1
#: settings/build/5552.4d08e2057a8ac6614ab8.js:1
msgid "Add to annex of Privacy Statement"
msgstr ""

#: settings/build/3990.4f2c03aad14e23760979.js:1
#: settings/build/5552.4d08e2057a8ac6614ab8.js:1
msgid "Does not conform with the Consent API"
msgstr ""

#: settings/build/3990.4f2c03aad14e23760979.js:1
#: settings/build/5552.4d08e2057a8ac6614ab8.js:1
msgid "Conforms to the Consent API"
msgstr ""

#: settings/build/4186.616704c21483c73a9fbe.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/5035.51b29bb58017f7b65b0b.js:1
#: settings/build/7338.18ed6f787e1915edb1be.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9487.e9838335195a59707dfe.js:1
msgid "Activate"
msgstr ""

#: settings/build/4186.616704c21483c73a9fbe.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/7338.18ed6f787e1915edb1be.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Activating..."
msgstr ""

#: settings/build/4186.616704c21483c73a9fbe.js:1
#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/7338.18ed6f787e1915edb1be.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Downloading..."
msgstr ""

#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9567.1bb4efce88343dfca145.js:1
msgid "Document is kept up to date by Complianz"
msgstr ""

#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9567.1bb4efce88343dfca145.js:1
msgid "Document is not kept up to date by Complianz"
msgstr ""

#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/8931.879cb0c93ebc3d815827.js:1
#: settings/build/9567.1bb4efce88343dfca145.js:1
msgid "Validated"
msgstr ""

#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9567.1bb4efce88343dfca145.js:1
msgid "Missing document"
msgstr ""

#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9567.1bb4efce88343dfca145.js:1
msgid "Not enabled"
msgstr ""

#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/8931.879cb0c93ebc3d815827.js:1
msgid "Synchronized"
msgstr ""

#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/6644.b133f6e44de4c0c58899.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Consent Statistics"
msgstr ""

#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/6644.b133f6e44de4c0c58899.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "A/B Testing"
msgstr ""

#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/6644.b133f6e44de4c0c58899.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Documentation"
msgstr ""

#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/6644.b133f6e44de4c0c58899.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Premium Support"
msgstr ""

#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/6644.b133f6e44de4c0c58899.js:1
#: settings/build/8084.b434fc1673c52d0e5cd9.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "Full Consent"
msgstr ""

#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/6644.b133f6e44de4c0c58899.js:1
#: settings/build/8084.b434fc1673c52d0e5cd9.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
msgid "No Consent"
msgstr ""

#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9582.87719556c11ecd0333a3.js:1
msgid "Progress"
msgstr ""

#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9582.87719556c11ecd0333a3.js:1
msgid "All tasks"
msgstr ""

#: settings/build/4570.d1fb179532750f4a995b.js:1
#: settings/build/8596.0fa35c664065b81a3008.js:1
#: settings/build/9582.87719556c11ecd0333a3.js:1
msgid "Remaining tasks"
msgstr ""

#: settings/build/4575.34faeb333c9031b5299f.js:1
#: settings/build/7161.b7d2c4b248f286201398.js:1
msgid "Export to CSV"
msgstr ""

#: settings/build/4575.34faeb333c9031b5299f.js:1
msgid "Your Data Requests Export has been completed."
msgstr ""

#: settings/build/4575.34faeb333c9031b5299f.js:1
#: settings/build/7161.b7d2c4b248f286201398.js:1
msgid "Your selection does not contain any data."
msgstr ""

#: settings/build/4604.df1a1edbd31680157df0.js:1
msgid "Disable Website Scan"
msgstr ""

#: settings/build/4604.df1a1edbd31680157df0.js:1
msgid "Enable Website Scan"
msgstr ""

#: settings/build/4604.df1a1edbd31680157df0.js:1
msgid "Reset Website Scan"
msgstr ""

#: settings/build/4604.df1a1edbd31680157df0.js:1
msgid "Activate Website Scan"
msgstr ""

#: settings/build/5016.86c12b6af899e8605bab.js:1
msgid "Allow"
msgstr ""

#: settings/build/5023.e939e70845602ba08cf1.js:1
msgid "Third-party services and social media are marked as not being used on your website in the wizard."
msgstr ""

#: settings/build/5023.e939e70845602ba08cf1.js:1
msgid "reCaptcha is connected and will be blocked before consent. To change your settings, disable reCaptcha in the list."
msgstr ""

#: settings/build/5023.e939e70845602ba08cf1.js:1
msgid "reCaptcha blocking enabled"
msgstr ""

#: settings/build/5023.e939e70845602ba08cf1.js:1
msgid "Enabled services will be blocked on the front-end of your website until the user has given consent (opt-in), or after the user has revoked consent (opt-out). When possible a placeholder is activated. You can also disable or configure the placeholder to your liking."
msgstr ""

#: settings/build/5023.e939e70845602ba08cf1.js:1
msgid "View services"
msgstr ""

#: settings/build/5023.e939e70845602ba08cf1.js:1
msgid "No services"
msgstr ""

#: settings/build/5035.51b29bb58017f7b65b0b.js:1
msgid "Deactivate"
msgstr ""

#: settings/build/5175.4b3b51a33ec4e2266785.js:1
msgid "New processor"
msgstr ""

#: settings/build/5175.4b3b51a33ec4e2266785.js:1
msgid "Add new Processors & Service Providers"
msgstr ""

#: settings/build/5279.5654d43ca89986d8ae81.js:1
#: settings/build/7660.5db62bb36578bb117af3.js:1
msgid "Today"
msgstr ""

#: settings/build/5279.5654d43ca89986d8ae81.js:1
#: settings/build/7660.5db62bb36578bb117af3.js:1
msgid "Yesterday"
msgstr ""

#: settings/build/5279.5654d43ca89986d8ae81.js:1
#: settings/build/7660.5db62bb36578bb117af3.js:1
msgid "Last 7 days"
msgstr ""

#: settings/build/5279.5654d43ca89986d8ae81.js:1
#: settings/build/7660.5db62bb36578bb117af3.js:1
msgid "Last 30 days"
msgstr ""

#: settings/build/5279.5654d43ca89986d8ae81.js:1
#: settings/build/7660.5db62bb36578bb117af3.js:1
msgid "Last 90 days"
msgstr ""

#: settings/build/5279.5654d43ca89986d8ae81.js:1
#: settings/build/7660.5db62bb36578bb117af3.js:1
msgid "Last month"
msgstr ""

#: settings/build/5279.5654d43ca89986d8ae81.js:1
#: settings/build/7660.5db62bb36578bb117af3.js:1
msgid "Year to date"
msgstr ""

#: settings/build/5279.5654d43ca89986d8ae81.js:1
#: settings/build/7660.5db62bb36578bb117af3.js:1
msgid "Last year"
msgstr ""

#: settings/build/5552.4d08e2057a8ac6614ab8.js:1
msgid "No plugins with suggested statements found."
msgstr ""

#: settings/build/5552.4d08e2057a8ac6614ab8.js:1
msgid "You have chosen to generate your own Privacy Statement, which means the option to add custom text to it is not applicable."
msgstr ""

#: settings/build/5575.fcad698abd3911da489e.js:1
msgid "Please activate Really Simple Security to unlock this feature."
msgstr ""

#: settings/build/5575.fcad698abd3911da489e.js:1
msgid "Please install Really Simple Security to unlock this feature."
msgstr ""

#: settings/build/5575.fcad698abd3911da489e.js:1
msgid "Not installed"
msgstr ""

#: settings/build/5575.fcad698abd3911da489e.js:1
msgid "Vulnerability Detection"
msgstr ""

#: settings/build/5575.fcad698abd3911da489e.js:1
msgid "HTTP Strict Transport Security and related security headers"
msgstr ""

#: settings/build/5575.fcad698abd3911da489e.js:1
msgid "TLS / SSL"
msgstr ""

#: settings/build/5575.fcad698abd3911da489e.js:1
msgid "Recommended site hardening features"
msgstr ""

#: settings/build/5575.fcad698abd3911da489e.js:1
msgid "We are committed to the security of personal data. We take appropriate security measures to limit abuse of and unauthorized access to personal data. This ensures that only the necessary persons have access to your data, that access to the data is protected, and that our security measures are regularly reviewed."
msgstr ""

#: settings/build/5575.fcad698abd3911da489e.js:1
msgid "The security measures we use consist of, but are not limited to:"
msgstr ""

#: settings/build/5575.fcad698abd3911da489e.js:1
msgid "No security measures enabled in Really Simple Security"
msgstr ""

#: settings/build/5575.fcad698abd3911da489e.js:1
msgid "Please upgrade Really Simple Security to the latest version to unlock this feature."
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "Sync cookie with cookiedatabase.org"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "Show cookie on Cookie Policy"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "1 year"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "Cookie function"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "e.g. store user ID"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "View cookie on cookiedatabase.org"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "Saved cookie"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "Restore"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "Deleted"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "Logged in users only, ignored"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "Admin, ignored"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "The data for this cookie is complete"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "This cookie has missing fields"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "This cookie is not synchronized with cookiedatabase.org."
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "This cookie will be on your Cookie Policy"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "This cookie is not shown on the Cookie Policy"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "This cookie has not been detected on your site in the last three months"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
#: settings/build/7102.55f3c75507ed6c5bb594.js:1
msgid "This cookie has recently been detected"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
msgid "Data is shared with this service"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
msgid "Sync service with cookiedatabase.org"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
msgid "Service Types"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
msgid "Privacy Statement URL"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
msgid "View service on cookiedatabase.org"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
msgid "Saved service"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
msgid "Delete Service"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
msgid "New Service"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
msgid "The data for this service is complete"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
msgid "This service has missing fields"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
msgid "This service has been synchronized with cookiedatabase.org"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
msgid "This service is not synchronized with cookiedatabase.org"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
msgid "Add cookie to %s"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
#: settings/build/6729.a20ac2221b845ac5b52c.js:1
msgid "Save service to be able to add cookies"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
msgid "No cookies have been found currently. Please try another site scan, or check the most common causes in the article below "
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
msgid "No cookies found"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
msgid "Synchronization disabled: All detected cookies and services have been synchronised."
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
msgid "CURL is not enabled on your site, which is required for the Cookiedatabase sync to function."
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
msgid "You have opted out of the use of the Cookiedatabase.org synchronization."
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
msgid "Unknown Service"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
msgid "Sync"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
msgid "Show deleted cookies"
msgstr ""

#: settings/build/5875.f8e7a4b8799474897bf2.js:1
msgid "Add service"
msgstr ""

#: settings/build/6281.81436365488010af52e4.js:1
msgid "You can only upload .pdf, .doc or .docs files"
msgstr ""

#: settings/build/6281.81436365488010af52e4.js:1
msgid "Service name"
msgstr ""

#: settings/build/6281.81436365488010af52e4.js:1
msgid "e.g. Alphabet Inc"
msgstr ""

#: settings/build/6281.81436365488010af52e4.js:1
msgid "Upload"
msgstr ""

#: settings/build/6449.6de76ea494992ea5f41f.js:1
msgid "Are you sure you want to reset this banner to the default settings?"
msgstr ""

#: settings/build/6875.12a6c4155d36b8eb1726.js:1
msgid "Create Proof of Consent"
msgstr ""

#: settings/build/6875.12a6c4155d36b8eb1726.js:1
msgid "Generate"
msgstr ""

#: settings/build/7161.b7d2c4b248f286201398.js:1
msgid "Your Records Of Consent Export has been completed."
msgstr ""

#: settings/build/7489.1a72810e3d829440aca8.js:1
msgid "Enabled"
msgstr ""

#: settings/build/7489.1a72810e3d829440aca8.js:1
msgid "Disabled"
msgstr ""

#: settings/build/7489.1a72810e3d829440aca8.js:1
msgid "Pending"
msgstr ""

#: settings/build/7489.1a72810e3d829440aca8.js:1
msgid "Error"
msgstr ""

#: settings/build/7489.1a72810e3d829440aca8.js:1
msgid "Token Status: "
msgstr ""

#: settings/build/7489.1a72810e3d829440aca8.js:1
msgid "Website Scan Status: "
msgstr ""

#: settings/build/7489.1a72810e3d829440aca8.js:1
msgid "Didn't receive the activation email? "
msgstr ""

#: settings/build/7489.1a72810e3d829440aca8.js:1
msgid "Request again!"
msgstr ""

#: settings/build/7489.1a72810e3d829440aca8.js:1
msgid "Check your email to activate the Website Scan!"
msgstr ""

#: settings/build/7489.1a72810e3d829440aca8.js:1
msgid "Didn't receive the activation email? Please wait 1 hour before requesting again."
msgstr ""

#: settings/build/7579.44551f36215f353bd7f7.js:1
msgid "Editor"
msgstr ""

#: settings/build/8358.de96f9467392c01f0c5f.js:1
msgid "Type at least two characters"
msgstr ""

#: settings/build/8447.3532dcb4d10ba0f879d3.js:1
#: settings/build/9877.ec35052e5cc35a3c8ada.js:1
msgid "We want you to get the most out of Complianz. So over the next week we'll be sending eight tips and tricks to your inbox - be sure to keep a lookout."
msgstr ""

#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "Get ready for privacy legislation around the world. Follow a quick tour or start configuring the plugin!"
msgstr ""

#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "Configure"
msgstr ""

#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "Start tour"
msgstr ""

#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "This is your Dashboard. When the Wizard is completed, this will give you an overview of tasks, tools, and documentation."
msgstr ""

#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "This is where everything regarding cookies is configured. We will come back to the Wizard soon."
msgstr ""

#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "Here you can configure and style your consent banner if the Wizard is completed. An extra tab will be added with region-specific settings."
msgstr ""

#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "Based on your answers in the Wizard, we will automatically enable integrations with relevant services and plugins. In case you want to block extra scripts, you can add them to the Script Center."
msgstr ""

#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "Complianz tracks changes in your Cookie Notice and Cookie Policy with time-stamped documents. This is your consent registration while respecting the data minimization guidelines and won't store any user data."
msgstr ""

#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "Let's start the Wizard"
msgstr ""

#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "You are ready to start the Wizard. For more information, FAQ, and support, please visit Complianz.io."
msgstr ""

#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "End tour"
msgstr ""

#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "Great, your license is activated and valid!"
msgstr ""

#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "To unlock the wizard and future updates, please enter and activate your license."
msgstr ""

#: settings/build/8525.433647e550a4b16270cb.js:1
msgid "Activate your license"
msgstr ""

#: settings/build/8550.b17ddca5f7d4e170622e.js:1
msgid "Download Data Breach Report"
msgstr ""

#: settings/build/8550.b17ddca5f7d4e170622e.js:1
msgid "Reporting not required"
msgstr ""

#: settings/build/8708.19bd0a9a3ec0adec8ee8.js:1
msgid "Complianz is currently copying settings of site %1$s to %2$s of %3$s sites."
msgstr ""

#: settings/build/8708.19bd0a9a3ec0adec8ee8.js:1
msgid "Copying settings..."
msgstr ""

#: settings/build/8708.19bd0a9a3ec0adec8ee8.js:1
msgid "Are you sure? This will overwrite the settings in all your subsites with the Complianz settings of this site."
msgstr ""

#: settings/build/9487.e9838335195a59707dfe.js:1
msgid "Activating"
msgstr ""

#: settings/build/9487.e9838335195a59707dfe.js:1
msgid "Checking status"
msgstr ""

#: settings/build/9487.e9838335195a59707dfe.js:1
msgid "%s rating based on %d ratings"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Saving settings..."
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Settings saved"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Unexpected error"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "New"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Go Pro"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "This field is required"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Please enter a valid URL"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Please enter a valid email address"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Please enter a valid number"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Please enter a valid phone number"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "The wizard is currently in use by user with ID: "
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "To prevent conflicts during saving the wizard is temporarily locked."
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "The lock will automatically clear after two minutes."
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "required"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Please check if security settings on the server or a plugin is blocking the requests from Complianz."
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "The Complianz Rest API is disabled."
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "The Complianz Rest API returned a not found."
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "The Complianz Rest API returned a 403 forbidden error."
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "A problem was detected during the loading of the settings"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "The request returned the following errors:"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Response code:"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Status code:"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Server response:"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "More information"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Switch between banners"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Banner"
msgstr ""

#: settings/build/index.303d2f4b49595cf9f25d.js:1
msgid "Edit consent types"
msgstr ""
