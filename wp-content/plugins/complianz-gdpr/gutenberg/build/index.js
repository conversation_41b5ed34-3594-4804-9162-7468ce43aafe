(()=>{var e={856:function(e){e.exports=function(){"use strict";const{entries:e,setPrototypeOf:t,isFrozen:n,getPrototypeOf:r,getOwnPropertyDescriptor:o}=Object;let{freeze:i,seal:a,create:s}=Object,{apply:c,construct:l}="undefined"!=typeof Reflect&&Reflect;i||(i=function(e){return e}),a||(a=function(e){return e}),c||(c=function(e,t,n){return e.apply(t,n)}),l||(l=function(e,t){return new e(...t)});const u=S(Array.prototype.forEach),d=S(Array.prototype.pop),f=S(Array.prototype.push),p=S(String.prototype.toLowerCase),m=S(String.prototype.toString),h=S(String.prototype.match),g=S(String.prototype.replace),y=S(String.prototype.indexOf),b=S(String.prototype.trim),E=S(RegExp.prototype.test),w=(T=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return l(T,t)});var T;function S(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return c(e,t,r)}}function A(e,r){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p;t&&t(e,null);let i=r.length;for(;i--;){let t=r[i];if("string"==typeof t){const e=o(t);e!==t&&(n(r)||(r[i]=e),t=e)}e[t]=!0}return e}function v(e){for(let t=0;t<e.length;t++)void 0===o(e,t)&&(e[t]=null);return e}function O(t){const n=s(null);for(const[r,i]of e(t))void 0!==o(t,r)&&(Array.isArray(i)?n[r]=v(i):i&&"object"==typeof i&&i.constructor===Object?n[r]=O(i):n[r]=i);return n}function R(e,t){for(;null!==e;){const n=o(e,t);if(n){if(n.get)return S(n.get);if("function"==typeof n.value)return S(n.value)}e=r(e)}return function(e){return console.warn("fallback value for",e),null}}const N=i(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),_=i(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),C=i(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),x=i(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),k=i(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),D=i(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),L=i(["#text"]),P=i(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),I=i(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),U=i(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),M=i(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),F=a(/\{\{[\w\W]*|[\w\W]*\}\}/gm),z=a(/<%[\w\W]*|[\w\W]*%>/gm),j=a(/\${[\w\W]*}/gm),B=a(/^data-[\-\w.\u00B7-\uFFFF]/),H=a(/^aria-[\-\w]+$/),q=a(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),W=a(/^(?:\w+script|data):/i),G=a(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Y=a(/^html$/i);var J=Object.freeze({__proto__:null,MUSTACHE_EXPR:F,ERB_EXPR:z,TMPLIT_EXPR:j,DATA_ATTR:B,ARIA_ATTR:H,IS_ALLOWED_URI:q,IS_SCRIPT_OR_DATA:W,ATTR_WHITESPACE:G,DOCTYPE_NAME:Y});const V=function(){return"undefined"==typeof window?null:window};return function t(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:V();const r=e=>t(e);if(r.version="3.0.8",r.removed=[],!n||!n.document||9!==n.document.nodeType)return r.isSupported=!1,r;let{document:o}=n;const a=o,c=a.currentScript,{DocumentFragment:l,HTMLTemplateElement:T,Node:S,Element:v,NodeFilter:F,NamedNodeMap:z=n.NamedNodeMap||n.MozNamedAttrMap,HTMLFormElement:j,DOMParser:B,trustedTypes:H}=n,W=v.prototype,G=R(W,"cloneNode"),K=R(W,"nextSibling"),Z=R(W,"childNodes"),X=R(W,"parentNode");if("function"==typeof T){const e=o.createElement("template");e.content&&e.content.ownerDocument&&(o=e.content.ownerDocument)}let $,Q="";const{implementation:ee,createNodeIterator:te,createDocumentFragment:ne,getElementsByTagName:re}=o,{importNode:oe}=a;let ie={};r.isSupported="function"==typeof e&&"function"==typeof X&&ee&&void 0!==ee.createHTMLDocument;const{MUSTACHE_EXPR:ae,ERB_EXPR:se,TMPLIT_EXPR:ce,DATA_ATTR:le,ARIA_ATTR:ue,IS_SCRIPT_OR_DATA:de,ATTR_WHITESPACE:fe}=J;let{IS_ALLOWED_URI:pe}=J,me=null;const he=A({},[...N,..._,...C,...k,...L]);let ge=null;const ye=A({},[...P,...I,...U,...M]);let be=Object.seal(s(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Ee=null,we=null,Te=!0,Se=!0,Ae=!1,ve=!0,Oe=!1,Re=!1,Ne=!1,_e=!1,Ce=!1,xe=!1,ke=!1,De=!0,Le=!1,Pe=!0,Ie=!1,Ue={},Me=null;const Fe=A({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let ze=null;const je=A({},["audio","video","img","source","image","track"]);let Be=null;const He=A({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),qe="http://www.w3.org/1998/Math/MathML",We="http://www.w3.org/2000/svg",Ge="http://www.w3.org/1999/xhtml";let Ye=Ge,Je=!1,Ve=null;const Ke=A({},[qe,We,Ge],m);let Ze=null;const Xe=["application/xhtml+xml","text/html"];let $e=null,Qe=null;const et=o.createElement("form"),tt=function(e){return e instanceof RegExp||e instanceof Function},nt=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!Qe||Qe!==e){if(e&&"object"==typeof e||(e={}),e=O(e),Ze=-1===Xe.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,$e="application/xhtml+xml"===Ze?m:p,me="ALLOWED_TAGS"in e?A({},e.ALLOWED_TAGS,$e):he,ge="ALLOWED_ATTR"in e?A({},e.ALLOWED_ATTR,$e):ye,Ve="ALLOWED_NAMESPACES"in e?A({},e.ALLOWED_NAMESPACES,m):Ke,Be="ADD_URI_SAFE_ATTR"in e?A(O(He),e.ADD_URI_SAFE_ATTR,$e):He,ze="ADD_DATA_URI_TAGS"in e?A(O(je),e.ADD_DATA_URI_TAGS,$e):je,Me="FORBID_CONTENTS"in e?A({},e.FORBID_CONTENTS,$e):Fe,Ee="FORBID_TAGS"in e?A({},e.FORBID_TAGS,$e):{},we="FORBID_ATTR"in e?A({},e.FORBID_ATTR,$e):{},Ue="USE_PROFILES"in e&&e.USE_PROFILES,Te=!1!==e.ALLOW_ARIA_ATTR,Se=!1!==e.ALLOW_DATA_ATTR,Ae=e.ALLOW_UNKNOWN_PROTOCOLS||!1,ve=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Oe=e.SAFE_FOR_TEMPLATES||!1,Re=e.WHOLE_DOCUMENT||!1,Ce=e.RETURN_DOM||!1,xe=e.RETURN_DOM_FRAGMENT||!1,ke=e.RETURN_TRUSTED_TYPE||!1,_e=e.FORCE_BODY||!1,De=!1!==e.SANITIZE_DOM,Le=e.SANITIZE_NAMED_PROPS||!1,Pe=!1!==e.KEEP_CONTENT,Ie=e.IN_PLACE||!1,pe=e.ALLOWED_URI_REGEXP||q,Ye=e.NAMESPACE||Ge,be=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&tt(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(be.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&tt(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(be.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(be.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Oe&&(Se=!1),xe&&(Ce=!0),Ue&&(me=A({},L),ge=[],!0===Ue.html&&(A(me,N),A(ge,P)),!0===Ue.svg&&(A(me,_),A(ge,I),A(ge,M)),!0===Ue.svgFilters&&(A(me,C),A(ge,I),A(ge,M)),!0===Ue.mathMl&&(A(me,k),A(ge,U),A(ge,M))),e.ADD_TAGS&&(me===he&&(me=O(me)),A(me,e.ADD_TAGS,$e)),e.ADD_ATTR&&(ge===ye&&(ge=O(ge)),A(ge,e.ADD_ATTR,$e)),e.ADD_URI_SAFE_ATTR&&A(Be,e.ADD_URI_SAFE_ATTR,$e),e.FORBID_CONTENTS&&(Me===Fe&&(Me=O(Me)),A(Me,e.FORBID_CONTENTS,$e)),Pe&&(me["#text"]=!0),Re&&A(me,["html","head","body"]),me.table&&(A(me,["tbody"]),delete Ee.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw w('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw w('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');$=e.TRUSTED_TYPES_POLICY,Q=$.createHTML("")}else void 0===$&&($=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const r="data-tt-policy-suffix";t&&t.hasAttribute(r)&&(n=t.getAttribute(r));const o="dompurify"+(n?"#"+n:"");try{return e.createPolicy(o,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+o+" could not be created."),null}}(H,c)),null!==$&&"string"==typeof Q&&(Q=$.createHTML(""));i&&i(e),Qe=e}},rt=A({},["mi","mo","mn","ms","mtext"]),ot=A({},["foreignobject","desc","title","annotation-xml"]),it=A({},["title","style","font","a","script"]),at=A({},[..._,...C,...x]),st=A({},[...k,...D]),ct=function(e){f(r.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){e.remove()}},lt=function(e,t){try{f(r.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){f(r.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!ge[e])if(Ce||xe)try{ct(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},ut=function(e){let t=null,n=null;if(_e)e="<remove></remove>"+e;else{const t=h(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===Ze&&Ye===Ge&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const r=$?$.createHTML(e):e;if(Ye===Ge)try{t=(new B).parseFromString(r,Ze)}catch(e){}if(!t||!t.documentElement){t=ee.createDocument(Ye,"template",null);try{t.documentElement.innerHTML=Je?Q:r}catch(e){}}const i=t.body||t.documentElement;return e&&n&&i.insertBefore(o.createTextNode(n),i.childNodes[0]||null),Ye===Ge?re.call(t,Re?"html":"body")[0]:Re?t.documentElement:i},dt=function(e){return te.call(e.ownerDocument||e,e,F.SHOW_ELEMENT|F.SHOW_COMMENT|F.SHOW_TEXT,null)},ft=function(e){return"function"==typeof S&&e instanceof S},pt=function(e,t,n){ie[e]&&u(ie[e],(e=>{e.call(r,t,n,Qe)}))},mt=function(e){let t=null;if(pt("beforeSanitizeElements",e,null),(n=e)instanceof j&&("string"!=typeof n.nodeName||"string"!=typeof n.textContent||"function"!=typeof n.removeChild||!(n.attributes instanceof z)||"function"!=typeof n.removeAttribute||"function"!=typeof n.setAttribute||"string"!=typeof n.namespaceURI||"function"!=typeof n.insertBefore||"function"!=typeof n.hasChildNodes))return ct(e),!0;var n;const o=$e(e.nodeName);if(pt("uponSanitizeElement",e,{tagName:o,allowedTags:me}),e.hasChildNodes()&&!ft(e.firstElementChild)&&E(/<[/\w]/g,e.innerHTML)&&E(/<[/\w]/g,e.textContent))return ct(e),!0;if(!me[o]||Ee[o]){if(!Ee[o]&&gt(o)){if(be.tagNameCheck instanceof RegExp&&E(be.tagNameCheck,o))return!1;if(be.tagNameCheck instanceof Function&&be.tagNameCheck(o))return!1}if(Pe&&!Me[o]){const t=X(e)||e.parentNode,n=Z(e)||e.childNodes;if(n&&t)for(let r=n.length-1;r>=0;--r)t.insertBefore(G(n[r],!0),K(e))}return ct(e),!0}return e instanceof v&&!function(e){let t=X(e);t&&t.tagName||(t={namespaceURI:Ye,tagName:"template"});const n=p(e.tagName),r=p(t.tagName);return!!Ve[e.namespaceURI]&&(e.namespaceURI===We?t.namespaceURI===Ge?"svg"===n:t.namespaceURI===qe?"svg"===n&&("annotation-xml"===r||rt[r]):Boolean(at[n]):e.namespaceURI===qe?t.namespaceURI===Ge?"math"===n:t.namespaceURI===We?"math"===n&&ot[r]:Boolean(st[n]):e.namespaceURI===Ge?!(t.namespaceURI===We&&!ot[r])&&!(t.namespaceURI===qe&&!rt[r])&&!st[n]&&(it[n]||!at[n]):!("application/xhtml+xml"!==Ze||!Ve[e.namespaceURI]))}(e)?(ct(e),!0):"noscript"!==o&&"noembed"!==o&&"noframes"!==o||!E(/<\/no(script|embed|frames)/i,e.innerHTML)?(Oe&&3===e.nodeType&&(t=e.textContent,u([ae,se,ce],(e=>{t=g(t,e," ")})),e.textContent!==t&&(f(r.removed,{element:e.cloneNode()}),e.textContent=t)),pt("afterSanitizeElements",e,null),!1):(ct(e),!0)},ht=function(e,t,n){if(De&&("id"===t||"name"===t)&&(n in o||n in et))return!1;if(Se&&!we[t]&&E(le,t));else if(Te&&E(ue,t));else if(!ge[t]||we[t]){if(!(gt(e)&&(be.tagNameCheck instanceof RegExp&&E(be.tagNameCheck,e)||be.tagNameCheck instanceof Function&&be.tagNameCheck(e))&&(be.attributeNameCheck instanceof RegExp&&E(be.attributeNameCheck,t)||be.attributeNameCheck instanceof Function&&be.attributeNameCheck(t))||"is"===t&&be.allowCustomizedBuiltInElements&&(be.tagNameCheck instanceof RegExp&&E(be.tagNameCheck,n)||be.tagNameCheck instanceof Function&&be.tagNameCheck(n))))return!1}else if(Be[t]);else if(E(pe,g(n,fe,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==y(n,"data:")||!ze[e])if(Ae&&!E(de,g(n,fe,"")));else if(n)return!1;return!0},gt=function(e){return e.indexOf("-")>0},yt=function(e){pt("beforeSanitizeAttributes",e,null);const{attributes:t}=e;if(!t)return;const n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:ge};let o=t.length;for(;o--;){const i=t[o],{name:a,namespaceURI:s,value:c}=i,l=$e(a);let f="value"===a?c:b(c);if(n.attrName=l,n.attrValue=f,n.keepAttr=!0,n.forceKeepAttr=void 0,pt("uponSanitizeAttribute",e,n),f=n.attrValue,n.forceKeepAttr)continue;if(lt(a,e),!n.keepAttr)continue;if(!ve&&E(/\/>/i,f)){lt(a,e);continue}Oe&&u([ae,se,ce],(e=>{f=g(f,e," ")}));const p=$e(e.nodeName);if(ht(p,l,f)){if(!Le||"id"!==l&&"name"!==l||(lt(a,e),f="user-content-"+f),$&&"object"==typeof H&&"function"==typeof H.getAttributeType)if(s);else switch(H.getAttributeType(p,l)){case"TrustedHTML":f=$.createHTML(f);break;case"TrustedScriptURL":f=$.createScriptURL(f)}try{s?e.setAttributeNS(s,a,f):e.setAttribute(a,f),d(r.removed)}catch(e){}}}pt("afterSanitizeAttributes",e,null)},bt=function e(t){let n=null;const r=dt(t);for(pt("beforeSanitizeShadowDOM",t,null);n=r.nextNode();)pt("uponSanitizeShadowNode",n,null),mt(n)||(n.content instanceof l&&e(n.content),yt(n));pt("afterSanitizeShadowDOM",t,null)};return r.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null,o=null,i=null,s=null;if(Je=!e,Je&&(e="\x3c!--\x3e"),"string"!=typeof e&&!ft(e)){if("function"!=typeof e.toString)throw w("toString is not a function");if("string"!=typeof(e=e.toString()))throw w("dirty is not a string, aborting")}if(!r.isSupported)return e;if(Ne||nt(t),r.removed=[],"string"==typeof e&&(Ie=!1),Ie){if(e.nodeName){const t=$e(e.nodeName);if(!me[t]||Ee[t])throw w("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof S)n=ut("\x3c!----\x3e"),o=n.ownerDocument.importNode(e,!0),1===o.nodeType&&"BODY"===o.nodeName||"HTML"===o.nodeName?n=o:n.appendChild(o);else{if(!Ce&&!Oe&&!Re&&-1===e.indexOf("<"))return $&&ke?$.createHTML(e):e;if(n=ut(e),!n)return Ce?null:ke?Q:""}n&&_e&&ct(n.firstChild);const c=dt(Ie?e:n);for(;i=c.nextNode();)mt(i)||(i.content instanceof l&&bt(i.content),yt(i));if(Ie)return e;if(Ce){if(xe)for(s=ne.call(n.ownerDocument);n.firstChild;)s.appendChild(n.firstChild);else s=n;return(ge.shadowroot||ge.shadowrootmode)&&(s=oe.call(a,s,!0)),s}let d=Re?n.outerHTML:n.innerHTML;return Re&&me["!doctype"]&&n.ownerDocument&&n.ownerDocument.doctype&&n.ownerDocument.doctype.name&&E(Y,n.ownerDocument.doctype.name)&&(d="<!DOCTYPE "+n.ownerDocument.doctype.name+">\n"+d),Oe&&u([ae,se,ce],(e=>{d=g(d,e," ")})),$&&ke?$.createHTML(d):d},r.setConfig=function(){nt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Ne=!0},r.clearConfig=function(){Qe=null,Ne=!1},r.isValidAttribute=function(e,t,n){Qe||nt({});const r=$e(e),o=$e(t);return ht(r,o,n)},r.addHook=function(e,t){"function"==typeof t&&(ie[e]=ie[e]||[],f(ie[e],t))},r.removeHook=function(e){if(ie[e])return d(ie[e])},r.removeHooks=function(e){ie[e]&&(ie[e]=[])},r.removeAllHooks=function(){ie={}},r}()}()}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e={};n.r(e),n.d(e,{hasBrowserEnv:()=>ne,hasStandardBrowserEnv:()=>re,hasStandardBrowserWebWorkerEnv:()=>ie});const t=window.React;function r(e,t){return function(){return e.apply(t,arguments)}}const{toString:o}=Object.prototype,{getPrototypeOf:i}=Object,a=(s=Object.create(null),e=>{const t=o.call(e);return s[t]||(s[t]=t.slice(8,-1).toLowerCase())});var s;const c=e=>(e=e.toLowerCase(),t=>a(t)===e),l=e=>t=>typeof t===e,{isArray:u}=Array,d=l("undefined"),f=c("ArrayBuffer"),p=l("string"),m=l("function"),h=l("number"),g=e=>null!==e&&"object"==typeof e,y=e=>{if("object"!==a(e))return!1;const t=i(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)},b=c("Date"),E=c("File"),w=c("Blob"),T=c("FileList"),S=c("URLSearchParams");function A(e,t,{allOwnKeys:n=!1}={}){if(null==e)return;let r,o;if("object"!=typeof e&&(e=[e]),u(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let a;for(r=0;r<i;r++)a=o[r],t.call(null,e[a],a,e)}}function v(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,o=n.length;for(;o-- >0;)if(r=n[o],t===r.toLowerCase())return r;return null}const O="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,R=e=>!d(e)&&e!==O,N=(_="undefined"!=typeof Uint8Array&&i(Uint8Array),e=>_&&e instanceof _);var _;const C=c("HTMLFormElement"),x=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),k=c("RegExp"),D=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};A(n,((n,o)=>{let i;!1!==(i=t(n,o,e))&&(r[o]=i||n)})),Object.defineProperties(e,r)},L="abcdefghijklmnopqrstuvwxyz",P="0123456789",I={DIGIT:P,ALPHA:L,ALPHA_DIGIT:L+L.toUpperCase()+P},U=c("AsyncFunction"),M={isArray:u,isArrayBuffer:f,isBuffer:function(e){return null!==e&&!d(e)&&null!==e.constructor&&!d(e.constructor)&&m(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||m(e.append)&&("formdata"===(t=a(e))||"object"===t&&m(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&f(e.buffer),t},isString:p,isNumber:h,isBoolean:e=>!0===e||!1===e,isObject:g,isPlainObject:y,isUndefined:d,isDate:b,isFile:E,isBlob:w,isRegExp:k,isFunction:m,isStream:e=>g(e)&&m(e.pipe),isURLSearchParams:S,isTypedArray:N,isFileList:T,forEach:A,merge:function e(){const{caseless:t}=R(this)&&this||{},n={},r=(r,o)=>{const i=t&&v(n,o)||o;y(n[i])&&y(r)?n[i]=e(n[i],r):y(r)?n[i]=e({},r):u(r)?n[i]=r.slice():n[i]=r};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&A(arguments[e],r);return n},extend:(e,t,n,{allOwnKeys:o}={})=>(A(t,((t,o)=>{n&&m(t)?e[o]=r(t,n):e[o]=t}),{allOwnKeys:o}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let o,a,s;const c={};if(t=t||{},null==e)return t;do{for(o=Object.getOwnPropertyNames(e),a=o.length;a-- >0;)s=o[a],r&&!r(s,e,t)||c[s]||(t[s]=e[s],c[s]=!0);e=!1!==n&&i(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:a,kindOfTest:c,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(u(e))return e;let t=e.length;if(!h(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Symbol.iterator]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:C,hasOwnProperty:x,hasOwnProp:x,reduceDescriptors:D,freezeMethods:e=>{D(e,((t,n)=>{if(m(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];m(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return u(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>(e=+e,Number.isFinite(e)?e:t),findKey:v,global:O,isContextDefined:R,ALPHABET:I,generateString:(e=16,t=I.ALPHA_DIGIT)=>{let n="";const{length:r}=t;for(;e--;)n+=t[Math.random()*r|0];return n},isSpecCompliantForm:function(e){return!!(e&&m(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(g(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const o=u(e)?[]:{};return A(e,((e,t)=>{const i=n(e,r+1);!d(i)&&(o[t]=i)})),t[r]=void 0,o}}return e};return n(e,0)},isAsyncFn:U,isThenable:e=>e&&(g(e)||m(e))&&m(e.then)&&m(e.catch)};function F(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o)}M.inherits(F,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:M.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const z=F.prototype,j={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{j[e]={value:e}})),Object.defineProperties(F,j),Object.defineProperty(z,"isAxiosError",{value:!0}),F.from=(e,t,n,r,o,i)=>{const a=Object.create(z);return M.toFlatObject(e,a,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),F.call(a,e.message,t,n,r,o),a.cause=e,a.name=e.name,i&&Object.assign(a,i),a};const B=F;function H(e){return M.isPlainObject(e)||M.isArray(e)}function q(e){return M.endsWith(e,"[]")?e.slice(0,-2):e}function W(e,t,n){return e?e.concat(t).map((function(e,t){return e=q(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const G=M.toFlatObject(M,{},null,(function(e){return/^is[A-Z]/.test(e)})),Y=function(e,t,n){if(!M.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=M.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!M.isUndefined(t[e])}))).metaTokens,o=n.visitor||l,i=n.dots,a=n.indexes,s=(n.Blob||"undefined"!=typeof Blob&&Blob)&&M.isSpecCompliantForm(t);if(!M.isFunction(o))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(M.isDate(e))return e.toISOString();if(!s&&M.isBlob(e))throw new B("Blob is not supported. Use a Buffer instead.");return M.isArrayBuffer(e)||M.isTypedArray(e)?s&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function l(e,n,o){let s=e;if(e&&!o&&"object"==typeof e)if(M.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(M.isArray(e)&&function(e){return M.isArray(e)&&!e.some(H)}(e)||(M.isFileList(e)||M.endsWith(n,"[]"))&&(s=M.toArray(e)))return n=q(n),s.forEach((function(e,r){!M.isUndefined(e)&&null!==e&&t.append(!0===a?W([n],r,i):null===a?n:n+"[]",c(e))})),!1;return!!H(e)||(t.append(W(o,n,i),c(e)),!1)}const u=[],d=Object.assign(G,{defaultVisitor:l,convertValue:c,isVisitable:H});if(!M.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!M.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),M.forEach(n,(function(n,i){!0===(!(M.isUndefined(n)||null===n)&&o.call(t,n,M.isString(i)?i.trim():i,r,d))&&e(n,r?r.concat(i):[i])})),u.pop()}}(e),t};function J(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function V(e,t){this._pairs=[],e&&Y(e,this,t)}const K=V.prototype;K.append=function(e,t){this._pairs.push([e,t])},K.toString=function(e){const t=e?function(t){return e.call(this,t,J)}:J;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const Z=V;function X(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function $(e,t,n){if(!t)return e;const r=n&&n.encode||X,o=n&&n.serialize;let i;if(i=o?o(t,n):M.isURLSearchParams(t)?t.toString():new Z(t,n).toString(r),i){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}const Q=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){M.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},ee={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},te={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:Z,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},ne="undefined"!=typeof window&&"undefined"!=typeof document,re=(oe="undefined"!=typeof navigator&&navigator.product,ne&&["ReactNative","NativeScript","NS"].indexOf(oe)<0);var oe;const ie="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,ae={...e,...te},se=function(e){function t(e,n,r,o){let i=e[o++];if("__proto__"===i)return!0;const a=Number.isFinite(+i),s=o>=e.length;return i=!i&&M.isArray(r)?r.length:i,s?(M.hasOwnProp(r,i)?r[i]=[r[i],n]:r[i]=n,!a):(r[i]&&M.isObject(r[i])||(r[i]=[]),t(e,n,r[i],o)&&M.isArray(r[i])&&(r[i]=function(e){const t={},n=Object.keys(e);let r;const o=n.length;let i;for(r=0;r<o;r++)i=n[r],t[i]=e[i];return t}(r[i])),!a)}if(M.isFormData(e)&&M.isFunction(e.entries)){const n={};return M.forEachEntry(e,((e,r)=>{t(function(e){return M.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null},ce={transitional:ee,adapter:["xhr","http"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,o=M.isObject(e);if(o&&M.isHTMLForm(e)&&(e=new FormData(e)),M.isFormData(e))return r?JSON.stringify(se(e)):e;if(M.isArrayBuffer(e)||M.isBuffer(e)||M.isStream(e)||M.isFile(e)||M.isBlob(e))return e;if(M.isArrayBufferView(e))return e.buffer;if(M.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let i;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return Y(e,new ae.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return ae.isNode&&M.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((i=M.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return Y(i?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||r?(t.setContentType("application/json",!1),function(e,t,n){if(M.isString(e))try{return(0,JSON.parse)(e),M.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||ce.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(e&&M.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(e){if(n){if("SyntaxError"===e.name)throw B.from(e,B.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ae.classes.FormData,Blob:ae.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};M.forEach(["delete","get","head","post","put","patch"],(e=>{ce.headers[e]={}}));const le=ce,ue=M.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),de=Symbol("internals");function fe(e){return e&&String(e).trim().toLowerCase()}function pe(e){return!1===e||null==e?e:M.isArray(e)?e.map(pe):String(e)}function me(e,t,n,r,o){return M.isFunction(r)?r.call(this,t,n):(o&&(t=n),M.isString(t)?M.isString(r)?-1!==t.indexOf(r):M.isRegExp(r)?r.test(t):void 0:void 0)}class he{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function o(e,t,n){const o=fe(t);if(!o)throw new Error("header name must be a non-empty string");const i=M.findKey(r,o);(!i||void 0===r[i]||!0===n||void 0===n&&!1!==r[i])&&(r[i||t]=pe(e))}const i=(e,t)=>M.forEach(e,((e,n)=>o(e,n,t)));return M.isPlainObject(e)||e instanceof this.constructor?i(e,t):M.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim())?i((e=>{const t={};let n,r,o;return e&&e.split("\n").forEach((function(e){o=e.indexOf(":"),n=e.substring(0,o).trim().toLowerCase(),r=e.substring(o+1).trim(),!n||t[n]&&ue[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t):null!=e&&o(t,e,n),this}get(e,t){if(e=fe(e)){const n=M.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(M.isFunction(t))return t.call(this,e,n);if(M.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=fe(e)){const n=M.findKey(this,e);return!(!n||void 0===this[n]||t&&!me(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function o(e){if(e=fe(e)){const o=M.findKey(n,e);!o||t&&!me(0,n[o],o,t)||(delete n[o],r=!0)}}return M.isArray(e)?e.forEach(o):o(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const o=t[n];e&&!me(0,this[o],o,e,!0)||(delete this[o],r=!0)}return r}normalize(e){const t=this,n={};return M.forEach(this,((r,o)=>{const i=M.findKey(n,o);if(i)return t[i]=pe(r),void delete t[o];const a=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(o):String(o).trim();a!==o&&delete t[o],t[a]=pe(r),n[a]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return M.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&M.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach((e=>n.set(e))),n}static accessor(e){const t=(this[de]=this[de]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=fe(e);t[r]||(function(e,t){const n=M.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,o){return this[r].call(this,t,e,n,o)},configurable:!0})}))}(n,e),t[r]=!0)}return M.isArray(e)?e.forEach(r):r(e),this}}he.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),M.reduceDescriptors(he.prototype,(({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}})),M.freezeMethods(he);const ge=he;function ye(e,t){const n=this||le,r=t||n,o=ge.from(r.headers);let i=r.data;return M.forEach(e,(function(e){i=e.call(n,i,o.normalize(),t?t.status:void 0)})),o.normalize(),i}function be(e){return!(!e||!e.__CANCEL__)}function Ee(e,t,n){B.call(this,null==e?"canceled":e,B.ERR_CANCELED,t,n),this.name="CanceledError"}M.inherits(Ee,B,{__CANCEL__:!0});const we=Ee,Te=ae.hasStandardBrowserEnv?{write(e,t,n,r,o,i){const a=[e+"="+encodeURIComponent(t)];M.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),M.isString(r)&&a.push("path="+r),M.isString(o)&&a.push("domain="+o),!0===i&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Se(e,t){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const Ae=ae.hasStandardBrowserEnv?function(){const e=/(msie|trident)/i.test(navigator.userAgent),t=document.createElement("a");let n;function r(n){let r=n;return e&&(t.setAttribute("href",r),r=t.href),t.setAttribute("href",r),{href:t.href,protocol:t.protocol?t.protocol.replace(/:$/,""):"",host:t.host,search:t.search?t.search.replace(/^\?/,""):"",hash:t.hash?t.hash.replace(/^#/,""):"",hostname:t.hostname,port:t.port,pathname:"/"===t.pathname.charAt(0)?t.pathname:"/"+t.pathname}}return n=r(window.location.href),function(e){const t=M.isString(e)?r(e):e;return t.protocol===n.protocol&&t.host===n.host}}():function(){return!0};function ve(e,t){let n=0;const r=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o,i=0,a=0;return t=void 0!==t?t:1e3,function(s){const c=Date.now(),l=r[a];o||(o=c),n[i]=s,r[i]=c;let u=a,d=0;for(;u!==i;)d+=n[u++],u%=e;if(i=(i+1)%e,i===a&&(a=(a+1)%e),c-o<t)return;const f=l&&c-l;return f?Math.round(1e3*d/f):void 0}}(50,250);return o=>{const i=o.loaded,a=o.lengthComputable?o.total:void 0,s=i-n,c=r(s);n=i;const l={loaded:i,total:a,progress:a?i/a:void 0,bytes:s,rate:c||void 0,estimated:c&&a&&i<=a?(a-i)/c:void 0,event:o};l[t?"download":"upload"]=!0,e(l)}}const Oe={http:null,xhr:"undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){let r=e.data;const o=ge.from(e.headers).normalize();let i,a,{responseType:s,withXSRFToken:c}=e;function l(){e.cancelToken&&e.cancelToken.unsubscribe(i),e.signal&&e.signal.removeEventListener("abort",i)}if(M.isFormData(r))if(ae.hasStandardBrowserEnv||ae.hasStandardBrowserWebWorkerEnv)o.setContentType(!1);else if(!1!==(a=o.getContentType())){const[e,...t]=a?a.split(";").map((e=>e.trim())).filter(Boolean):[];o.setContentType([e||"multipart/form-data",...t].join("; "))}let u=new XMLHttpRequest;if(e.auth){const t=e.auth.username||"",n=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";o.set("Authorization","Basic "+btoa(t+":"+n))}const d=Se(e.baseURL,e.url);function f(){if(!u)return;const r=ge.from("getAllResponseHeaders"in u&&u.getAllResponseHeaders());!function(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new B("Request failed with status code "+n.status,[B.ERR_BAD_REQUEST,B.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}((function(e){t(e),l()}),(function(e){n(e),l()}),{data:s&&"text"!==s&&"json"!==s?u.response:u.responseText,status:u.status,statusText:u.statusText,headers:r,config:e,request:u}),u=null}if(u.open(e.method.toUpperCase(),$(d,e.params,e.paramsSerializer),!0),u.timeout=e.timeout,"onloadend"in u?u.onloadend=f:u.onreadystatechange=function(){u&&4===u.readyState&&(0!==u.status||u.responseURL&&0===u.responseURL.indexOf("file:"))&&setTimeout(f)},u.onabort=function(){u&&(n(new B("Request aborted",B.ECONNABORTED,e,u)),u=null)},u.onerror=function(){n(new B("Network Error",B.ERR_NETWORK,e,u)),u=null},u.ontimeout=function(){let t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded";const r=e.transitional||ee;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(new B(t,r.clarifyTimeoutError?B.ETIMEDOUT:B.ECONNABORTED,e,u)),u=null},ae.hasStandardBrowserEnv&&(c&&M.isFunction(c)&&(c=c(e)),c||!1!==c&&Ae(d))){const t=e.xsrfHeaderName&&e.xsrfCookieName&&Te.read(e.xsrfCookieName);t&&o.set(e.xsrfHeaderName,t)}void 0===r&&o.setContentType(null),"setRequestHeader"in u&&M.forEach(o.toJSON(),(function(e,t){u.setRequestHeader(t,e)})),M.isUndefined(e.withCredentials)||(u.withCredentials=!!e.withCredentials),s&&"json"!==s&&(u.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&u.addEventListener("progress",ve(e.onDownloadProgress,!0)),"function"==typeof e.onUploadProgress&&u.upload&&u.upload.addEventListener("progress",ve(e.onUploadProgress)),(e.cancelToken||e.signal)&&(i=t=>{u&&(n(!t||t.type?new we(null,e,u):t),u.abort(),u=null)},e.cancelToken&&e.cancelToken.subscribe(i),e.signal&&(e.signal.aborted?i():e.signal.addEventListener("abort",i)));const p=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(d);p&&-1===ae.protocols.indexOf(p)?n(new B("Unsupported protocol "+p+":",B.ERR_BAD_REQUEST,e)):u.send(r||null)}))}};M.forEach(Oe,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}}));const Re=e=>`- ${e}`,Ne=e=>M.isFunction(e)||null===e||!1===e,_e=e=>{e=M.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let i=0;i<t;i++){let t;if(n=e[i],r=n,!Ne(n)&&(r=Oe[(t=String(n)).toLowerCase()],void 0===r))throw new B(`Unknown adapter '${t}'`);if(r)break;o[t||"#"+i]=r}if(!r){const e=Object.entries(o).map((([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build")));let n=t?e.length>1?"since :\n"+e.map(Re).join("\n"):" "+Re(e[0]):"as no adapter specified";throw new B("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function Ce(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new we(null,e)}function xe(e){return Ce(e),e.headers=ge.from(e.headers),e.data=ye.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),_e(e.adapter||le.adapter)(e).then((function(t){return Ce(e),t.data=ye.call(e,e.transformResponse,t),t.headers=ge.from(t.headers),t}),(function(t){return be(t)||(Ce(e),t&&t.response&&(t.response.data=ye.call(e,e.transformResponse,t.response),t.response.headers=ge.from(t.response.headers))),Promise.reject(t)}))}const ke=e=>e instanceof ge?e.toJSON():e;function De(e,t){t=t||{};const n={};function r(e,t,n){return M.isPlainObject(e)&&M.isPlainObject(t)?M.merge.call({caseless:n},e,t):M.isPlainObject(t)?M.merge({},t):M.isArray(t)?t.slice():t}function o(e,t,n){return M.isUndefined(t)?M.isUndefined(e)?void 0:r(void 0,e,n):r(e,t,n)}function i(e,t){if(!M.isUndefined(t))return r(void 0,t)}function a(e,t){return M.isUndefined(t)?M.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function s(n,o,i){return i in t?r(n,o):i in e?r(void 0,n):void 0}const c={url:i,method:i,data:i,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:s,headers:(e,t)=>o(ke(e),ke(t),!0)};return M.forEach(Object.keys(Object.assign({},e,t)),(function(r){const i=c[r]||o,a=i(e[r],t[r],r);M.isUndefined(a)&&i!==s||(n[r]=a)})),n}const Le={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{Le[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const Pe={};Le.transitional=function(e,t,n){function r(e,t){return"[Axios v1.6.7] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,o,i)=>{if(!1===e)throw new B(r(o," has been removed"+(t?" in "+t:"")),B.ERR_DEPRECATED);return t&&!Pe[o]&&(Pe[o]=!0,console.warn(r(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,o,i)}};const Ie={assertOptions:function(e,t,n){if("object"!=typeof e)throw new B("options must be an object",B.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const i=r[o],a=t[i];if(a){const t=e[i],n=void 0===t||a(t,i,e);if(!0!==n)throw new B("option "+i+" must be "+n,B.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new B("Unknown option "+i,B.ERR_BAD_OPTION)}},validators:Le},Ue=Ie.validators;class Me{constructor(e){this.defaults=e,this.interceptors={request:new Q,response:new Q}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t;Error.captureStackTrace?Error.captureStackTrace(t={}):t=new Error;const n=t.stack?t.stack.replace(/^.+\n/,""):"";e.stack?n&&!String(e.stack).endsWith(n.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+n):e.stack=n}throw e}}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=De(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:o}=t;void 0!==n&&Ie.assertOptions(n,{silentJSONParsing:Ue.transitional(Ue.boolean),forcedJSONParsing:Ue.transitional(Ue.boolean),clarifyTimeoutError:Ue.transitional(Ue.boolean)},!1),null!=r&&(M.isFunction(r)?t.paramsSerializer={serialize:r}:Ie.assertOptions(r,{encode:Ue.function,serialize:Ue.function},!0)),t.method=(t.method||this.defaults.method||"get").toLowerCase();let i=o&&M.merge(o.common,o[t.method]);o&&M.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete o[e]})),t.headers=ge.concat(i,o);const a=[];let s=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(s=s&&e.synchronous,a.unshift(e.fulfilled,e.rejected))}));const c=[];let l;this.interceptors.response.forEach((function(e){c.push(e.fulfilled,e.rejected)}));let u,d=0;if(!s){const e=[xe.bind(this),void 0];for(e.unshift.apply(e,a),e.push.apply(e,c),u=e.length,l=Promise.resolve(t);d<u;)l=l.then(e[d++],e[d++]);return l}u=a.length;let f=t;for(d=0;d<u;){const e=a[d++],t=a[d++];try{f=e(f)}catch(e){t.call(this,e);break}}try{l=xe.call(this,f)}catch(e){return Promise.reject(e)}for(d=0,u=c.length;d<u;)l=l.then(c[d++],c[d++]);return l}getUri(e){return $(Se((e=De(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}}M.forEach(["delete","get","head","options"],(function(e){Me.prototype[e]=function(t,n){return this.request(De(n||{},{method:e,url:t,data:(n||{}).data}))}})),M.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,o){return this.request(De(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}Me.prototype[e]=t(),Me.prototype[e+"Form"]=t(!0)}));const Fe=Me;class ze{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,o){n.reason||(n.reason=new we(e,r,o),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}static source(){let e;return{token:new ze((function(t){e=t})),cancel:e}}}const je=ze,Be={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Be).forEach((([e,t])=>{Be[t]=e}));const He=Be,qe=function e(t){const n=new Fe(t),o=r(Fe.prototype.request,n);return M.extend(o,Fe.prototype,n,{allOwnKeys:!0}),M.extend(o,n,null,{allOwnKeys:!0}),o.create=function(n){return e(De(t,n))},o}(le);qe.Axios=Fe,qe.CanceledError=we,qe.CancelToken=je,qe.isCancel=be,qe.VERSION="1.6.7",qe.toFormData=Y,qe.AxiosError=B,qe.Cancel=qe.CanceledError,qe.all=function(e){return Promise.all(e)},qe.spread=function(e){return function(t){return e.apply(null,t)}},qe.isAxiosError=function(e){return M.isObject(e)&&!0===e.isAxiosError},qe.mergeConfig=De,qe.AxiosHeaders=ge,qe.formToJSON=e=>se(M.isHTMLForm(e)?new FormData(e):e),qe.getAdapter=_e,qe.HttpStatusCode=He,qe.default=qe;const We=qe,Ge=window.wp.element;var Ye=n(856),Je=n.n(Ye);const{__}=wp.i18n,{registerBlockType:Ve}=wp.blocks,{InspectorControls:Ke,BlockControls:Ze,useBlockProps:Xe}=wp.blockEditor,{PanelBody:$e,PanelRow:Qe,SelectControl:et,TextControl:tt,TextareaControl:nt,ToolbarButton:rt,ToolbarGroup:ot,Icon:it}=wp.components,at=()=>(0,t.createElement)(it,{icon:(0,t.createElement)("svg",{id:"uuid-098657ec-4091-4c5d-b2f8-761b37dc9655",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 22 19.26"},(0,t.createElement)("path",{className:"uuid-fb9dd603-42c0-4281-8513-899f01887edf",d:"m13.54,4.47c-.06-.06-.15-.06-.21,0l-4.9,4.9.21.21,4.9-4.9c.06-.06.06-.16,0-.21h0Zm-4.48,3.19c-.06-.06-.15-.06-.21,0l-1.06,1.06.21.21,1.06-1.06c.06-.06.06-.15,0-.21ZM15.14,1.21c.46,0,.9.19,1.22.53.61.66.56,1.7-.08,2.34l-2.89,2.89c-.06.06-.06.15,0,.21.06.06.15.06.21,0l2.89-2.89c.76-.76.8-2.05.04-2.81-.38-.38-.88-.57-1.39-.57s-1,.19-1.39.57l-6.6,6.6.21.21L13.96,1.7c.31-.32.73-.49,1.17-.49h0Zm-5.64,3.46c-.06-.06-.15-.06-.21,0l-2.77,2.78.21.21,2.78-2.78c.06-.06.06-.15,0-.21h0Zm.74-.52l3.09-3.09c.48-.48,1.13-.75,1.81-.75.52,0,1.01.16,1.43.44.06.04.14.04.19-.02.07-.07.06-.18-.02-.23-.48-.33-1.04-.49-1.6-.49-.73,0-1.47.28-2.03.84l-3.09,3.1c-.06.06-.06.15,0,.21.06.06.16.06.21,0h0Zm2.63,3.56c-.06-.06-.15-.06-.21,0l-2.3,2.3c-.06.06-.06.15,0,.21h0c.06.06.15.06.21,0l2.3-2.3c.06-.06.06-.15,0-.21h0Zm3.46-2.39l-5.97,5.97.21.21,5.97-5.97c.06-.06.06-.15,0-.21-.06-.06-.16-.06-.22,0Zm1.2-4.03c-.05-.08-.17-.09-.23-.03-.05.05-.06.13-.02.19.28.42.43.91.43,1.42,0,.64-.23,1.24-.65,1.71-.06.06-.06.15,0,.21.06.06.16.06.22,0,.88-.98.97-2.43.25-3.5h0Zm-3.24,2.66l.93-.93c.06-.06.06-.15,0-.21-.06-.06-.15-.06-.21,0l-.93.93c-.06.06-.06.15,0,.21.06.06.16.06.21,0h0Zm1.59-1.84c-.2-.19-.47-.29-.73-.29s-.54.1-.75.31l-4.8,4.81c-.06.06-.06.15,0,.21.06.06.15.06.21,0l4.78-4.79c.12-.12.28-.21.46-.24.24-.03.47.05.63.21.14.14.22.33.22.53s-.08.39-.22.53l-6.6,6.6.21.21,6.57-6.57c.42-.42.44-1.13.01-1.54h0Z"}),(0,t.createElement)("g",null,(0,t.createElement)("rect",{className:"uuid-fb9dd603-42c0-4281-8513-899f01887edf",x:"7.81",y:"5.93",width:"2.91",height:"5.64",transform:"translate(-3.47 9.11) rotate(-45)"}),(0,t.createElement)("path",{className:"uuid-fb9dd603-42c0-4281-8513-899f01887edf",d:"m10.7,11.35l-4.62-4.62h0l-1.28-1.28c-.55-.55-1.27-.83-1.99-.83s-1.44.27-1.99.83c-1.1,1.1-1.1,2.89,0,3.99l3.54,3.54h0l1.6,1.6c.06.06.15.06.21,0,0,0,0,0,0,0s0,0,0,0l.36-.36.03.88s0,.04.01.06c0,.02.02.04.03.05.06.06.15.06.21,0h0s0-.01,0-.01l3.82-3.82h0s.04-.04.04-.04Z"}),(0,t.createElement)("path",{className:"uuid-fb9dd603-42c0-4281-8513-899f01887edf",d:"m11.09,10.9l4.62-4.62h0l1.28-1.28c.55-.55.83-1.27.83-1.99s-.27-1.44-.83-1.99c-1.1-1.1-2.89-1.1-3.99,0l-3.54,3.54h0l-1.6,1.6c-.06.06-.06.15,0,.21,0,0,0,0,0,0s0,0,0,0l.36.36-.88.03s-.04,0-.06.01c-.02,0-.04.02-.05.03-.06.06-.06.15,0,.21h0s0,.01,0,.01l3.82,3.82h0s.04.04.04.04Z"})))});Ve("complianz/document",{title:__("Legal document - Complianz","complianz-gdpr"),icon:at,category:"widgets",example:{attributes:{preview:!0}},keywords:[__("Privacy Statement","complianz-gdpr"),__("Cookie Policy","complianz-gdpr"),__("Disclaimer","complianz-gdpr")],attributes:{documentSyncStatus:{type:"string",default:"sync"},customDocument:{type:"string",default:""},hasDocuments:{type:"string",default:"false"},content:{type:"string",source:"children",selector:"p"},selectedDocument:{type:"string",default:""},documents:{type:"array"},document:{type:"array"},preview:{type:"boolean",default:!1}},edit:({className:e,isSelected:n,attributes:r,setAttributes:o})=>{const[i,a]=(0,Ge.useState)([]),[s,c]=(0,Ge.useState)(!1),[l,u]=(0,Ge.useState)(r.selectedDocument),[d,f]=(0,Ge.useState)(r.documentSyncStatus),[p,m]=(0,Ge.useState)("");(0,Ge.useEffect)((()=>{We.get(complianz.site_url+"complianz/v1/documents").then((e=>{let t=e.data;a(t),c(!0);let n=!1;t&&0!==l&&(n=t.find((e=>e.id===l)),0===t.length&&o({hasDocuments:!1}));let i="";n&&(i=n.content),r.customDocument&&r.customDocument.length>0&&(i=r.customDocument),m(i)}))}),[]);const h=e=>{u(e),o({selectedDocument:e})},g=e=>{f(e),o({documentSyncStatus:e});const t=i.find((e=>e.id===l));m(t.content),o({customDocument:t.content})};let y=[{value:0,label:__("Select a document","complianz-gdpr")}],b=__("Loading...","complianz-gdpr"),E=[{value:"sync",label:__("Synchronize document with Complianz","complianz-gdpr")},{value:"unlink",label:__("Edit document and stop synchronization","complianz-gdpr")}];if(r.hasDocuments||(b=__("No documents found. Please finish the Complianz Privacy Suite wizard to generate documents","complianz-gdpr")),r.preview)return(0,t.createElement)("img",{alt:"preview",src:complianz.cmplz_preview});if(s&&(b=__(n?"Select a document type from the dropdownlist":"Click this block to show the options","complianz-gdpr"),i.forEach((e=>{y.push({value:e.id,label:e.title})}))),0!==r.selectedDocument&&s&&r.selectedDocument.length>0){const e=i.find((e=>e.id===r.selectedDocument));e&&(b=e.content)}if("sync"===d)return[!!n&&(0,t.createElement)(Ke,{key:"inspector-document"},(0,t.createElement)($e,{title:__("Document settings","complianz-gdpr"),initialOpen:!0},(0,t.createElement)(Qe,{key:"1"},(0,t.createElement)(et,{onChange:e=>h(e),value:l,label:__("Select a document","complianz-gdpr"),options:y})),(0,t.createElement)(Qe,{key:"2"},(0,t.createElement)(et,{onChange:e=>g(e),value:d,label:__("Document sync status","complianz-gdpr"),options:E})))),(0,t.createElement)("div",{key:r.selectedDocument,className:e,dangerouslySetInnerHTML:{__html:Je().sanitize(b)}})];{s||__("Loading...","complianz-gdpr");let i=e+" cmplz-unlinked-mode";return[!!n&&(0,t.createElement)(Ke,{key:"inspector-document-settings"},(0,t.createElement)($e,{title:__("Document settings","complianz-gdpr"),initialOpen:!0},(0,t.createElement)(Qe,null,(0,t.createElement)(et,{onChange:e=>h(e),value:r.selectedDocument,label:__("Select a document","complianz-gdpr"),options:y})),(0,t.createElement)(Qe,null,(0,t.createElement)(et,{onChange:e=>g(e),value:d,label:__("Document sync status","complianz-gdpr"),options:E})))),(0,t.createElement)("div",{contentEditable:!0,onInput:e=>{return t=e.currentTarget.innerHTML,void o({customDocument:t});var t},dangerouslySetInnerHTML:{__html:Je().sanitize(p)},className:i})]}},save:function(){return null}}),Ve("complianz/consent-area",{title:__("Consent Area Block"),icon:at,category:"widgets",example:{attributes:{preview:!0}},attributes:{category:{type:"string",default:"marketing"},service:{type:"service",default:"general"},blockId:{type:"string",default:""},postId:{type:"integer",default:-1},placeholderContent:{type:"string",default:""},consentedContent:{type:"string",default:""}},edit:e=>{const{getCurrentPostId:n}=wp.data.select("core/editor"),r=n(),{attributes:o,setAttributes:i,isSelected:a,className:s}=e,[c,l]=(0,Ge.useState)("consented"),[u,d]=(0,Ge.useState)(!1);(0,Ge.useEffect)((()=>{if(i({postId:r,category:o.category,service:o.service}),""===o.blockId){let e=(Math.random()+1).toString(36).substring(7);i({blockId:e})}}),[o]);const f=Xe();let p=!complianz.user_can_unfiltered_html;return[!!a&&(0,t.createElement)(Ke,{key:"inspector-document-settings"},(0,t.createElement)($e,{title:__("Document settings","complianz-gdpr"),initialOpen:!0},(0,t.createElement)(Qe,{key:"1"},(0,t.createElement)(et,{disabled:p,label:__("Category","complianz-gdpr"),value:o.category,options:[{label:__("Preferences","complianz-gdpr"),value:"preferences"},{label:__("Statistics","complianz-gdpr"),value:"statistics"},{label:__("Marketing","complianz-gdpr"),value:"marketing"}],onChange:e=>i({category:e})})),(0,t.createElement)(Qe,{key:"2"},(0,t.createElement)(tt,{disabled:p,label:__("Service","complianz-gdpr"),value:o.service,onChange:e=>i({service:e})})),(0,t.createElement)(Qe,{key:"3"},(0,t.createElement)(et,{disabled:p,label:__("View"),value:c,options:[{label:__("Placeholder","complianz-gdpr"),value:"placeholder"},{label:__("Default","complianz-gdpr"),value:"consented"}],onChange:e=>(e=>{l(e)})(e)})))),(0,t.createElement)("div",{...f,key:"cmplz-consent-area"},(0,t.createElement)(Ze,null,(0,t.createElement)(ot,null,(0,t.createElement)(rt,{className:"components-tab-button",isPressed:!u,onClick:()=>d(!1)},"HTML"),(0,t.createElement)(rt,{className:"components-tab-button",isPressed:u,onClick:()=>d(!0)},__("Preview")))),p&&(0,t.createElement)("div",null,__("You do not have sufficient permissions to edit this block.","complianz-gdpr")),!u&&(0,t.createElement)(t.Fragment,null,"placeholder"===c&&(0,t.createElement)(nt,{key:"1",disabled:p,placeholder:__("You can add custom HTML to create your own placeholder. This placeholder is visible before consent.","complianz-gdpr"),className:s,value:o.placeholderContent,onChange:e=>i({placeholderContent:e})}),"consented"===c&&(0,t.createElement)(nt,{key:"2",disabled:p,placeholder:__("You can add custom HTML that requires consent. In the right-side bar you will find the options for this custom block. For instructions, please go to complianz.io/gutenberg for more information.","complianz-gdpr"),className:s,value:o.consentedContent,onChange:e=>i({consentedContent:e})})),!!u&&(0,t.createElement)(t.Fragment,null,"placeholder"===c&&(0,t.createElement)("div",null,(0,t.createElement)("div",{dangerouslySetInnerHTML:{__html:Je().sanitize(o.placeholderContent)}})),"consented"===c&&(0,t.createElement)("div",null,(0,t.createElement)("div",{dangerouslySetInnerHTML:{__html:Je().sanitize(o.consentedContent)}}))))]},save:function(){return null}})})()})();