{"content": [{"id": "4e7213f0", "settings": [], "elements": [{"id": "28bba604", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "70ef797a", "settings": {"alert_type": "danger", "alert_title": "Remove before publishing ", "alert_description": "This default Legal Hub is created to serve as an entry to your legal documents, managing consent and rights under certain privacy laws. To learn more about this template, styling and other possibilities. <a href=\"https://complianz.io/creating-the-legal-hub/\" target=\"_blank\">Read more in this article</a>", "show_dismiss": "hide"}, "elements": [], "isInner": false, "widgetType": "alert", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "8b75a76", "settings": {"margin": {"unit": "px", "top": "10", "right": 0, "bottom": "10", "left": 0, "isLinked": true}}, "elements": [{"id": "58deb759", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "699c067d", "settings": {"structure": "20"}, "elements": [{"id": "2fef074e", "settings": {"_column_size": 50, "_inline_size": null, "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "-10", "isLinked": false}}, "elements": [{"id": "6ef8a4d8", "settings": {"title": "Legal Hub", "align": "left", "title_color": "#000000", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 34, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "75d12bb1", "settings": {"_column_size": 50, "_inline_size": null}, "elements": [], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "37da2f50", "settings": {"editor": "<p>This is our Legal Hub. Have a look at our legal documents, manage your consent and exercise your privacy rights. For any specific questions, please note the communication possibilities as stated in the specific document.</p>", "align": "left", "text_color": "#000000", "typography_typography": "custom", "typography_line_height": {"unit": "em", "size": 1.5, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "ac4f120", "settings": {"structure": "30", "margin": {"unit": "px", "top": "10", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "background_background": "classic", "padding": {"unit": "px", "top": "20", "right": "0", "bottom": "0", "left": "0", "isLinked": false}}, "elements": [{"id": "291dc0aa", "settings": {"_column_size": 33, "_inline_size": null, "background_background": "classic", "background_color": "#FFFFFF", "border_radius": {"unit": "px", "top": "12", "right": "12", "bottom": "12", "left": "12", "isLinked": true}, "box_shadow_box_shadow_type": "yes", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 30, "spread": -10, "color": "rgba(0, 0, 0, 0.25)"}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "20", "right": "20", "bottom": "20", "left": "20", "isLinked": true}, "border_border": "solid", "border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "border_color": "#F2F2F2", "heading_color": "#020101", "color_text": "#333333", "color_link": "#1E73BE"}, "elements": [{"id": "45b9ba44", "settings": {"title": "<PERSON><PERSON>", "header_size": "h3", "align": "left", "title_color": "#000000", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "3ed15ca5", "settings": {"align": "left", "text_color": "#000000", "typography_typography": "custom", "editor": "<p>Read more about the cookies we use and manage your consent.</p>"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "574eaf25", "settings": {"align": "left", "text_color": "#1E73BE", "typography_typography": "custom", "editor": "<p><a href=\"/cookie-policy/?cmplz_region_redirect=true\">Read more</a></p>", "typography_text_decoration": "underline"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "6e4a6621", "settings": {"_column_size": 33, "_inline_size": null, "border_radius": {"unit": "px", "top": "12", "right": "12", "bottom": "12", "left": "12", "isLinked": true}, "box_shadow_box_shadow_type": "yes", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 30, "spread": -10, "color": "rgba(0, 0, 0, 0.25)"}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "20", "right": "20", "bottom": "20", "left": "20", "isLinked": true}, "background_background": "classic", "background_color": "#FFFFFF", "border_border": "solid", "border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "border_color": "#F2F2F2", "heading_color": "#020101", "color_text": "#333333", "color_link": "#1E73BE"}, "elements": [{"id": "7fe22671", "settings": {"title": "Privacy Statement", "header_size": "h3", "align": "left", "title_color": "#000000", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "6e9da8a9", "settings": {"align": "left", "text_color": "#000000", "typography_typography": "custom", "editor": "<p>Read more about how we process, store and share your data.</p>"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "767e3bf7", "settings": {"align": "left", "text_color": "#1E73BE", "typography_typography": "custom", "editor": "<p><a href=\"/privacy-statement/?cmplz_region_redirect=true\">Read more</a></p>", "typography_text_decoration": "underline"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "4d7119b0", "settings": {"_column_size": 33, "_inline_size": null, "border_radius": {"unit": "px", "top": "12", "right": "12", "bottom": "12", "left": "12", "isLinked": true}, "box_shadow_box_shadow_type": "yes", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 30, "spread": -10, "color": "rgba(0, 0, 0, 0.25)"}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "20", "right": "20", "bottom": "20", "left": "20", "isLinked": true}, "background_background": "classic", "background_color": "#FFFFFF", "border_border": "solid", "border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "border_color": "#F2F2F2", "heading_color": "#020101", "color_text": "#333333", "color_link": "#1E73BE"}, "elements": [{"id": "191c1a30", "settings": {"title": "Opt-out Preferences", "header_size": "h3", "align": "left", "title_color": "#000000", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "5560acb7", "settings": {"align": "left", "text_color": "#000000", "typography_typography": "custom", "editor": "<p>Specifically for the USA, and California. Read about the cookies we use and object to the use of your data.</p>"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "59b29a7f", "settings": {"align": "left", "text_color": "#1E73BE", "typography_typography": "custom", "editor": "<p><a href=\"/do-not-sell-my-personal-information/\">Read more</a></p>", "typography_text_decoration": "underline"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "7f23ee13", "settings": {"structure": "30", "margin": {"unit": "px", "top": "0", "right": 0, "bottom": "0", "left": 0, "isLinked": true}, "background_background": "classic", "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}}, "elements": [{"id": "52caad65", "settings": {"_column_size": 33, "_inline_size": null, "border_radius": {"unit": "px", "top": "12", "right": "12", "bottom": "12", "left": "12", "isLinked": true}, "box_shadow_box_shadow_type": "yes", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 30, "spread": -10, "color": "rgba(0, 0, 0, 0.25)"}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "20", "right": "20", "bottom": "20", "left": "20", "isLinked": true}, "background_background": "classic", "background_color": "#FFFFFF", "border_border": "solid", "border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "border_color": "#F2F2F2", "heading_color": "#020101", "color_text": "#333333", "color_link": "#1E73BE"}, "elements": [{"id": "50f122fe", "settings": {"title": "Imprint", "header_size": "h3", "align": "left", "title_color": "#000000", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "1930539c", "settings": {"align": "left", "text_color": "#000000", "typography_typography": "custom", "editor": "<p>The Imprint contains our legal information and legally required public information.</p>"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "434a49f0", "settings": {"align": "left", "text_color": "#1E73BE", "typography_typography": "custom", "editor": "<p><a href=\"/imprint/\">Read more</a></p>", "typography_text_decoration": "underline"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "378fb894", "settings": {"_column_size": 33, "_inline_size": null, "border_radius": {"unit": "px", "top": "12", "right": "12", "bottom": "12", "left": "12", "isLinked": true}, "box_shadow_box_shadow_type": "yes", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 30, "spread": -10, "color": "rgba(0, 0, 0, 0.25)"}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "20", "right": "20", "bottom": "20", "left": "20", "isLinked": true}, "background_background": "classic", "background_color": "#FFFFFF", "border_border": "solid", "border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "border_color": "#F2F2F2", "heading_color": "#020101", "color_text": "#333333", "color_link": "#1E73BE"}, "elements": [{"id": "732af5da", "settings": {"title": "Disclaimer", "header_size": "h3", "align": "left", "title_color": "#000000", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "3974b1fe", "settings": {"align": "left", "text_color": "#000000", "typography_typography": "custom", "editor": "<p>Our disclaimer explains our goal towards copyright, accessibility and security.</p>"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "25600e12", "settings": {"align": "left", "text_color": "#1E73BE", "typography_typography": "custom", "editor": "<p><a href=\"/disclaimer/\">Read more</a></p>", "typography_text_decoration": "underline"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "7e827da1", "settings": {"_column_size": 33, "_inline_size": null, "border_radius": {"unit": "px", "top": "12", "right": "12", "bottom": "12", "left": "12", "isLinked": true}, "box_shadow_box_shadow_type": "yes", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 30, "spread": -10, "color": "rgba(0, 0, 0, 0.25)"}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "20", "right": "20", "bottom": "20", "left": "20", "isLinked": true}, "background_background": "classic", "background_color": "#FFFFFF", "border_border": "solid", "border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "border_color": "#F2F2F2", "heading_color": "#020101", "color_text": "#333333", "color_link": "#1E73BE"}, "elements": [{"id": "4e590ff5", "settings": {"title": "Terms & Conditions", "header_size": "h3", "align": "left", "title_color": "#000000", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "7db929d8", "settings": {"align": "left", "text_color": "#000000", "typography_typography": "custom", "editor": "<p>These are our terms and conditions while using our website, and related products and services.</p>"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "7b16a362", "settings": {"align": "left", "text_color": "#1E73BE", "typography_typography": "custom", "editor": "<p><a href=\"/terms-conditions/\">Read more</a></p>", "typography_text_decoration": "underline"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "122bb698", "settings": {"margin": {"unit": "px", "top": "10", "right": 0, "bottom": "10", "left": 0, "isLinked": true}}, "elements": [{"id": "1b2496d3", "settings": {"_column_size": 100, "_inline_size": null}, "elements": [{"id": "3377f65e", "settings": {"title": "Manage Consent", "align": "left", "title_color": "#000000", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 34, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "6cd7c41c", "settings": {"editor": "<p>Below you can manage your consent. For more information, you can read more on the cookie policy of your region. Fill in the form to exercise your privacy rights or ask a question.</p>", "align": "left", "text_color": "#000000", "typography_typography": "custom", "typography_line_height": {"unit": "em", "size": 1.5, "sizes": []}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "-15", "left": "0", "isLinked": false}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "10520b91", "settings": {"structure": "21", "margin": {"unit": "px", "top": "10", "right": 0, "bottom": "10", "left": 0, "isLinked": true}, "background_background": "classic", "padding": {"unit": "px", "top": "20", "right": "0", "bottom": "20", "left": "0", "isLinked": false}}, "elements": [{"id": "3ef85c16", "settings": {"_column_size": 33, "_inline_size": null, "border_radius": {"unit": "px", "top": "12", "right": "12", "bottom": "12", "left": "12", "isLinked": true}, "box_shadow_box_shadow_type": "yes", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 30, "spread": -10, "color": "rgba(0, 0, 0, 0.25)"}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "20", "right": "20", "bottom": "20", "left": "20", "isLinked": true}, "background_background": "classic", "background_color": "#FFFFFF", "border_border": "solid", "border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "border_color": "#F2F2F2", "heading_color": "#020101", "color_text": "#333333", "color_link": "#1E73BE"}, "elements": [{"id": "5f64c62d", "settings": {"shortcode": "[cmplz-manage-consent]"}, "elements": [], "isInner": false, "widgetType": "shortcode", "elType": "widget"}, {"id": "588201f7", "settings": {"editor": "<p>Read about deleting cookies in your browser and enhancing privacy.</p>", "align": "left", "text_color": "#000000", "typography_typography": "custom", "typography_line_height": {"unit": "em", "size": 1.5, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "42561964", "settings": {"align": "left", "text_color": "#1E73BE", "typography_typography": "custom", "editor": "<p><a href=\"https://complianz.io/privacy-in-your-browser/\" target=\"_blank\" rel=\"noopener\">Read more</a></p>", "typography_text_decoration": "underline"}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "739f233", "settings": {"_column_size": 66, "_inline_size": null, "border_radius": {"unit": "px", "top": "12", "right": "12", "bottom": "12", "left": "12", "isLinked": true}, "box_shadow_box_shadow_type": "yes", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 30, "spread": -10, "color": "rgba(0, 0, 0, 0.25)"}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "20", "right": "20", "bottom": "20", "left": "20", "isLinked": false}, "background_background": "classic", "background_color": "#FFFFFF", "border_color": "#E4E4E4"}, "elements": [{"id": "15722cf5", "settings": {"form_name": "Form by <PERSON><PERSON><PERSON><PERSON>", "form_fields": [{"custom_id": "name", "field_label": "Full Name", "placeholder": "<PERSON>", "width": "50", "_id": "29baf6a", "previous_button": "", "next_button": "", "file_sizes": "", "file_types": "", "allow_multiple_upload": "", "max_files": "", "acceptance_text": "", "checked_by_default": "", "field_min": "", "field_max": "", "min_date": "", "max_date": "", "use_native_date": "", "use_native_time": ""}, {"custom_id": "email", "field_type": "email", "required": "true", "field_label": "Email", "placeholder": "Email", "width": "50", "_id": "7c9f340", "previous_button": "", "next_button": "", "file_sizes": "", "file_types": "", "allow_multiple_upload": "", "max_files": "", "acceptance_text": "", "checked_by_default": "", "field_min": "", "field_max": "", "min_date": "", "max_date": "", "use_native_date": "", "use_native_time": ""}, {"_id": "82611b8", "field_type": "select", "field_label": "Exercise your Rights", "required": "true", "field_options": "1. Right to be forgotten - Delete your data (art. 17 - GDPR)\n2. Right to data portability - Request your data (art. 20 - GDPR)\n3. Right to rectification - Rectify your data (art. 16 - GDPR)\n4. Right of Access - Request to see if data is being processed (art. 15 - GDPR)\n5. Objection to sharing data - CCPA\n6. Other questions regarding privacy", "select_size": 2, "custom_id": "field_82611b8", "previous_button": "", "next_button": "", "file_sizes": "", "file_types": "", "allow_multiple_upload": "", "max_files": "", "acceptance_text": "", "checked_by_default": "", "field_min": "", "field_max": "", "min_date": "", "max_date": "", "use_native_date": "", "use_native_time": ""}, {"_id": "6bfc24e", "field_type": "textarea", "field_label": "Message", "placeholder": "Questions regarding privacy.", "custom_id": "field_6bfc24e", "previous_button": "", "next_button": "", "file_sizes": "", "file_types": "", "allow_multiple_upload": "", "max_files": "", "acceptance_text": "", "checked_by_default": "", "field_min": "", "field_max": "", "min_date": "", "max_date": "", "use_native_date": "", "use_native_time": ""}], "mark_required": "yes", "button_align": "start", "step_next_label": "Next", "step_previous_label": "Previous", "button_text": "Submit", "submit_actions": ["save-to-database"], "email_content": "[all-fields]", "email_content_2": "[all-fields]", "step_type": "progress_bar", "form_id": "cmplz_form", "custom_messages": "yes", "success_message": "The form was sent successfully.", "error_message": "An error occurred.", "required_field_message": "This field is required.", "invalid_message": "There's something wrong. The form is invalid.", "label_color": "#000000", "label_typography_typography": "custom", "html_color": "#000000", "html_typography_typography": "custom", "field_text_color": "#000000", "field_typography_typography": "custom", "button_typography_typography": "custom", "button_background_color": "#F9F9F9", "previous_button_background_color": "#000000", "message_typography_typography": "custom", "step_inactive_primary_color": "#000000", "step_active_primary_color": "#1E73BE", "step_completed_primary_color": "#1E73BE", "button_size": "xs", "column_gap": {"unit": "px", "size": 15, "sizes": []}, "row_gap": {"unit": "px", "size": 20, "sizes": []}, "label_spacing": {"unit": "px", "size": 5, "sizes": []}, "field_border_color": "#DADADA", "button_border_border": "solid", "button_border_width": {"unit": "px", "top": "2", "right": "2", "bottom": "2", "left": "2", "isLinked": true}, "button_text_color": "#1E73BE", "button_border_radius": {"unit": "px", "top": "50", "right": "50", "bottom": "50", "left": "50", "isLinked": true}}, "elements": [], "isInner": false, "widgetType": "form", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}], "page_settings": [], "version": "0.4", "title": "Legal Hub", "type": "single-page"}