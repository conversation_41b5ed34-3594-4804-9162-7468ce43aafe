{"version": 3, "file": "acf-pro-blocks-legacy.js", "mappings": ";;;;;;;;;;;;;;;;;AAAA,CAAE,UAAWA,CAAC,EAAEC,SAAS,EAAG;EAC3B;EACA,MAAM;IAAEC,aAAa;IAAEC,iBAAiB;IAAEC;EAAY,CAAC,GAAGC,EAAE,CAACC,WAAW;EACxE,MAAM;IAAEC,OAAO;IAAEC,UAAU;IAAEC,WAAW;IAAEC;EAAQ,CAAC,GAAGL,EAAE,CAACM,UAAU;EACnE,MAAM;IAAEC;EAAS,CAAC,GAAGP,EAAE,CAACQ,OAAO;EAC/B,MAAM;IAAEC;EAAU,CAAC,GAAGC,KAAK;EAC3B,MAAM;IAAEC;EAAW,CAAC,GAAGX,EAAE,CAACY,IAAI;EAC9B,MAAM;IAAEC;EAA2B,CAAC,GAAGb,EAAE,CAACc,OAAO;;EAEjD;AACD;AACA;AACA;AACA;AACA;EACC,MAAMC,UAAU,GAAG,CAAC,CAAC;;EAErB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,YAAY,CAAEC,IAAI,EAAG;IAC7B,OAAOF,UAAU,CAAEE,IAAI,CAAE,IAAI,KAAK;EACnC;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,WAAW,CAAED,IAAI,EAAG;IAC5B,OAAO,CAAC,CAAEF,UAAU,CAAEE,IAAI,CAAE;EAC7B;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASE,UAAU,CAAEC,KAAK,EAAG;IAC5B,OAAO,CAAEA,KAAK,CAACC,UAAU,CAACC,EAAE;EAC7B;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,gBAAgB,CAAEH,KAAK,EAAG;IAClC,OAAOI,SAAS,EAAE,CAChBC,MAAM,CAAIC,KAAK,IAAMA,KAAK,CAACL,UAAU,CAACC,EAAE,KAAKF,KAAK,CAACC,UAAU,CAACC,EAAE,CAAE,CAClEG,MAAM,CAAIC,KAAK,IAAMA,KAAK,CAACC,QAAQ,KAAKP,KAAK,CAACO,QAAQ,CAAE,CAACC,MAAM;EAClE;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,iBAAiB,CAAEC,SAAS,EAAG;IACvC;IACA,IAAIC,YAAY,GAAGD,SAAS,CAACE,UAAU,IAAI,EAAE;IAC7C,IAAKD,YAAY,CAACH,MAAM,EAAG;MAC1B;MACAG,YAAY,CAACE,IAAI,CAAE,UAAU,CAAE;;MAE/B;MACA,IAAIC,QAAQ,GAAGC,GAAG,CAACC,GAAG,CAAE,UAAU,CAAE;MACpC,IAAKL,YAAY,CAACM,OAAO,CAAEH,QAAQ,CAAE,KAAK,CAAC,CAAC,EAAG;QAC9C,OAAO,KAAK;MACb;IACD;;IAEA;IACA,IACC,OAAOJ,SAAS,CAACQ,IAAI,KAAK,QAAQ,IAClCR,SAAS,CAACQ,IAAI,CAACC,MAAM,CAAE,CAAC,EAAE,CAAC,CAAE,KAAK,MAAM,EACvC;MACD,MAAMC,QAAQ,GAAGV,SAAS,CAACQ,IAAI;MAC/BR,SAAS,CAACQ,IAAI,GAAG,kEAAC,GAAG,QAAGE,QAAQ,CAAQ;IACzC;;IAEA;IACA;IACA,IAAK,CAAEV,SAAS,CAACQ,IAAI,EAAG;MACvB,OAAOR,SAAS,CAACQ,IAAI;IACtB;;IAEA;IACA,IAAIG,QAAQ,GAAGzC,EAAE,CAAC0C,MAAM,CACtBC,aAAa,EAAE,CACflB,MAAM,CAAImB,GAAG,IAAMA,GAAG,CAACC,IAAI,KAAKf,SAAS,CAACW,QAAQ,CAAE,CACpDK,GAAG,EAAE;IACP,IAAK,CAAEL,QAAQ,EAAG;MACjB;MACAX,SAAS,CAACW,QAAQ,GAAG,QAAQ;IAC9B;;IAEA;IACA;IACA;IACA,IAAIpB,UAAU,GAAG;MAChBC,EAAE,EAAE;QACHyB,IAAI,EAAE;MACP,CAAC;MACD9B,IAAI,EAAE;QACL8B,IAAI,EAAE;MACP,CAAC;MACDnC,IAAI,EAAE;QACLmC,IAAI,EAAE;MACP,CAAC;MACDC,KAAK,EAAE;QACND,IAAI,EAAE;MACP,CAAC;MACDE,IAAI,EAAE;QACLF,IAAI,EAAE;MACP;IACD,CAAC;;IAED;IACA,IAAIG,aAAa,GAAGC,SAAS;IAC7B,IAAIC,aAAa,GAAGC,SAAS;;IAE7B;IACA,IAAKvB,SAAS,CAACwB,QAAQ,CAACC,UAAU,EAAG;MACpClC,UAAU,GAAGmC,uBAAuB,CAAEnC,UAAU,CAAE;MAClD6B,aAAa,GAAGO,sBAAsB,CAAEP,aAAa,EAAEpB,SAAS,CAAE;IACnE;;IAEA;IACA,IAAKA,SAAS,CAACwB,QAAQ,CAACI,aAAa,EAAG;MACvCrC,UAAU,GAAGsC,0BAA0B,CAAEtC,UAAU,CAAE;MACrD6B,aAAa,GAAGU,yBAAyB,CACxCV,aAAa,EACbpB,SAAS,CACT;IACF;;IAEA;IACAA,SAAS,GAAGK,GAAG,CAAC0B,SAAS,CAAE/B,SAAS,EAAE;MACrCgC,KAAK,EAAE,EAAE;MACT7C,IAAI,EAAE,EAAE;MACRwB,QAAQ,EAAE,EAAE;MACZpB,UAAU,EAAEA,UAAU;MACtB0C,IAAI,EAAE,UAAW3C,KAAK,EAAG;QACxB,OAAO,kEAAC,aAAa,EAAMA,KAAK,CAAK;MACtC,CAAC;MACD4C,IAAI,EAAE,UAAW5C,KAAK,EAAG;QACxB,OAAO,kEAAC,aAAa,EAAMA,KAAK,CAAK;MACtC;IACD,CAAC,CAAE;;IAEH;IACA;IACA,KAAM,MAAM6C,GAAG,IAAInC,SAAS,CAACT,UAAU,EAAG;MACzC,OAAOS,SAAS,CAACT,UAAU,CAAE4C,GAAG,CAAE,CAACC,OAAO;IAC3C;;IAEA;IACAnD,UAAU,CAAEe,SAAS,CAACb,IAAI,CAAE,GAAGa,SAAS;;IAExC;IACA,IAAIqC,MAAM,GAAGnE,EAAE,CAAC0C,MAAM,CAACb,iBAAiB,CAAEC,SAAS,CAACb,IAAI,EAAEa,SAAS,CAAE;;IAErE;IACA;IACA,IAAKqC,MAAM,CAAC9C,UAAU,CAAC+C,MAAM,EAAG;MAC/BD,MAAM,CAAC9C,UAAU,CAAC+C,MAAM,GAAG;QAC1BrB,IAAI,EAAE;MACP,CAAC;IACF;;IAEA;IACA,OAAOoB,MAAM;EACd;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASE,MAAM,CAAEC,QAAQ,EAAG;IAC3B,IAAKA,QAAQ,KAAK,mBAAmB,EAAG;MACvC,OACCtE,EAAE,CAACY,IAAI,CAACyD,MAAM,CAAE,mBAAmB,CAAE,IACrCrE,EAAE,CAACY,IAAI,CAACyD,MAAM,CAAE,aAAa,CAAE;IAEjC;IACA,OAAOrE,EAAE,CAACY,IAAI,CAACyD,MAAM,CAAEC,QAAQ,CAAE;EAClC;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,QAAQ,CAAED,QAAQ,EAAG;IAC7B,OAAOtE,EAAE,CAACY,IAAI,CAAC2D,QAAQ,CAAED,QAAQ,CAAE;EACpC;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAAS9C,SAAS,CAAEgD,IAAI,EAAG;IAC1B;IACA,IAAI9B,MAAM,GAAG2B,MAAM,CAAE,mBAAmB,CAAE,CAAC7C,SAAS,EAAE;;IAEtD;IACA,IAAIiD,CAAC,GAAG,CAAC;IACT,OAAQA,CAAC,GAAG/B,MAAM,CAACd,MAAM,EAAG;MAC3Bc,MAAM,GAAGA,MAAM,CAACgC,MAAM,CAAEhC,MAAM,CAAE+B,CAAC,CAAE,CAACE,WAAW,CAAE;MACjDF,CAAC,EAAE;IACJ;;IAEA;IACA,KAAM,IAAIG,CAAC,IAAIJ,IAAI,EAAG;MACrB9B,MAAM,GAAGA,MAAM,CAACjB,MAAM,CACnBC,KAAK,IAAMA,KAAK,CAACL,UAAU,CAAEuD,CAAC,CAAE,KAAKJ,IAAI,CAAEI,CAAC,CAAE,CAChD;IACF;;IAEA;IACA,OAAOlC,MAAM;EACd;;EAEA;EACA,MAAMmC,SAAS,GAAG,CAAC,CAAC;;EAEpB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,UAAU,CAAEN,IAAI,EAAG;IAC3B,MAAM;MAAEnD,UAAU,GAAG,CAAC,CAAC;MAAE0D,KAAK,GAAG,CAAC,CAAC;MAAEC,KAAK,GAAG;IAAE,CAAC,GAAGR,IAAI;;IAEvD;IACA,MAAM;MAAElD;IAAG,CAAC,GAAGD,UAAU;IACzB,MAAMT,IAAI,GAAGiE,SAAS,CAAEvD,EAAE,CAAE,IAAI;MAC/ByD,KAAK,EAAE,CAAC,CAAC;MACTE,OAAO,EAAE,KAAK;MACdC,OAAO,EAAEvF,CAAC,CAACwF,QAAQ;IACpB,CAAC;;IAED;IACAvE,IAAI,CAACmE,KAAK,mCAAQnE,IAAI,CAACmE,KAAK,GAAKA,KAAK,CAAE;;IAExC;IACAK,YAAY,CAAExE,IAAI,CAACqE,OAAO,CAAE;IAC5BrE,IAAI,CAACqE,OAAO,GAAGI,UAAU,CAAE,YAAY;MACtC1F,CAAC,CAAC2F,IAAI,CAAE;QACPC,GAAG,EAAEpD,GAAG,CAACC,GAAG,CAAE,SAAS,CAAE;QACzBoD,QAAQ,EAAE,MAAM;QAChBzC,IAAI,EAAE,MAAM;QACZ0C,KAAK,EAAE,KAAK;QACZ7E,IAAI,EAAEuB,GAAG,CAACuD,cAAc,CAAE;UACzBC,MAAM,EAAE,sBAAsB;UAC9BjE,KAAK,EAAEkE,IAAI,CAACC,SAAS,CAAExE,UAAU,CAAE;UACnC0D,KAAK,EAAEnE,IAAI,CAACmE;QACb,CAAC;MACF,CAAC,CAAE,CACDe,MAAM,CAAE,YAAY;QACpB;QACAjB,SAAS,CAAEvD,EAAE,CAAE,GAAG,IAAI;MACvB,CAAC,CAAE,CACFyE,IAAI,CAAE,YAAY;QAClBnF,IAAI,CAACsE,OAAO,CAACc,OAAO,CAACC,KAAK,CAAE,IAAI,EAAEC,SAAS,CAAE;MAC9C,CAAC,CAAE,CACFC,IAAI,CAAE,YAAY;QAClBvF,IAAI,CAACsE,OAAO,CAACkB,MAAM,CAACH,KAAK,CAAE,IAAI,EAAEC,SAAS,CAAE;MAC7C,CAAC,CAAE;IACL,CAAC,EAAElB,KAAK,CAAE;;IAEV;IACAH,SAAS,CAAEvD,EAAE,CAAE,GAAGV,IAAI;;IAEtB;IACA,OAAOA,IAAI,CAACsE,OAAO;EACpB;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASmB,cAAc,CAAEC,IAAI,EAAEC,IAAI,EAAG;IACrC,OAAOX,IAAI,CAACC,SAAS,CAAES,IAAI,CAAE,KAAKV,IAAI,CAACC,SAAS,CAAEU,IAAI,CAAE;EACzD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACCpE,GAAG,CAACqE,QAAQ,GAAG,UAAWC,IAAI,EAAG;IAChC,OAAOC,SAAS,CAAE/G,CAAC,CAAE8G,IAAI,CAAE,CAAE,CAAC,CAAE,CAAE;EACnC,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,SAAS,CAAEC,IAAI,EAAG;IAC1B;IACA,IAAIC,QAAQ,GAAGC,aAAa,CAAEF,IAAI,CAACC,QAAQ,CAACE,WAAW,EAAE,CAAE;IAC3D,IAAK,CAAEF,QAAQ,EAAG;MACjB,OAAO,IAAI;IACZ;;IAEA;IACA,IAAIG,SAAS,GAAG,CAAC,CAAC;IAClB5E,GAAG,CAAC6E,SAAS,CAAEL,IAAI,CAACtF,UAAU,CAAE,CAC9B4F,GAAG,CAAEC,aAAa,CAAE,CACpBC,OAAO,CAAE,UAAWC,IAAI,EAAG;MAC3BL,SAAS,CAAEK,IAAI,CAACnG,IAAI,CAAE,GAAGmG,IAAI,CAACC,KAAK;IACpC,CAAC,CAAE;;IAEJ;IACA,IAAI7C,IAAI,GAAG,CAAEoC,QAAQ,EAAEG,SAAS,CAAE;IAClC5E,GAAG,CAAC6E,SAAS,CAAEL,IAAI,CAACW,UAAU,CAAE,CAACH,OAAO,CAAE,UAAWI,KAAK,EAAG;MAC5D,IAAKA,KAAK,YAAYC,IAAI,EAAG;QAC5B,IAAIC,IAAI,GAAGF,KAAK,CAACG,WAAW;QAC5B,IAAKD,IAAI,EAAG;UACXjD,IAAI,CAACvC,IAAI,CAAEwF,IAAI,CAAE;QAClB;MACD,CAAC,MAAM;QACNjD,IAAI,CAACvC,IAAI,CAAEyE,SAAS,CAAEa,KAAK,CAAE,CAAE;MAChC;IACD,CAAC,CAAE;;IAEH;IACA,OAAO7G,KAAK,CAACiH,aAAa,CAAC1B,KAAK,CAAE,IAAI,EAAEzB,IAAI,CAAE;EAC/C;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASoD,UAAU,CAAE3G,IAAI,EAAG;IAC3B,IAAI4G,WAAW,GAAG1F,GAAG,CAAC2F,KAAK,CAAE3F,GAAG,EAAE,qBAAqB,EAAElB,IAAI,CAAE;IAC/D,IAAK4G,WAAW,EAAG,OAAOA,WAAW;IACrC,OAAO5G,IAAI;EACZ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAAS4F,aAAa,CAAE5F,IAAI,EAAG;IAC9B,QAASA,IAAI;MACZ,KAAK,aAAa;QACjB,OAAOlB,WAAW;MACnB,KAAK,QAAQ;QACZ,OAAOgI,MAAM;MACd,KAAK,UAAU;QACd,OAAO,IAAI;MACZ;QACC;QACA9G,IAAI,GAAG2G,UAAU,CAAE3G,IAAI,CAAE;IAAC;IAE5B,OAAOA,IAAI;EACZ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASiG,aAAa,CAAEc,QAAQ,EAAG;IAClC,IAAI/G,IAAI,GAAG+G,QAAQ,CAAC/G,IAAI;IACxB,IAAIoG,KAAK,GAAGW,QAAQ,CAACX,KAAK;IAC1B,QAASpG,IAAI;MACZ;MACA,KAAK,OAAO;QACXA,IAAI,GAAG,WAAW;QAClB;;MAED;MACA,KAAK,OAAO;QACX,IAAIgH,GAAG,GAAG,CAAC,CAAC;QACZZ,KAAK,CAACa,KAAK,CAAE,GAAG,CAAE,CAACf,OAAO,CAAE,UAAWgB,CAAC,EAAG;UAC1C,IAAIC,GAAG,GAAGD,CAAC,CAAC9F,OAAO,CAAE,GAAG,CAAE;UAC1B,IAAK+F,GAAG,GAAG,CAAC,EAAG;YACd,IAAIC,QAAQ,GAAGF,CAAC,CAAC5F,MAAM,CAAE,CAAC,EAAE6F,GAAG,CAAE,CAACE,IAAI,EAAE;YACxC,IAAIC,SAAS,GAAGJ,CAAC,CAAC5F,MAAM,CAAE6F,GAAG,GAAG,CAAC,CAAE,CAACE,IAAI,EAAE;;YAE1C;YACA,IAAKD,QAAQ,CAACG,MAAM,CAAE,CAAC,CAAE,KAAK,GAAG,EAAG;cACnCH,QAAQ,GAAGlG,GAAG,CAACsG,YAAY,CAAEJ,QAAQ,CAAE;YACxC;YACAJ,GAAG,CAAEI,QAAQ,CAAE,GAAGE,SAAS;UAC5B;QACD,CAAC,CAAE;QACHlB,KAAK,GAAGY,GAAG;QACX;;MAED;MACA;QACC;QACA,IAAKhH,IAAI,CAACoB,OAAO,CAAE,OAAO,CAAE,KAAK,CAAC,EAAG;UACpC;QACD;;QAEA;QACApB,IAAI,GAAG2G,UAAU,CAAE3G,IAAI,CAAE;;QAEzB;QACA,IAAIyH,EAAE,GAAGrB,KAAK,CAACmB,MAAM,CAAE,CAAC,CAAE;QAC1B,IAAKE,EAAE,KAAK,GAAG,IAAIA,EAAE,KAAK,GAAG,EAAG;UAC/BrB,KAAK,GAAGzB,IAAI,CAAC+C,KAAK,CAAEtB,KAAK,CAAE;QAC5B;;QAEA;QACA,IAAKA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,EAAG;UAC5CA,KAAK,GAAGA,KAAK,KAAK,MAAM;QACzB;QACA;IAAM;IAER,OAAO;MACNpG,IAAI,EAAEA,IAAI;MACVoG,KAAK,EAAEA;IACR,CAAC;EACF;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIuB,qBAAqB,GAAG/H,0BAA0B,CAAE,UACvDgI,cAAc,EACb;IACD,OAAO,MAAMC,gBAAgB,SAASrI,SAAS,CAAC;MAC/CsI,WAAW,CAAE3H,KAAK,EAAG;QACpB,KAAK,CAAEA,KAAK,CAAE;;QAEd;QACA,MAAM;UAAEH,IAAI;UAAEI;QAAW,CAAC,GAAG,IAAI,CAACD,KAAK;;QAEvC;QACA,MAAMU,SAAS,GAAGd,YAAY,CAAEC,IAAI,CAAE;QACtC,IAAK,CAAEa,SAAS,EAAG;UAClB;QACD;;QAEA;QACA,IAAKX,UAAU,CAAEC,KAAK,CAAE,EAAG;UAC1BC,UAAU,CAACC,EAAE,GAAGa,GAAG,CAAC6G,MAAM,CAAE,QAAQ,CAAE;UACtC,KAAM,IAAIC,SAAS,IAAInH,SAAS,CAACT,UAAU,EAAG;YAC7C,IACCA,UAAU,CAAE4H,SAAS,CAAE,KAAKrJ,SAAS,IACrCkC,SAAS,CAAEmH,SAAS,CAAE,KAAKrJ,SAAS,EACnC;cACDyB,UAAU,CAAE4H,SAAS,CAAE,GAAGnH,SAAS,CAAEmH,SAAS,CAAE;YACjD;UACD;UACA;QACD;;QAEA;QACA,IAAK1H,gBAAgB,CAAEH,KAAK,CAAE,EAAG;UAChCC,UAAU,CAACC,EAAE,GAAGa,GAAG,CAAC6G,MAAM,CAAE,QAAQ,CAAE;UACtC;QACD;MACD;MACAE,MAAM,GAAG;QACR,OAAO,kEAAC,cAAc,EAAM,IAAI,CAAC9H,KAAK,CAAK;MAC5C;IACD,CAAC;EACF,CAAC,EACD,uBAAuB,CAAE;EACzBpB,EAAE,CAACmJ,KAAK,CAACC,SAAS,CACjB,uBAAuB,EACvB,6BAA6B,EAC7BR,qBAAqB,CACrB;;EAED;AACD;AACA;AACA;AACA;AACA;EACC,SAASvF,SAAS,GAAG;IACpB,OAAO,kEAAC,WAAW,CAAC,OAAO,OAAG;EAC/B;;EAEA;AACD;AACA;AACA;AACA;AACA;EACC,MAAMF,SAAS,SAAS1C,SAAS,CAAC;IACjCsI,WAAW,CAAE3H,KAAK,EAAG;MACpB,KAAK,CAAEA,KAAK,CAAE;MACd,IAAI,CAACiI,KAAK,EAAE;IACb;IAEAA,KAAK,GAAG;MACP,MAAM;QAAEpI,IAAI;QAAEI;MAAW,CAAC,GAAG,IAAI,CAACD,KAAK;MACvC,MAAMU,SAAS,GAAGd,YAAY,CAAEC,IAAI,CAAE;;MAEtC;MACA,SAASqI,YAAY,CAAEC,KAAK,EAAG;QAC9B,IAAKA,KAAK,CAAClH,OAAO,CAAEhB,UAAU,CAAC4B,IAAI,CAAE,KAAK,CAAC,CAAC,EAAG;UAC9C5B,UAAU,CAAC4B,IAAI,GAAGsG,KAAK,CAAE,CAAC,CAAE;QAC7B;MACD;MACA,QAASzH,SAAS,CAACmB,IAAI;QACtB,KAAK,MAAM;UACVqG,YAAY,CAAE,CAAE,MAAM,EAAE,SAAS,CAAE,CAAE;UACrC;QACD,KAAK,SAAS;UACbA,YAAY,CAAE,CAAE,SAAS,EAAE,MAAM,CAAE,CAAE;UACrC;QACD;UACCA,YAAY,CAAE,CAAE,MAAM,CAAE,CAAE;UAC1B;MAAM;IAET;IAEAJ,MAAM,GAAG;MACR,MAAM;QAAEjI,IAAI;QAAEI,UAAU;QAAEmI;MAAc,CAAC,GAAG,IAAI,CAACpI,KAAK;MACtD,MAAM;QAAE6B;MAAK,CAAC,GAAG5B,UAAU;MAC3B,MAAMS,SAAS,GAAGd,YAAY,CAAEC,IAAI,CAAE;;MAEtC;MACA,IAAIwI,UAAU,GAAG3H,SAAS,CAACwB,QAAQ,CAACL,IAAI;MACxC,IAAKA,IAAI,KAAK,MAAM,EAAG;QACtBwG,UAAU,GAAG,KAAK;MACnB;;MAEA;MACA,MAAMC,UAAU,GACfzG,IAAI,KAAK,SAAS,GACfd,GAAG,CAACwH,EAAE,CAAE,gBAAgB,CAAE,GAC1BxH,GAAG,CAACwH,EAAE,CAAE,mBAAmB,CAAE;MACjC,MAAMC,UAAU,GACf3G,IAAI,KAAK,SAAS,GAAG,MAAM,GAAG,mBAAmB;MAClD,SAAS4G,UAAU,GAAG;QACrBL,aAAa,CAAE;UACdvG,IAAI,EAAEA,IAAI,KAAK,SAAS,GAAG,MAAM,GAAG;QACrC,CAAC,CAAE;MACJ;;MAEA;MACA,OACC,kEAAC,QAAQ,QACR,kEAAC,aAAa,QACXwG,UAAU,IACX,kEAAC,OAAO,QACP,kEAAC,UAAU;QACV,SAAS,EAAC,oDAAoD;QAC9D,KAAK,EAAGC,UAAY;QACpB,IAAI,EAAGE,UAAY;QACnB,OAAO,EAAGC;MAAY,EACrB,CAEH,CACc,EAEhB,kEAAC,iBAAiB,QACf5G,IAAI,KAAK,SAAS,IACnB;QAAK,SAAS,EAAC;MAAqC,GACnD,kEAAC,SAAS,EAAM,IAAI,CAAC7B,KAAK,CAAK,CAEhC,CACkB,EAEpB,kEAAC,SAAS,EAAM,IAAI,CAACA,KAAK,CAAK,CACrB;IAEb;EACD;;EAEA;AACD;AACA;AACA;AACA;AACA;EACC,MAAM0I,UAAU,SAASrJ,SAAS,CAAC;IAClCyI,MAAM,GAAG;MACR,MAAM;QAAE7H,UAAU;QAAE0I;MAAW,CAAC,GAAG,IAAI,CAAC3I,KAAK;MAC7C,MAAM;QAAE6B;MAAK,CAAC,GAAG5B,UAAU;MAC3B,OACC;QAAK,SAAS,EAAC;MAAoC,GAChD4B,IAAI,KAAK,MAAM,IAAI8G,UAAU,GAC9B,kEAAC,SAAS,EAAM,IAAI,CAAC3I,KAAK,CAAK,GAC5B6B,IAAI,KAAK,MAAM,IAAI,CAAE8G,UAAU,GAClC,kEAAC,YAAY,EAAM,IAAI,CAAC3I,KAAK,CAAK,GAC/B6B,IAAI,KAAK,SAAS,GACrB,kEAAC,YAAY,EAAM,IAAI,CAAC7B,KAAK,CAAK,GAElC,kEAAC,SAAS,EAAM,IAAI,CAACA,KAAK,CAC1B,CACI;IAER;EACD;;EAEA;EACA,MAAM4I,SAAS,GAAGrJ,UAAU,CAAE,UAAW0D,MAAM,EAAE4F,QAAQ,EAAG;IAC3D,MAAM;MAAEtI;IAAS,CAAC,GAAGsI,QAAQ;IAC7B;IACA,MAAMC,YAAY,GAAG7F,MAAM,CAAE,mBAAmB,CAAE,CAAC8F,oBAAoB,CACtExI,QAAQ,CACR;IACD,MAAMyI,KAAK,GAAG/F,MAAM,CAAE,mBAAmB,CAAE,CAACgG,aAAa,CACxD1I,QAAQ,EACRuI,YAAY,CACZ;IACD,OAAO;MACNE;IACD,CAAC;EACF,CAAC,CAAE,CAAEN,UAAU,CAAE;;EAEjB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,MAAMQ,GAAG,SAAS7J,SAAS,CAAC;IAC3ByI,MAAM,GAAG;MACR,OACC;QACC,uBAAuB,EAAG;UAAEqB,MAAM,EAAE,IAAI,CAACnJ,KAAK,CAACoJ;QAAS;MAAG,EAC1D;IAEJ;EACD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,MAAMzC,MAAM,SAAStH,SAAS,CAAC;IAC9ByI,MAAM,GAAG;MACR,OAAO;QAAK,GAAG,EAAKuB,EAAE,IAAQ,IAAI,CAACA,EAAE,GAAGA;MAAM,EAAG;IAClD;IACAC,OAAO,CAAEjE,IAAI,EAAG;MACf9G,CAAC,CAAE,IAAI,CAAC8K,EAAE,CAAE,CAAChE,IAAI,CAAG,WAAWA,IAAM,WAAU,CAAE;IAClD;IACAkE,kBAAkB,GAAG;MACpB,IAAI,CAACD,OAAO,CAAE,IAAI,CAACtJ,KAAK,CAACoJ,QAAQ,CAAE;IACpC;IACAI,iBAAiB,GAAG;MACnB,IAAI,CAACF,OAAO,CAAE,IAAI,CAACtJ,KAAK,CAACoJ,QAAQ,CAAE;IACpC;EACD;;EAEA;EACA,MAAMK,KAAK,GAAG,CAAC,CAAC;;EAEhB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,MAAMC,WAAW,SAASrK,SAAS,CAAC;IACnCsI,WAAW,CAAE3H,KAAK,EAAG;MACpB,KAAK,CAAEA,KAAK,CAAE;;MAEd;MACA,IAAI,CAAC2J,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,IAAI,CAAE,IAAI,CAAE;;MAEtC;MACA,IAAI,CAAC1J,EAAE,GAAG,EAAE;MACZ,IAAI,CAACmJ,EAAE,GAAG,KAAK;MACf,IAAI,CAACQ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,YAAY,GAAG,QAAQ;MAC5B,IAAI,CAAC7B,KAAK,CAAEjI,KAAK,CAAE;;MAEnB;MACA,IAAI,CAAC+J,SAAS,EAAE;IACjB;IAEA9B,KAAK,CAAEjI,KAAK,EAAG;MACd;IAAA;IAGDgK,KAAK,GAAG;MACP;IAAA;IAGDD,SAAS,GAAG;MACX,IAAI,CAACE,KAAK,GAAGR,KAAK,CAAE,IAAI,CAACvJ,EAAE,CAAE,IAAI,CAAC,CAAC;IACpC;IAEAgK,QAAQ,CAAED,KAAK,EAAG;MACjBR,KAAK,CAAE,IAAI,CAACvJ,EAAE,CAAE,mCAAQ,IAAI,CAAC+J,KAAK,GAAKA,KAAK,CAAE;;MAE9C;MACA;MACA,IAAK,IAAI,CAACJ,UAAU,EAAG;QACtB,KAAK,CAACK,QAAQ,CAAED,KAAK,CAAE;MACxB;IACD;IAEAE,OAAO,CAAE9E,IAAI,EAAG;MACfA,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAAC6B,IAAI,EAAE,GAAG,EAAE;;MAE9B;MACA,IAAK7B,IAAI,KAAK,IAAI,CAAC4E,KAAK,CAAC5E,IAAI,EAAG;QAC/B;MACD;;MAEA;MACA,IAAI4E,KAAK,GAAG;QACX5E,IAAI,EAAEA;MACP,CAAC;MACD,IAAK,IAAI,CAACyE,YAAY,KAAK,KAAK,EAAG;QAClCG,KAAK,CAACG,GAAG,GAAGrJ,GAAG,CAACqE,QAAQ,CAAEC,IAAI,CAAE;QAChC4E,KAAK,CAACI,GAAG,GAAG9L,CAAC,CAAE,IAAI,CAAC8K,EAAE,CAAE;MACzB,CAAC,MAAM;QACNY,KAAK,CAACI,GAAG,GAAG9L,CAAC,CAAE8G,IAAI,CAAE;MACtB;MACA,IAAI,CAAC6E,QAAQ,CAAED,KAAK,CAAE;IACvB;IAEAN,MAAM,CAAEN,EAAE,EAAG;MACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;IACb;IAEAvB,MAAM,GAAG;MACR;MACA,IAAK,IAAI,CAACmC,KAAK,CAACG,GAAG,EAAG;QACrB,OAAO;UAAK,GAAG,EAAG,IAAI,CAACT;QAAQ,GAAG,IAAI,CAACM,KAAK,CAACG,GAAG,CAAQ;MACzD;;MAEA;MACA,OACC;QAAK,GAAG,EAAG,IAAI,CAACT;MAAQ,GACvB,kEAAC,WAAW,QACX,kEAAC,OAAO,OAAG,CACE,CACT;IAER;IAEAW,qBAAqB,CAAEC,SAAS,EAAEC,SAAS,EAAG;MAC7C,IAAKD,SAAS,CAACvB,KAAK,KAAK,IAAI,CAAChJ,KAAK,CAACgJ,KAAK,EAAG;QAC3C,IAAI,CAACyB,iBAAiB,EAAE;MACzB;MACA,OAAOD,SAAS,CAACnF,IAAI,KAAK,IAAI,CAAC4E,KAAK,CAAC5E,IAAI;IAC1C;IAEAqF,OAAO,CAAEC,OAAO,EAAG;MAClB;MACA;MACA,IAAK,IAAI,CAACb,YAAY,KAAK,QAAQ,EAAG;QACrC,IAAIO,GAAG,GAAG,IAAI,CAACJ,KAAK,CAACI,GAAG;QACxB,IAAIO,WAAW,GAAGP,GAAG,CAACQ,MAAM,EAAE;QAC9B,IAAIC,WAAW,GAAGvM,CAAC,CAAE,IAAI,CAAC8K,EAAE,CAAE;;QAE9B;QACAyB,WAAW,CAACzF,IAAI,CAAEgF,GAAG,CAAE;;QAEvB;QACA;QACA;QACA;QACA;QACA,IACCO,WAAW,CAACpK,MAAM,IAClBoK,WAAW,CAAE,CAAC,CAAE,KAAKE,WAAW,CAAE,CAAC,CAAE,EACpC;UACDF,WAAW,CAACvF,IAAI,CAAEgF,GAAG,CAACU,KAAK,EAAE,CAAE;QAChC;MACD;;MAEA;MACA,QAASJ,OAAO;QACf,KAAK,QAAQ;UACZ,IAAI,CAACK,kBAAkB,EAAE;UACzB;QACD,KAAK,SAAS;UACb,IAAI,CAACC,mBAAmB,EAAE;UAC1B;MAAM;IAET;IAEAzB,iBAAiB,GAAG;MACnB;MACA,IAAK,IAAI,CAACS,KAAK,CAAC5E,IAAI,KAAK7G,SAAS,EAAG;QACpC;QACA,IAAI,CAACwL,KAAK,EAAE;;QAEZ;MACD,CAAC,MAAM;QACN,IAAI,CAACU,OAAO,CAAE,SAAS,CAAE;MAC1B;IACD;IAEAnB,kBAAkB,CAAE2B,SAAS,EAAEC,SAAS,EAAG;MAC1C;MACA,IAAI,CAACT,OAAO,CAAE,QAAQ,CAAE;IACzB;IAEAM,kBAAkB,GAAG;MACpBjK,GAAG,CAACqK,QAAQ,CAAE,QAAQ,EAAE,IAAI,CAACnB,KAAK,CAACI,GAAG,CAAE;IACzC;IAEAgB,oBAAoB,GAAG;MACtBtK,GAAG,CAACqK,QAAQ,CAAE,SAAS,EAAE,IAAI,CAACnB,KAAK,CAACI,GAAG,CAAE;;MAEzC;MACA,IAAI,CAACR,UAAU,GAAG,KAAK;IACxB;IAEAoB,mBAAmB,GAAG;MACrB,IAAI,CAACpB,UAAU,GAAG,IAAI;;MAEtB;MACA;MACA;MACA;MACA;MACA5F,UAAU,CAAE,MAAM;QACjBlD,GAAG,CAACqK,QAAQ,CAAE,SAAS,EAAE,IAAI,CAACnB,KAAK,CAACI,GAAG,CAAE;MAC1C,CAAC,CAAE;IACJ;IAEAI,iBAAiB,GAAG;MACnB1J,GAAG,CAACqK,QAAQ,CAAE,SAAS,EAAE,IAAI,CAACnB,KAAK,CAACI,GAAG,CAAE;MACzCpG,UAAU,CAAE,MAAM;QACjBlD,GAAG,CAACqK,QAAQ,CAAE,SAAS,EAAE,IAAI,CAACnB,KAAK,CAACI,GAAG,CAAE;MAC1C,CAAC,CAAE;IACJ;EACD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,MAAMiB,SAAS,SAAS5B,WAAW,CAAC;IACnCzB,KAAK,CAAEjI,KAAK,EAAG;MACd,IAAI,CAACE,EAAE,GAAI,aAAaF,KAAK,CAACC,UAAU,CAACC,EAAI,EAAC;IAC/C;IAEA8J,KAAK,GAAG;MACP;MACA,MAAM;QAAE/J;MAAW,CAAC,GAAG,IAAI,CAACD,KAAK;;MAEjC;MACA0D,UAAU,CAAE;QACXzD,UAAU,EAAEA,UAAU;QACtB0D,KAAK,EAAE;UACN4H,IAAI,EAAE;QACP;MACD,CAAC,CAAE,CAAC5G,IAAI,CAAI6G,IAAI,IAAM;QACrB,IAAI,CAACrB,OAAO,CAAEqB,IAAI,CAAChM,IAAI,CAAC+L,IAAI,CAAE;MAC/B,CAAC,CAAE;IACJ;IAEAP,kBAAkB,GAAG;MACpB,KAAK,CAACA,kBAAkB,EAAE;;MAE1B;MACA,MAAM;QAAE/K,UAAU;QAAEmI;MAAc,CAAC,GAAG,IAAI,CAACpI,KAAK;MAChD,MAAM;QAAEqK;MAAI,CAAC,GAAG,IAAI,CAACJ,KAAK;;MAE1B;MACA,SAASwB,aAAa,GAAmB;QAAA,IAAjBC,MAAM,uEAAG,KAAK;QACrC,MAAMlM,IAAI,GAAGuB,GAAG,CAAC4K,SAAS,CAAEtB,GAAG,EAAG,OAAOpK,UAAU,CAACC,EAAI,EAAC,CAAE;QAC3D,IAAKwL,MAAM,EAAG;UACbzL,UAAU,CAACT,IAAI,GAAGA,IAAI;QACvB,CAAC,MAAM;UACN4I,aAAa,CAAE;YACd5I,IAAI,EAAEA;UACP,CAAC,CAAE;QACJ;MACD;;MAEA;MACA,IAAIqE,OAAO,GAAG,KAAK;MACnBwG,GAAG,CAACuB,EAAE,CAAE,cAAc,EAAE,YAAY;QACnC5H,YAAY,CAAEH,OAAO,CAAE;QACvBA,OAAO,GAAGI,UAAU,CAAEwH,aAAa,EAAE,GAAG,CAAE;MAC3C,CAAC,CAAE;;MAEH;MACA;MACA,IAAK,CAAExL,UAAU,CAACT,IAAI,EAAG;QACxBiM,aAAa,CAAE,IAAI,CAAE;MACtB;IACD;EACD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,MAAMI,YAAY,SAASnC,WAAW,CAAC;IACtCzB,KAAK,CAAEjI,KAAK,EAAG;MACd,IAAI,CAACE,EAAE,GAAI,gBAAgBF,KAAK,CAACC,UAAU,CAACC,EAAI,EAAC;MACjD,IAAIQ,SAAS,GAAGd,YAAY,CAAEI,KAAK,CAACH,IAAI,CAAE;MAC1C,IAAKa,SAAS,CAACwB,QAAQ,CAACkI,GAAG,EAAG;QAC7B,IAAI,CAACN,YAAY,GAAG,KAAK;MAC1B;MACA;IACD;;IAEAE,KAAK,GAAc;MAAA,IAAZ5G,IAAI,uEAAG,CAAC,CAAC;MACf,MAAM;QAAEnD,UAAU,GAAG,IAAI,CAACD,KAAK,CAACC,UAAU;QAAE2D,KAAK,GAAG;MAAE,CAAC,GAAGR,IAAI;;MAE9D;MACA,IAAI,CAAC8G,QAAQ,CAAE;QACd4B,cAAc,EAAE7L;MACjB,CAAC,CAAE;;MAEH;MACAyD,UAAU,CAAE;QACXzD,UAAU,EAAEA,UAAU;QACtB0D,KAAK,EAAE;UACNoI,OAAO,EAAE;QACV,CAAC;QACDnI,KAAK,EAAEA;MACR,CAAC,CAAE,CAACe,IAAI,CAAI6G,IAAI,IAAM;QACrB,IAAI,CAACrB,OAAO,CAAE,iCAAiC,GAAGqB,IAAI,CAAChM,IAAI,CAACuM,OAAO,GAAG,QAAQ,CAAE;MACjF,CAAC,CAAE;IACJ;IAEAf,kBAAkB,GAAG;MACpB,KAAK,CAACA,kBAAkB,EAAE;;MAE1B;MACA,MAAM;QAAE/K;MAAW,CAAC,GAAG,IAAI,CAACD,KAAK;MACjC,MAAM;QAAEqK;MAAI,CAAC,GAAG,IAAI,CAACJ,KAAK;;MAE1B;MACA,MAAMtI,IAAI,GAAG1B,UAAU,CAACJ,IAAI,CAACmM,OAAO,CAAE,MAAM,EAAE,EAAE,CAAE;;MAElD;MACAjL,GAAG,CAACqK,QAAQ,CAAE,sBAAsB,EAAEf,GAAG,EAAEpK,UAAU,CAAE;MACvDc,GAAG,CAACqK,QAAQ,CACV,6BAA6BzJ,IAAM,EAAC,EACrC0I,GAAG,EACHpK,UAAU,CACV;IACF;IAEAqK,qBAAqB,CAAEC,SAAS,EAAEC,SAAS,EAAG;MAC7C,MAAMyB,cAAc,GAAG1B,SAAS,CAACtK,UAAU;MAC3C,MAAMiM,cAAc,GAAG,IAAI,CAAClM,KAAK,CAACC,UAAU;;MAE5C;MACA,IAAK,CAAEgF,cAAc,CAAEgH,cAAc,EAAEC,cAAc,CAAE,EAAG;QACzD,IAAItI,KAAK,GAAG,CAAC;;QAEb;QACA,IAAKqI,cAAc,CAACE,SAAS,KAAKD,cAAc,CAACC,SAAS,EAAG;UAC5DvI,KAAK,GAAG,GAAG;QACZ;QACA,IAAKqI,cAAc,CAACjJ,MAAM,KAAKkJ,cAAc,CAAClJ,MAAM,EAAG;UACtDY,KAAK,GAAG,GAAG;QACZ;QAEA,IAAI,CAACoG,KAAK,CAAE;UACX/J,UAAU,EAAEgM,cAAc;UAC1BrI,KAAK,EAAEA;QACR,CAAC,CAAE;MACJ;MACA,OAAO,KAAK,CAAC0G,qBAAqB,CAAEC,SAAS,EAAEC,SAAS,CAAE;IAC3D;IAEAS,mBAAmB,GAAG;MACrB,KAAK,CAACA,mBAAmB,EAAE;;MAE3B;MACA,IACC,CAAEhG,cAAc,CACf,IAAI,CAACgF,KAAK,CAAC6B,cAAc,EACzB,IAAI,CAAC9L,KAAK,CAACC,UAAU,CACrB,EACA;QACD;QACA,IAAI,CAAC+J,KAAK,EAAE;MACb;IACD;EACD;;EAEA;AACD;AACA;AACA;AACA;EACC,SAASoC,UAAU,GAAG;IACrB;IACA,IAAK,CAAExN,EAAE,CAACC,WAAW,EAAG;MACvBD,EAAE,CAACC,WAAW,GAAGD,EAAE,CAACyN,MAAM;IAC3B;;IAEA;IACA,IAAI1M,UAAU,GAAGoB,GAAG,CAACC,GAAG,CAAE,YAAY,CAAE;IACxC,IAAKrB,UAAU,EAAG;MACjBA,UAAU,CAACkG,GAAG,CAAEpF,iBAAiB,CAAE;IACpC;EACD;;EAEA;EACA;EACAM,GAAG,CAACuL,SAAS,CAAE,SAAS,EAAEF,UAAU,CAAE;;EAEtC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASG,yBAAyB,CAAE3K,KAAK,EAAG;IAC3C,MAAM4K,UAAU,GAAG,CAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAE;IAChD,MAAMC,OAAO,GAAG,KAAK;IACrB,OAAOD,UAAU,CAACE,QAAQ,CAAE9K,KAAK,CAAE,GAAGA,KAAK,GAAG6K,OAAO;EACtD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASE,2BAA2B,CAAE/K,KAAK,EAAG;IAC7C,MAAM4K,UAAU,GAAG,CAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAE;IAChD,MAAMC,OAAO,GAAG1L,GAAG,CAACC,GAAG,CAAE,KAAK,CAAE,GAAG,OAAO,GAAG,MAAM;IACnD,OAAOwL,UAAU,CAACE,QAAQ,CAAE9K,KAAK,CAAE,GAAGA,KAAK,GAAG6K,OAAO;EACtD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASG,uBAAuB,CAAEhL,KAAK,EAAG;IACzC,MAAM6K,OAAO,GAAG,eAAe;IAC/B,IAAK7K,KAAK,EAAG;MACZ,MAAM,CAAEiL,CAAC,EAAEC,CAAC,CAAE,GAAGlL,KAAK,CAACkF,KAAK,CAAE,GAAG,CAAE;MACnC,OACCyF,yBAAyB,CAAEM,CAAC,CAAE,GAC9B,GAAG,GACHF,2BAA2B,CAAEG,CAAC,CAAE;IAElC;IACA,OAAOL,OAAO;EACf;;EAEA;EACA,MAAM;IAAEM,gBAAgB;IAAEC;EAA8B,CAAC,GAAGpO,EAAE,CAACC,WAAW;EAC1E,MAAMoO,2BAA2B,GAChCrO,EAAE,CAACC,WAAW,CAACqO,yCAAyC,IACxDtO,EAAE,CAACC,WAAW,CAACoO,2BAA2B;EAC3C;EACA,MAAME,2BAA2B,GAChCvO,EAAE,CAACC,WAAW,CAACuO,yCAAyC,IACxDxO,EAAE,CAACC,WAAW,CAACsO,2BAA2B;;EAE3C;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAAS5K,0BAA0B,CAAEtC,UAAU,EAAG;IACjDA,UAAU,CAACqC,aAAa,GAAG;MAC1BX,IAAI,EAAE;IACP,CAAC;IACD,OAAO1B,UAAU;EAClB;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASuC,yBAAyB,CAAE6K,iBAAiB,EAAE3M,SAAS,EAAG;IAClE;IACA,IAAIiB,IAAI,GAAGjB,SAAS,CAACwB,QAAQ,CAACI,aAAa;IAC3C,IAAIgL,kBAAkB,EAAEC,iBAAiB;IACzC,QAAS5L,IAAI;MACZ,KAAK,QAAQ;QACZ2L,kBAAkB,GACjBH,2BAA2B,IAAIF,2BAA2B;QAC3DM,iBAAiB,GAAGX,uBAAuB;QAC3C;MACD;QACCU,kBAAkB,GAAGN,6BAA6B;QAClDO,iBAAiB,GAAGhB,yBAAyB;QAC7C;IAAM;;IAGR;IACA,IAAKe,kBAAkB,KAAK9O,SAAS,EAAG;MACvCgP,OAAO,CAACC,IAAI,CACV,QAAQ9L,IAAM,sCAAqC,CACpD;MACD,OAAO0L,iBAAiB;IACzB;;IAEA;IACA3M,SAAS,CAAC4B,aAAa,GAAGiL,iBAAiB,CAAE7M,SAAS,CAAC4B,aAAa,CAAE;;IAEtE;IACA,OAAO,MAAMoF,gBAAgB,SAASrI,SAAS,CAAC;MAC/CyI,MAAM,GAAG;QACR,MAAM;UAAE7H,UAAU;UAAEmI;QAAc,CAAC,GAAG,IAAI,CAACpI,KAAK;QAChD,MAAM;UAAEsC;QAAc,CAAC,GAAGrC,UAAU;QACpC,SAASyN,oBAAoB,CAAEpL,aAAa,EAAG;UAC9C8F,aAAa,CAAE;YACd9F,aAAa,EAAEiL,iBAAiB,CAAEjL,aAAa;UAChD,CAAC,CAAE;QACJ;QACA,OACC,kEAAC,QAAQ,QACR,kEAAC,aAAa;UAAC,KAAK,EAAC;QAAO,GAC3B,kEAAC,kBAAkB;UAClB,KAAK,EAAGvB,GAAG,CAACwH,EAAE,CAAE,0BAA0B,CAAI;UAC9C,KAAK,EAAGgF,iBAAiB,CAAEjL,aAAa,CAAI;UAC5C,QAAQ,EAAGoL;QAAsB,EAChC,CACa,EAChB,kEAAC,iBAAiB,EAAM,IAAI,CAAC1N,KAAK,CAAK,CAC7B;MAEb;IACD,CAAC;EACF;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASoC,uBAAuB,CAAEnC,UAAU,EAAG;IAC9CA,UAAU,CAACkC,UAAU,GAAG;MACvBR,IAAI,EAAE;IACP,CAAC;IACD,OAAO1B,UAAU;EAClB;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASoC,sBAAsB,CAAEgL,iBAAiB,EAAE3M,SAAS,EAAG;IAC/D,MAAM6M,iBAAiB,GAAGZ,2BAA2B;;IAErD;IACAjM,SAAS,CAACyB,UAAU,GAAGoL,iBAAiB,CAAE7M,SAAS,CAACyB,UAAU,CAAE;;IAEhE;IACA,OAAO,MAAMuF,gBAAgB,SAASrI,SAAS,CAAC;MAC/CyI,MAAM,GAAG;QACR,MAAM;UAAE7H,UAAU;UAAEmI;QAAc,CAAC,GAAG,IAAI,CAACpI,KAAK;QAChD,MAAM;UAAEmC;QAAW,CAAC,GAAGlC,UAAU;QAEjC,SAAS0N,iBAAiB,CAAExL,UAAU,EAAG;UACxCiG,aAAa,CAAE;YACdjG,UAAU,EAAEoL,iBAAiB,CAAEpL,UAAU;UAC1C,CAAC,CAAE;QACJ;QAEA,OACC,kEAAC,QAAQ,QACR,kEAAC,aAAa,QACb,kEAAC,gBAAgB;UAChB,KAAK,EAAGoL,iBAAiB,CAAEpL,UAAU,CAAI;UACzC,QAAQ,EAAGwL;QAAmB,EAC7B,CACa,EAChB,kEAAC,iBAAiB,EAAM,IAAI,CAAC3N,KAAK,CAAK,CAC7B;MAEb;IACD,CAAC;EACF;AACD,CAAC,EAAI4N,MAAM,CAAE;;;;;;;;;;AClyCb,CAAE,UAAWrP,CAAC,EAAEC,SAAS,EAAG;EAC3BuC,GAAG,CAAC8M,mBAAmB,GAAG;IACzB,eAAe,EAAE,cAAc;IAC/BC,YAAY,EAAE,cAAc;IAC5B,gBAAgB,EAAE,eAAe;IACjCC,aAAa,EAAE,eAAe;IAC9BC,SAAS,EAAE,WAAW;IACtB,oBAAoB,EAAE,mBAAmB;IACzCC,iBAAiB,EAAE,mBAAmB;IACtCC,aAAa,EAAE,eAAe;IAC9BC,eAAe,EAAE,iBAAiB;IAClCC,YAAY,EAAE,cAAc;IAC5B,aAAa,EAAE,YAAY;IAC3BC,UAAU,EAAE,YAAY;IACxBC,aAAa,EAAE,eAAe;IAC9BC,aAAa,EAAE,eAAe;IAC9BC,cAAc,EAAE,gBAAgB;IAChCC,YAAY,EAAE,cAAc;IAC5BC,WAAW,EAAE,aAAa;IAC1BC,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,UAAU;IACpBC,aAAa,EAAE,eAAe;IAC9B,gBAAgB,EAAE,eAAe;IACjCC,aAAa,EAAE,eAAe;IAC9BC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,UAAU;IACpB,YAAY,EAAE,WAAW;IACzBC,SAAS,EAAE,WAAW;IACtBC,WAAW,EAAE,aAAa;IAC1BC,WAAW,EAAE,aAAa;IAC1BC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,WAAW;IAClBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,WAAW;IACtB,WAAW,EAAE,UAAU;IACvB,WAAW,EAAE,UAAU;IACvBC,QAAQ,EAAE,UAAU;IACpBC,aAAa,EAAE,eAAe;IAC9BC,QAAQ,EAAE,UAAU;IACpB,qBAAqB,EAAE,oBAAoB;IAC3C,6BAA6B,EAAE,2BAA2B;IAC1D,eAAe,EAAE,cAAc;IAC/B,iBAAiB,EAAE,gBAAgB;IACnCC,kBAAkB,EAAE,oBAAoB;IACxCC,yBAAyB,EAAE,2BAA2B;IACtDC,YAAY,EAAE,cAAc;IAC5BC,cAAc,EAAE,gBAAgB;IAChCC,OAAO,EAAE,SAAS;IAClBC,eAAe,EAAE,iBAAiB;IAClCC,iBAAiB,EAAE,mBAAmB;IACtCC,gBAAgB,EAAE,kBAAkB;IACpCC,WAAW,EAAE,aAAa;IAC1BC,YAAY,EAAE,cAAc;IAC5BC,WAAW,EAAE,aAAa;IAC1BC,uBAAuB,EAAE,yBAAyB;IAClDC,QAAQ,EAAE,UAAU;IACpBC,cAAc,EAAE,gBAAgB;IAChCC,YAAY,EAAE,cAAc;IAC5BC,eAAe,EAAE,iBAAiB;IAClCC,uBAAuB,EAAE,yBAAyB;IAClDC,qBAAqB,EAAE,uBAAuB;IAC9C,mBAAmB,EAAE,kBAAkB;IACvCC,gBAAgB,EAAE,kBAAkB;IACpCC,QAAQ,EAAE,UAAU;IACpB,mBAAmB,EAAE,kBAAkB;IACvCC,gBAAgB,EAAE,kBAAkB;IACpCC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,cAAc;IAC5BC,yBAAyB,EAAE,2BAA2B;IACtD,cAAc,EAAE,aAAa;IAC7B,WAAW,EAAE,UAAU;IACvBC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,WAAW;IACtBC,WAAW,EAAE,aAAa;IAC1B,aAAa,EAAE,YAAY;IAC3B,eAAe,EAAE,cAAc;IAC/BC,UAAU,EAAE,YAAY;IACxBC,YAAY,EAAE,cAAc;IAC5B,aAAa,EAAE,YAAY;IAC3B,WAAW,EAAE,UAAU;IACvB,kBAAkB,EAAE,gBAAgB;IACpC,cAAc,EAAE,aAAa;IAC7B,YAAY,EAAE,WAAW;IACzB,cAAc,EAAE,aAAa;IAC7B,aAAa,EAAE,YAAY;IAC3BC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,UAAU;IACpBC,cAAc,EAAE,gBAAgB;IAChCC,WAAW,EAAE,aAAa;IAC1BC,SAAS,EAAE,WAAW;IACtBC,WAAW,EAAE,aAAa;IAC1BC,UAAU,EAAE,YAAY;IACxBC,GAAG,EAAE,SAAS;IACdC,aAAa,EAAE,eAAe;IAC9BC,UAAU,EAAE,YAAY;IACxBC,WAAW,EAAE,aAAa;IAC1BC,UAAU,EAAE,YAAY;IACxBC,cAAc,EAAE,gBAAgB;IAChCC,UAAU,EAAE,YAAY;IACxBC,WAAW,EAAE,aAAa;IAC1B,YAAY,EAAE,WAAW;IACzB,8BAA8B,EAAE,4BAA4B;IAC5D,4BAA4B,EAAE,0BAA0B;IACxDC,SAAS,EAAE,WAAW;IACtBC,0BAA0B,EAAE,4BAA4B;IACxDC,wBAAwB,EAAE,0BAA0B;IACpDC,QAAQ,EAAE,UAAU;IACpBC,iBAAiB,EAAE,mBAAmB;IACtCC,aAAa,EAAE,eAAe;IAC9B,aAAa,EAAE,WAAW;IAC1B,gBAAgB,EAAE,cAAc;IAChCC,SAAS,EAAE,WAAW;IACtBC,YAAY,EAAE,cAAc;IAC5BC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE,WAAW;IACzBC,SAAS,EAAE,WAAW;IACtB,iBAAiB,EAAE,gBAAgB;IACnCC,cAAc,EAAE,gBAAgB;IAChCC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,WAAW;IACtBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE,UAAU;IACpBC,YAAY,EAAE,cAAc;IAC5BC,gBAAgB,EAAE,kBAAkB;IACpCC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,WAAW;IACtBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,cAAc;IAC5B,gBAAgB,EAAE,eAAe;IACjCC,aAAa,EAAE,eAAe;IAC9B,gBAAgB,EAAE,eAAe;IACjCC,aAAa,EAAE,eAAe;IAC9BC,iBAAiB,EAAE,mBAAmB;IACtCC,YAAY,EAAE,cAAc;IAC5BC,WAAW,EAAE,aAAa;IAC1B,YAAY,EAAE,WAAW;IACzB,YAAY,EAAE,WAAW;IACzB,cAAc,EAAE,aAAa;IAC7BC,SAAS,EAAE,WAAW;IACtBC,YAAY,EAAE,cAAc;IAC5BC,SAAS,EAAE,WAAW;IACtBC,WAAW,EAAE,aAAa;IAC1BC,WAAW,EAAE,aAAa;IAC1BC,WAAW,EAAE,aAAa;IAC1BC,gBAAgB,EAAE,kBAAkB;IACpCC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,WAAW;IACtBC,UAAU,EAAE,YAAY;IACxBC,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,YAAY;IACxBC,UAAU,EAAE,YAAY;IACxB,mBAAmB,EAAE,kBAAkB;IACvC,oBAAoB,EAAE,mBAAmB;IACzCC,gBAAgB,EAAE,kBAAkB;IACpCC,iBAAiB,EAAE,mBAAmB;IACtC,aAAa,EAAE,YAAY;IAC3BC,UAAU,EAAE,YAAY;IACxB,UAAU,EAAE,SAAS;IACrBC,UAAU,EAAE,YAAY;IACxBC,mBAAmB,EAAE,qBAAqB;IAC1CC,gBAAgB,EAAE,kBAAkB;IACpCC,YAAY,EAAE,cAAc;IAC5BC,WAAW,EAAE,aAAa;IAC1B,gBAAgB,EAAE,eAAe;IACjCC,aAAa,EAAE,eAAe;IAC9BC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,WAAW;IACtBC,aAAa,EAAE,eAAe;IAC9BC,mBAAmB,EAAE,qBAAqB;IAC1CC,cAAc,EAAE,gBAAgB;IAChCC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,UAAU;IACpBC,cAAc,EAAE,gBAAgB;IAChCC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZ,kBAAkB,EAAE,iBAAiB;IACrCC,eAAe,EAAE,iBAAiB;IAClCC,WAAW,EAAE,aAAa;IAC1BC,SAAS,EAAE,WAAW;IACtBC,kBAAkB,EAAE,oBAAoB;IACxCC,gBAAgB,EAAE,kBAAkB;IACpCC,OAAO,EAAE,SAAS;IAClB,iBAAiB,EAAE,gBAAgB;IACnCC,cAAc,EAAE,gBAAgB;IAChCC,gBAAgB,EAAE,kBAAkB;IACpCC,gBAAgB,EAAE,kBAAkB;IACpCC,UAAU,EAAE,YAAY;IACxBC,YAAY,EAAE,cAAc;IAC5BC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,WAAW,EAAE,aAAa;IAC1BC,YAAY,EAAE,cAAc;IAC5BC,WAAW,EAAE,aAAa;IAC1B,YAAY,EAAE,WAAW;IACzB,cAAc,EAAE,aAAa;IAC7BC,SAAS,EAAE,WAAW;IACtBC,WAAW,EAAE,aAAa;IAC1B,wBAAwB,EAAE,uBAAuB;IACjD,yBAAyB,EAAE,wBAAwB;IACnDC,qBAAqB,EAAE,uBAAuB;IAC9CC,sBAAsB,EAAE,wBAAwB;IAChD,kBAAkB,EAAE,iBAAiB;IACrC,mBAAmB,EAAE,kBAAkB;IACvC,gBAAgB,EAAE,eAAe;IACjC,iBAAiB,EAAE,gBAAgB;IACnC,mBAAmB,EAAE,kBAAkB;IACvC,gBAAgB,EAAE,eAAe;IACjC,cAAc,EAAE,aAAa;IAC7BC,eAAe,EAAE,iBAAiB;IAClCC,gBAAgB,EAAE,kBAAkB;IACpCC,aAAa,EAAE,eAAe;IAC9BC,cAAc,EAAE,gBAAgB;IAChCC,gBAAgB,EAAE,kBAAkB;IACpCC,aAAa,EAAE,eAAe;IAC9BC,WAAW,EAAE,aAAa;IAC1BC,8BAA8B,EAAE,gCAAgC;IAChEC,wBAAwB,EAAE,0BAA0B;IACpDC,YAAY,EAAE,cAAc;IAC5BC,cAAc,EAAE,gBAAgB;IAChCC,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,aAAa;IAC1BC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,cAAc;IAC5B,aAAa,EAAE,YAAY;IAC3B,iBAAiB,EAAE,gBAAgB;IACnC,gBAAgB,EAAE,eAAe;IACjCC,UAAU,EAAE,YAAY;IACxBC,cAAc,EAAE,gBAAgB;IAChCC,UAAU,EAAE,YAAY;IACxBC,aAAa,EAAE,eAAe;IAC9B,oBAAoB,EAAE,mBAAmB;IACzC,qBAAqB,EAAE,oBAAoB;IAC3CC,iBAAiB,EAAE,mBAAmB;IACtCC,kBAAkB,EAAE,oBAAoB;IACxC,cAAc,EAAE,aAAa;IAC7B,eAAe,EAAE,cAAc;IAC/BC,WAAW,EAAE,aAAa;IAC1BC,YAAY,EAAE,cAAc;IAC5B,cAAc,EAAE,YAAY;IAC5BC,UAAU,EAAE,YAAY;IACxBC,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE,aAAa;IAC7B,WAAW,EAAE,UAAU;IACvB,eAAe,EAAE,cAAc;IAC/B,gBAAgB,EAAE,eAAe;IACjCC,WAAW,EAAE,aAAa;IAC1B,eAAe,EAAE,cAAc;IAC/BC,YAAY,EAAE,cAAc;IAC5B,YAAY,EAAE,UAAU;IACxB,eAAe,EAAE,aAAa;IAC9B,eAAe,EAAE,aAAa;IAC9BC,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,aAAa;IAC1BC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,UAAU;IACpBC,YAAY,EAAE,cAAc;IAC5BC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,YAAY;IACxBC,aAAa,EAAE,eAAe;IAC9B,cAAc,EAAE,aAAa;IAC7BC,WAAW,EAAE,aAAa;IAC1B,cAAc,EAAE,aAAa;IAC7BC,WAAW,EAAE,aAAa;IAC1B,UAAU,EAAE,SAAS;IACrBC,gBAAgB,EAAE,kBAAkB;IACpCC,OAAO,EAAE,SAAS;IAClB,eAAe,EAAE,cAAc;IAC/B,eAAe,EAAE,cAAc;IAC/B,YAAY,EAAE,WAAW;IACzB,YAAY,EAAE,WAAW;IACzB,YAAY,EAAE,WAAW;IACzB,aAAa,EAAE,YAAY;IAC3B,YAAY,EAAE,WAAW;IACzBC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,cAAc;IAC5BC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,WAAW;IACtBC,UAAU,EAAE,YAAY;IACxBC,SAAS,EAAE,WAAW;IACtB,UAAU,EAAE,SAAS;IACrB,UAAU,EAAE,SAAS;IACrB,WAAW,EAAE,UAAU;IACvBC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClB,aAAa,EAAE,YAAY;IAC3BC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,UAAU;IACpBC,gBAAgB,EAAE,kBAAkB;IACpCC,UAAU,EAAE;EACb,CAAC;AACF,CAAC,EAAIzN,MAAM,CAAE;;;;;;;;;;;AChTb;AACA;AACA;AACA;AACA;;AAEa;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;;AAEA;AACA;AACA,kBAAkB,QAAQ;AAC1B;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH,kCAAkC;AAClC;AACA;AACA;;AAEA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,iBAAiB,sBAAsB;AACvC;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,mBAAmB,oBAAoB;AACvC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;;;;;;;;;;ACzFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEa;;AAEb,IAAI,IAAqC;AACzC;AACA;;AAEA,cAAc,mBAAO,CAAC,4DAAe;;AAErC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB,kBAAkB;AAClB,gBAAgB;AAChB;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,EAAE,gBAAgB;AAClB,EAAE,kBAAkB;AACpB,EAAE,gBAAgB;AAClB;AACA;AACA;AACA,EAAE,gBAAgB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;;;AAGL;;AAEA;AACA,oBAAoB;;AAEpB;AACA;AACA,MAAM;;;AAGN;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,2FAA2F,aAAa;AACxG;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,+FAA+F,eAAe;AAC9G;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK,GAAG;;AAER,kDAAkD;AAClD;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,8MAA8M;;AAE9M;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA,aAAa,YAAY;AACzB,cAAc,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,YAAY;AACzB,aAAa,WAAW;AACxB,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,YAAY;AACzB,aAAa,QAAQ;AACrB,aAAa,WAAW;AACxB,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,YAAY;AACzB,aAAa,QAAQ;AACrB,aAAa,WAAW;AACxB,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA,0BAA0B;;AAE1B,2BAA2B;AAC3B;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,iBAAiB;AAC5B;AACA,WAAW,WAAW;AACtB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,WAAW;AACtB;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,0BAA0B;;AAE1B;AACA;AACA;;AAEA;AACA,oDAAoD;;AAEpD;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,GAAG;AACd,WAAW,GAAG;AACd,WAAW,eAAe;AAC1B,WAAW,GAAG;AACd,WAAW,GAAG;AACd;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK,GAAG;;AAER;AACA;AACA;AACA;AACA;AACA,KAAK,GAAG;AACR;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,gBAAgB;;AAEhB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,qEAAqE;;AAErE;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;;;AAGA;;AAEA;AACA;AACA,IAAI;AACJ;;AAEA,oBAAoB,oBAAoB;AACxC;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAI;;;AAGJ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,gBAAgB;;AAEhB,wBAAwB,kBAAkB;;;AAG1C;AACA,yBAAyB;;AAEzB,4BAA4B;AAC5B;AACA;;AAEA,gCAAgC;;AAEhC;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,MAAM;;;AAGN;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,IAAI;AACJ;;;AAGA;;AAEA;AACA;AACA,IAAI;AACJ;;AAEA,oBAAoB,oBAAoB;AACxC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,SAAS;AACpB,YAAY,SAAS;AACrB;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY,QAAQ;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,QAAQ;AACnB,YAAY;AACZ;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;;AAGJ;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,wCAAwC;AACxC;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,wBAAwB;;AAExB;;AAEA;AACA,oBAAoB,qBAAqB;AACzC;AACA;AACA;AACA;AACA,IAAI;AACJ;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;;AAEA;AACA;AACA,sIAAsI,yCAAyC;AAC/K;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,IAAI;AACf,WAAW,kBAAkB;AAC7B,WAAW,GAAG;AACd,YAAY,QAAQ;AACpB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,IAAI;AACf,YAAY,QAAQ;AACpB;;;AAGA;AACA;AACA;AACA,SAAS;AACT,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,IAAI;AACf,WAAW,kBAAkB;AAC7B,WAAW,GAAG;AACd;AACA;AACA;AACA,wCAAwC;AACxC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,SAAS;AACpB,YAAY,cAAc;AAC1B;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK,GAAG;;AAER;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,2BAA2B;;AAE3B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU;;;AAGV;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,mBAAmB;;AAEnB;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA,0CAA0C;AAC1C;;AAEA;AACA;AACA,WAAW;AACX;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA,oCAAoC;AACpC;;AAEA;AACA;AACA,WAAW;AACX;AACA;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;;AAEA,4BAA4B;;AAE5B;AACA;AACA;AACA,IAAI;;;AAGJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,MAAM;;;AAGN;AACA,0CAA0C;AAC1C;;AAEA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;;AAEvC;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA,uBAAuB;AACvB;AACA,SAAS;AACT,wBAAwB;AACxB;AACA,SAAS;AACT,wBAAwB;AACxB;AACA,SAAS;AACT,yBAAyB;AACzB;AACA,SAAS;AACT,yBAAyB;AACzB;AACA,SAAS;AACT,kCAAkC;AAClC;AACA,SAAS;AACT,4BAA4B;AAC5B;AACA,SAAS;AACT,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,MAAM;;;AAGN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,2DAA2D;;AAE3D;AACA;;AAEA;AACA,2DAA2D;AAC3D;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;;AAGT;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA,QAAQ;AACR;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,aAAa,kBAAkB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,kBAAkB;;;AAGlB;AACA;AACA,cAAc;AACd;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;;AAEA;AACA;AACA;AACA;;AAEA;AACA,IAAI;;;AAGJ;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,8BAA8B;AAC9B;AACA;;AAEA;AACA;AACA;AACA;AACA,2HAA2H;AAC3H;AACA;AACA;;AAEA;AACA,UAAU;AACV;AACA;;AAEA;AACA;;AAEA,oEAAoE;;AAEpE;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,GAAG;AACd;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,2DAA2D;AAC3D;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,WAAW;AACtB,WAAW,GAAG;AACd;;;AAGA;AACA;AACA;AACA;;AAEA;AACA,oBAAoB,iBAAiB;AACrC;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,IAAI;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN,4CAA4C;;AAE5C;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB;;;AAGA;AACA;AACA;;AAEA,oBAAoB,iBAAiB;AACrC;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,4CAA4C;AAC5C;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,MAAM;AACN;AACA;;AAEA;;AAEA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;;AAEA,sDAAsD;AACtD;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;;AAGA;AACA,oBAAoB,sBAAsB;AAC1C;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,MAAM;;;AAGN;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;;AAEA,kBAAkB,sBAAsB;AACxC;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA,uCAAuC;AACvC;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,gBAAgB;AAChB,iBAAiB;AACjB,qBAAqB;AACrB,0DAA0D;AAC1D,oBAAoB;AACpB,qBAAqB;AACrB,qBAAqB;AACrB,qBAAqB;AACrB,iBAAiB;AACjB,kBAAkB;AAClB,sBAAsB;AACtB,YAAY;AACZ,YAAY;AACZ,mBAAmB;AACnB,kBAAkB;AAClB,qBAAqB;AACrB,iBAAiB;AACjB,2BAA2B;AAC3B,uBAAuB;AACvB,eAAe;AACf,kBAAkB;AAClB,cAAc;AACd,gBAAgB;AAChB,eAAe;AACf,GAAG;AACH;;;;;;;;;;;;AC5xEa;;AAEb,IAAI,KAAqC,EAAE,EAE1C,CAAC;AACF,EAAE,uHAAsD;AACxD;;;;;;;;;;;;;;;;ACNe;AACf;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;AACA;AACA;;;;;;UCZA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA,eAAe,4BAA4B;WAC3C,eAAe;WACf,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA,8CAA8C;;;;;WCA9C;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;ACN6B", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/_acf-blocks-legacy.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/_acf-jsx-names.js", "webpack://advanced-custom-fields-pro/./node_modules/object-assign/index.js", "webpack://advanced-custom-fields-pro/./node_modules/react/cjs/react.development.js", "webpack://advanced-custom-fields-pro/./node_modules/react/index.js", "webpack://advanced-custom-fields-pro/./node_modules/@babel/runtime/helpers/esm/defineProperty.js", "webpack://advanced-custom-fields-pro/webpack/bootstrap", "webpack://advanced-custom-fields-pro/webpack/runtime/compat get default export", "webpack://advanced-custom-fields-pro/webpack/runtime/define property getters", "webpack://advanced-custom-fields-pro/webpack/runtime/hasOwnProperty shorthand", "webpack://advanced-custom-fields-pro/webpack/runtime/make namespace object", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/pro/acf-pro-blocks-legacy.js"], "sourcesContent": ["( function ( $, undefined ) {\n\t// Dependencies.\n\tconst { BlockControls, InspectorControls, InnerBlocks } = wp.blockEditor;\n\tconst { <PERSON><PERSON><PERSON>, IconButton, Placeholder, Spinner } = wp.components;\n\tconst { Fragment } = wp.element;\n\tconst { Component } = React;\n\tconst { withSelect } = wp.data;\n\tconst { createHigherOrderComponent } = wp.compose;\n\n\t/**\n\t * Storage for registered block types.\n\t *\n\t * @since 5.8.0\n\t * @var object\n\t */\n\tconst blockTypes = {};\n\n\t/**\n\t * Returns a block type for the given name.\n\t *\n\t * @date\t20/2/19\n\t * @since\t5.8.0\n\t *\n\t * @param\tstring name The block name.\n\t * @return\t(object|false)\n\t */\n\tfunction getBlockType( name ) {\n\t\treturn blockTypes[ name ] || false;\n\t}\n\n\t/**\n\t * Returns true if a block exists for the given name.\n\t *\n\t * @date\t20/2/19\n\t * @since\t5.8.0\n\t *\n\t * @param\tstring name The block name.\n\t * @return\tbool\n\t */\n\tfunction isBlockType( name ) {\n\t\treturn !! blockTypes[ name ];\n\t}\n\n\t/**\n\t * Returns true if the provided block is new.\n\t *\n\t * @date\t31/07/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tobject props The block props.\n\t * @return\tbool\n\t */\n\tfunction isNewBlock( props ) {\n\t\treturn ! props.attributes.id;\n\t}\n\n\t/**\n\t * Returns true if the provided block is a duplicate:\n\t * True when there are is another block with the same \"id\", but a different \"clientId\".\n\t *\n\t * @date\t31/07/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tobject props The block props.\n\t * @return\tbool\n\t */\n\tfunction isDuplicateBlock( props ) {\n\t\treturn getBlocks()\n\t\t\t.filter( ( block ) => block.attributes.id === props.attributes.id )\n\t\t\t.filter( ( block ) => block.clientId !== props.clientId ).length;\n\t}\n\n\t/**\n\t * Registers a block type.\n\t *\n\t * @date\t19/2/19\n\t * @since\t5.8.0\n\t *\n\t * @param\tobject blockType The block type settings localized from PHP.\n\t * @return\tobject The result from wp.blocks.registerBlockType().\n\t */\n\tfunction registerBlockType( blockType ) {\n\t\t// bail early if is excluded post_type.\n\t\tvar allowedTypes = blockType.post_types || [];\n\t\tif ( allowedTypes.length ) {\n\t\t\t// Always allow block to appear on \"Edit reusable Block\" screen.\n\t\t\tallowedTypes.push( 'wp_block' );\n\n\t\t\t// Check post type.\n\t\t\tvar postType = acf.get( 'postType' );\n\t\t\tif ( allowedTypes.indexOf( postType ) === -1 ) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\t// Handle svg HTML.\n\t\tif (\n\t\t\ttypeof blockType.icon === 'string' &&\n\t\t\tblockType.icon.substr( 0, 4 ) === '<svg'\n\t\t) {\n\t\t\tconst iconHTML = blockType.icon;\n\t\t\tblockType.icon = <Div>{ iconHTML }</Div>;\n\t\t}\n\n\t\t// Remove icon if empty to allow for default \"block\".\n\t\t// Avoids JS error preventing block from being registered.\n\t\tif ( ! blockType.icon ) {\n\t\t\tdelete blockType.icon;\n\t\t}\n\n\t\t// Check category exists and fallback to \"common\".\n\t\tvar category = wp.blocks\n\t\t\t.getCategories()\n\t\t\t.filter( ( cat ) => cat.slug === blockType.category )\n\t\t\t.pop();\n\t\tif ( ! category ) {\n\t\t\t//console.warn( `The block \"${blockType.name}\" is registered with an unknown category \"${blockType.category}\".` );\n\t\t\tblockType.category = 'common';\n\t\t}\n\n\t\t// Define block type attributes.\n\t\t// Leave default undefined to allow WP to serialize attributes in HTML comments.\n\t\t// See https://github.com/WordPress/gutenberg/issues/7342\n\t\tlet attributes = {\n\t\t\tid: {\n\t\t\t\ttype: 'string',\n\t\t\t},\n\t\t\tname: {\n\t\t\t\ttype: 'string',\n\t\t\t},\n\t\t\tdata: {\n\t\t\t\ttype: 'object',\n\t\t\t},\n\t\t\talign: {\n\t\t\t\ttype: 'string',\n\t\t\t},\n\t\t\tmode: {\n\t\t\t\ttype: 'string',\n\t\t\t},\n\t\t};\n\n\t\t// Append edit and save functions.\n\t\tlet ThisBlockEdit = BlockEdit;\n\t\tlet ThisBlockSave = BlockSave;\n\n\t\t// Apply align_text functionality.\n\t\tif ( blockType.supports.align_text ) {\n\t\t\tattributes = withAlignTextAttributes( attributes );\n\t\t\tThisBlockEdit = withAlignTextComponent( ThisBlockEdit, blockType );\n\t\t}\n\n\t\t// Apply align_content functionality.\n\t\tif ( blockType.supports.align_content ) {\n\t\t\tattributes = withAlignContentAttributes( attributes );\n\t\t\tThisBlockEdit = withAlignContentComponent(\n\t\t\t\tThisBlockEdit,\n\t\t\t\tblockType\n\t\t\t);\n\t\t}\n\n\t\t// Merge in block settings.\n\t\tblockType = acf.parseArgs( blockType, {\n\t\t\ttitle: '',\n\t\t\tname: '',\n\t\t\tcategory: '',\n\t\t\tattributes: attributes,\n\t\t\tedit: function ( props ) {\n\t\t\t\treturn <ThisBlockEdit { ...props } />;\n\t\t\t},\n\t\t\tsave: function ( props ) {\n\t\t\t\treturn <ThisBlockSave { ...props } />;\n\t\t\t},\n\t\t} );\n\n\t\t// Remove all attribute defaults from PHP values to allow serialisation.\n\t\t// https://github.com/WordPress/gutenberg/issues/7342\n\t\tfor ( const key in blockType.attributes ) {\n\t\t\tdelete blockType.attributes[ key ].default;\n\t\t}\n\n\t\t// Add to storage.\n\t\tblockTypes[ blockType.name ] = blockType;\n\n\t\t// Register with WP.\n\t\tvar result = wp.blocks.registerBlockType( blockType.name, blockType );\n\n\t\t// Fix bug in 'core/anchor/attribute' filter overwriting attribute.\n\t\t// See https://github.com/WordPress/gutenberg/issues/15240\n\t\tif ( result.attributes.anchor ) {\n\t\t\tresult.attributes.anchor = {\n\t\t\t\ttype: 'string',\n\t\t\t};\n\t\t}\n\n\t\t// Return result.\n\t\treturn result;\n\t}\n\n\t/**\n\t * Returns the wp.data.select() response with backwards compatibility.\n\t *\n\t * @date\t17/06/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tstring selector The selector name.\n\t * @return\tmixed\n\t */\n\tfunction select( selector ) {\n\t\tif ( selector === 'core/block-editor' ) {\n\t\t\treturn (\n\t\t\t\twp.data.select( 'core/block-editor' ) ||\n\t\t\t\twp.data.select( 'core/editor' )\n\t\t\t);\n\t\t}\n\t\treturn wp.data.select( selector );\n\t}\n\n\t/**\n\t * Returns the wp.data.dispatch() response with backwards compatibility.\n\t *\n\t * @date\t17/06/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tstring selector The selector name.\n\t * @return\tmixed\n\t */\n\tfunction dispatch( selector ) {\n\t\treturn wp.data.dispatch( selector );\n\t}\n\n\t/**\n\t * Returns an array of all blocks for the given args.\n\t *\n\t * @date\t27/2/19\n\t * @since\t5.7.13\n\t *\n\t * @param\tobject args An object of key=>value pairs used to filter results.\n\t * @return\tarray.\n\t */\n\tfunction getBlocks( args ) {\n\t\t// Get all blocks (avoid deprecated warning).\n\t\tlet blocks = select( 'core/block-editor' ).getBlocks();\n\n\t\t// Append innerBlocks.\n\t\tlet i = 0;\n\t\twhile ( i < blocks.length ) {\n\t\t\tblocks = blocks.concat( blocks[ i ].innerBlocks );\n\t\t\ti++;\n\t\t}\n\n\t\t// Loop over args and filter.\n\t\tfor ( var k in args ) {\n\t\t\tblocks = blocks.filter(\n\t\t\t\t( block ) => block.attributes[ k ] === args[ k ]\n\t\t\t);\n\t\t}\n\n\t\t// Return results.\n\t\treturn blocks;\n\t}\n\n\t// Data storage for AJAX requests.\n\tconst ajaxQueue = {};\n\n\t/**\n\t * Fetches a JSON result from the AJAX API.\n\t *\n\t * @date\t28/2/19\n\t * @since\t5.7.13\n\t *\n\t * @param\tobject block The block props.\n\t * @query\tobject The query args used in AJAX callback.\n\t * @return\tobject The AJAX promise.\n\t */\n\tfunction fetchBlock( args ) {\n\t\tconst { attributes = {}, query = {}, delay = 0 } = args;\n\n\t\t// Use storage or default data.\n\t\tconst { id } = attributes;\n\t\tconst data = ajaxQueue[ id ] || {\n\t\t\tquery: {},\n\t\t\ttimeout: false,\n\t\t\tpromise: $.Deferred(),\n\t\t};\n\n\t\t// Append query args to storage.\n\t\tdata.query = { ...data.query, ...query };\n\n\t\t// Set fresh timeout.\n\t\tclearTimeout( data.timeout );\n\t\tdata.timeout = setTimeout( function () {\n\t\t\t$.ajax( {\n\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\tdataType: 'json',\n\t\t\t\ttype: 'post',\n\t\t\t\tcache: false,\n\t\t\t\tdata: acf.prepareForAjax( {\n\t\t\t\t\taction: 'acf/ajax/fetch-block',\n\t\t\t\t\tblock: JSON.stringify( attributes ),\n\t\t\t\t\tquery: data.query,\n\t\t\t\t} ),\n\t\t\t} )\n\t\t\t\t.always( function () {\n\t\t\t\t\t// Clean up queue after AJAX request is complete.\n\t\t\t\t\tajaxQueue[ id ] = null;\n\t\t\t\t} )\n\t\t\t\t.done( function () {\n\t\t\t\t\tdata.promise.resolve.apply( this, arguments );\n\t\t\t\t} )\n\t\t\t\t.fail( function () {\n\t\t\t\t\tdata.promise.reject.apply( this, arguments );\n\t\t\t\t} );\n\t\t}, delay );\n\n\t\t// Update storage.\n\t\tajaxQueue[ id ] = data;\n\n\t\t// Return promise.\n\t\treturn data.promise;\n\t}\n\n\t/**\n\t * Returns true if both object are the same.\n\t *\n\t * @date\t19/05/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tobject obj1\n\t * @param\tobject obj2\n\t * @return\tbool\n\t */\n\tfunction compareObjects( obj1, obj2 ) {\n\t\treturn JSON.stringify( obj1 ) === JSON.stringify( obj2 );\n\t}\n\n\t/**\n\t * Converts HTML into a React element.\n\t *\n\t * @date\t19/05/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tstring html The HTML to convert.\n\t * @return\tobject Result of React.createElement().\n\t */\n\tacf.parseJSX = function ( html ) {\n\t\treturn parseNode( $( html )[ 0 ] );\n\t};\n\n\t/**\n\t * Converts a DOM node into a React element.\n\t *\n\t * @date\t19/05/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tDOM node The DOM node.\n\t * @return\tobject Result of React.createElement().\n\t */\n\tfunction parseNode( node ) {\n\t\t// Get node name.\n\t\tvar nodeName = parseNodeName( node.nodeName.toLowerCase() );\n\t\tif ( ! nodeName ) {\n\t\t\treturn null;\n\t\t}\n\n\t\t// Get node attributes in React friendly format.\n\t\tvar nodeAttrs = {};\n\t\tacf.arrayArgs( node.attributes )\n\t\t\t.map( parseNodeAttr )\n\t\t\t.forEach( function ( attr ) {\n\t\t\t\tnodeAttrs[ attr.name ] = attr.value;\n\t\t\t} );\n\n\t\t// Define args for React.createElement().\n\t\tvar args = [ nodeName, nodeAttrs ];\n\t\tacf.arrayArgs( node.childNodes ).forEach( function ( child ) {\n\t\t\tif ( child instanceof Text ) {\n\t\t\t\tvar text = child.textContent;\n\t\t\t\tif ( text ) {\n\t\t\t\t\targs.push( text );\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\targs.push( parseNode( child ) );\n\t\t\t}\n\t\t} );\n\n\t\t// Return element.\n\t\treturn React.createElement.apply( this, args );\n\t}\n\n\t/**\n\t * Converts a node or attribute name into it's JSX compliant name\n\t *\n\t * @date     05/07/2021\n\t * @since    5.9.8\n\t *\n\t * @param    string name The node or attribute name.\n\t * @returns  string\n\t */\n\tfunction getJSXName( name ) {\n\t\tvar replacement = acf.isget( acf, 'jsxNameReplacements', name );\n\t\tif ( replacement ) return replacement;\n\t\treturn name;\n\t}\n\n\t/**\n\t * Converts the given name into a React friendly name or component.\n\t *\n\t * @date\t19/05/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tstring name The node name in lowercase.\n\t * @return\tmixed\n\t */\n\tfunction parseNodeName( name ) {\n\t\tswitch ( name ) {\n\t\t\tcase 'innerblocks':\n\t\t\t\treturn InnerBlocks;\n\t\t\tcase 'script':\n\t\t\t\treturn Script;\n\t\t\tcase '#comment':\n\t\t\t\treturn null;\n\t\t\tdefault:\n\t\t\t\t// Replace names for JSX counterparts.\n\t\t\t\tname = getJSXName( name );\n\t\t}\n\t\treturn name;\n\t}\n\n\t/**\n\t * Converts the given attribute into a React friendly name and value object.\n\t *\n\t * @date\t19/05/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tobj nodeAttr The node attribute.\n\t * @return\tobj\n\t */\n\tfunction parseNodeAttr( nodeAttr ) {\n\t\tvar name = nodeAttr.name;\n\t\tvar value = nodeAttr.value;\n\t\tswitch ( name ) {\n\t\t\t// Class.\n\t\t\tcase 'class':\n\t\t\t\tname = 'className';\n\t\t\t\tbreak;\n\n\t\t\t// Style.\n\t\t\tcase 'style':\n\t\t\t\tvar css = {};\n\t\t\t\tvalue.split( ';' ).forEach( function ( s ) {\n\t\t\t\t\tvar pos = s.indexOf( ':' );\n\t\t\t\t\tif ( pos > 0 ) {\n\t\t\t\t\t\tvar ruleName = s.substr( 0, pos ).trim();\n\t\t\t\t\t\tvar ruleValue = s.substr( pos + 1 ).trim();\n\n\t\t\t\t\t\t// Rename core properties, but not CSS variables.\n\t\t\t\t\t\tif ( ruleName.charAt( 0 ) !== '-' ) {\n\t\t\t\t\t\t\truleName = acf.strCamelCase( ruleName );\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcss[ ruleName ] = ruleValue;\n\t\t\t\t\t}\n\t\t\t\t} );\n\t\t\t\tvalue = css;\n\t\t\t\tbreak;\n\n\t\t\t// Default.\n\t\t\tdefault:\n\t\t\t\t// No formatting needed for \"data-x\" attributes.\n\t\t\t\tif ( name.indexOf( 'data-' ) === 0 ) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\t// Replace names for JSX counterparts.\n\t\t\t\tname = getJSXName( name );\n\n\t\t\t\t// Convert JSON values.\n\t\t\t\tvar c1 = value.charAt( 0 );\n\t\t\t\tif ( c1 === '[' || c1 === '{' ) {\n\t\t\t\t\tvalue = JSON.parse( value );\n\t\t\t\t}\n\n\t\t\t\t// Convert bool values.\n\t\t\t\tif ( value === 'true' || value === 'false' ) {\n\t\t\t\t\tvalue = value === 'true';\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t}\n\t\treturn {\n\t\t\tname: name,\n\t\t\tvalue: value,\n\t\t};\n\t}\n\n\t/**\n\t * Higher Order Component used to set default block attribute values.\n\t *\n\t * By modifying block attributes directly, instead of defining defaults in registerBlockType(),\n\t * WordPress will include them always within the saved block serialized JSON.\n\t *\n\t * @date\t31/07/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tComponent BlockListBlock The BlockListBlock Component.\n\t * @return\tComponent\n\t */\n\tvar withDefaultAttributes = createHigherOrderComponent( function (\n\t\tBlockListBlock\n\t) {\n\t\treturn class WrappedBlockEdit extends Component {\n\t\t\tconstructor( props ) {\n\t\t\t\tsuper( props );\n\n\t\t\t\t// Extract vars.\n\t\t\t\tconst { name, attributes } = this.props;\n\n\t\t\t\t// Only run on ACF Blocks.\n\t\t\t\tconst blockType = getBlockType( name );\n\t\t\t\tif ( ! blockType ) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// Set unique ID and default attributes for newly added blocks.\n\t\t\t\tif ( isNewBlock( props ) ) {\n\t\t\t\t\tattributes.id = acf.uniqid( 'block_' );\n\t\t\t\t\tfor ( let attribute in blockType.attributes ) {\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tattributes[ attribute ] === undefined &&\n\t\t\t\t\t\t\tblockType[ attribute ] !== undefined\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tattributes[ attribute ] = blockType[ attribute ];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// Generate new ID for duplicated blocks.\n\t\t\t\tif ( isDuplicateBlock( props ) ) {\n\t\t\t\t\tattributes.id = acf.uniqid( 'block_' );\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\trender() {\n\t\t\t\treturn <BlockListBlock { ...this.props } />;\n\t\t\t}\n\t\t};\n\t},\n\t'withDefaultAttributes' );\n\twp.hooks.addFilter(\n\t\t'editor.BlockListBlock',\n\t\t'acf/with-default-attributes',\n\t\twithDefaultAttributes\n\t);\n\n\t/**\n\t * The BlockSave functional component.\n\t *\n\t * @date\t08/07/2020\n\t * @since\t5.9.0\n\t */\n\tfunction BlockSave() {\n\t\treturn <InnerBlocks.Content />;\n\t}\n\n\t/**\n\t * The BlockEdit component.\n\t *\n\t * @date\t19/2/19\n\t * @since\t5.7.12\n\t */\n\tclass BlockEdit extends Component {\n\t\tconstructor( props ) {\n\t\t\tsuper( props );\n\t\t\tthis.setup();\n\t\t}\n\n\t\tsetup() {\n\t\t\tconst { name, attributes } = this.props;\n\t\t\tconst blockType = getBlockType( name );\n\n\t\t\t// Restrict current mode.\n\t\t\tfunction restrictMode( modes ) {\n\t\t\t\tif ( modes.indexOf( attributes.mode ) === -1 ) {\n\t\t\t\t\tattributes.mode = modes[ 0 ];\n\t\t\t\t}\n\t\t\t}\n\t\t\tswitch ( blockType.mode ) {\n\t\t\t\tcase 'edit':\n\t\t\t\t\trestrictMode( [ 'edit', 'preview' ] );\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'preview':\n\t\t\t\t\trestrictMode( [ 'preview', 'edit' ] );\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\trestrictMode( [ 'auto' ] );\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\trender() {\n\t\t\tconst { name, attributes, setAttributes } = this.props;\n\t\t\tconst { mode } = attributes;\n\t\t\tconst blockType = getBlockType( name );\n\n\t\t\t// Show toggle only for edit/preview modes.\n\t\t\tlet showToggle = blockType.supports.mode;\n\t\t\tif ( mode === 'auto' ) {\n\t\t\t\tshowToggle = false;\n\t\t\t}\n\n\t\t\t// Configure toggle variables.\n\t\t\tconst toggleText =\n\t\t\t\tmode === 'preview'\n\t\t\t\t\t? acf.__( 'Switch to Edit' )\n\t\t\t\t\t: acf.__( 'Switch to Preview' );\n\t\t\tconst toggleIcon =\n\t\t\t\tmode === 'preview' ? 'edit' : 'welcome-view-site';\n\t\t\tfunction toggleMode() {\n\t\t\t\tsetAttributes( {\n\t\t\t\t\tmode: mode === 'preview' ? 'edit' : 'preview',\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\t// Return template.\n\t\t\treturn (\n\t\t\t\t<Fragment>\n\t\t\t\t\t<BlockControls>\n\t\t\t\t\t\t{ showToggle && (\n\t\t\t\t\t\t\t<Toolbar>\n\t\t\t\t\t\t\t\t<IconButton\n\t\t\t\t\t\t\t\t\tclassName=\"components-icon-button components-toolbar__control\"\n\t\t\t\t\t\t\t\t\tlabel={ toggleText }\n\t\t\t\t\t\t\t\t\ticon={ toggleIcon }\n\t\t\t\t\t\t\t\t\tonClick={ toggleMode }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</Toolbar>\n\t\t\t\t\t\t) }\n\t\t\t\t\t</BlockControls>\n\n\t\t\t\t\t<InspectorControls>\n\t\t\t\t\t\t{ mode === 'preview' && (\n\t\t\t\t\t\t\t<div className=\"acf-block-component acf-block-panel\">\n\t\t\t\t\t\t\t\t<BlockForm { ...this.props } />\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t) }\n\t\t\t\t\t</InspectorControls>\n\n\t\t\t\t\t<BlockBody { ...this.props } />\n\t\t\t\t</Fragment>\n\t\t\t);\n\t\t}\n\t}\n\n\t/**\n\t * The BlockBody component.\n\t *\n\t * @date\t19/2/19\n\t * @since\t5.7.12\n\t */\n\tclass _BlockBody extends Component {\n\t\trender() {\n\t\t\tconst { attributes, isSelected } = this.props;\n\t\t\tconst { mode } = attributes;\n\t\t\treturn (\n\t\t\t\t<div className=\"acf-block-component acf-block-body\">\n\t\t\t\t\t{ mode === 'auto' && isSelected ? (\n\t\t\t\t\t\t<BlockForm { ...this.props } />\n\t\t\t\t\t) : mode === 'auto' && ! isSelected ? (\n\t\t\t\t\t\t<BlockPreview { ...this.props } />\n\t\t\t\t\t) : mode === 'preview' ? (\n\t\t\t\t\t\t<BlockPreview { ...this.props } />\n\t\t\t\t\t) : (\n\t\t\t\t\t\t<BlockForm { ...this.props } />\n\t\t\t\t\t) }\n\t\t\t\t</div>\n\t\t\t);\n\t\t}\n\t}\n\n\t// Append blockIndex to component props.\n\tconst BlockBody = withSelect( function ( select, ownProps ) {\n\t\tconst { clientId } = ownProps;\n\t\t// Use optional rootClientId to allow discoverability of child blocks.\n\t\tconst rootClientId = select( 'core/block-editor' ).getBlockRootClientId(\n\t\t\tclientId\n\t\t);\n\t\tconst index = select( 'core/block-editor' ).getBlockIndex(\n\t\t\tclientId,\n\t\t\trootClientId\n\t\t);\n\t\treturn {\n\t\t\tindex,\n\t\t};\n\t} )( _BlockBody );\n\n\t/**\n\t * A react component to append HTMl.\n\t *\n\t * @date\t19/2/19\n\t * @since\t5.7.12\n\t *\n\t * @param\tstring children The html to insert.\n\t * @return\tvoid\n\t */\n\tclass Div extends Component {\n\t\trender() {\n\t\t\treturn (\n\t\t\t\t<div\n\t\t\t\t\tdangerouslySetInnerHTML={ { __html: this.props.children } }\n\t\t\t\t/>\n\t\t\t);\n\t\t}\n\t}\n\n\t/**\n\t * A react Component for inline scripts.\n\t *\n\t * This Component uses a combination of React references and jQuery to append the\n\t * inline <script> HTML each time the component is rendered.\n\t *\n\t * @date\t29/05/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\ttype Var Description.\n\t * @return\ttype Description.\n\t */\n\tclass Script extends Component {\n\t\trender() {\n\t\t\treturn <div ref={ ( el ) => ( this.el = el ) } />;\n\t\t}\n\t\tsetHTML( html ) {\n\t\t\t$( this.el ).html( `<script>${ html }</script>` );\n\t\t}\n\t\tcomponentDidUpdate() {\n\t\t\tthis.setHTML( this.props.children );\n\t\t}\n\t\tcomponentDidMount() {\n\t\t\tthis.setHTML( this.props.children );\n\t\t}\n\t}\n\n\t// Data storage for DynamicHTML components.\n\tconst store = {};\n\n\t/**\n\t * DynamicHTML Class.\n\t *\n\t * A react componenet to load and insert dynamic HTML.\n\t *\n\t * @date\t19/2/19\n\t * @since\t5.7.12\n\t *\n\t * @param\tvoid\n\t * @return\tvoid\n\t */\n\tclass DynamicHTML extends Component {\n\t\tconstructor( props ) {\n\t\t\tsuper( props );\n\n\t\t\t// Bind callbacks.\n\t\t\tthis.setRef = this.setRef.bind( this );\n\n\t\t\t// Define default props and call setup().\n\t\t\tthis.id = '';\n\t\t\tthis.el = false;\n\t\t\tthis.subscribed = true;\n\t\t\tthis.renderMethod = 'jQuery';\n\t\t\tthis.setup( props );\n\n\t\t\t// Load state.\n\t\t\tthis.loadState();\n\t\t}\n\n\t\tsetup( props ) {\n\t\t\t// Do nothing.\n\t\t}\n\n\t\tfetch() {\n\t\t\t// Do nothing.\n\t\t}\n\n\t\tloadState() {\n\t\t\tthis.state = store[ this.id ] || {};\n\t\t}\n\n\t\tsetState( state ) {\n\t\t\tstore[ this.id ] = { ...this.state, ...state };\n\n\t\t\t// Update component state if subscribed.\n\t\t\t// - Allows AJAX callback to update store without modifying state of an unmounted component.\n\t\t\tif ( this.subscribed ) {\n\t\t\t\tsuper.setState( state );\n\t\t\t}\n\t\t}\n\n\t\tsetHtml( html ) {\n\t\t\thtml = html ? html.trim() : '';\n\n\t\t\t// Bail early if html has not changed.\n\t\t\tif ( html === this.state.html ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Update state.\n\t\t\tvar state = {\n\t\t\t\thtml: html,\n\t\t\t};\n\t\t\tif ( this.renderMethod === 'jsx' ) {\n\t\t\t\tstate.jsx = acf.parseJSX( html );\n\t\t\t\tstate.$el = $( this.el );\n\t\t\t} else {\n\t\t\t\tstate.$el = $( html );\n\t\t\t}\n\t\t\tthis.setState( state );\n\t\t}\n\n\t\tsetRef( el ) {\n\t\t\tthis.el = el;\n\t\t}\n\n\t\trender() {\n\t\t\t// Render JSX.\n\t\t\tif ( this.state.jsx ) {\n\t\t\t\treturn <div ref={ this.setRef }>{ this.state.jsx }</div>;\n\t\t\t}\n\n\t\t\t// Return HTML.\n\t\t\treturn (\n\t\t\t\t<div ref={ this.setRef }>\n\t\t\t\t\t<Placeholder>\n\t\t\t\t\t\t<Spinner />\n\t\t\t\t\t</Placeholder>\n\t\t\t\t</div>\n\t\t\t);\n\t\t}\n\n\t\tshouldComponentUpdate( nextProps, nextState ) {\n\t\t\tif ( nextProps.index !== this.props.index ) {\n\t\t\t\tthis.componentWillMove();\n\t\t\t}\n\t\t\treturn nextState.html !== this.state.html;\n\t\t}\n\n\t\tdisplay( context ) {\n\t\t\t// This method is called after setting new HTML and the Component render.\n\t\t\t// The jQuery render method simply needs to move $el into place.\n\t\t\tif ( this.renderMethod === 'jQuery' ) {\n\t\t\t\tvar $el = this.state.$el;\n\t\t\t\tvar $prevParent = $el.parent();\n\t\t\t\tvar $thisParent = $( this.el );\n\n\t\t\t\t// Move $el into place.\n\t\t\t\t$thisParent.html( $el );\n\n\t\t\t\t// Special case for reusable blocks.\n\t\t\t\t// Multiple instances of the same reusable block share the same block id.\n\t\t\t\t// This causes all instances to share the same state (cool), which unfortunately\n\t\t\t\t// pulls $el back and forth between the last rendered reusable block.\n\t\t\t\t// This simple fix leaves a \"clone\" behind :)\n\t\t\t\tif (\n\t\t\t\t\t$prevParent.length &&\n\t\t\t\t\t$prevParent[ 0 ] !== $thisParent[ 0 ]\n\t\t\t\t) {\n\t\t\t\t\t$prevParent.html( $el.clone() );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Call context specific method.\n\t\t\tswitch ( context ) {\n\t\t\t\tcase 'append':\n\t\t\t\t\tthis.componentDidAppend();\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'remount':\n\t\t\t\t\tthis.componentDidRemount();\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tcomponentDidMount() {\n\t\t\t// Fetch on first load.\n\t\t\tif ( this.state.html === undefined ) {\n\t\t\t\t//console.log('componentDidMount', this.id);\n\t\t\t\tthis.fetch();\n\n\t\t\t\t// Or remount existing HTML.\n\t\t\t} else {\n\t\t\t\tthis.display( 'remount' );\n\t\t\t}\n\t\t}\n\n\t\tcomponentDidUpdate( prevProps, prevState ) {\n\t\t\t// HTML has changed.\n\t\t\tthis.display( 'append' );\n\t\t}\n\n\t\tcomponentDidAppend() {\n\t\t\tacf.doAction( 'append', this.state.$el );\n\t\t}\n\n\t\tcomponentWillUnmount() {\n\t\t\tacf.doAction( 'unmount', this.state.$el );\n\n\t\t\t// Unsubscribe this component from state.\n\t\t\tthis.subscribed = false;\n\t\t}\n\n\t\tcomponentDidRemount() {\n\t\t\tthis.subscribed = true;\n\n\t\t\t// Use setTimeout to avoid incorrect timing of events.\n\t\t\t// React will unmount and mount components in DOM order.\n\t\t\t// This means a new component can be mounted before an old one is unmounted.\n\t\t\t// ACF shares $el across new/old components which is un-React-like.\n\t\t\t// This timout ensures that unmounting occurs before remounting.\n\t\t\tsetTimeout( () => {\n\t\t\t\tacf.doAction( 'remount', this.state.$el );\n\t\t\t} );\n\t\t}\n\n\t\tcomponentWillMove() {\n\t\t\tacf.doAction( 'unmount', this.state.$el );\n\t\t\tsetTimeout( () => {\n\t\t\t\tacf.doAction( 'remount', this.state.$el );\n\t\t\t} );\n\t\t}\n\t}\n\n\t/**\n\t * BlockForm Class.\n\t *\n\t * A react componenet to handle the block form.\n\t *\n\t * @date\t19/2/19\n\t * @since\t5.7.12\n\t *\n\t * @param\tstring id the block id.\n\t * @return\tvoid\n\t */\n\tclass BlockForm extends DynamicHTML {\n\t\tsetup( props ) {\n\t\t\tthis.id = `BlockForm-${ props.attributes.id }`;\n\t\t}\n\n\t\tfetch() {\n\t\t\t// Extract props.\n\t\t\tconst { attributes } = this.props;\n\n\t\t\t// Request AJAX and update HTML on complete.\n\t\t\tfetchBlock( {\n\t\t\t\tattributes: attributes,\n\t\t\t\tquery: {\n\t\t\t\t\tform: true,\n\t\t\t\t},\n\t\t\t} ).done( ( json ) => {\n\t\t\t\tthis.setHtml( json.data.form );\n\t\t\t} );\n\t\t}\n\n\t\tcomponentDidAppend() {\n\t\t\tsuper.componentDidAppend();\n\n\t\t\t// Extract props.\n\t\t\tconst { attributes, setAttributes } = this.props;\n\t\t\tconst { $el } = this.state;\n\n\t\t\t// Callback for updating block data.\n\t\t\tfunction serializeData( silent = false ) {\n\t\t\t\tconst data = acf.serialize( $el, `acf-${ attributes.id }` );\n\t\t\t\tif ( silent ) {\n\t\t\t\t\tattributes.data = data;\n\t\t\t\t} else {\n\t\t\t\t\tsetAttributes( {\n\t\t\t\t\t\tdata: data,\n\t\t\t\t\t} );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Add events.\n\t\t\tvar timeout = false;\n\t\t\t$el.on( 'change keyup', function () {\n\t\t\t\tclearTimeout( timeout );\n\t\t\t\ttimeout = setTimeout( serializeData, 300 );\n\t\t\t} );\n\n\t\t\t// Ensure newly added block is saved with data.\n\t\t\t// Do it silently to avoid triggering a preview render.\n\t\t\tif ( ! attributes.data ) {\n\t\t\t\tserializeData( true );\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * BlockPreview Class.\n\t *\n\t * A react componenet to handle the block preview.\n\t *\n\t * @date\t19/2/19\n\t * @since\t5.7.12\n\t *\n\t * @param\tstring id the block id.\n\t * @return\tvoid\n\t */\n\tclass BlockPreview extends DynamicHTML {\n\t\tsetup( props ) {\n\t\t\tthis.id = `BlockPreview-${ props.attributes.id }`;\n\t\t\tvar blockType = getBlockType( props.name );\n\t\t\tif ( blockType.supports.jsx ) {\n\t\t\t\tthis.renderMethod = 'jsx';\n\t\t\t}\n\t\t\t//console.log('setup', this.id);\n\t\t}\n\n\t\tfetch( args = {} ) {\n\t\t\tconst { attributes = this.props.attributes, delay = 0 } = args;\n\n\t\t\t// Remember attributes used to fetch HTML.\n\t\t\tthis.setState( {\n\t\t\t\tprevAttributes: attributes,\n\t\t\t} );\n\n\t\t\t// Request AJAX and update HTML on complete.\n\t\t\tfetchBlock( {\n\t\t\t\tattributes: attributes,\n\t\t\t\tquery: {\n\t\t\t\t\tpreview: true,\n\t\t\t\t},\n\t\t\t\tdelay: delay,\n\t\t\t} ).done( ( json ) => {\n\t\t\t\tthis.setHtml( '<div class=\"acf-block-preview\">' + json.data.preview + '</div>' );\n\t\t\t} );\n\t\t}\n\n\t\tcomponentDidAppend() {\n\t\t\tsuper.componentDidAppend();\n\n\t\t\t// Extract props.\n\t\t\tconst { attributes } = this.props;\n\t\t\tconst { $el } = this.state;\n\n\t\t\t// Generate action friendly type.\n\t\t\tconst type = attributes.name.replace( 'acf/', '' );\n\n\t\t\t// Do action.\n\t\t\tacf.doAction( 'render_block_preview', $el, attributes );\n\t\t\tacf.doAction(\n\t\t\t\t`render_block_preview/type=${ type }`,\n\t\t\t\t$el,\n\t\t\t\tattributes\n\t\t\t);\n\t\t}\n\n\t\tshouldComponentUpdate( nextProps, nextState ) {\n\t\t\tconst nextAttributes = nextProps.attributes;\n\t\t\tconst thisAttributes = this.props.attributes;\n\n\t\t\t// Update preview if block data has changed.\n\t\t\tif ( ! compareObjects( nextAttributes, thisAttributes ) ) {\n\t\t\t\tlet delay = 0;\n\n\t\t\t\t// Delay fetch when editing className or anchor to simulate conscistent logic to custom fields.\n\t\t\t\tif ( nextAttributes.className !== thisAttributes.className ) {\n\t\t\t\t\tdelay = 300;\n\t\t\t\t}\n\t\t\t\tif ( nextAttributes.anchor !== thisAttributes.anchor ) {\n\t\t\t\t\tdelay = 300;\n\t\t\t\t}\n\n\t\t\t\tthis.fetch( {\n\t\t\t\t\tattributes: nextAttributes,\n\t\t\t\t\tdelay: delay,\n\t\t\t\t} );\n\t\t\t}\n\t\t\treturn super.shouldComponentUpdate( nextProps, nextState );\n\t\t}\n\n\t\tcomponentDidRemount() {\n\t\t\tsuper.componentDidRemount();\n\n\t\t\t// Update preview if data has changed since last render (changing from \"edit\" to \"preview\").\n\t\t\tif (\n\t\t\t\t! compareObjects(\n\t\t\t\t\tthis.state.prevAttributes,\n\t\t\t\t\tthis.props.attributes\n\t\t\t\t)\n\t\t\t) {\n\t\t\t\t//console.log('componentDidRemount', this.id);\n\t\t\t\tthis.fetch();\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Initializes ACF Blocks logic and registration.\n\t *\n\t * @since 5.9.0\n\t */\n\tfunction initialize() {\n\t\t// Add support for WordPress versions before 5.2.\n\t\tif ( ! wp.blockEditor ) {\n\t\t\twp.blockEditor = wp.editor;\n\t\t}\n\n\t\t// Register block types.\n\t\tvar blockTypes = acf.get( 'blockTypes' );\n\t\tif ( blockTypes ) {\n\t\t\tblockTypes.map( registerBlockType );\n\t\t}\n\t}\n\n\t// Run the initialize callback during the \"prepare\" action.\n\t// This ensures that all localized data is available and that blocks are registered before the WP editor has been instantiated.\n\tacf.addAction( 'prepare', initialize );\n\n\t/**\n\t * Returns a valid vertical alignment.\n\t *\n\t * @date\t07/08/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tstring align A vertical alignment.\n\t * @return\tstring\n\t */\n\tfunction validateVerticalAlignment( align ) {\n\t\tconst ALIGNMENTS = [ 'top', 'center', 'bottom' ];\n\t\tconst DEFAULT = 'top';\n\t\treturn ALIGNMENTS.includes( align ) ? align : DEFAULT;\n\t}\n\n\t/**\n\t * Returns a valid horizontal alignment.\n\t *\n\t * @date\t07/08/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tstring align A horizontal alignment.\n\t * @return\tstring\n\t */\n\tfunction validateHorizontalAlignment( align ) {\n\t\tconst ALIGNMENTS = [ 'left', 'center', 'right' ];\n\t\tconst DEFAULT = acf.get( 'rtl' ) ? 'right' : 'left';\n\t\treturn ALIGNMENTS.includes( align ) ? align : DEFAULT;\n\t}\n\n\t/**\n\t * Returns a valid matrix alignment.\n\t *\n\t * Written for \"upgrade-path\" compatibility from vertical alignment to matrix alignment.\n\t *\n\t * @date\t07/08/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tstring align A matrix alignment.\n\t * @return\tstring\n\t */\n\tfunction validateMatrixAlignment( align ) {\n\t\tconst DEFAULT = 'center center';\n\t\tif ( align ) {\n\t\t\tconst [ y, x ] = align.split( ' ' );\n\t\t\treturn (\n\t\t\t\tvalidateVerticalAlignment( y ) +\n\t\t\t\t' ' +\n\t\t\t\tvalidateHorizontalAlignment( x )\n\t\t\t);\n\t\t}\n\t\treturn DEFAULT;\n\t}\n\n\t// Dependencies.\n\tconst { AlignmentToolbar, BlockVerticalAlignmentToolbar } = wp.blockEditor;\n\tconst BlockAlignmentMatrixToolbar =\n\t\twp.blockEditor.__experimentalBlockAlignmentMatrixToolbar ||\n\t\twp.blockEditor.BlockAlignmentMatrixToolbar;\n\t// Gutenberg v10.x begins transition from Toolbar components to Control components.\n\tconst BlockAlignmentMatrixControl =\n\t\twp.blockEditor.__experimentalBlockAlignmentMatrixControl ||\n\t\twp.blockEditor.BlockAlignmentMatrixControl;\n\n\t/**\n\t * Appends extra attributes for block types that support align_content.\n\t *\n\t * @date\t08/07/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tobject attributes The block type attributes.\n\t * @return\tobject\n\t */\n\tfunction withAlignContentAttributes( attributes ) {\n\t\tattributes.align_content = {\n\t\t\ttype: 'string',\n\t\t};\n\t\treturn attributes;\n\t}\n\n\t/**\n\t * A higher order component adding align_content editing functionality.\n\t *\n\t * @date\t08/07/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tcomponent OriginalBlockEdit The original BlockEdit component.\n\t * @param\tobject blockType The block type settings.\n\t * @return\tcomponent\n\t */\n\tfunction withAlignContentComponent( OriginalBlockEdit, blockType ) {\n\t\t// Determine alignment vars\n\t\tlet type = blockType.supports.align_content;\n\t\tlet AlignmentComponent, validateAlignment;\n\t\tswitch ( type ) {\n\t\t\tcase 'matrix':\n\t\t\t\tAlignmentComponent =\n\t\t\t\t\tBlockAlignmentMatrixControl || BlockAlignmentMatrixToolbar;\n\t\t\t\tvalidateAlignment = validateMatrixAlignment;\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tAlignmentComponent = BlockVerticalAlignmentToolbar;\n\t\t\t\tvalidateAlignment = validateVerticalAlignment;\n\t\t\t\tbreak;\n\t\t}\n\n\t\t// Ensure alignment component exists.\n\t\tif ( AlignmentComponent === undefined ) {\n\t\t\tconsole.warn(\n\t\t\t\t`The \"${ type }\" alignment component was not found.`\n\t\t\t);\n\t\t\treturn OriginalBlockEdit;\n\t\t}\n\n\t\t// Ensure correct block attribute data is sent in intial preview AJAX request.\n\t\tblockType.align_content = validateAlignment( blockType.align_content );\n\n\t\t// Return wrapped component.\n\t\treturn class WrappedBlockEdit extends Component {\n\t\t\trender() {\n\t\t\t\tconst { attributes, setAttributes } = this.props;\n\t\t\t\tconst { align_content } = attributes;\n\t\t\t\tfunction onChangeAlignContent( align_content ) {\n\t\t\t\t\tsetAttributes( {\n\t\t\t\t\t\talign_content: validateAlignment( align_content ),\n\t\t\t\t\t} );\n\t\t\t\t}\n\t\t\t\treturn (\n\t\t\t\t\t<Fragment>\n\t\t\t\t\t\t<BlockControls group=\"block\">\n\t\t\t\t\t\t\t<AlignmentComponent\n\t\t\t\t\t\t\t\tlabel={ acf.__( 'Change content alignment' ) }\n\t\t\t\t\t\t\t\tvalue={ validateAlignment( align_content ) }\n\t\t\t\t\t\t\t\tonChange={ onChangeAlignContent }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</BlockControls>\n\t\t\t\t\t\t<OriginalBlockEdit { ...this.props } />\n\t\t\t\t\t</Fragment>\n\t\t\t\t);\n\t\t\t}\n\t\t};\n\t}\n\n\t/**\n\t * Appends extra attributes for block types that support align_text.\n\t *\n\t * @date\t08/07/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tobject attributes The block type attributes.\n\t * @return\tobject\n\t */\n\tfunction withAlignTextAttributes( attributes ) {\n\t\tattributes.align_text = {\n\t\t\ttype: 'string',\n\t\t};\n\t\treturn attributes;\n\t}\n\n\t/**\n\t * A higher order component adding align_text editing functionality.\n\t *\n\t * @date\t08/07/2020\n\t * @since\t5.9.0\n\t *\n\t * @param\tcomponent OriginalBlockEdit The original BlockEdit component.\n\t * @param\tobject blockType The block type settings.\n\t * @return\tcomponent\n\t */\n\tfunction withAlignTextComponent( OriginalBlockEdit, blockType ) {\n\t\tconst validateAlignment = validateHorizontalAlignment;\n\n\t\t// Ensure correct block attribute data is sent in intial preview AJAX request.\n\t\tblockType.align_text = validateAlignment( blockType.align_text );\n\n\t\t// Return wrapped component.\n\t\treturn class WrappedBlockEdit extends Component {\n\t\t\trender() {\n\t\t\t\tconst { attributes, setAttributes } = this.props;\n\t\t\t\tconst { align_text } = attributes;\n\n\t\t\t\tfunction onChangeAlignText( align_text ) {\n\t\t\t\t\tsetAttributes( {\n\t\t\t\t\t\talign_text: validateAlignment( align_text ),\n\t\t\t\t\t} );\n\t\t\t\t}\n\n\t\t\t\treturn (\n\t\t\t\t\t<Fragment>\n\t\t\t\t\t\t<BlockControls>\n\t\t\t\t\t\t\t<AlignmentToolbar\n\t\t\t\t\t\t\t\tvalue={ validateAlignment( align_text ) }\n\t\t\t\t\t\t\t\tonChange={ onChangeAlignText }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</BlockControls>\n\t\t\t\t\t\t<OriginalBlockEdit { ...this.props } />\n\t\t\t\t\t</Fragment>\n\t\t\t\t);\n\t\t\t}\n\t\t};\n\t}\n} )( jQuery );\n", "( function ( $, undefined ) {\n\tacf.jsxNameReplacements = {\n\t\t'accent-height': 'accentHeight',\n\t\taccentheight: 'accentHeight',\n\t\t'accept-charset': 'acceptCharset',\n\t\tacceptcharset: 'acceptCharset',\n\t\taccesskey: 'accessKey',\n\t\t'alignment-baseline': 'alignmentBaseline',\n\t\talignmentbaseline: 'alignmentBaseline',\n\t\tallowedblocks: 'allowedBlocks',\n\t\tallowfullscreen: 'allowFullScreen',\n\t\tallowreorder: 'allowReorder',\n\t\t'arabic-form': 'arabicForm',\n\t\tarabicform: 'arabicForm',\n\t\tattributename: 'attributeName',\n\t\tattributetype: 'attributeType',\n\t\tautocapitalize: 'autoCapitalize',\n\t\tautocomplete: 'autoComplete',\n\t\tautocorrect: 'autoCorrect',\n\t\tautofocus: 'autoFocus',\n\t\tautoplay: 'autoPlay',\n\t\tautoreverse: 'autoReverse',\n\t\tautosave: 'autoSave',\n\t\tbasefrequency: 'baseFrequency',\n\t\t'baseline-shift': 'baselineShift',\n\t\tbaselineshift: 'baselineShift',\n\t\tbaseprofile: 'baseProfile',\n\t\tcalcmode: 'calcMode',\n\t\t'cap-height': 'capHeight',\n\t\tcapheight: 'capHeight',\n\t\tcellpadding: 'cellPadding',\n\t\tcellspacing: 'cellSpacing',\n\t\tcharset: 'charSet',\n\t\tclass: 'className',\n\t\tclassid: 'classID',\n\t\tclassname: 'className',\n\t\t'clip-path': 'clipPath',\n\t\t'clip-rule': 'clipRule',\n\t\tclippath: 'clipPath',\n\t\tclippathunits: 'clipPathUnits',\n\t\tcliprule: 'clipRule',\n\t\t'color-interpolation': 'colorInterpolation',\n\t\t'color-interpolation-filters': 'colorInterpolationFilters',\n\t\t'color-profile': 'colorProfile',\n\t\t'color-rendering': 'colorRendering',\n\t\tcolorinterpolation: 'colorInterpolation',\n\t\tcolorinterpolationfilters: 'colorInterpolationFilters',\n\t\tcolorprofile: 'colorProfile',\n\t\tcolorrendering: 'colorRendering',\n\t\tcolspan: 'colSpan',\n\t\tcontenteditable: 'contentEditable',\n\t\tcontentscripttype: 'contentScriptType',\n\t\tcontentstyletype: 'contentStyleType',\n\t\tcontextmenu: 'contextMenu',\n\t\tcontrolslist: 'controlsList',\n\t\tcrossorigin: 'crossOrigin',\n\t\tdangerouslysetinnerhtml: 'dangerouslySetInnerHTML',\n\t\tdatetime: 'dateTime',\n\t\tdefaultchecked: 'defaultChecked',\n\t\tdefaultvalue: 'defaultValue',\n\t\tdiffuseconstant: 'diffuseConstant',\n\t\tdisablepictureinpicture: 'disablePictureInPicture',\n\t\tdisableremoteplayback: 'disableRemotePlayback',\n\t\t'dominant-baseline': 'dominantBaseline',\n\t\tdominantbaseline: 'dominantBaseline',\n\t\tedgemode: 'edgeMode',\n\t\t'enable-background': 'enableBackground',\n\t\tenablebackground: 'enableBackground',\n\t\tenctype: 'encType',\n\t\tenterkeyhint: 'enterKeyHint',\n\t\texternalresourcesrequired: 'externalResourcesRequired',\n\t\t'fill-opacity': 'fillOpacity',\n\t\t'fill-rule': 'fillRule',\n\t\tfillopacity: 'fillOpacity',\n\t\tfillrule: 'fillRule',\n\t\tfilterres: 'filterRes',\n\t\tfilterunits: 'filterUnits',\n\t\t'flood-color': 'floodColor',\n\t\t'flood-opacity': 'floodOpacity',\n\t\tfloodcolor: 'floodColor',\n\t\tfloodopacity: 'floodOpacity',\n\t\t'font-family': 'fontFamily',\n\t\t'font-size': 'fontSize',\n\t\t'font-size-adjust': 'fontSizeAdjust',\n\t\t'font-stretch': 'fontStretch',\n\t\t'font-style': 'fontStyle',\n\t\t'font-variant': 'fontVariant',\n\t\t'font-weight': 'fontWeight',\n\t\tfontfamily: 'fontFamily',\n\t\tfontsize: 'fontSize',\n\t\tfontsizeadjust: 'fontSizeAdjust',\n\t\tfontstretch: 'fontStretch',\n\t\tfontstyle: 'fontStyle',\n\t\tfontvariant: 'fontVariant',\n\t\tfontweight: 'fontWeight',\n\t\tfor: 'htmlFor',\n\t\tforeignobject: 'foreignObject',\n\t\tformaction: 'formAction',\n\t\tformenctype: 'formEncType',\n\t\tformmethod: 'formMethod',\n\t\tformnovalidate: 'formNoValidate',\n\t\tformtarget: 'formTarget',\n\t\tframeborder: 'frameBorder',\n\t\t'glyph-name': 'glyphName',\n\t\t'glyph-orientation-horizontal': 'glyphOrientationHorizontal',\n\t\t'glyph-orientation-vertical': 'glyphOrientationVertical',\n\t\tglyphname: 'glyphName',\n\t\tglyphorientationhorizontal: 'glyphOrientationHorizontal',\n\t\tglyphorientationvertical: 'glyphOrientationVertical',\n\t\tglyphref: 'glyphRef',\n\t\tgradienttransform: 'gradientTransform',\n\t\tgradientunits: 'gradientUnits',\n\t\t'horiz-adv-x': 'horizAdvX',\n\t\t'horiz-origin-x': 'horizOriginX',\n\t\thorizadvx: 'horizAdvX',\n\t\thorizoriginx: 'horizOriginX',\n\t\threflang: 'hrefLang',\n\t\thtmlfor: 'htmlFor',\n\t\t'http-equiv': 'httpEquiv',\n\t\thttpequiv: 'httpEquiv',\n\t\t'image-rendering': 'imageRendering',\n\t\timagerendering: 'imageRendering',\n\t\tinnerhtml: 'innerHTML',\n\t\tinputmode: 'inputMode',\n\t\titemid: 'itemID',\n\t\titemprop: 'itemProp',\n\t\titemref: 'itemRef',\n\t\titemscope: 'itemScope',\n\t\titemtype: 'itemType',\n\t\tkernelmatrix: 'kernelMatrix',\n\t\tkernelunitlength: 'kernelUnitLength',\n\t\tkeyparams: 'keyParams',\n\t\tkeypoints: 'keyPoints',\n\t\tkeysplines: 'keySplines',\n\t\tkeytimes: 'keyTimes',\n\t\tkeytype: 'keyType',\n\t\tlengthadjust: 'lengthAdjust',\n\t\t'letter-spacing': 'letterSpacing',\n\t\tletterspacing: 'letterSpacing',\n\t\t'lighting-color': 'lightingColor',\n\t\tlightingcolor: 'lightingColor',\n\t\tlimitingconeangle: 'limitingConeAngle',\n\t\tmarginheight: 'marginHeight',\n\t\tmarginwidth: 'marginWidth',\n\t\t'marker-end': 'markerEnd',\n\t\t'marker-mid': 'markerMid',\n\t\t'marker-start': 'markerStart',\n\t\tmarkerend: 'markerEnd',\n\t\tmarkerheight: 'markerHeight',\n\t\tmarkermid: 'markerMid',\n\t\tmarkerstart: 'markerStart',\n\t\tmarkerunits: 'markerUnits',\n\t\tmarkerwidth: 'markerWidth',\n\t\tmaskcontentunits: 'maskContentUnits',\n\t\tmaskunits: 'maskUnits',\n\t\tmaxlength: 'maxLength',\n\t\tmediagroup: 'mediaGroup',\n\t\tminlength: 'minLength',\n\t\tnomodule: 'noModule',\n\t\tnovalidate: 'noValidate',\n\t\tnumoctaves: 'numOctaves',\n\t\t'overline-position': 'overlinePosition',\n\t\t'overline-thickness': 'overlineThickness',\n\t\toverlineposition: 'overlinePosition',\n\t\toverlinethickness: 'overlineThickness',\n\t\t'paint-order': 'paintOrder',\n\t\tpaintorder: 'paintOrder',\n\t\t'panose-1': 'panose1',\n\t\tpathlength: 'pathLength',\n\t\tpatterncontentunits: 'patternContentUnits',\n\t\tpatterntransform: 'patternTransform',\n\t\tpatternunits: 'patternUnits',\n\t\tplaysinline: 'playsInline',\n\t\t'pointer-events': 'pointerEvents',\n\t\tpointerevents: 'pointerEvents',\n\t\tpointsatx: 'pointsAtX',\n\t\tpointsaty: 'pointsAtY',\n\t\tpointsatz: 'pointsAtZ',\n\t\tpreservealpha: 'preserveAlpha',\n\t\tpreserveaspectratio: 'preserveAspectRatio',\n\t\tprimitiveunits: 'primitiveUnits',\n\t\tradiogroup: 'radioGroup',\n\t\treadonly: 'readOnly',\n\t\treferrerpolicy: 'referrerPolicy',\n\t\trefx: 'refX',\n\t\trefy: 'refY',\n\t\t'rendering-intent': 'renderingIntent',\n\t\trenderingintent: 'renderingIntent',\n\t\trepeatcount: 'repeatCount',\n\t\trepeatdur: 'repeatDur',\n\t\trequiredextensions: 'requiredExtensions',\n\t\trequiredfeatures: 'requiredFeatures',\n\t\trowspan: 'rowSpan',\n\t\t'shape-rendering': 'shapeRendering',\n\t\tshaperendering: 'shapeRendering',\n\t\tspecularconstant: 'specularConstant',\n\t\tspecularexponent: 'specularExponent',\n\t\tspellcheck: 'spellCheck',\n\t\tspreadmethod: 'spreadMethod',\n\t\tsrcdoc: 'srcDoc',\n\t\tsrclang: 'srcLang',\n\t\tsrcset: 'srcSet',\n\t\tstartoffset: 'startOffset',\n\t\tstddeviation: 'stdDeviation',\n\t\tstitchtiles: 'stitchTiles',\n\t\t'stop-color': 'stopColor',\n\t\t'stop-opacity': 'stopOpacity',\n\t\tstopcolor: 'stopColor',\n\t\tstopopacity: 'stopOpacity',\n\t\t'strikethrough-position': 'strikethroughPosition',\n\t\t'strikethrough-thickness': 'strikethroughThickness',\n\t\tstrikethroughposition: 'strikethroughPosition',\n\t\tstrikethroughthickness: 'strikethroughThickness',\n\t\t'stroke-dasharray': 'strokeDasharray',\n\t\t'stroke-dashoffset': 'strokeDashoffset',\n\t\t'stroke-linecap': 'strokeLinecap',\n\t\t'stroke-linejoin': 'strokeLinejoin',\n\t\t'stroke-miterlimit': 'strokeMiterlimit',\n\t\t'stroke-opacity': 'strokeOpacity',\n\t\t'stroke-width': 'strokeWidth',\n\t\tstrokedasharray: 'strokeDasharray',\n\t\tstrokedashoffset: 'strokeDashoffset',\n\t\tstrokelinecap: 'strokeLinecap',\n\t\tstrokelinejoin: 'strokeLinejoin',\n\t\tstrokemiterlimit: 'strokeMiterlimit',\n\t\tstrokeopacity: 'strokeOpacity',\n\t\tstrokewidth: 'strokeWidth',\n\t\tsuppresscontenteditablewarning: 'suppressContentEditableWarning',\n\t\tsuppresshydrationwarning: 'suppressHydrationWarning',\n\t\tsurfacescale: 'surfaceScale',\n\t\tsystemlanguage: 'systemLanguage',\n\t\ttabindex: 'tabIndex',\n\t\ttablevalues: 'tableValues',\n\t\ttargetx: 'targetX',\n\t\ttargety: 'targetY',\n\t\ttemplatelock: 'templateLock',\n\t\t'text-anchor': 'textAnchor',\n\t\t'text-decoration': 'textDecoration',\n\t\t'text-rendering': 'textRendering',\n\t\ttextanchor: 'textAnchor',\n\t\ttextdecoration: 'textDecoration',\n\t\ttextlength: 'textLength',\n\t\ttextrendering: 'textRendering',\n\t\t'underline-position': 'underlinePosition',\n\t\t'underline-thickness': 'underlineThickness',\n\t\tunderlineposition: 'underlinePosition',\n\t\tunderlinethickness: 'underlineThickness',\n\t\t'unicode-bidi': 'unicodeBidi',\n\t\t'unicode-range': 'unicodeRange',\n\t\tunicodebidi: 'unicodeBidi',\n\t\tunicoderange: 'unicodeRange',\n\t\t'units-per-em': 'unitsPerEm',\n\t\tunitsperem: 'unitsPerEm',\n\t\tusemap: 'useMap',\n\t\t'v-alphabetic': 'vAlphabetic',\n\t\t'v-hanging': 'vHanging',\n\t\t'v-ideographic': 'vIdeographic',\n\t\t'v-mathematical': 'vMathematical',\n\t\tvalphabetic: 'vAlphabetic',\n\t\t'vector-effect': 'vectorEffect',\n\t\tvectoreffect: 'vectorEffect',\n\t\t'vert-adv-y': 'vertAdvY',\n\t\t'vert-origin-x': 'vertOriginX',\n\t\t'vert-origin-y': 'vertOriginY',\n\t\tvertadvy: 'vertAdvY',\n\t\tvertoriginx: 'vertOriginX',\n\t\tvertoriginy: 'vertOriginY',\n\t\tvhanging: 'vHanging',\n\t\tvideographic: 'vIdeographic',\n\t\tviewbox: 'viewBox',\n\t\tviewtarget: 'viewTarget',\n\t\tvmathematical: 'vMathematical',\n\t\t'word-spacing': 'wordSpacing',\n\t\twordspacing: 'wordSpacing',\n\t\t'writing-mode': 'writingMode',\n\t\twritingmode: 'writingMode',\n\t\t'x-height': 'xHeight',\n\t\txchannelselector: 'xChannelSelector',\n\t\txheight: 'xHeight',\n\t\t'xlink:actuate': 'xlinkActuate',\n\t\t'xlink:arcrole': 'xlinkArcrole',\n\t\t'xlink:href': 'xlinkHref',\n\t\t'xlink:role': 'xlinkRole',\n\t\t'xlink:show': 'xlinkShow',\n\t\t'xlink:title': 'xlinkTitle',\n\t\t'xlink:type': 'xlinkType',\n\t\txlinkactuate: 'xlinkActuate',\n\t\txlinkarcrole: 'xlinkArcrole',\n\t\txlinkhref: 'xlinkHref',\n\t\txlinkrole: 'xlinkRole',\n\t\txlinkshow: 'xlinkShow',\n\t\txlinktitle: 'xlinkTitle',\n\t\txlinktype: 'xlinkType',\n\t\t'xml:base': 'xmlBase',\n\t\t'xml:lang': 'xmlLang',\n\t\t'xml:space': 'xmlSpace',\n\t\txmlbase: 'xmlBase',\n\t\txmllang: 'xmlLang',\n\t\t'xmlns:xlink': 'xmlnsXlink',\n\t\txmlnsxlink: 'xmlnsXlink',\n\t\txmlspace: 'xmlSpace',\n\t\tychannelselector: 'yChannelSelector',\n\t\tzoomandpan: 'zoomAndPan',\n\t};\n} )( jQuery );\n", "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "/** @license React v17.0.2\n * react.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\nvar _assign = require('object-assign');\n\n// TODO: this is special because it gets imported during build.\nvar ReactVersion = '17.0.2';\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar REACT_ELEMENT_TYPE = 0xeac7;\nvar REACT_PORTAL_TYPE = 0xeaca;\nexports.Fragment = 0xeacb;\nexports.StrictMode = 0xeacc;\nexports.Profiler = 0xead2;\nvar REACT_PROVIDER_TYPE = 0xeacd;\nvar REACT_CONTEXT_TYPE = 0xeace;\nvar REACT_FORWARD_REF_TYPE = 0xead0;\nexports.Suspense = 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = 0xead8;\nvar REACT_MEMO_TYPE = 0xead3;\nvar REACT_LAZY_TYPE = 0xead4;\nvar REACT_BLOCK_TYPE = 0xead9;\nvar REACT_SERVER_BLOCK_TYPE = 0xeada;\nvar REACT_FUNDAMENTAL_TYPE = 0xead5;\nvar REACT_SCOPE_TYPE = 0xead7;\nvar REACT_OPAQUE_ID_TYPE = 0xeae0;\nvar REACT_DEBUG_TRACING_MODE_TYPE = 0xeae1;\nvar REACT_OFFSCREEN_TYPE = 0xeae2;\nvar REACT_LEGACY_HIDDEN_TYPE = 0xeae3;\n\nif (typeof Symbol === 'function' && Symbol.for) {\n  var symbolFor = Symbol.for;\n  REACT_ELEMENT_TYPE = symbolFor('react.element');\n  REACT_PORTAL_TYPE = symbolFor('react.portal');\n  exports.Fragment = symbolFor('react.fragment');\n  exports.StrictMode = symbolFor('react.strict_mode');\n  exports.Profiler = symbolFor('react.profiler');\n  REACT_PROVIDER_TYPE = symbolFor('react.provider');\n  REACT_CONTEXT_TYPE = symbolFor('react.context');\n  REACT_FORWARD_REF_TYPE = symbolFor('react.forward_ref');\n  exports.Suspense = symbolFor('react.suspense');\n  REACT_SUSPENSE_LIST_TYPE = symbolFor('react.suspense_list');\n  REACT_MEMO_TYPE = symbolFor('react.memo');\n  REACT_LAZY_TYPE = symbolFor('react.lazy');\n  REACT_BLOCK_TYPE = symbolFor('react.block');\n  REACT_SERVER_BLOCK_TYPE = symbolFor('react.server.block');\n  REACT_FUNDAMENTAL_TYPE = symbolFor('react.fundamental');\n  REACT_SCOPE_TYPE = symbolFor('react.scope');\n  REACT_OPAQUE_ID_TYPE = symbolFor('react.opaque.id');\n  REACT_DEBUG_TRACING_MODE_TYPE = symbolFor('react.debug_trace_mode');\n  REACT_OFFSCREEN_TYPE = symbolFor('react.offscreen');\n  REACT_LEGACY_HIDDEN_TYPE = symbolFor('react.legacy_hidden');\n}\n\nvar MAYBE_ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\n/**\n * Keeps track of the current dispatcher.\n */\nvar ReactCurrentDispatcher = {\n  /**\n   * @internal\n   * @type {ReactComponent}\n   */\n  current: null\n};\n\n/**\n * Keeps track of the current batch's configuration such as how long an update\n * should suspend for if it needs to.\n */\nvar ReactCurrentBatchConfig = {\n  transition: 0\n};\n\n/**\n * Keeps track of the current owner.\n *\n * The current owner is the component who should own any components that are\n * currently being constructed.\n */\nvar ReactCurrentOwner = {\n  /**\n   * @internal\n   * @type {ReactComponent}\n   */\n  current: null\n};\n\nvar ReactDebugCurrentFrame = {};\nvar currentExtraStackFrame = null;\nfunction setExtraStackFrame(stack) {\n  {\n    currentExtraStackFrame = stack;\n  }\n}\n\n{\n  ReactDebugCurrentFrame.setExtraStackFrame = function (stack) {\n    {\n      currentExtraStackFrame = stack;\n    }\n  }; // Stack implementation injected by the current renderer.\n\n\n  ReactDebugCurrentFrame.getCurrentStack = null;\n\n  ReactDebugCurrentFrame.getStackAddendum = function () {\n    var stack = ''; // Add an extra top frame while an element is being validated\n\n    if (currentExtraStackFrame) {\n      stack += currentExtraStackFrame;\n    } // Delegate to the injected renderer-specific implementation\n\n\n    var impl = ReactDebugCurrentFrame.getCurrentStack;\n\n    if (impl) {\n      stack += impl() || '';\n    }\n\n    return stack;\n  };\n}\n\n/**\n * Used by act() to track whether you're inside an act() scope.\n */\nvar IsSomeRendererActing = {\n  current: false\n};\n\nvar ReactSharedInternals = {\n  ReactCurrentDispatcher: ReactCurrentDispatcher,\n  ReactCurrentBatchConfig: ReactCurrentBatchConfig,\n  ReactCurrentOwner: ReactCurrentOwner,\n  IsSomeRendererActing: IsSomeRendererActing,\n  // Used by renderers to avoid bundling object-assign twice in UMD bundles:\n  assign: _assign\n};\n\n{\n  ReactSharedInternals.ReactDebugCurrentFrame = ReactDebugCurrentFrame;\n}\n\n// by calls to these methods by a Babel plugin.\n//\n// In PROD (or in packages without access to React internals),\n// they are left as they are instead.\n\nfunction warn(format) {\n  {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    printWarning('warn', format, args);\n  }\n}\nfunction error(format) {\n  {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n\n    printWarning('error', format, args);\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    }\n\n    var argsWithFormat = args.map(function (item) {\n      return '' + item;\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\nvar didWarnStateUpdateForUnmountedComponent = {};\n\nfunction warnNoop(publicInstance, callerName) {\n  {\n    var _constructor = publicInstance.constructor;\n    var componentName = _constructor && (_constructor.displayName || _constructor.name) || 'ReactClass';\n    var warningKey = componentName + \".\" + callerName;\n\n    if (didWarnStateUpdateForUnmountedComponent[warningKey]) {\n      return;\n    }\n\n    error(\"Can't call %s on a component that is not yet mounted. \" + 'This is a no-op, but it might indicate a bug in your application. ' + 'Instead, assign to `this.state` directly or define a `state = {};` ' + 'class property with the desired state in the %s component.', callerName, componentName);\n\n    didWarnStateUpdateForUnmountedComponent[warningKey] = true;\n  }\n}\n/**\n * This is the abstract API for an update queue.\n */\n\n\nvar ReactNoopUpdateQueue = {\n  /**\n   * Checks whether or not this composite component is mounted.\n   * @param {ReactClass} publicInstance The instance we want to test.\n   * @return {boolean} True if mounted, false otherwise.\n   * @protected\n   * @final\n   */\n  isMounted: function (publicInstance) {\n    return false;\n  },\n\n  /**\n   * Forces an update. This should only be invoked when it is known with\n   * certainty that we are **not** in a DOM transaction.\n   *\n   * You may want to call this when you know that some deeper aspect of the\n   * component's state has changed but `setState` was not called.\n   *\n   * This will not invoke `shouldComponentUpdate`, but it will invoke\n   * `componentWillUpdate` and `componentDidUpdate`.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} callerName name of the calling function in the public API.\n   * @internal\n   */\n  enqueueForceUpdate: function (publicInstance, callback, callerName) {\n    warnNoop(publicInstance, 'forceUpdate');\n  },\n\n  /**\n   * Replaces all of the state. Always use this or `setState` to mutate state.\n   * You should treat `this.state` as immutable.\n   *\n   * There is no guarantee that `this.state` will be immediately updated, so\n   * accessing `this.state` after calling this method may return the old value.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {object} completeState Next state.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} callerName name of the calling function in the public API.\n   * @internal\n   */\n  enqueueReplaceState: function (publicInstance, completeState, callback, callerName) {\n    warnNoop(publicInstance, 'replaceState');\n  },\n\n  /**\n   * Sets a subset of the state. This only exists because _pendingState is\n   * internal. This provides a merging strategy that is not available to deep\n   * properties which is confusing. TODO: Expose pendingState or don't use it\n   * during the merge.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {object} partialState Next partial state to be merged with state.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} Name of the calling function in the public API.\n   * @internal\n   */\n  enqueueSetState: function (publicInstance, partialState, callback, callerName) {\n    warnNoop(publicInstance, 'setState');\n  }\n};\n\nvar emptyObject = {};\n\n{\n  Object.freeze(emptyObject);\n}\n/**\n * Base class helpers for the updating state of a component.\n */\n\n\nfunction Component(props, context, updater) {\n  this.props = props;\n  this.context = context; // If a component has string refs, we will assign a different object later.\n\n  this.refs = emptyObject; // We initialize the default updater but the real one gets injected by the\n  // renderer.\n\n  this.updater = updater || ReactNoopUpdateQueue;\n}\n\nComponent.prototype.isReactComponent = {};\n/**\n * Sets a subset of the state. Always use this to mutate\n * state. You should treat `this.state` as immutable.\n *\n * There is no guarantee that `this.state` will be immediately updated, so\n * accessing `this.state` after calling this method may return the old value.\n *\n * There is no guarantee that calls to `setState` will run synchronously,\n * as they may eventually be batched together.  You can provide an optional\n * callback that will be executed when the call to setState is actually\n * completed.\n *\n * When a function is provided to setState, it will be called at some point in\n * the future (not synchronously). It will be called with the up to date\n * component arguments (state, props, context). These values can be different\n * from this.* because your function may be called after receiveProps but before\n * shouldComponentUpdate, and this new state, props, and context will not yet be\n * assigned to this.\n *\n * @param {object|function} partialState Next partial state or function to\n *        produce next partial state to be merged with current state.\n * @param {?function} callback Called after state is updated.\n * @final\n * @protected\n */\n\nComponent.prototype.setState = function (partialState, callback) {\n  if (!(typeof partialState === 'object' || typeof partialState === 'function' || partialState == null)) {\n    {\n      throw Error( \"setState(...): takes an object of state variables to update or a function which returns an object of state variables.\" );\n    }\n  }\n\n  this.updater.enqueueSetState(this, partialState, callback, 'setState');\n};\n/**\n * Forces an update. This should only be invoked when it is known with\n * certainty that we are **not** in a DOM transaction.\n *\n * You may want to call this when you know that some deeper aspect of the\n * component's state has changed but `setState` was not called.\n *\n * This will not invoke `shouldComponentUpdate`, but it will invoke\n * `componentWillUpdate` and `componentDidUpdate`.\n *\n * @param {?function} callback Called after update is complete.\n * @final\n * @protected\n */\n\n\nComponent.prototype.forceUpdate = function (callback) {\n  this.updater.enqueueForceUpdate(this, callback, 'forceUpdate');\n};\n/**\n * Deprecated APIs. These APIs used to exist on classic React classes but since\n * we would like to deprecate them, we're not going to move them over to this\n * modern base class. Instead, we define a getter that warns if it's accessed.\n */\n\n\n{\n  var deprecatedAPIs = {\n    isMounted: ['isMounted', 'Instead, make sure to clean up subscriptions and pending requests in ' + 'componentWillUnmount to prevent memory leaks.'],\n    replaceState: ['replaceState', 'Refactor your code to use setState instead (see ' + 'https://github.com/facebook/react/issues/3236).']\n  };\n\n  var defineDeprecationWarning = function (methodName, info) {\n    Object.defineProperty(Component.prototype, methodName, {\n      get: function () {\n        warn('%s(...) is deprecated in plain JavaScript React classes. %s', info[0], info[1]);\n\n        return undefined;\n      }\n    });\n  };\n\n  for (var fnName in deprecatedAPIs) {\n    if (deprecatedAPIs.hasOwnProperty(fnName)) {\n      defineDeprecationWarning(fnName, deprecatedAPIs[fnName]);\n    }\n  }\n}\n\nfunction ComponentDummy() {}\n\nComponentDummy.prototype = Component.prototype;\n/**\n * Convenience component with default shallow equality check for sCU.\n */\n\nfunction PureComponent(props, context, updater) {\n  this.props = props;\n  this.context = context; // If a component has string refs, we will assign a different object later.\n\n  this.refs = emptyObject;\n  this.updater = updater || ReactNoopUpdateQueue;\n}\n\nvar pureComponentPrototype = PureComponent.prototype = new ComponentDummy();\npureComponentPrototype.constructor = PureComponent; // Avoid an extra prototype jump for these methods.\n\n_assign(pureComponentPrototype, Component.prototype);\n\npureComponentPrototype.isPureReactComponent = true;\n\n// an immutable object with a single mutable value\nfunction createRef() {\n  var refObject = {\n    current: null\n  };\n\n  {\n    Object.seal(refObject);\n  }\n\n  return refObject;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var functionName = innerType.displayName || innerType.name || '';\n  return outerType.displayName || (functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName);\n}\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nfunction getComponentName(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentName(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case exports.Fragment:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case exports.Profiler:\n      return 'Profiler';\n\n    case exports.StrictMode:\n      return 'StrictMode';\n\n    case exports.Suspense:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        return getComponentName(type.type);\n\n      case REACT_BLOCK_TYPE:\n        return getComponentName(type._render);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentName(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown, specialPropRefWarningShown, didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  var warnAboutAccessingKey = function () {\n    {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    }\n  };\n\n  warnAboutAccessingKey.isReactWarning = true;\n  Object.defineProperty(props, 'key', {\n    get: warnAboutAccessingKey,\n    configurable: true\n  });\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  var warnAboutAccessingRef = function () {\n    {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    }\n  };\n\n  warnAboutAccessingRef.isReactWarning = true;\n  Object.defineProperty(props, 'ref', {\n    get: warnAboutAccessingRef,\n    configurable: true\n  });\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && config.__self && ReactCurrentOwner.current.stateNode !== config.__self) {\n      var componentName = getComponentName(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', componentName, config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * Create and return a new ReactElement of the given type.\n * See https://reactjs.org/docs/react-api.html#createelement\n */\n\nfunction createElement(type, config, children) {\n  var propName; // Reserved names are extracted\n\n  var props = {};\n  var key = null;\n  var ref = null;\n  var self = null;\n  var source = null;\n\n  if (config != null) {\n    if (hasValidRef(config)) {\n      ref = config.ref;\n\n      {\n        warnIfStringRefCannotBeAutoConverted(config);\n      }\n    }\n\n    if (hasValidKey(config)) {\n      key = '' + config.key;\n    }\n\n    self = config.__self === undefined ? null : config.__self;\n    source = config.__source === undefined ? null : config.__source; // Remaining properties are added to a new props object\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    }\n  } // Children can be more than one argument, and those are transferred onto\n  // the newly allocated props object.\n\n\n  var childrenLength = arguments.length - 2;\n\n  if (childrenLength === 1) {\n    props.children = children;\n  } else if (childrenLength > 1) {\n    var childArray = Array(childrenLength);\n\n    for (var i = 0; i < childrenLength; i++) {\n      childArray[i] = arguments[i + 2];\n    }\n\n    {\n      if (Object.freeze) {\n        Object.freeze(childArray);\n      }\n    }\n\n    props.children = childArray;\n  } // Resolve default props\n\n\n  if (type && type.defaultProps) {\n    var defaultProps = type.defaultProps;\n\n    for (propName in defaultProps) {\n      if (props[propName] === undefined) {\n        props[propName] = defaultProps[propName];\n      }\n    }\n  }\n\n  {\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n  }\n\n  return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n}\nfunction cloneAndReplaceKey(oldElement, newKey) {\n  var newElement = ReactElement(oldElement.type, newKey, oldElement.ref, oldElement._self, oldElement._source, oldElement._owner, oldElement.props);\n  return newElement;\n}\n/**\n * Clone and return a new ReactElement using element as the starting point.\n * See https://reactjs.org/docs/react-api.html#cloneelement\n */\n\nfunction cloneElement(element, config, children) {\n  if (!!(element === null || element === undefined)) {\n    {\n      throw Error( \"React.cloneElement(...): The argument must be a React element, but you passed \" + element + \".\" );\n    }\n  }\n\n  var propName; // Original props are copied\n\n  var props = _assign({}, element.props); // Reserved names are extracted\n\n\n  var key = element.key;\n  var ref = element.ref; // Self is preserved since the owner is preserved.\n\n  var self = element._self; // Source is preserved since cloneElement is unlikely to be targeted by a\n  // transpiler, and the original source is probably a better indicator of the\n  // true owner.\n\n  var source = element._source; // Owner will be preserved, unless ref is overridden\n\n  var owner = element._owner;\n\n  if (config != null) {\n    if (hasValidRef(config)) {\n      // Silently steal the ref from the parent.\n      ref = config.ref;\n      owner = ReactCurrentOwner.current;\n    }\n\n    if (hasValidKey(config)) {\n      key = '' + config.key;\n    } // Remaining properties override existing props\n\n\n    var defaultProps;\n\n    if (element.type && element.type.defaultProps) {\n      defaultProps = element.type.defaultProps;\n    }\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        if (config[propName] === undefined && defaultProps !== undefined) {\n          // Resolve default props\n          props[propName] = defaultProps[propName];\n        } else {\n          props[propName] = config[propName];\n        }\n      }\n    }\n  } // Children can be more than one argument, and those are transferred onto\n  // the newly allocated props object.\n\n\n  var childrenLength = arguments.length - 2;\n\n  if (childrenLength === 1) {\n    props.children = children;\n  } else if (childrenLength > 1) {\n    var childArray = Array(childrenLength);\n\n    for (var i = 0; i < childrenLength; i++) {\n      childArray[i] = arguments[i + 2];\n    }\n\n    props.children = childArray;\n  }\n\n  return ReactElement(element.type, key, ref, self, source, owner, props);\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\n\nvar SEPARATOR = '.';\nvar SUBSEPARATOR = ':';\n/**\n * Escape and wrap key so it is safe to use as a reactid\n *\n * @param {string} key to be escaped.\n * @return {string} the escaped key.\n */\n\nfunction escape(key) {\n  var escapeRegex = /[=:]/g;\n  var escaperLookup = {\n    '=': '=0',\n    ':': '=2'\n  };\n  var escapedString = key.replace(escapeRegex, function (match) {\n    return escaperLookup[match];\n  });\n  return '$' + escapedString;\n}\n/**\n * TODO: Test that a single child and an array with one item have the same key\n * pattern.\n */\n\n\nvar didWarnAboutMaps = false;\nvar userProvidedKeyEscapeRegex = /\\/+/g;\n\nfunction escapeUserProvidedKey(text) {\n  return text.replace(userProvidedKeyEscapeRegex, '$&/');\n}\n/**\n * Generate a key string that identifies a element within a set.\n *\n * @param {*} element A element that could contain a manual key.\n * @param {number} index Index that is used if a manual key is not provided.\n * @return {string}\n */\n\n\nfunction getElementKey(element, index) {\n  // Do some typechecking here since we call this blindly. We want to ensure\n  // that we don't block potential future ES APIs.\n  if (typeof element === 'object' && element !== null && element.key != null) {\n    // Explicit key\n    return escape('' + element.key);\n  } // Implicit key determined by the index in the set\n\n\n  return index.toString(36);\n}\n\nfunction mapIntoArray(children, array, escapedPrefix, nameSoFar, callback) {\n  var type = typeof children;\n\n  if (type === 'undefined' || type === 'boolean') {\n    // All of the above are perceived as null.\n    children = null;\n  }\n\n  var invokeCallback = false;\n\n  if (children === null) {\n    invokeCallback = true;\n  } else {\n    switch (type) {\n      case 'string':\n      case 'number':\n        invokeCallback = true;\n        break;\n\n      case 'object':\n        switch (children.$$typeof) {\n          case REACT_ELEMENT_TYPE:\n          case REACT_PORTAL_TYPE:\n            invokeCallback = true;\n        }\n\n    }\n  }\n\n  if (invokeCallback) {\n    var _child = children;\n    var mappedChild = callback(_child); // If it's the only child, treat the name as if it was wrapped in an array\n    // so that it's consistent if the number of children grows:\n\n    var childKey = nameSoFar === '' ? SEPARATOR + getElementKey(_child, 0) : nameSoFar;\n\n    if (Array.isArray(mappedChild)) {\n      var escapedChildKey = '';\n\n      if (childKey != null) {\n        escapedChildKey = escapeUserProvidedKey(childKey) + '/';\n      }\n\n      mapIntoArray(mappedChild, array, escapedChildKey, '', function (c) {\n        return c;\n      });\n    } else if (mappedChild != null) {\n      if (isValidElement(mappedChild)) {\n        mappedChild = cloneAndReplaceKey(mappedChild, // Keep both the (mapped) and old keys if they differ, just as\n        // traverseAllChildren used to do for objects as children\n        escapedPrefix + ( // $FlowFixMe Flow incorrectly thinks React.Portal doesn't have a key\n        mappedChild.key && (!_child || _child.key !== mappedChild.key) ? // $FlowFixMe Flow incorrectly thinks existing element's key can be a number\n        escapeUserProvidedKey('' + mappedChild.key) + '/' : '') + childKey);\n      }\n\n      array.push(mappedChild);\n    }\n\n    return 1;\n  }\n\n  var child;\n  var nextName;\n  var subtreeCount = 0; // Count of children found in the current subtree.\n\n  var nextNamePrefix = nameSoFar === '' ? SEPARATOR : nameSoFar + SUBSEPARATOR;\n\n  if (Array.isArray(children)) {\n    for (var i = 0; i < children.length; i++) {\n      child = children[i];\n      nextName = nextNamePrefix + getElementKey(child, i);\n      subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n    }\n  } else {\n    var iteratorFn = getIteratorFn(children);\n\n    if (typeof iteratorFn === 'function') {\n      var iterableChildren = children;\n\n      {\n        // Warn about using Maps as children\n        if (iteratorFn === iterableChildren.entries) {\n          if (!didWarnAboutMaps) {\n            warn('Using Maps as children is not supported. ' + 'Use an array of keyed ReactElements instead.');\n          }\n\n          didWarnAboutMaps = true;\n        }\n      }\n\n      var iterator = iteratorFn.call(iterableChildren);\n      var step;\n      var ii = 0;\n\n      while (!(step = iterator.next()).done) {\n        child = step.value;\n        nextName = nextNamePrefix + getElementKey(child, ii++);\n        subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n      }\n    } else if (type === 'object') {\n      var childrenString = '' + children;\n\n      {\n        {\n          throw Error( \"Objects are not valid as a React child (found: \" + (childrenString === '[object Object]' ? 'object with keys {' + Object.keys(children).join(', ') + '}' : childrenString) + \"). If you meant to render a collection of children, use an array instead.\" );\n        }\n      }\n    }\n  }\n\n  return subtreeCount;\n}\n\n/**\n * Maps children that are typically specified as `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenmap\n *\n * The provided mapFunction(child, index) will be called for each\n * leaf child.\n *\n * @param {?*} children Children tree container.\n * @param {function(*, int)} func The map function.\n * @param {*} context Context for mapFunction.\n * @return {object} Object containing the ordered map of results.\n */\nfunction mapChildren(children, func, context) {\n  if (children == null) {\n    return children;\n  }\n\n  var result = [];\n  var count = 0;\n  mapIntoArray(children, result, '', '', function (child) {\n    return func.call(context, child, count++);\n  });\n  return result;\n}\n/**\n * Count the number of children that are typically specified as\n * `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrencount\n *\n * @param {?*} children Children tree container.\n * @return {number} The number of children.\n */\n\n\nfunction countChildren(children) {\n  var n = 0;\n  mapChildren(children, function () {\n    n++; // Don't return anything\n  });\n  return n;\n}\n\n/**\n * Iterates through children that are typically specified as `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenforeach\n *\n * The provided forEachFunc(child, index) will be called for each\n * leaf child.\n *\n * @param {?*} children Children tree container.\n * @param {function(*, int)} forEachFunc\n * @param {*} forEachContext Context for forEachContext.\n */\nfunction forEachChildren(children, forEachFunc, forEachContext) {\n  mapChildren(children, function () {\n    forEachFunc.apply(this, arguments); // Don't return anything.\n  }, forEachContext);\n}\n/**\n * Flatten a children object (typically specified as `props.children`) and\n * return an array with appropriately re-keyed children.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrentoarray\n */\n\n\nfunction toArray(children) {\n  return mapChildren(children, function (child) {\n    return child;\n  }) || [];\n}\n/**\n * Returns the first child in a collection of children and verifies that there\n * is only one child in the collection.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenonly\n *\n * The current implementation of this function assumes that a single child gets\n * passed without a wrapper, but the purpose of this helper function is to\n * abstract away the particular structure of children.\n *\n * @param {?object} children Child collection structure.\n * @return {ReactElement} The first and only `ReactElement` contained in the\n * structure.\n */\n\n\nfunction onlyChild(children) {\n  if (!isValidElement(children)) {\n    {\n      throw Error( \"React.Children.only expected to receive a single React element child.\" );\n    }\n  }\n\n  return children;\n}\n\nfunction createContext(defaultValue, calculateChangedBits) {\n  if (calculateChangedBits === undefined) {\n    calculateChangedBits = null;\n  } else {\n    {\n      if (calculateChangedBits !== null && typeof calculateChangedBits !== 'function') {\n        error('createContext: Expected the optional second argument to be a ' + 'function. Instead received: %s', calculateChangedBits);\n      }\n    }\n  }\n\n  var context = {\n    $$typeof: REACT_CONTEXT_TYPE,\n    _calculateChangedBits: calculateChangedBits,\n    // As a workaround to support multiple concurrent renderers, we categorize\n    // some renderers as primary and others as secondary. We only expect\n    // there to be two concurrent renderers at most: React Native (primary) and\n    // Fabric (secondary); React DOM (primary) and React ART (secondary).\n    // Secondary renderers store their context values on separate fields.\n    _currentValue: defaultValue,\n    _currentValue2: defaultValue,\n    // Used to track how many concurrent renderers this context currently\n    // supports within in a single renderer. Such as parallel server rendering.\n    _threadCount: 0,\n    // These are circular\n    Provider: null,\n    Consumer: null\n  };\n  context.Provider = {\n    $$typeof: REACT_PROVIDER_TYPE,\n    _context: context\n  };\n  var hasWarnedAboutUsingNestedContextConsumers = false;\n  var hasWarnedAboutUsingConsumerProvider = false;\n  var hasWarnedAboutDisplayNameOnConsumer = false;\n\n  {\n    // A separate object, but proxies back to the original context object for\n    // backwards compatibility. It has a different $$typeof, so we can properly\n    // warn for the incorrect usage of Context as a Consumer.\n    var Consumer = {\n      $$typeof: REACT_CONTEXT_TYPE,\n      _context: context,\n      _calculateChangedBits: context._calculateChangedBits\n    }; // $FlowFixMe: Flow complains about not setting a value, which is intentional here\n\n    Object.defineProperties(Consumer, {\n      Provider: {\n        get: function () {\n          if (!hasWarnedAboutUsingConsumerProvider) {\n            hasWarnedAboutUsingConsumerProvider = true;\n\n            error('Rendering <Context.Consumer.Provider> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Provider> instead?');\n          }\n\n          return context.Provider;\n        },\n        set: function (_Provider) {\n          context.Provider = _Provider;\n        }\n      },\n      _currentValue: {\n        get: function () {\n          return context._currentValue;\n        },\n        set: function (_currentValue) {\n          context._currentValue = _currentValue;\n        }\n      },\n      _currentValue2: {\n        get: function () {\n          return context._currentValue2;\n        },\n        set: function (_currentValue2) {\n          context._currentValue2 = _currentValue2;\n        }\n      },\n      _threadCount: {\n        get: function () {\n          return context._threadCount;\n        },\n        set: function (_threadCount) {\n          context._threadCount = _threadCount;\n        }\n      },\n      Consumer: {\n        get: function () {\n          if (!hasWarnedAboutUsingNestedContextConsumers) {\n            hasWarnedAboutUsingNestedContextConsumers = true;\n\n            error('Rendering <Context.Consumer.Consumer> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Consumer> instead?');\n          }\n\n          return context.Consumer;\n        }\n      },\n      displayName: {\n        get: function () {\n          return context.displayName;\n        },\n        set: function (displayName) {\n          if (!hasWarnedAboutDisplayNameOnConsumer) {\n            warn('Setting `displayName` on Context.Consumer has no effect. ' + \"You should set it directly on the context with Context.displayName = '%s'.\", displayName);\n\n            hasWarnedAboutDisplayNameOnConsumer = true;\n          }\n        }\n      }\n    }); // $FlowFixMe: Flow complains about missing properties because it doesn't understand defineProperty\n\n    context.Consumer = Consumer;\n  }\n\n  {\n    context._currentRenderer = null;\n    context._currentRenderer2 = null;\n  }\n\n  return context;\n}\n\nvar Uninitialized = -1;\nvar Pending = 0;\nvar Resolved = 1;\nvar Rejected = 2;\n\nfunction lazyInitializer(payload) {\n  if (payload._status === Uninitialized) {\n    var ctor = payload._result;\n    var thenable = ctor(); // Transition to the next state.\n\n    var pending = payload;\n    pending._status = Pending;\n    pending._result = thenable;\n    thenable.then(function (moduleObject) {\n      if (payload._status === Pending) {\n        var defaultExport = moduleObject.default;\n\n        {\n          if (defaultExport === undefined) {\n            error('lazy: Expected the result of a dynamic import() call. ' + 'Instead received: %s\\n\\nYour code should look like: \\n  ' + // Break up imports to avoid accidentally parsing them as dependencies.\n            'const MyComponent = lazy(() => imp' + \"ort('./MyComponent'))\", moduleObject);\n          }\n        } // Transition to the next state.\n\n\n        var resolved = payload;\n        resolved._status = Resolved;\n        resolved._result = defaultExport;\n      }\n    }, function (error) {\n      if (payload._status === Pending) {\n        // Transition to the next state.\n        var rejected = payload;\n        rejected._status = Rejected;\n        rejected._result = error;\n      }\n    });\n  }\n\n  if (payload._status === Resolved) {\n    return payload._result;\n  } else {\n    throw payload._result;\n  }\n}\n\nfunction lazy(ctor) {\n  var payload = {\n    // We use these fields to store the result.\n    _status: -1,\n    _result: ctor\n  };\n  var lazyType = {\n    $$typeof: REACT_LAZY_TYPE,\n    _payload: payload,\n    _init: lazyInitializer\n  };\n\n  {\n    // In production, this would just set it on the object.\n    var defaultProps;\n    var propTypes; // $FlowFixMe\n\n    Object.defineProperties(lazyType, {\n      defaultProps: {\n        configurable: true,\n        get: function () {\n          return defaultProps;\n        },\n        set: function (newDefaultProps) {\n          error('React.lazy(...): It is not supported to assign `defaultProps` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n\n          defaultProps = newDefaultProps; // Match production behavior more closely:\n          // $FlowFixMe\n\n          Object.defineProperty(lazyType, 'defaultProps', {\n            enumerable: true\n          });\n        }\n      },\n      propTypes: {\n        configurable: true,\n        get: function () {\n          return propTypes;\n        },\n        set: function (newPropTypes) {\n          error('React.lazy(...): It is not supported to assign `propTypes` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n\n          propTypes = newPropTypes; // Match production behavior more closely:\n          // $FlowFixMe\n\n          Object.defineProperty(lazyType, 'propTypes', {\n            enumerable: true\n          });\n        }\n      }\n    });\n  }\n\n  return lazyType;\n}\n\nfunction forwardRef(render) {\n  {\n    if (render != null && render.$$typeof === REACT_MEMO_TYPE) {\n      error('forwardRef requires a render function but received a `memo` ' + 'component. Instead of forwardRef(memo(...)), use ' + 'memo(forwardRef(...)).');\n    } else if (typeof render !== 'function') {\n      error('forwardRef requires a render function but was given %s.', render === null ? 'null' : typeof render);\n    } else {\n      if (render.length !== 0 && render.length !== 2) {\n        error('forwardRef render functions accept exactly two parameters: props and ref. %s', render.length === 1 ? 'Did you forget to use the ref parameter?' : 'Any additional parameter will be undefined.');\n      }\n    }\n\n    if (render != null) {\n      if (render.defaultProps != null || render.propTypes != null) {\n        error('forwardRef render functions do not support propTypes or defaultProps. ' + 'Did you accidentally pass a React component?');\n      }\n    }\n  }\n\n  var elementType = {\n    $$typeof: REACT_FORWARD_REF_TYPE,\n    render: render\n  };\n\n  {\n    var ownName;\n    Object.defineProperty(elementType, 'displayName', {\n      enumerable: false,\n      configurable: true,\n      get: function () {\n        return ownName;\n      },\n      set: function (name) {\n        ownName = name;\n\n        if (render.displayName == null) {\n          render.displayName = name;\n        }\n      }\n    });\n  }\n\n  return elementType;\n}\n\n// Filter certain DOM attributes (e.g. src, href) if their values are empty strings.\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === exports.Fragment || type === exports.Profiler || type === REACT_DEBUG_TRACING_MODE_TYPE || type === exports.StrictMode || type === exports.Suspense || type === REACT_SUSPENSE_LIST_TYPE || type === REACT_LEGACY_HIDDEN_TYPE || enableScopeAPI ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_BLOCK_TYPE || type[0] === REACT_SERVER_BLOCK_TYPE) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction memo(type, compare) {\n  {\n    if (!isValidElementType(type)) {\n      error('memo: The first argument must be a component. Instead ' + 'received: %s', type === null ? 'null' : typeof type);\n    }\n  }\n\n  var elementType = {\n    $$typeof: REACT_MEMO_TYPE,\n    type: type,\n    compare: compare === undefined ? null : compare\n  };\n\n  {\n    var ownName;\n    Object.defineProperty(elementType, 'displayName', {\n      enumerable: false,\n      configurable: true,\n      get: function () {\n        return ownName;\n      },\n      set: function (name) {\n        ownName = name;\n\n        if (type.displayName == null) {\n          type.displayName = name;\n        }\n      }\n    });\n  }\n\n  return elementType;\n}\n\nfunction resolveDispatcher() {\n  var dispatcher = ReactCurrentDispatcher.current;\n\n  if (!(dispatcher !== null)) {\n    {\n      throw Error( \"Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\\n1. You might have mismatching versions of React and the renderer (such as React DOM)\\n2. You might be breaking the Rules of Hooks\\n3. You might have more than one copy of React in the same app\\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.\" );\n    }\n  }\n\n  return dispatcher;\n}\n\nfunction useContext(Context, unstable_observedBits) {\n  var dispatcher = resolveDispatcher();\n\n  {\n    if (unstable_observedBits !== undefined) {\n      error('useContext() second argument is reserved for future ' + 'use in React. Passing it is not supported. ' + 'You passed: %s.%s', unstable_observedBits, typeof unstable_observedBits === 'number' && Array.isArray(arguments[2]) ? '\\n\\nDid you call array.map(useContext)? ' + 'Calling Hooks inside a loop is not supported. ' + 'Learn more at https://reactjs.org/link/rules-of-hooks' : '');\n    } // TODO: add a more generic warning for invalid values.\n\n\n    if (Context._context !== undefined) {\n      var realContext = Context._context; // Don't deduplicate because this legitimately causes bugs\n      // and nobody should be using this in existing code.\n\n      if (realContext.Consumer === Context) {\n        error('Calling useContext(Context.Consumer) is not supported, may cause bugs, and will be ' + 'removed in a future major release. Did you mean to call useContext(Context) instead?');\n      } else if (realContext.Provider === Context) {\n        error('Calling useContext(Context.Provider) is not supported. ' + 'Did you mean to call useContext(Context) instead?');\n      }\n    }\n  }\n\n  return dispatcher.useContext(Context, unstable_observedBits);\n}\nfunction useState(initialState) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useState(initialState);\n}\nfunction useReducer(reducer, initialArg, init) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useReducer(reducer, initialArg, init);\n}\nfunction useRef(initialValue) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useRef(initialValue);\n}\nfunction useEffect(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useEffect(create, deps);\n}\nfunction useLayoutEffect(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useLayoutEffect(create, deps);\n}\nfunction useCallback(callback, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useCallback(callback, deps);\n}\nfunction useMemo(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useMemo(create, deps);\n}\nfunction useImperativeHandle(ref, create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useImperativeHandle(ref, create, deps);\n}\nfunction useDebugValue(value, formatterFn) {\n  {\n    var dispatcher = resolveDispatcher();\n    return dispatcher.useDebugValue(value, formatterFn);\n  }\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: _assign({}, props, {\n          value: prevLog\n        }),\n        info: _assign({}, props, {\n          value: prevInfo\n        }),\n        warn: _assign({}, props, {\n          value: prevWarn\n        }),\n        error: _assign({}, props, {\n          value: prevError\n        }),\n        group: _assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: _assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: _assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher$1 = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher$1.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher$1.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at ');\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher$1.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case exports.Suspense:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_BLOCK_TYPE:\n        return describeFunctionComponentFrame(type._render);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(Object.prototype.hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      setExtraStackFrame(stack);\n    } else {\n      setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n\nfunction getDeclarationErrorAddendum() {\n  if (ReactCurrentOwner.current) {\n    var name = getComponentName(ReactCurrentOwner.current.type);\n\n    if (name) {\n      return '\\n\\nCheck the render method of `' + name + '`.';\n    }\n  }\n\n  return '';\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  if (source !== undefined) {\n    var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n    var lineNumber = source.lineNumber;\n    return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n  }\n\n  return '';\n}\n\nfunction getSourceInfoErrorAddendumForProps(elementProps) {\n  if (elementProps !== null && elementProps !== undefined) {\n    return getSourceInfoErrorAddendum(elementProps.__source);\n  }\n\n  return '';\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  var info = getDeclarationErrorAddendum();\n\n  if (!info) {\n    var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n    if (parentName) {\n      info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n    }\n  }\n\n  return info;\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  if (!element._store || element._store.validated || element.key != null) {\n    return;\n  }\n\n  element._store.validated = true;\n  var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n  if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n    return;\n  }\n\n  ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n  // property, it may be the creator of the child that's responsible for\n  // assigning it a key.\n\n  var childOwner = '';\n\n  if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n    // Give the component that originally created this child.\n    childOwner = \" It was passed a child from \" + getComponentName(element._owner.type) + \".\";\n  }\n\n  {\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  if (typeof node !== 'object') {\n    return;\n  }\n\n  if (Array.isArray(node)) {\n    for (var i = 0; i < node.length; i++) {\n      var child = node[i];\n\n      if (isValidElement(child)) {\n        validateExplicitKey(child, parentType);\n      }\n    }\n  } else if (isValidElement(node)) {\n    // This element was passed in a valid location.\n    if (node._store) {\n      node._store.validated = true;\n    }\n  } else if (node) {\n    var iteratorFn = getIteratorFn(node);\n\n    if (typeof iteratorFn === 'function') {\n      // Entry iterators used to provide implicit keys,\n      // but now we print a separate warning for them later.\n      if (iteratorFn !== node.entries) {\n        var iterator = iteratorFn.call(node);\n        var step;\n\n        while (!(step = iterator.next()).done) {\n          if (isValidElement(step.value)) {\n            validateExplicitKey(step.value, parentType);\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentName(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentName(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\nfunction createElementWithValidation(type, props, children) {\n  var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n  // succeed and there will likely be errors in render.\n\n  if (!validType) {\n    var info = '';\n\n    if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n      info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n    }\n\n    var sourceInfo = getSourceInfoErrorAddendumForProps(props);\n\n    if (sourceInfo) {\n      info += sourceInfo;\n    } else {\n      info += getDeclarationErrorAddendum();\n    }\n\n    var typeString;\n\n    if (type === null) {\n      typeString = 'null';\n    } else if (Array.isArray(type)) {\n      typeString = 'array';\n    } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n      typeString = \"<\" + (getComponentName(type.type) || 'Unknown') + \" />\";\n      info = ' Did you accidentally export a JSX literal instead of a component?';\n    } else {\n      typeString = typeof type;\n    }\n\n    {\n      error('React.createElement: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n  }\n\n  var element = createElement.apply(this, arguments); // The result can be nullish if a mock or a custom function is used.\n  // TODO: Drop this when these are no longer allowed as the type argument.\n\n  if (element == null) {\n    return element;\n  } // Skip key warning if the type isn't valid since our key validation logic\n  // doesn't expect a non-string/function type and can throw confusing errors.\n  // We don't want exception behavior to differ between dev and prod.\n  // (Rendering will throw with a helpful message and as soon as the type is\n  // fixed, the key warnings will appear.)\n\n\n  if (validType) {\n    for (var i = 2; i < arguments.length; i++) {\n      validateChildKeys(arguments[i], type);\n    }\n  }\n\n  if (type === exports.Fragment) {\n    validateFragmentProps(element);\n  } else {\n    validatePropTypes(element);\n  }\n\n  return element;\n}\nvar didWarnAboutDeprecatedCreateFactory = false;\nfunction createFactoryWithValidation(type) {\n  var validatedFactory = createElementWithValidation.bind(null, type);\n  validatedFactory.type = type;\n\n  {\n    if (!didWarnAboutDeprecatedCreateFactory) {\n      didWarnAboutDeprecatedCreateFactory = true;\n\n      warn('React.createFactory() is deprecated and will be removed in ' + 'a future major release. Consider using JSX ' + 'or use React.createElement() directly instead.');\n    } // Legacy hook: remove it\n\n\n    Object.defineProperty(validatedFactory, 'type', {\n      enumerable: false,\n      get: function () {\n        warn('Factory.type is deprecated. Access the class directly ' + 'before passing it to createFactory.');\n\n        Object.defineProperty(this, 'type', {\n          value: type\n        });\n        return type;\n      }\n    });\n  }\n\n  return validatedFactory;\n}\nfunction cloneElementWithValidation(element, props, children) {\n  var newElement = cloneElement.apply(this, arguments);\n\n  for (var i = 2; i < arguments.length; i++) {\n    validateChildKeys(arguments[i], newElement.type);\n  }\n\n  validatePropTypes(newElement);\n  return newElement;\n}\n\n{\n\n  try {\n    var frozenObject = Object.freeze({});\n    /* eslint-disable no-new */\n\n    new Map([[frozenObject, null]]);\n    new Set([frozenObject]);\n    /* eslint-enable no-new */\n  } catch (e) {\n  }\n}\n\nvar createElement$1 =  createElementWithValidation ;\nvar cloneElement$1 =  cloneElementWithValidation ;\nvar createFactory =  createFactoryWithValidation ;\nvar Children = {\n  map: mapChildren,\n  forEach: forEachChildren,\n  count: countChildren,\n  toArray: toArray,\n  only: onlyChild\n};\n\nexports.Children = Children;\nexports.Component = Component;\nexports.PureComponent = PureComponent;\nexports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = ReactSharedInternals;\nexports.cloneElement = cloneElement$1;\nexports.createContext = createContext;\nexports.createElement = createElement$1;\nexports.createFactory = createFactory;\nexports.createRef = createRef;\nexports.forwardRef = forwardRef;\nexports.isValidElement = isValidElement;\nexports.lazy = lazy;\nexports.memo = memo;\nexports.useCallback = useCallback;\nexports.useContext = useContext;\nexports.useDebugValue = useDebugValue;\nexports.useEffect = useEffect;\nexports.useImperativeHandle = useImperativeHandle;\nexports.useLayoutEffect = useLayoutEffect;\nexports.useMemo = useMemo;\nexports.useReducer = useReducer;\nexports.useRef = useRef;\nexports.useState = useState;\nexports.version = ReactVersion;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react.production.min.js');\n} else {\n  module.exports = require('./cjs/react.development.js');\n}\n", "export default function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import './_acf-jsx-names.js';\nimport './_acf-blocks-legacy.js';\n"], "names": ["$", "undefined", "BlockControls", "InspectorCont<PERSON><PERSON>", "InnerBlocks", "wp", "blockEditor", "<PERSON><PERSON><PERSON>", "IconButton", "Placeholder", "Spinner", "components", "Fragment", "element", "Component", "React", "withSelect", "data", "createHigherOrderComponent", "compose", "blockTypes", "getBlockType", "name", "isBlockType", "isNewBlock", "props", "attributes", "id", "isDuplicateBlock", "getBlocks", "filter", "block", "clientId", "length", "registerBlockType", "blockType", "allowedTypes", "post_types", "push", "postType", "acf", "get", "indexOf", "icon", "substr", "iconHTML", "category", "blocks", "getCategories", "cat", "slug", "pop", "type", "align", "mode", "ThisBlockEdit", "BlockEdit", "ThisBlockSave", "BlockSave", "supports", "align_text", "withAlignTextAttributes", "withAlignTextComponent", "align_content", "withAlignContentAttributes", "withAlignContentComponent", "parseArgs", "title", "edit", "save", "key", "default", "result", "anchor", "select", "selector", "dispatch", "args", "i", "concat", "innerBlocks", "k", "ajaxQueue", "fetchBlock", "query", "delay", "timeout", "promise", "Deferred", "clearTimeout", "setTimeout", "ajax", "url", "dataType", "cache", "prepareForAjax", "action", "JSON", "stringify", "always", "done", "resolve", "apply", "arguments", "fail", "reject", "compareObjects", "obj1", "obj2", "parseJSX", "html", "parseNode", "node", "nodeName", "parseNodeName", "toLowerCase", "nodeAttrs", "arrayArgs", "map", "parseNodeAttr", "for<PERSON>ach", "attr", "value", "childNodes", "child", "Text", "text", "textContent", "createElement", "getJSXName", "replacement", "isget", "<PERSON><PERSON><PERSON>", "nodeAttr", "css", "split", "s", "pos", "ruleName", "trim", "ruleValue", "char<PERSON>t", "strCamelCase", "c1", "parse", "withDefaultAttributes", "BlockListBlock", "WrappedBlockEdit", "constructor", "uniqid", "attribute", "render", "hooks", "addFilter", "setup", "restrictMode", "modes", "setAttributes", "showToggle", "toggleText", "__", "toggleIcon", "toggleMode", "_BlockBody", "isSelected", "BlockBody", "ownProps", "rootClientId", "getBlockRootClientId", "index", "getBlockIndex", "Div", "__html", "children", "el", "setHTML", "componentDidUpdate", "componentDidMount", "store", "DynamicHTML", "setRef", "bind", "subscribed", "renderMethod", "loadState", "fetch", "state", "setState", "setHtml", "jsx", "$el", "shouldComponentUpdate", "nextProps", "nextState", "componentWillMove", "display", "context", "$prevParent", "parent", "$thisParent", "clone", "componentDidAppend", "componentDidRemount", "prevProps", "prevState", "doAction", "componentWillUnmount", "BlockForm", "form", "json", "serializeData", "silent", "serialize", "on", "BlockPreview", "prevAttributes", "preview", "replace", "nextAttributes", "thisAttributes", "className", "initialize", "editor", "addAction", "validateVerticalAlignment", "ALIGNMENTS", "DEFAULT", "includes", "validateHorizontalAlignment", "validateMatrixAlignment", "y", "x", "AlignmentToolbar", "BlockVerticalAlignmentToolbar", "BlockAlignmentMatrixToolbar", "__experimentalBlockAlignmentMatrixToolbar", "BlockAlignmentMatrixControl", "__experimentalBlockAlignmentMatrixControl", "OriginalBlockEdit", "AlignmentComponent", "validateAlignment", "console", "warn", "onChangeAlignContent", "onChangeAlignText", "j<PERSON><PERSON><PERSON>", "jsxNameReplacements", "accentheight", "acceptcharse<PERSON>", "accesskey", "alignmentbaseline", "allowedblocks", "allowfullscreen", "allowreorder", "arabicform", "attributename", "attributetype", "autocapitalize", "autocomplete", "autocorrect", "autofocus", "autoplay", "autoreverse", "autosave", "basefrequency", "baselineshift", "baseprofile", "calcmode", "capheight", "cellpadding", "cellspacing", "charset", "class", "classid", "classname", "clippath", "clippathunits", "<PERSON><PERSON><PERSON>", "colorinterpolation", "colorinterpolationfilters", "colorprofile", "colorrendering", "colspan", "contenteditable", "contentscripttype", "contentstyletype", "contextmenu", "controlslist", "crossorigin", "dangerouslysetinnerhtml", "datetime", "defaultchecked", "defaultvalue", "diffuseconstant", "disablepictureinpicture", "disableremoteplayback", "dominantbaseline", "edgemode", "enablebackground", "enctype", "enterkeyhint", "externalresourcesrequired", "fillopacity", "<PERSON><PERSON><PERSON>", "filterres", "filterunits", "floodcolor", "floodopacity", "fontfamily", "fontsize", "fontsizeadjust", "<PERSON><PERSON><PERSON><PERSON>", "fontstyle", "fontvariant", "fontweight", "for", "foreignobject", "formaction", "formenctype", "formmethod", "formnovalidate", "formtarget", "frameborder", "glyphname", "glyphorientationhorizontal", "glyphorientationvertical", "glyphref", "gradienttransform", "gradientunits", "horizadvx", "horizoriginx", "hreflang", "htmlfor", "httpequiv", "imagerendering", "innerhtml", "inputmode", "itemid", "itemprop", "itemref", "itemscope", "itemtype", "kernelmatrix", "kernelunitlength", "keyparams", "keypoints", "keysplines", "keytimes", "keytype", "lengthadjust", "letterspacing", "lightingcolor", "limitingconeangle", "marginheight", "marginwidth", "markerend", "markerheight", "markermid", "markerstart", "markerunits", "markerwidth", "maskcontentunits", "maskunits", "maxlength", "mediagroup", "minlength", "nomodule", "novalidate", "numoctaves", "overlineposition", "overlinethickness", "paintorder", "pathlength", "patterncontentunits", "patterntransform", "patternunits", "playsinline", "pointerevents", "pointsatx", "pointsaty", "pointsatz", "<PERSON><PERSON><PERSON>", "<PERSON>as<PERSON><PERSON>io", "primitiveunits", "radiogroup", "readonly", "referrerpolicy", "refx", "refy", "renderingintent", "repeatcount", "<PERSON>dur", "requiredextensions", "requiredfeatures", "rowspan", "shaperendering", "specularconstant", "specularexponent", "spellcheck", "spreadmethod", "srcdoc", "srclang", "srcset", "startoffset", "stddeviation", "stitchtiles", "stopcolor", "stopopacity", "strikethroughposition", "strikethroughthickness", "<PERSON><PERSON><PERSON><PERSON>", "strokedashoffset", "strokelinecap", "strokelinejoin", "strokemiterlimit", "strokeopacity", "strokewidth", "suppresscontenteditablewarning", "suppresshydrationwarning", "surfacescale", "systemlanguage", "tabindex", "tablevalues", "targetx", "targety", "templatelock", "textan<PERSON>", "textdecoration", "textlength", "textrendering", "underlineposition", "underlinethickness", "unicodebidi", "unicoderange", "unitsperem", "usemap", "valphabetic", "vectoreffect", "vertadvy", "vertoriginx", "vertoriginy", "vhanging", "videographic", "viewbox", "viewtarget", "vmathematical", "wordspacing", "writingmode", "xchannelselector", "xheight", "xlinkactuate", "xlinkarcrole", "xlinkhref", "xlinkrole", "xlinkshow", "xlinktitle", "xlinktype", "xmlbase", "xmllang", "xmlnsxlink", "xmlspace", "ychannelselector", "zoomandpan"], "sourceRoot": ""}