# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2022-12-13T15:07:38+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: hr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: acf.php:3, pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: acf.php:4, acf.php:8
#, fuzzy
#| msgid "https://www.advancedcustomfields.com/"
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com/"

#: acf.php:5
#, fuzzy
#| msgid ""
#| "Customise WordPress with powerful, professional and intuitive fields."
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Prilagodite WordPress sa moćnim, profesionalnim i intuitivnim dodatnim "
"poljima."

#: acf.php:7
msgid "Delicious Brains"
msgstr ""

#: acf.php:70
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:359, includes/admin/admin.php:50, includes/admin/admin.php:50
msgid "Field Groups"
msgstr "Grupe polja"

#: acf.php:360
msgid "Field Group"
msgstr "Grupa polja"

#: acf.php:361, acf.php:395, includes/admin/admin.php:51,
#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Add New"
msgstr "Dodaj"

#: acf.php:362
msgid "Add New Field Group"
msgstr "Dodaj novo polje"

#: acf.php:363
msgid "Edit Field Group"
msgstr "Uredi polje"

#: acf.php:364
msgid "New Field Group"
msgstr "Novo polje"

#: acf.php:365
msgid "View Field Group"
msgstr "Pregledaj polje"

#: acf.php:366
msgid "Search Field Groups"
msgstr "Pretraži polja"

#: acf.php:367
msgid "No Field Groups found"
msgstr "Niste dodali nijedno polje"

#: acf.php:368
msgid "No Field Groups found in Trash"
msgstr "Nije pronađena nijedna stranica"

#: acf.php:393, includes/admin/admin-field-group.php:233,
#: includes/admin/admin-field-groups.php:266,
#: pro/fields/class-acf-field-clone.php:814
msgid "Fields"
msgstr "Polja"

#: acf.php:394
msgid "Field"
msgstr "Polje"

#: acf.php:396
msgid "Add New Field"
msgstr "Dodaj polje"

#: acf.php:397
msgid "Edit Field"
msgstr "Uredi polje"

#: acf.php:398, includes/admin/views/field-group-fields.php:56
msgid "New Field"
msgstr "Dodaj polje"

#: acf.php:399
msgid "View Field"
msgstr "Pregledaj polje"

#: acf.php:400
msgid "Search Fields"
msgstr "Pretraži polja"

#: acf.php:401
msgid "No Fields found"
msgstr "Nije pronađeno nijedno polje"

#: acf.php:402
msgid "No Fields found in Trash"
msgstr "Nije pronađeno nijedno polje u smeću"

#: acf.php:440, includes/admin/admin-field-group.php:385,
#: includes/admin/admin-field-groups.php:230
#, fuzzy
#| msgid "Table"
msgctxt "post status"
msgid "Disabled"
msgstr "Tablica"

#: acf.php:445
#, fuzzy
#| msgid "Active <span class=\"count\">(%s)</span>"
#| msgid_plural "Active <span class=\"count\">(%s)</span>"
msgid "Disabled <span class=\"count\">(%s)</span>"
msgid_plural "Disabled <span class=\"count\">(%s)</span>"
msgstr[0] "Aktivno <span class=“count”>(%s)</span>"
msgstr[1] "Aktivno <span class=“count”>(%s)</span>"
msgstr[2] "Aktivno <span class=“count”>(%s)</span>"

#: acf.php:495
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""

#: acf.php:497
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""

#: includes/acf-field-functions.php:841,
#: includes/admin/admin-field-group.php:171
#, fuzzy
#| msgid "(no title)"
msgid "(no label)"
msgstr "(bez naziva)"

#: includes/acf-field-group-functions.php:846,
#: includes/admin/admin-field-group.php:173
msgid "copy"
msgstr "kopiraj"

#: includes/acf-value-functions.php:353
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" target=\"_blank"
"\">Learn how to fix this</a>."
msgstr ""

#: includes/acf-wp-functions.php:41
#, fuzzy
#| msgid "Post"
msgid "Posts"
msgstr "Objava"

#: includes/acf-wp-functions.php:54
#, fuzzy
#| msgid "Taxonomy"
msgid "Taxonomies"
msgstr "Taksonomija"

#: includes/acf-wp-functions.php:59
#, fuzzy
#| msgid "Attachment"
msgid "Attachments"
msgstr "Prilog"

#: includes/acf-wp-functions.php:63,
#: includes/admin/views/field-group-options.php:144
msgid "Comments"
msgstr "Komentari"

#: includes/acf-wp-functions.php:67
#, fuzzy
#| msgid "Widget"
msgid "Widgets"
msgstr "Widget"

#: includes/acf-wp-functions.php:71,
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Izbornici"

#: includes/acf-wp-functions.php:75
#, fuzzy
#| msgid "Menu Item"
msgid "Menu items"
msgstr "Stavka izbornika"

#: includes/acf-wp-functions.php:79
#, fuzzy
#| msgid "User"
msgid "Users"
msgstr "Korisnik"

#: includes/acf-wp-functions.php:83, pro/options-page.php:47
msgid "Options"
msgstr "Postavke"

#: includes/acf-wp-functions.php:87
#, fuzzy
#| msgid "Block"
msgid "Blocks"
msgstr "Blok"

#: includes/assets.php:348
msgid "Are you sure?"
msgstr "Jeste li sigurni?"

#: includes/assets.php:349, includes/fields/class-acf-field-true_false.php:80,
#: includes/fields/class-acf-field-true_false.php:176,
#: pro/admin/views/html-settings-updates.php:104
msgid "Yes"
msgstr "Da"

#: includes/assets.php:350, includes/fields/class-acf-field-true_false.php:83,
#: includes/fields/class-acf-field-true_false.php:193,
#: pro/admin/views/html-settings-updates.php:116
msgid "No"
msgstr "Ne"

#: includes/assets.php:351, includes/fields/class-acf-field-file.php:159,
#: includes/fields/class-acf-field-image.php:139,
#: includes/fields/class-acf-field-link.php:142,
#: pro/fields/class-acf-field-gallery.php:336,
#: pro/fields/class-acf-field-gallery.php:491
msgid "Remove"
msgstr "Ukloni"

#: includes/assets.php:352
msgid "Cancel"
msgstr "Otkaži"

#: includes/assets.php:362
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Izmjene koje ste napravili bit će izgubljene ukoliko napustite ovu stranicu"

#: includes/assets.php:365
msgid "Validation successful"
msgstr "Uspješna verifikacija"

#: includes/assets.php:366, includes/validation.php:286,
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Verifikacija nije uspjela"

#: includes/assets.php:367
msgid "1 field requires attention"
msgstr "1 polje treba vašu pažnju"

#: includes/assets.php:368
msgid "%d fields require attention"
msgstr "Nekoliko polja treba vašu pažnje: %d"

#: includes/assets.php:371, includes/forms/form-comment.php:160,
#: pro/admin/admin-options-page.php:309
msgid "Edit field group"
msgstr "Uredi skup polja"

#: includes/fields.php:313
msgid "Field type does not exist"
msgstr "Tip polja ne postoji"

#: includes/fields.php:313
msgid "Unknown"
msgstr "Nepoznato polje"

#: includes/fields.php:354
msgid "Basic"
msgstr "Osnovno"

#: includes/fields.php:355, includes/forms/form-front.php:49
msgid "Content"
msgstr "Sadržaj"

#: includes/fields.php:356
msgid "Choice"
msgstr "Odabir"

#: includes/fields.php:357
msgid "Relational"
msgstr "Relacijski"

#: includes/fields.php:358
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:359,
#: includes/fields/class-acf-field-button-group.php:181,
#: includes/fields/class-acf-field-checkbox.php:377,
#: includes/fields/class-acf-field-group.php:462,
#: includes/fields/class-acf-field-radio.php:256,
#: pro/fields/class-acf-field-clone.php:850,
#: pro/fields/class-acf-field-flexible-content.php:549,
#: pro/fields/class-acf-field-flexible-content.php:604,
#: pro/fields/class-acf-field-repeater.php:451
msgid "Layout"
msgstr "Format"

#: includes/locations.php:25
#, fuzzy
#| msgid "Field type does not exist"
msgid "Class \"%s\" does not exist."
msgstr "Tip polja ne postoji"

#: includes/locations.php:36
msgid "Location type \"%s\" is already registered."
msgstr ""

#: includes/locations.php:99, includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Objava"

#: includes/locations.php:100,
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Stranice"

#: includes/locations.php:101, includes/fields/class-acf-field-user.php:20
msgid "User"
msgstr "Korisnik"

#: includes/locations.php:102
msgid "Forms"
msgstr "Forme"

#: includes/media.php:48, includes/fields/class-acf-field-select.php:255
msgctxt "verb"
msgid "Select"
msgstr "Odaberi"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Uredi"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "Ažuriraj"

#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "Postavljeno uz ovu objavu"

#: includes/media.php:52
msgid "Expand Details"
msgstr "Prošireni prikaz"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "Sakrij detalje"

#: includes/media.php:54
msgid "Restricted"
msgstr "Ograničen pristup"

#: includes/media.php:55, includes/fields/class-acf-field-image.php:67
msgid "All images"
msgstr "Sve slike"

#: includes/validation.php:364
msgid "%s value is required"
msgstr "%s je obavezno"

#: pro/blocks.php:37
#, fuzzy
#| msgid "%s value is required"
msgid "Block type name is required."
msgstr "%s je obavezno"

#: pro/blocks.php:44
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:495
msgid "Switch to Edit"
msgstr ""

#: pro/blocks.php:496
msgid "Switch to Preview"
msgstr ""

#: pro/blocks.php:497
msgid "Change content alignment"
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:500
#, fuzzy
#| msgid "Settings"
msgid "%s settings"
msgstr "Postavke"

#: pro/options-page.php:77, includes/forms/form-front.php:106,
#: pro/fields/class-acf-field-gallery.php:523
msgid "Update"
msgstr "Ažuriraj"

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Postavke spremljene"

#: pro/updates.php:99
#, fuzzy
#| msgid ""
#| "To enable updates, please enter your license key on the <a href=\"%s"
#| "\">Updates</a> page. If you don't have a licence key, please see <a href="
#| "\"%s\">details & pricing</a>."
msgid ""
"To enable updates, please enter your license key on the <a href=\"%1$s"
"\">Updates</a> page. If you don't have a licence key, please see <a href="
"\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Da bi omogućili automatsko ažuriranje, molimo unesite licencu na stranici <a "
"href=“%s”>ažuriranja</a>. Ukoliko nemate licencu, pogledajte <a "
"href=“%s”>opcije i cijene</a>."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
#, fuzzy
#| msgid "<b>Error</b>. Could not connect to update server"
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr "<b>Greška</b>. Greška prilikom spajanja na server"

#: pro/updates.php:279, pro/admin/views/html-settings-updates.php:117
msgid "Check Again"
msgstr "Provjeri ponovno"

#: pro/updates.php:561
#, fuzzy
#| msgid "<b>Error</b>. Could not connect to update server"
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr "<b>Greška</b>. Greška prilikom spajanja na server"

#: includes/admin/admin-field-group.php:84,
#: includes/admin/admin-field-group.php:85,
#: includes/admin/admin-field-group.php:87
msgid "Field group updated."
msgstr "Skup polja ažuriran."

#: includes/admin/admin-field-group.php:86
msgid "Field group deleted."
msgstr "Skup polja izbrisan."

#: includes/admin/admin-field-group.php:89
msgid "Field group published."
msgstr "Skup polja objavljen."

#: includes/admin/admin-field-group.php:90
msgid "Field group saved."
msgstr "Skup polja spremljen."

#: includes/admin/admin-field-group.php:91
msgid "Field group submitted."
msgstr "Skup polja je spremljen."

#: includes/admin/admin-field-group.php:92
msgid "Field group scheduled for."
msgstr "Skup polja je označen za."

#: includes/admin/admin-field-group.php:93
msgid "Field group draft updated."
msgstr "Skica ažurirana."

#: includes/admin/admin-field-group.php:164
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Polje ne može započinjati sa “field_”, odabrite drugi naziv"

#: includes/admin/admin-field-group.php:165
msgid "This field cannot be moved until its changes have been saved"
msgstr "Potrebno je spremiti izmjene prije nego možete premjestiti polje"

#: includes/admin/admin-field-group.php:166
msgid "Field group title is required"
msgstr "Naziv polja je obavezna"

#: includes/admin/admin-field-group.php:167
msgid "Move to trash. Are you sure?"
msgstr "Premjesti u smeće?"

#: includes/admin/admin-field-group.php:168
msgid "No toggle fields available"
msgstr "Nema polja koji omoguću korisniku odabir"

#: includes/admin/admin-field-group.php:169
msgid "Move Custom Field"
msgstr "Premjesti polje"

#: includes/admin/admin-field-group.php:170
#, fuzzy
#| msgid "checked"
msgid "Checked"
msgstr "odabrano"

#: includes/admin/admin-field-group.php:172
#, fuzzy
#| msgid "Show this field if"
msgid "(this field)"
msgstr "Prikaži polje ako"

#: includes/admin/admin-field-group.php:174, includes/api/api-helpers.php:3419,
#: includes/admin/views/field-group-field-conditional-logic.php:60,
#: includes/admin/views/field-group-field-conditional-logic.php:170,
#: includes/admin/views/field-group-locations.php:36,
#: includes/admin/views/html-location-group.php:3
msgid "or"
msgstr "ili"

#: includes/admin/admin-field-group.php:175,
#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Prikaži ovaj skup polja ako"

#: includes/admin/admin-field-group.php:176
msgid "Null"
msgstr "Null"

#: includes/admin/admin-field-group.php:179
msgid "Has any value"
msgstr ""

#: includes/admin/admin-field-group.php:180
msgid "Has no value"
msgstr ""

#: includes/admin/admin-field-group.php:181
#, fuzzy
#| msgid "is equal to"
msgid "Value is equal to"
msgstr "je jednako"

#: includes/admin/admin-field-group.php:182
#, fuzzy
#| msgid "is not equal to"
msgid "Value is not equal to"
msgstr "je drukčije"

#: includes/admin/admin-field-group.php:183
#, fuzzy
#| msgid "Value must be a number"
msgid "Value matches pattern"
msgstr "Vrijednost mora biti broj"

#: includes/admin/admin-field-group.php:184
msgid "Value contains"
msgstr ""

#: includes/admin/admin-field-group.php:185
#, fuzzy
#| msgid "Value must be equal to or lower than %d"
msgid "Value is greater than"
msgstr "Unešena vrijednost mora biti jednaka ili niža od %d"

#: includes/admin/admin-field-group.php:186
#, fuzzy
#| msgid "Value must be a number"
msgid "Value is less than"
msgstr "Vrijednost mora biti broj"

#: includes/admin/admin-field-group.php:187
msgid "Selection is greater than"
msgstr ""

#: includes/admin/admin-field-group.php:188
#, fuzzy
#| msgid "Select File"
msgid "Selection is less than"
msgstr "Odaberite datoteku"

#: includes/admin/admin-field-group.php:191
msgid "Repeater (Pro only)"
msgstr ""

#: includes/admin/admin-field-group.php:192
#, fuzzy
#| msgid "Flexible Content"
msgid "Flexible Content (Pro only)"
msgstr "Fleksibilno polje"

#: includes/admin/admin-field-group.php:193
msgid "Clone (Pro only)"
msgstr ""

#: includes/admin/admin-field-group.php:194
msgid "Gallery (Pro only)"
msgstr ""

#: includes/admin/admin-field-group.php:234,
#: includes/admin/admin-field-groups.php:265
msgid "Location"
msgstr "Lokacija"

#: includes/admin/admin-field-group.php:235,
#: includes/admin/tools/class-acf-admin-tool-export.php:288
msgid "Settings"
msgstr "Postavke"

#: includes/admin/admin-field-group.php:361
msgid "Field Keys"
msgstr "Oznaka polja"

#: includes/admin/admin-field-group.php:385
#, fuzzy
#| msgid "Active"
msgctxt "post status"
msgid "Active"
msgstr "Aktivan"

#: includes/admin/admin-field-group.php:752
msgid "Move Complete."
msgstr "Premještanje dovršeno."

#: includes/admin/admin-field-group.php:754
#, fuzzy
#| msgid "The %s field can now be found in the %s field group"
msgid "The %1$s field can now be found in the %2$s field group"
msgstr ""
"Polje %s od sada možete naći na drugoj lokacaiji, kao dio %s skupa polja"

#: includes/admin/admin-field-group.php:758
msgid "Close Window"
msgstr "Zatvori prozor"

#: includes/admin/admin-field-group.php:797
msgid "Please select the destination for this field"
msgstr "Odaberite lokaciju za ovo polje"

#: includes/admin/admin-field-group.php:804
msgid "Move Field"
msgstr "Premjesti polje"

#: includes/admin/admin-field-groups.php:116
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Aktivno <span class=“count”>(%s)</span>"
msgstr[1] "Aktivno <span class=“count”>(%s)</span>"
msgstr[2] "Aktivno <span class=“count”>(%s)</span>"

#: includes/admin/admin-field-groups.php:196
msgid "Review local JSON changes"
msgstr ""

#: includes/admin/admin-field-groups.php:197
#, fuzzy
#| msgid "Loading"
msgid "Loading diff"
msgstr "Učitavanje"

#: includes/admin/admin-field-groups.php:198,
#: includes/admin/admin-field-groups.php:533
msgid "Sync changes"
msgstr ""

#: includes/admin/admin-field-groups.php:263,
#: pro/fields/class-acf-field-gallery.php:388,
#: includes/admin/views/field-group-options.php:127,
#: includes/admin/views/html-admin-page-upgrade-network.php:38,
#: includes/admin/views/html-admin-page-upgrade-network.php:49
msgid "Description"
msgstr "Opis"

#: includes/admin/admin-field-groups.php:264,
#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Ključ"

#: includes/admin/admin-field-groups.php:269
msgid "Local JSON"
msgstr "Učitavanje polja iz JSON datoteke"

#: includes/admin/admin-field-groups.php:419
msgid "Various"
msgstr ""

#: includes/admin/admin-field-groups.php:449
#, fuzzy
#| msgid "Location"
msgid "Located in: %s"
msgstr "Lokacija"

#: includes/admin/admin-field-groups.php:445
msgid "Located in plugin: %s"
msgstr ""

#: includes/admin/admin-field-groups.php:441
msgid "Located in theme: %s"
msgstr ""

#: includes/admin/admin-field-groups.php:484
msgid "Awaiting save"
msgstr ""

#: includes/admin/admin-field-groups.php:481
msgid "Saved"
msgstr ""

#: includes/admin/admin-field-groups.php:469,
#: includes/admin/admin-field-groups.php:687
msgid "Sync available"
msgstr "Sinkronizacija dostupna"

#: includes/admin/admin-field-groups.php:477
msgid "Import"
msgstr "Uvoz"

#: includes/admin/admin-field-groups.php:472
msgid "Sync"
msgstr "Sinkroniziraj"

#: includes/admin/admin-field-groups.php:473
msgid "Review changes"
msgstr ""

#: includes/admin/admin-field-groups.php:505
msgid "Duplicate this item"
msgstr "Dupliciraj"

#: includes/admin/admin-field-groups.php:505,
#: includes/admin/admin-field-groups.php:525,
#: pro/fields/class-acf-field-flexible-content.php:553,
#: includes/admin/views/field-group-field.php:57
msgid "Duplicate"
msgstr "Dupliciraj"

#: includes/admin/admin-field-groups.php:555
#, fuzzy
#| msgid "Field group duplicated. %s"
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Skup polja %s dupliciran"
msgstr[1] "Skup polja %s dupliciran"
msgstr[2] "Skup polja %s dupliciran"

#: includes/admin/admin-field-groups.php:612
#, fuzzy
#| msgid "Field group synchronised. %s"
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "Skup polja sinkroniziran. %s"
msgstr[1] "Skup polja sinkroniziran. %s"
msgstr[2] "Skup polja sinkroniziran. %s"

#: includes/admin/admin-field-groups.php:800
msgid "Select %s"
msgstr "Odaberi %s"

#: includes/admin/admin-tools.php:119,
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Alati"

#: includes/admin/admin-upgrade.php:51, includes/admin/admin-upgrade.php:113,
#: includes/admin/admin-upgrade.php:114, includes/admin/admin-upgrade.php:177,
#: includes/admin/views/html-admin-page-upgrade-network.php:24,
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Nadogradi bazu podataka"

#: includes/admin/admin-upgrade.php:201
msgid "Review sites & upgrade"
msgstr "Pregledaj stranice i nadogradi"

#: includes/admin/admin.php:49,
#: includes/admin/views/field-group-options.php:142
msgid "Custom Fields"
msgstr "Dodatna polja"

#: includes/admin/admin.php:129, includes/admin/admin.php:131
msgid "Overview"
msgstr ""

#: includes/admin/admin.php:132
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""

#: includes/admin/admin.php:134
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""

#: includes/admin/admin.php:137
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""

#: includes/admin/admin.php:146, includes/admin/admin.php:148
#, fuzzy
#| msgid "Support"
msgid "Help & Support"
msgstr "Podrška"

#: includes/admin/admin.php:149
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""

#: includes/admin/admin.php:152
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""

#: includes/admin/admin.php:156
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the ‘how-tos’ of the ACF world."
msgstr ""

#: includes/admin/admin.php:160
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""

#: includes/admin/admin.php:169
#, fuzzy
#| msgid "Update Information"
msgid "Information"
msgstr "Ažuriraj informacije"

#: includes/admin/admin.php:170
#, fuzzy
#| msgid "Revisions"
msgid "Version %s"
msgstr "Revizija"

#: includes/admin/admin.php:171
#, fuzzy
#| msgid "View Field"
msgid "View details"
msgstr "Pregledaj polje"

#: includes/admin/admin.php:172
#, fuzzy
#| msgid "Website"
msgid "Visit website"
msgstr "Web mjesto"

#: includes/admin/admin.php:201,
#: includes/admin/views/field-group-field-conditional-logic.php:157,
#: includes/admin/views/html-location-rule.php:92
msgid "and"
msgstr "i"

#: includes/ajax/class-acf-ajax-local-json-diff.php:36
#, fuzzy
#| msgid "%s field group duplicated."
#| msgid_plural "%s field groups duplicated."
msgid "Invalid field group parameter(s)."
msgstr "Polja duplicirana (%s)."

#: includes/ajax/class-acf-ajax-local-json-diff.php:43
#, fuzzy
#| msgid "Edit field group"
msgid "Invalid field group ID."
msgstr "Uredi skup polja"

#: includes/ajax/class-acf-ajax-local-json-diff.php:53
msgid "Sorry, this field group is unavailable for diff comparison."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:59
#, fuzzy
#| msgid "Post updated"
msgid "Last updated: %s"
msgstr "Objava ažurirana"

#: includes/ajax/class-acf-ajax-local-json-diff.php:64
#, fuzzy
#| msgid "Edit field group"
msgid "Original field group"
msgstr "Uredi skup polja"

#: includes/ajax/class-acf-ajax-local-json-diff.php:68
#, fuzzy
#| msgid "No field groups selected"
msgid "JSON field group (newer)"
msgstr "Niste odabrali polje"

#: includes/ajax/class-acf-ajax-upgrade.php:34,
#: includes/admin/views/html-admin-page-upgrade.php:94
msgid "No updates available."
msgstr "Nema novih nadogradnji."

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr ""

#: includes/api/api-helpers.php:821
msgid "Thumbnail"
msgstr "Sličica"

#: includes/api/api-helpers.php:822
msgid "Medium"
msgstr "Srednja"

#: includes/api/api-helpers.php:823
msgid "Large"
msgstr "Velika"

#: includes/api/api-helpers.php:864
msgid "Full Size"
msgstr "Puna veličina"

#: includes/api/api-helpers.php:1569, includes/api/api-term.php:147,
#: pro/fields/class-acf-field-clone.php:1005
msgid "(no title)"
msgstr "(bez naziva)"

#: includes/api/api-helpers.php:3353
msgid "Image width must not exceed %dpx."
msgstr "Širina slike ne smije biti veća od %dpx."

#: includes/api/api-helpers.php:3348
msgid "Image width must be at least %dpx."
msgstr "Širina slike mora biti najmanje %dpx."

#: includes/api/api-helpers.php:3372
msgid "Image height must not exceed %dpx."
msgstr "Visina slike ne smije biti veća od %dpx."

#: includes/api/api-helpers.php:3367
msgid "Image height must be at least %dpx."
msgstr "Visina slike mora biti najmanje %dpx."

#: includes/api/api-helpers.php:3392
#, fuzzy
#| msgid "File size must must not exceed %s."
msgid "File size must not exceed %s."
msgstr "Datoteke ne smije biti veća od %s."

#: includes/api/api-helpers.php:3387
msgid "File size must be at least %s."
msgstr "Veličina datoteke mora biti najmanje %s."

#: includes/api/api-helpers.php:3423
msgid "File type must be %s."
msgstr "Tip datoteke mora biti %s."

#: includes/fields/class-acf-field-accordion.php:25
msgid "Accordion"
msgstr "Multi prošireno"

#: includes/fields/class-acf-field-accordion.php:102
msgid "Open"
msgstr "Otvori"

#: includes/fields/class-acf-field-accordion.php:103
msgid "Display this accordion as open on page load."
msgstr "Prikaži accordion polje kao otvoreno prilikom učitavanja."

#: includes/fields/class-acf-field-accordion.php:114
msgid "Multi-expand"
msgstr "Mulit-proširenje"

#: includes/fields/class-acf-field-accordion.php:115
msgid "Allow this accordion to open without closing others."
msgstr "Omogući prikaz ovog accordion polja bez zatvaranje ostalih."

#: includes/fields/class-acf-field-accordion.php:126,
#: includes/fields/class-acf-field-tab.php:117
msgid "Endpoint"
msgstr "Prijelomna točka"

#: includes/fields/class-acf-field-accordion.php:127
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Preciziraj prijelomnu točku za prethoda polja accordion. Ovo će omogućiti "
"novi skup polja nakon prijelomne točke."

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Skup dugmadi"

#: includes/fields/class-acf-field-button-group.php:147,
#: includes/fields/class-acf-field-checkbox.php:324,
#: includes/fields/class-acf-field-radio.php:191,
#: includes/fields/class-acf-field-select.php:364
msgid "Choices"
msgstr "Mogući odabiri"

#: includes/fields/class-acf-field-button-group.php:148,
#: includes/fields/class-acf-field-checkbox.php:325,
#: includes/fields/class-acf-field-radio.php:192,
#: includes/fields/class-acf-field-select.php:365
msgid "Enter each choice on a new line."
msgstr "Svaki odabir je potrebno dodati kao novi red."

#: includes/fields/class-acf-field-button-group.php:148,
#: includes/fields/class-acf-field-checkbox.php:325,
#: includes/fields/class-acf-field-radio.php:192,
#: includes/fields/class-acf-field-select.php:365
msgid "For more control, you may specify both a value and label like this:"
msgstr "Za bolju kontrolu unesite oboje, vrijednost i naziv, kao npr:"

#: includes/fields/class-acf-field-button-group.php:148,
#: includes/fields/class-acf-field-checkbox.php:325,
#: includes/fields/class-acf-field-radio.php:192,
#: includes/fields/class-acf-field-select.php:365
msgid "red : Red"
msgstr "crvena : Crvena"

#: includes/fields/class-acf-field-button-group.php:158,
#: includes/fields/class-acf-field-page_link.php:482,
#: includes/fields/class-acf-field-post_object.php:394,
#: includes/fields/class-acf-field-radio.php:202,
#: includes/fields/class-acf-field-select.php:386,
#: includes/fields/class-acf-field-taxonomy.php:748,
#: includes/fields/class-acf-field-user.php:68
msgid "Allow Null?"
msgstr "Dozvoli null vrijednost?"

#: includes/fields/class-acf-field-button-group.php:170,
#: includes/fields/class-acf-field-checkbox.php:366,
#: includes/fields/class-acf-field-color_picker.php:155,
#: includes/fields/class-acf-field-email.php:117,
#: includes/fields/class-acf-field-number.php:125,
#: includes/fields/class-acf-field-radio.php:245,
#: includes/fields/class-acf-field-range.php:163,
#: includes/fields/class-acf-field-select.php:375,
#: includes/fields/class-acf-field-text.php:97,
#: includes/fields/class-acf-field-textarea.php:103,
#: includes/fields/class-acf-field-true_false.php:148,
#: includes/fields/class-acf-field-url.php:101,
#: includes/fields/class-acf-field-wysiwyg.php:334
msgid "Default Value"
msgstr "Zadana vrijednost"

#: includes/fields/class-acf-field-button-group.php:171,
#: includes/fields/class-acf-field-email.php:118,
#: includes/fields/class-acf-field-number.php:126,
#: includes/fields/class-acf-field-radio.php:246,
#: includes/fields/class-acf-field-range.php:164,
#: includes/fields/class-acf-field-text.php:98,
#: includes/fields/class-acf-field-textarea.php:104,
#: includes/fields/class-acf-field-url.php:102,
#: includes/fields/class-acf-field-wysiwyg.php:335
msgid "Appears when creating a new post"
msgstr "Prikazuje se prilikom kreiranje nove objave"

#: includes/fields/class-acf-field-button-group.php:187,
#: includes/fields/class-acf-field-checkbox.php:384,
#: includes/fields/class-acf-field-radio.php:263
msgid "Horizontal"
msgstr "Horizontalno"

#: includes/fields/class-acf-field-button-group.php:188,
#: includes/fields/class-acf-field-checkbox.php:383,
#: includes/fields/class-acf-field-radio.php:262
msgid "Vertical"
msgstr "Vertikalno"

#: includes/fields/class-acf-field-button-group.php:197,
#: includes/fields/class-acf-field-checkbox.php:405,
#: includes/fields/class-acf-field-file.php:227,
#: includes/fields/class-acf-field-link.php:170,
#: includes/fields/class-acf-field-radio.php:272,
#: includes/fields/class-acf-field-taxonomy.php:801
msgid "Return Value"
msgstr "Vrati vrijednost"

#: includes/fields/class-acf-field-button-group.php:198,
#: includes/fields/class-acf-field-checkbox.php:406,
#: includes/fields/class-acf-field-file.php:228,
#: includes/fields/class-acf-field-link.php:171,
#: includes/fields/class-acf-field-radio.php:273
msgid "Specify the returned value on front end"
msgstr "Vrijednost koja će biti vraćena na pristupnom dijelu"

#: includes/fields/class-acf-field-button-group.php:203,
#: includes/fields/class-acf-field-checkbox.php:411,
#: includes/fields/class-acf-field-radio.php:278,
#: includes/fields/class-acf-field-select.php:444
msgid "Value"
msgstr "Vrijednost"

#: includes/fields/class-acf-field-button-group.php:204,
#: includes/fields/class-acf-field-checkbox.php:412,
#: includes/fields/class-acf-field-radio.php:279,
#: includes/fields/class-acf-field-select.php:445,
#: pro/fields/class-acf-field-flexible-content.php:581,
#: includes/admin/views/field-group-fields.php:5
msgid "Label"
msgstr "Oznaka"

#: includes/fields/class-acf-field-button-group.php:205,
#: includes/fields/class-acf-field-checkbox.php:413,
#: includes/fields/class-acf-field-radio.php:280,
#: includes/fields/class-acf-field-select.php:446
msgid "Both (Array)"
msgstr "Oboje (podatkovni niz)"

#: includes/fields/class-acf-field-checkbox.php:25,
#: includes/fields/class-acf-field-taxonomy.php:733
msgid "Checkbox"
msgstr "Skup dugmadi"

#: includes/fields/class-acf-field-checkbox.php:144
msgid "Toggle All"
msgstr "Sakrij sve"

#: includes/fields/class-acf-field-checkbox.php:207
msgid "Add new choice"
msgstr "Dodaj odabir"

#: includes/fields/class-acf-field-checkbox.php:335
msgid "Allow Custom"
msgstr "Obogući dodatne"

#: includes/fields/class-acf-field-checkbox.php:340
msgid "Allow 'custom' values to be added"
msgstr "Omogući ‘dodatne’ vrijednosti"

#: includes/fields/class-acf-field-checkbox.php:348
msgid "Save Custom"
msgstr "Spremi"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Save 'custom' values to the field's choices"
msgstr "Spremi ‘dodatne’ vrijednosti i prikaži ih omogući njihov odabir"

#: includes/fields/class-acf-field-checkbox.php:367,
#: includes/fields/class-acf-field-select.php:376
msgid "Enter each default value on a new line"
msgstr "Unesite svaku novu vrijednost u zasebnu liniju"

#: includes/fields/class-acf-field-checkbox.php:393
msgid "Toggle"
msgstr "Prikaži/Sakrij"

#: includes/fields/class-acf-field-checkbox.php:394
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Dodaj okvir za izbor koji omogućje odabir svih opcija"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Odabir boje"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Clear"
msgstr "Ukloni"

#: includes/fields/class-acf-field-color_picker.php:70
#, fuzzy
#| msgid "Clear location"
msgid "Clear color"
msgstr "Ukloni lokaciju"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Default"
msgstr "Zadano"

#: includes/fields/class-acf-field-color_picker.php:72
#, fuzzy
#| msgid "Select Color"
msgid "Select default color"
msgstr "Odaberite boju"

#: includes/fields/class-acf-field-color_picker.php:73
msgid "Select Color"
msgstr "Odaberite boju"

#: includes/fields/class-acf-field-color_picker.php:74
#, fuzzy
#| msgid "Color Picker"
msgid "Color value"
msgstr "Odabir boje"

#: includes/fields/class-acf-field-color_picker.php:95,
#: includes/fields/class-acf-field-color_picker.php:185
msgid "Hex String"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:96
msgid "RGBA String"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:167
msgid "Enable Transparency"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:179,
#: includes/fields/class-acf-field-date_picker.php:216,
#: includes/fields/class-acf-field-date_time_picker.php:201,
#: includes/fields/class-acf-field-image.php:204,
#: includes/fields/class-acf-field-post_object.php:418,
#: includes/fields/class-acf-field-relationship.php:662,
#: includes/fields/class-acf-field-select.php:439,
#: includes/fields/class-acf-field-time_picker.php:131,
#: includes/fields/class-acf-field-user.php:90,
#: pro/fields/class-acf-field-gallery.php:573
msgid "Return Format"
msgstr "Format za prikaz na web stranici"

#: includes/fields/class-acf-field-color_picker.php:186
#, fuzzy
#| msgid "Both (Array)"
msgid "RGBA Array"
msgstr "Oboje (podatkovni niz)"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Odabir datuma"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Završeno"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Danas"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Slijedeći"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Prethodni"

#: includes/fields/class-acf-field-date_picker.php:64
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Tjedan"

#: includes/fields/class-acf-field-date_picker.php:181,
#: includes/fields/class-acf-field-date_time_picker.php:182,
#: includes/fields/class-acf-field-time_picker.php:114
msgid "Display Format"
msgstr "Format prikaza"

#: includes/fields/class-acf-field-date_picker.php:182,
#: includes/fields/class-acf-field-date_time_picker.php:183,
#: includes/fields/class-acf-field-time_picker.php:115
msgid "The format displayed when editing a post"
msgstr "Format za prikaz prilikom administracije"

#: includes/fields/class-acf-field-date_picker.php:190,
#: includes/fields/class-acf-field-date_picker.php:226,
#: includes/fields/class-acf-field-date_time_picker.php:192,
#: includes/fields/class-acf-field-date_time_picker.php:211,
#: includes/fields/class-acf-field-time_picker.php:122,
#: includes/fields/class-acf-field-time_picker.php:139
msgid "Custom:"
msgstr "Prilagođeno:"

#: includes/fields/class-acf-field-date_picker.php:217,
#: includes/fields/class-acf-field-date_time_picker.php:202,
#: includes/fields/class-acf-field-time_picker.php:132
msgid "The format returned via template functions"
msgstr "Format koji vraća funkcija"

#: includes/fields/class-acf-field-date_picker.php:202
msgid "Save Format"
msgstr "Spremi format"

#: includes/fields/class-acf-field-date_picker.php:203
msgid "The format used when saving a value"
msgstr "Format koji će biti spremljen"

#: includes/fields/class-acf-field-date_picker.php:237,
#: includes/fields/class-acf-field-date_time_picker.php:220
msgid "Week Starts On"
msgstr "Tjedan počinje"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Odabir datuma i sata"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Odaberi vrijeme"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Vrijeme"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Sat"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuta"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Sekunda"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milisekunda"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosekunda"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Vremenska zona"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Trenutno vrijeme"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Završeno"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Odaberi"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "Prije podne"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "Prije podne"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "Poslije podne"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "Poslije podne"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-email.php:128,
#: includes/fields/class-acf-field-number.php:136,
#: includes/fields/class-acf-field-password.php:73,
#: includes/fields/class-acf-field-text.php:108,
#: includes/fields/class-acf-field-textarea.php:114,
#: includes/fields/class-acf-field-url.php:112
msgid "Placeholder Text"
msgstr "Zadana vrijednost"

#: includes/fields/class-acf-field-email.php:129,
#: includes/fields/class-acf-field-number.php:137,
#: includes/fields/class-acf-field-password.php:74,
#: includes/fields/class-acf-field-text.php:109,
#: includes/fields/class-acf-field-textarea.php:115,
#: includes/fields/class-acf-field-url.php:113
msgid "Appears within the input"
msgstr "Prikazuje se unutar polja"

#: includes/fields/class-acf-field-email.php:139,
#: includes/fields/class-acf-field-number.php:147,
#: includes/fields/class-acf-field-password.php:84,
#: includes/fields/class-acf-field-range.php:210,
#: includes/fields/class-acf-field-text.php:119
msgid "Prepend"
msgstr "Umetni ispred"

#: includes/fields/class-acf-field-email.php:140,
#: includes/fields/class-acf-field-number.php:148,
#: includes/fields/class-acf-field-password.php:85,
#: includes/fields/class-acf-field-range.php:211,
#: includes/fields/class-acf-field-text.php:120
msgid "Appears before the input"
msgstr "Prijazuje se ispred polja"

#: includes/fields/class-acf-field-email.php:150,
#: includes/fields/class-acf-field-number.php:158,
#: includes/fields/class-acf-field-password.php:95,
#: includes/fields/class-acf-field-range.php:221,
#: includes/fields/class-acf-field-text.php:130
msgid "Append"
msgstr "Umetni na kraj"

#: includes/fields/class-acf-field-email.php:151,
#: includes/fields/class-acf-field-number.php:159,
#: includes/fields/class-acf-field-password.php:96,
#: includes/fields/class-acf-field-range.php:222,
#: includes/fields/class-acf-field-text.php:131
msgid "Appears after the input"
msgstr "Prikazuje se iza polja"

#: includes/fields/class-acf-field-email.php:175
msgid "'%s' is not a valid email address"
msgstr ""

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Datoteka"

#: includes/fields/class-acf-field-file.php:58,
#: includes/admin/tools/class-acf-admin-tool-import.php:55
msgid "Select File"
msgstr "Odaberite datoteku"

#: includes/fields/class-acf-field-file.php:59
msgid "Edit File"
msgstr "Uredi datoteku"

#: includes/fields/class-acf-field-file.php:60
msgid "Update File"
msgstr "Ažuriraj datoteku"

#: includes/fields/class-acf-field-file.php:147
msgid "File name"
msgstr "Naziv datoteke"

#: includes/fields/class-acf-field-file.php:151,
#: includes/fields/class-acf-field-file.php:264,
#: includes/fields/class-acf-field-file.php:277,
#: includes/fields/class-acf-field-image.php:276,
#: includes/fields/class-acf-field-image.php:313,
#: pro/fields/class-acf-field-gallery.php:682,
#: pro/fields/class-acf-field-gallery.php:719
msgid "File size"
msgstr "Veličina datoteke"

#: includes/fields/class-acf-field-file.php:157,
#: includes/fields/class-acf-field-image.php:137,
#: includes/fields/class-acf-field-link.php:142,
#: pro/fields/class-acf-field-gallery.php:335,
#: includes/admin/views/field-group-field.php:56
msgid "Edit"
msgstr "Uredi"

#: includes/fields/class-acf-field-file.php:182,
#: includes/admin/tools/class-acf-admin-tool-import.php:89
msgid "No file selected"
msgstr "Niste odabrali datoteku"

#: includes/fields/class-acf-field-file.php:182
msgid "Add File"
msgstr "Dodaj datoteku"

#: includes/fields/class-acf-field-file.php:233
msgid "File Array"
msgstr "Vrijednost kao niz"

#: includes/fields/class-acf-field-file.php:234
msgid "File URL"
msgstr "Putanja datoteke"

#: includes/fields/class-acf-field-file.php:235
msgid "File ID"
msgstr "Vrijednost kao ID"

#: includes/fields/class-acf-field-file.php:244,
#: includes/fields/class-acf-field-image.php:233,
#: pro/fields/class-acf-field-gallery.php:617
msgid "Library"
msgstr "Zbirka"

#: includes/fields/class-acf-field-file.php:245,
#: includes/fields/class-acf-field-image.php:234,
#: pro/fields/class-acf-field-gallery.php:618
msgid "Limit the media library choice"
msgstr "Ograniči odabir iz zbirke"

#: includes/fields/class-acf-field-file.php:250,
#: includes/fields/class-acf-field-image.php:239,
#: includes/locations/class-acf-location-attachment.php:73,
#: includes/locations/class-acf-location-comment.php:61,
#: includes/locations/class-acf-location-nav-menu.php:74,
#: includes/locations/class-acf-location-taxonomy.php:63,
#: includes/locations/class-acf-location-user-form.php:71,
#: includes/locations/class-acf-location-user-role.php:78,
#: includes/locations/class-acf-location-widget.php:65,
#: pro/fields/class-acf-field-gallery.php:623,
#: pro/locations/class-acf-location-block.php:66
msgid "All"
msgstr "Sve"

#: includes/fields/class-acf-field-file.php:251,
#: includes/fields/class-acf-field-image.php:240,
#: pro/fields/class-acf-field-gallery.php:624
msgid "Uploaded to post"
msgstr "Dodani uz trenutnu objavu"

#: includes/fields/class-acf-field-file.php:260,
#: includes/fields/class-acf-field-image.php:249,
#: pro/fields/class-acf-field-gallery.php:655
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:261,
#: includes/fields/class-acf-field-file.php:274
msgid "Restrict which files can be uploaded"
msgstr "Ograniči tip datoteka koji se smije uvesti"

#: includes/fields/class-acf-field-file.php:273,
#: includes/fields/class-acf-field-image.php:286,
#: pro/fields/class-acf-field-gallery.php:692
msgid "Maximum"
msgstr "Maksimum"

#: includes/fields/class-acf-field-file.php:286,
#: includes/fields/class-acf-field-image.php:323,
#: pro/fields/class-acf-field-gallery.php:729
msgid "Allowed file types"
msgstr "Dozvoljeni tipovi datoteka"

#: includes/fields/class-acf-field-file.php:287,
#: includes/fields/class-acf-field-image.php:324,
#: pro/fields/class-acf-field-gallery.php:730
msgid "Comma separated list. Leave blank for all types"
msgstr ""
"Dodaj kao niz odvojen zarezom, npr: .txt, .jpg, ... Ukoliko je prazno, sve "
"datoteke su dozvoljene"

#: includes/fields/class-acf-field-file.php:469
msgid "%s requires a valid attachment ID."
msgstr ""

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google mapa"

#: includes/fields/class-acf-field-google-map.php:60
msgid "Sorry, this browser does not support geolocation"
msgstr "Nažalost, ovaj preglednik ne podržava geo lociranje"

#: includes/fields/class-acf-field-google-map.php:155,
#: includes/fields/class-acf-field-relationship.php:615
msgid "Search"
msgstr "Pretraži"

#: includes/fields/class-acf-field-google-map.php:156
msgid "Clear location"
msgstr "Ukloni lokaciju"

#: includes/fields/class-acf-field-google-map.php:157
msgid "Find current location"
msgstr "Pronađi trenutnu lokaciju"

#: includes/fields/class-acf-field-google-map.php:160
msgid "Search for address..."
msgstr "Pretraži po adresi..."

#: includes/fields/class-acf-field-google-map.php:192,
#: includes/fields/class-acf-field-google-map.php:205
msgid "Center"
msgstr "Centriraj"

#: includes/fields/class-acf-field-google-map.php:193,
#: includes/fields/class-acf-field-google-map.php:206
msgid "Center the initial map"
msgstr "Centriraj prilikom učitavanja"

#: includes/fields/class-acf-field-google-map.php:219
msgid "Zoom"
msgstr "Uvećaj"

#: includes/fields/class-acf-field-google-map.php:220
msgid "Set the initial zoom level"
msgstr "Postavi zadanu vrijednost uvećanja"

#: includes/fields/class-acf-field-google-map.php:231,
#: includes/fields/class-acf-field-image.php:264,
#: includes/fields/class-acf-field-image.php:301,
#: includes/fields/class-acf-field-oembed.php:292,
#: pro/fields/class-acf-field-gallery.php:670,
#: pro/fields/class-acf-field-gallery.php:707
msgid "Height"
msgstr "Visina"

#: includes/fields/class-acf-field-google-map.php:232
#, fuzzy
#| msgid "Customise the map height"
msgid "Customize the map height"
msgstr "Uredi visinu mape"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Skup polja"

#: includes/fields/class-acf-field-group.php:446,
#: pro/fields/class-acf-field-repeater.php:381
msgid "Sub Fields"
msgstr "Pod polja"

#: includes/fields/class-acf-field-group.php:463,
#: pro/fields/class-acf-field-clone.php:851
msgid "Specify the style used to render the selected fields"
msgstr "Odaberite način prikaza odabranih polja"

#: includes/fields/class-acf-field-group.php:468,
#: pro/fields/class-acf-field-clone.php:856,
#: pro/fields/class-acf-field-flexible-content.php:617,
#: pro/fields/class-acf-field-repeater.php:459,
#: pro/locations/class-acf-location-block.php:22
msgid "Block"
msgstr "Blok"

#: includes/fields/class-acf-field-group.php:469,
#: pro/fields/class-acf-field-clone.php:857,
#: pro/fields/class-acf-field-flexible-content.php:616,
#: pro/fields/class-acf-field-repeater.php:458
msgid "Table"
msgstr "Tablica"

#: includes/fields/class-acf-field-group.php:470,
#: pro/fields/class-acf-field-clone.php:858,
#: pro/fields/class-acf-field-flexible-content.php:618,
#: pro/fields/class-acf-field-repeater.php:460
msgid "Row"
msgstr "Red"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Slika"

#: includes/fields/class-acf-field-image.php:64
msgid "Select Image"
msgstr "Odaberi sliku"

#: includes/fields/class-acf-field-image.php:65
msgid "Edit Image"
msgstr "Uredi sliku"

#: includes/fields/class-acf-field-image.php:66
msgid "Update Image"
msgstr "Ažuriraj sliku"

#: includes/fields/class-acf-field-image.php:158
msgid "No image selected"
msgstr "Nema odabranih slika"

#: includes/fields/class-acf-field-image.php:158
msgid "Add Image"
msgstr "Dodaj sliku"

#: includes/fields/class-acf-field-image.php:210,
#: pro/fields/class-acf-field-gallery.php:579
msgid "Image Array"
msgstr "Podaci kao niz"

#: includes/fields/class-acf-field-image.php:211,
#: pro/fields/class-acf-field-gallery.php:580
msgid "Image URL"
msgstr "Putanja slike"

#: includes/fields/class-acf-field-image.php:212,
#: pro/fields/class-acf-field-gallery.php:581
msgid "Image ID"
msgstr "ID slike"

#: includes/fields/class-acf-field-image.php:221,
#: pro/fields/class-acf-field-gallery.php:590
msgid "Preview Size"
msgstr "Veličina prikaza prilikom uređivanja stranice"

#: includes/fields/class-acf-field-image.php:250,
#: includes/fields/class-acf-field-image.php:287,
#: pro/fields/class-acf-field-gallery.php:656,
#: pro/fields/class-acf-field-gallery.php:693
msgid "Restrict which images can be uploaded"
msgstr "Ograniči koje slike mogu biti dodane"

#: includes/fields/class-acf-field-image.php:253,
#: includes/fields/class-acf-field-image.php:290,
#: includes/fields/class-acf-field-oembed.php:279,
#: pro/fields/class-acf-field-gallery.php:659,
#: pro/fields/class-acf-field-gallery.php:696
msgid "Width"
msgstr "Širina"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Poveznica"

#: includes/fields/class-acf-field-link.php:137
msgid "Select Link"
msgstr "Odaberite poveznicu"

#: includes/fields/class-acf-field-link.php:142
msgid "Opens in a new window/tab"
msgstr "Otvori u novom prozoru/kartici"

#: includes/fields/class-acf-field-link.php:176
msgid "Link Array"
msgstr "Vrijednost kao niz"

#: includes/fields/class-acf-field-link.php:177
msgid "Link URL"
msgstr "Putanja poveznice"

#: includes/fields/class-acf-field-message.php:26,
#: includes/fields/class-acf-field-message.php:100,
#: includes/fields/class-acf-field-true_false.php:137
msgid "Message"
msgstr "Poruka"

#: includes/fields/class-acf-field-message.php:111,
#: includes/fields/class-acf-field-textarea.php:148
msgid "New Lines"
msgstr "Broj linija"

#: includes/fields/class-acf-field-message.php:112,
#: includes/fields/class-acf-field-textarea.php:149
msgid "Controls how new lines are rendered"
msgstr "Određuje način prikaza novih linija"

#: includes/fields/class-acf-field-message.php:116,
#: includes/fields/class-acf-field-textarea.php:153
msgid "Automatically add paragraphs"
msgstr "Dodaj paragraf"

#: includes/fields/class-acf-field-message.php:117,
#: includes/fields/class-acf-field-textarea.php:154
msgid "Automatically add &lt;br&gt;"
msgstr "Dodaj novi red - &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:118,
#: includes/fields/class-acf-field-textarea.php:155
msgid "No Formatting"
msgstr "Bez obrade"

#: includes/fields/class-acf-field-message.php:127
msgid "Escape HTML"
msgstr "Onemogući HTML"

#: includes/fields/class-acf-field-message.php:128
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Prikažite HTML kodove kao tekst umjesto iscrtavanja"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Broj"

#: includes/fields/class-acf-field-number.php:169,
#: includes/fields/class-acf-field-range.php:174
msgid "Minimum Value"
msgstr "Minimum"

#: includes/fields/class-acf-field-number.php:180,
#: includes/fields/class-acf-field-range.php:186
msgid "Maximum Value"
msgstr "Maksimum"

#: includes/fields/class-acf-field-number.php:191,
#: includes/fields/class-acf-field-range.php:198
msgid "Step Size"
msgstr "Korak"

#: includes/fields/class-acf-field-number.php:229
msgid "Value must be a number"
msgstr "Vrijednost mora biti broj"

#: includes/fields/class-acf-field-number.php:244
msgid "Value must be equal to or higher than %d"
msgstr "Unešena vrijednost mora biti jednaka ili viša od %d"

#: includes/fields/class-acf-field-number.php:251
msgid "Value must be equal to or lower than %d"
msgstr "Unešena vrijednost mora biti jednaka ili niža od %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:230
msgid "Enter URL"
msgstr "Poveznica"

#: includes/fields/class-acf-field-oembed.php:276,
#: includes/fields/class-acf-field-oembed.php:289
msgid "Embed Size"
msgstr "Dimenzija umetka"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "URL stranice"

#: includes/fields/class-acf-field-page_link.php:165
msgid "Archives"
msgstr "Arhiva"

#: includes/fields/class-acf-field-page_link.php:249,
#: includes/fields/class-acf-field-post_object.php:250,
#: includes/fields/class-acf-field-taxonomy.php:948
msgid "Parent"
msgstr "Matični"

#: includes/fields/class-acf-field-page_link.php:450,
#: includes/fields/class-acf-field-post_object.php:362,
#: includes/fields/class-acf-field-relationship.php:578
msgid "Filter by Post Type"
msgstr "Filtriraj po tipu posta"

#: includes/fields/class-acf-field-page_link.php:458,
#: includes/fields/class-acf-field-post_object.php:370,
#: includes/fields/class-acf-field-relationship.php:586
msgid "All post types"
msgstr "Svi tipovi"

#: includes/fields/class-acf-field-page_link.php:466,
#: includes/fields/class-acf-field-post_object.php:378,
#: includes/fields/class-acf-field-relationship.php:594
msgid "Filter by Taxonomy"
msgstr "Filtriraj prema taksonomiji"

#: includes/fields/class-acf-field-page_link.php:474,
#: includes/fields/class-acf-field-post_object.php:386,
#: includes/fields/class-acf-field-relationship.php:602
msgid "All taxonomies"
msgstr "Sve taksonomije"

#: includes/fields/class-acf-field-page_link.php:494
msgid "Allow Archives URLs"
msgstr "Omogući odabir arhive tipova"

#: includes/fields/class-acf-field-page_link.php:506,
#: includes/fields/class-acf-field-post_object.php:406,
#: includes/fields/class-acf-field-select.php:398,
#: includes/fields/class-acf-field-user.php:79
msgid "Select multiple values?"
msgstr "Dozvoli odabir više vrijednosti?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Lozinka"

#: includes/fields/class-acf-field-post_object.php:25,
#: includes/fields/class-acf-field-post_object.php:423,
#: includes/fields/class-acf-field-relationship.php:667
msgid "Post Object"
msgstr "Objekt"

#: includes/fields/class-acf-field-post_object.php:424,
#: includes/fields/class-acf-field-relationship.php:668
msgid "Post ID"
msgstr "ID objave"

#: includes/fields/class-acf-field-post_object.php:642
msgid "%1$s must have a valid post ID."
msgstr ""

#: includes/fields/class-acf-field-post_object.php:651
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-post_object.php:667
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Radiogumb"

#: includes/fields/class-acf-field-radio.php:214
msgid "Other"
msgstr "Drugo"

#: includes/fields/class-acf-field-radio.php:219
msgid "Add 'other' choice to allow for custom values"
msgstr "Dodaj odabir ’ostalo’ za slobodan unost"

#: includes/fields/class-acf-field-radio.php:227
msgid "Save Other"
msgstr "Spremi ostale"

#: includes/fields/class-acf-field-radio.php:232
msgid "Save 'other' values to the field's choices"
msgstr "Spremi ostale vrijednosti i omogući njihov odabir"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Raspon"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Veza"

#: includes/fields/class-acf-field-relationship.php:63
msgid "Maximum values reached ( {max} values )"
msgstr "Već ste dodali najviše dozvoljenih vrijednosti (najviše: {max})"

#: includes/fields/class-acf-field-relationship.php:64
msgid "Loading"
msgstr "Učitavanje"

#: includes/fields/class-acf-field-relationship.php:65
msgid "No matches found"
msgstr "Nema rezultata"

#: includes/fields/class-acf-field-relationship.php:390
msgid "Select post type"
msgstr "Odaberi tip posta"

#: includes/fields/class-acf-field-relationship.php:399
msgid "Select taxonomy"
msgstr "Odebarite taksonomiju"

#: includes/fields/class-acf-field-relationship.php:469
msgid "Search..."
msgstr "Pretraga…"

#: includes/fields/class-acf-field-relationship.php:610
msgid "Filters"
msgstr "Filteri"

#: includes/fields/class-acf-field-relationship.php:616,
#: includes/locations/class-acf-location-post-type.php:22
msgid "Post Type"
msgstr "Tip objave"

#: includes/fields/class-acf-field-relationship.php:617,
#: includes/fields/class-acf-field-taxonomy.php:28,
#: includes/fields/class-acf-field-taxonomy.php:714,
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taksonomija"

#: includes/fields/class-acf-field-relationship.php:626
msgid "Elements"
msgstr "Elementi"

#: includes/fields/class-acf-field-relationship.php:627
msgid "Selected elements will be displayed in each result"
msgstr "Odabrani elementi bit će prikazani u svakom rezultatu"

#: includes/fields/class-acf-field-relationship.php:631,
#: includes/admin/views/field-group-options.php:150
msgid "Featured Image"
msgstr "Istaknuta slika"

#: includes/fields/class-acf-field-relationship.php:640
msgid "Minimum posts"
msgstr "Minimalno"

#: includes/fields/class-acf-field-relationship.php:651
msgid "Maximum posts"
msgstr "Maksimalno"

#: includes/fields/class-acf-field-relationship.php:752,
#: pro/fields/class-acf-field-gallery.php:832
#, fuzzy
#| msgid "1 field requires attention"
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "1 polje treba vašu pažnju"
msgstr[1] "1 polje treba vašu pažnju"
msgstr[2] "1 polje treba vašu pažnju"

#: includes/fields/class-acf-field-select.php:25,
#: includes/fields/class-acf-field-taxonomy.php:738
msgctxt "noun"
msgid "Select"
msgstr "Odaberi"

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Jedan rezultat dostupan, pritisnite enter za odabir."

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d rezultata dostupno, za pomicanje koristite strelice gore/dole."

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Nema rezultata"

#: includes/fields/class-acf-field-select.php:110
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Molimo unesite 1 ili više znakova"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Molimo unesite najmanje %d ili više znakova"

#: includes/fields/class-acf-field-select.php:112
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Molimo obrišite 1 znak"

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Molimo obrišite višak znakova - %d znak(ova) je višak"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Moguće je odabrati samo jednu opciju"

#: includes/fields/class-acf-field-select.php:115
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Odabir opcija je ograničen na najviše %d"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Učitavam rezultate&hellip;"

#: includes/fields/class-acf-field-select.php:117
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Pretražujem&hellip;"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Neuspješno učitavanje"

#: includes/fields/class-acf-field-select.php:410,
#: includes/fields/class-acf-field-true_false.php:159
msgid "Stylised UI"
msgstr "Stilizirano sučelje"

#: includes/fields/class-acf-field-select.php:422
msgid "Use AJAX to lazy load choices?"
msgstr "Asinkrono učitaj dostupne odabire?"

#: includes/fields/class-acf-field-select.php:440
msgid "Specify the value returned"
msgstr "Preciziraj vrijednost za povrat"

#: includes/fields/class-acf-field-select.php:663
msgid "%1$s is not one of %2$s"
msgstr ""

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Razdjelnik"

#: includes/fields/class-acf-field-tab.php:26
msgid "Tab"
msgstr "Kartica"

#: includes/fields/class-acf-field-tab.php:103
msgid "Placement"
msgstr "Pozicija"

#: includes/fields/class-acf-field-tab.php:107,
#: includes/admin/views/field-group-options.php:87
msgid "Top aligned"
msgstr "Poravnato sa vrhom"

#: includes/fields/class-acf-field-tab.php:108,
#: includes/admin/views/field-group-options.php:88
msgid "Left aligned"
msgstr "Lijevo poravnato"

#: includes/fields/class-acf-field-tab.php:118
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""
"Preciziraj prijelomnu točku za prethodne kartice. Ovo će omogućiti novi skup "
"kartica nakon prijelomne točke."

#: includes/fields/class-acf-field-taxonomy.php:673
msgctxt "No terms"
msgid "No %s"
msgstr "Nema %s"

#: includes/fields/class-acf-field-taxonomy.php:715
msgid "Select the taxonomy to be displayed"
msgstr "Odaberite taksonomiju za prikaz"

#: includes/fields/class-acf-field-taxonomy.php:726
msgid "Appearance"
msgstr "Prikaz"

#: includes/fields/class-acf-field-taxonomy.php:727
msgid "Select the appearance of this field"
msgstr "Odaberite izgled polja"

#: includes/fields/class-acf-field-taxonomy.php:732
msgid "Multiple Values"
msgstr "Omogući odabir više vrijednosti"

#: includes/fields/class-acf-field-taxonomy.php:734
msgid "Multi Select"
msgstr "Više odabira"

#: includes/fields/class-acf-field-taxonomy.php:736
msgid "Single Value"
msgstr "Jedan odabir"

#: includes/fields/class-acf-field-taxonomy.php:737
msgid "Radio Buttons"
msgstr "Radiogumbi"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Create Terms"
msgstr "Kreiraj pojmove"

#: includes/fields/class-acf-field-taxonomy.php:766
msgid "Allow new terms to be created whilst editing"
msgstr "Omogući kreiranje pojmova prilikom uređivanja"

#: includes/fields/class-acf-field-taxonomy.php:777
msgid "Save Terms"
msgstr "Spremi pojmove"

#: includes/fields/class-acf-field-taxonomy.php:778
msgid "Connect selected terms to the post"
msgstr "Spoji odabrane pojmove sa objavom"

#: includes/fields/class-acf-field-taxonomy.php:789
msgid "Load Terms"
msgstr "Učitaj pojmove"

#: includes/fields/class-acf-field-taxonomy.php:790
msgid "Load value from posts terms"
msgstr "Učitaj pojmove iz objave"

#: includes/fields/class-acf-field-taxonomy.php:806
msgid "Term Object"
msgstr "Vrijednost pojma kao objekt"

#: includes/fields/class-acf-field-taxonomy.php:807
msgid "Term ID"
msgstr "Vrijednost kao: ID pojma"

#: includes/fields/class-acf-field-taxonomy.php:862
msgid "User unable to add new %s"
msgstr "Korisnik nije u mogućnosti dodati %s"

#: includes/fields/class-acf-field-taxonomy.php:874
msgid "%s already exists"
msgstr "%s već postoji"

#: includes/fields/class-acf-field-taxonomy.php:910
msgid "%s added"
msgstr "Dodano: %s"

#: includes/fields/class-acf-field-taxonomy.php:926,
#: pro/fields/class-acf-field-flexible-content.php:597,
#: includes/admin/views/field-group-fields.php:6
msgid "Name"
msgstr "Naziv"

#: includes/fields/class-acf-field-taxonomy.php:961,
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Dodaj"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Tekst"

#: includes/fields/class-acf-field-text.php:141,
#: includes/fields/class-acf-field-textarea.php:125
msgid "Character Limit"
msgstr "Ograniči broj znakova"

#: includes/fields/class-acf-field-text.php:142,
#: includes/fields/class-acf-field-textarea.php:126
msgid "Leave blank for no limit"
msgstr "Ostavite prazno za neograničeno"

#: includes/fields/class-acf-field-text.php:168,
#: includes/fields/class-acf-field-textarea.php:221
#, fuzzy
#| msgctxt "Select2 JS input_too_long_n"
#| msgid "Please delete %d characters"
msgid "Value must not exceed %d characters"
msgstr "Molimo obrišite višak znakova - %d znak(ova) je višak"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Tekst polje"

#: includes/fields/class-acf-field-textarea.php:136
msgid "Rows"
msgstr "Broj redova"

#: includes/fields/class-acf-field-textarea.php:137
msgid "Sets the textarea height"
msgstr "Podesi visinu tekstualnog polja"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Odabri vremena (sat i minute)"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "True / False"

#: includes/fields/class-acf-field-true_false.php:138
msgid "Displays text alongside the checkbox"
msgstr "Prikazuje tekst uz odabirni okvir"

#: includes/fields/class-acf-field-true_false.php:172
msgid "On Text"
msgstr "Tekst za aktivno stanje"

#: includes/fields/class-acf-field-true_false.php:173
msgid "Text shown when active"
msgstr "Tekst prikazan dok je polje aktivno"

#: includes/fields/class-acf-field-true_false.php:189
msgid "Off Text"
msgstr "Tekst za neaktivno stanje"

#: includes/fields/class-acf-field-true_false.php:190
msgid "Text shown when inactive"
msgstr "Tekst prikazan dok je polje neaktivno"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Poveznica"

#: includes/fields/class-acf-field-url.php:154
msgid "Value must be a valid URL"
msgstr "Vrijednost molja biti valjana"

#: includes/fields/class-acf-field-user.php:53
msgid "Filter by role"
msgstr "Filtar prema ulozi"

#: includes/fields/class-acf-field-user.php:61
msgid "All user roles"
msgstr "Sve uloge"

#: includes/fields/class-acf-field-user.php:95
#, fuzzy
#| msgid "File Array"
msgid "User Array"
msgstr "Vrijednost kao niz"

#: includes/fields/class-acf-field-user.php:96
#, fuzzy
#| msgid "Term Object"
msgid "User Object"
msgstr "Vrijednost pojma kao objekt"

#: includes/fields/class-acf-field-user.php:97
#, fuzzy
#| msgid "User"
msgid "User ID"
msgstr "Korisnik"

#: includes/fields/class-acf-field-user.php:350
#, fuzzy
#| msgctxt "Select2 JS load_fail"
#| msgid "Loading failed"
msgid "Error loading field."
msgstr "Neuspješno učitavanje"

#: includes/fields/class-acf-field-user.php:355
#, fuzzy
#| msgid "Error validating request"
msgid "Invalid request."
msgstr "Greška prilikom verifikacije"

#: includes/fields/class-acf-field-user.php:517
msgid "%1$s must have a valid user ID."
msgstr ""

#: includes/fields/class-acf-field-user.php:526
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Vizualno uređivanje"

#: includes/fields/class-acf-field-wysiwyg.php:282
msgid "Visual"
msgstr "Vizualno"

#: includes/fields/class-acf-field-wysiwyg.php:283
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Tekst polje"

#: includes/fields/class-acf-field-wysiwyg.php:289
msgid "Click to initialize TinyMCE"
msgstr "Aktiviraj vizualno uređivanje na klik"

#: includes/fields/class-acf-field-wysiwyg.php:345
msgid "Tabs"
msgstr "Kartice"

#: includes/fields/class-acf-field-wysiwyg.php:350
msgid "Visual & Text"
msgstr "Vizualno i tekstualno"

#: includes/fields/class-acf-field-wysiwyg.php:351
msgid "Visual Only"
msgstr "Samo vizualni"

#: includes/fields/class-acf-field-wysiwyg.php:352
msgid "Text Only"
msgstr "Samo tekstualno"

#: includes/fields/class-acf-field-wysiwyg.php:361
msgid "Toolbar"
msgstr "Alatna traka"

#: includes/fields/class-acf-field-wysiwyg.php:378
msgid "Show Media Upload Buttons?"
msgstr "Prikaži gumb za odabir datoteka?"

#: includes/fields/class-acf-field-wysiwyg.php:390
msgid "Delay initialization?"
msgstr "Odgodi učitavanje?"

#: includes/fields/class-acf-field-wysiwyg.php:391
#, fuzzy
#| msgid "TinyMCE will not be initalized until field is clicked"
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE neće biti učitan dok korisnik ne klikne na polje"

#: includes/forms/form-front.php:40, pro/fields/class-acf-field-gallery.php:352
msgid "Title"
msgstr "Naziv"

#: includes/forms/form-front.php:57
msgid "Validate Email"
msgstr "Verificiraj email"

#: includes/forms/form-front.php:107
msgid "Post updated"
msgstr "Objava ažurirana"

#: includes/forms/form-front.php:236
msgid "Spam Detected"
msgstr "Spam"

#: includes/forms/form-user.php:353
msgid "<strong>Error</strong>: %s"
msgstr ""

#: includes/locations/abstract-acf-location.php:105
msgid "is equal to"
msgstr "je jednako"

#: includes/locations/abstract-acf-location.php:106
msgid "is not equal to"
msgstr "je drukčije"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Prilog"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Svi oblici %s"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Komentar"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Trenutni tip korisnika"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super Admin"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Trenutni korisnik"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Prijavljen"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Prikazuje web stranicu"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Prikazuje administracijki dio"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Stavka izbornika"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Izbornik"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Lokacije izbornika"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Matična stranica"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Predložak stranice"

#: includes/locations/class-acf-location-page-template.php:73,
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Zadani predložak"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Tip stranice"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Početna stranica"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Stranica za objave"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Matična stranica (Nije podstranica)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Matičan stranica (Ima podstranice)"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Pod-stranica (Ima matičnu stranicu)"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Kategorija objave"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Format objave"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Status objave"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Taksonomija objave"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Predložak stranice"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Korisnički obrazac"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Dodaj / Uredi"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Registriraj"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Tip korisnika"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Objavi"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Niste dodali nijedan skup polja na ovu stranicu, <a href=\"%s\">Dodaj skup "
"polja</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Greška</b>. Greška prilikom spajanja na server"

#: pro/admin/admin-updates.php:122,
#: pro/admin/views/html-settings-updates.php:12
msgid "Updates"
msgstr "Ažuriranja"

#: pro/admin/admin-updates.php:209
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""

#: pro/admin/admin-updates.php:196
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Kloniraj"

#: pro/fields/class-acf-field-clone.php:815
msgid "Select one or more fields you wish to clone"
msgstr "Odaberite jedno ili više polja koja želite klonirati"

#: pro/fields/class-acf-field-clone.php:834
msgid "Display"
msgstr "Prikaz"

#: pro/fields/class-acf-field-clone.php:835
msgid "Specify the style used to render the clone field"
msgstr "Odaberite način prikaza kloniranog polja"

#: pro/fields/class-acf-field-clone.php:840
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Skupno (Prikazuje odabrana polja kao dodatni skup unutar trenutnog polja)"

#: pro/fields/class-acf-field-clone.php:841
msgid "Seamless (replaces this field with selected fields)"
msgstr "Zamjena (Prikazuje odabrana polja umjesto trenutnog polja)"

#: pro/fields/class-acf-field-clone.php:864
msgid "Labels will be displayed as %s"
msgstr "Oznake će biti prikazane kao %s"

#: pro/fields/class-acf-field-clone.php:869
msgid "Prefix Field Labels"
msgstr "Dodaj prefiks ispred oznake"

#: pro/fields/class-acf-field-clone.php:880
msgid "Values will be saved as %s"
msgstr "Vrijednosti će biti spremljene kao %s"

#: pro/fields/class-acf-field-clone.php:885
msgid "Prefix Field Names"
msgstr "Dodaj prefiks ispred naziva polja"

#: pro/fields/class-acf-field-clone.php:1001
msgid "Unknown field"
msgstr "Nepoznato polje"

#: pro/fields/class-acf-field-clone.php:1038
msgid "Unknown field group"
msgstr "Nepoznat skup polja"

#: pro/fields/class-acf-field-clone.php:1042
msgid "All fields from %s field group"
msgstr "Sva polje iz %s skupa polja"

#: pro/fields/class-acf-field-flexible-content.php:25,
#: includes/admin/views/html-notice-upgrade.php:12
msgid "Flexible Content"
msgstr "Fleksibilno polje"

#: pro/fields/class-acf-field-flexible-content.php:31,
#: pro/fields/class-acf-field-repeater.php:182,
#: pro/fields/class-acf-field-repeater.php:473
msgid "Add Row"
msgstr "Dodaj red"

#: pro/fields/class-acf-field-flexible-content.php:71,
#: pro/fields/class-acf-field-flexible-content.php:917,
#: pro/fields/class-acf-field-flexible-content.php:996
#, fuzzy
#| msgid "layout"
msgid "layout"
msgid_plural "layouts"
msgstr[0] "raspored"
msgstr[1] "raspored"
msgstr[2] "raspored"

#: pro/fields/class-acf-field-flexible-content.php:72
msgid "layouts"
msgstr "rasporedi"

#: pro/fields/class-acf-field-flexible-content.php:75,
#: pro/fields/class-acf-field-flexible-content.php:916,
#: pro/fields/class-acf-field-flexible-content.php:995
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Polje mora sadržavati najmanje {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:76
#, fuzzy
#| msgid "This field has a limit of {max} {identifier}"
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Polje je ograničeno na najviše {max} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:79
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} preostalo (najviše {max})"

#: pro/fields/class-acf-field-flexible-content.php:80
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} obavezno (najmanje {min})"

#: pro/fields/class-acf-field-flexible-content.php:83
msgid "Flexible Content requires at least 1 layout"
msgstr "Potrebno je unijeti najmanje jedno fleksibilni polje"

#: pro/fields/class-acf-field-flexible-content.php:276
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Kliknite “%s” gumb kako bi započeki kreiranje raspored"

#: pro/fields/class-acf-field-flexible-content.php:410,
#: pro/fields/class-acf-field-repeater.php:295,
#: includes/admin/views/field-group-field.php:49
msgid "Drag to reorder"
msgstr "Presloži polja povlačenjem"

#: pro/fields/class-acf-field-flexible-content.php:413
msgid "Add layout"
msgstr "Dodaj razmještaj"

#: pro/fields/class-acf-field-flexible-content.php:414
#, fuzzy
#| msgid "Duplicate Layout"
msgid "Duplicate layout"
msgstr "Dupliciraj razmještaj"

#: pro/fields/class-acf-field-flexible-content.php:415
msgid "Remove layout"
msgstr "Ukloni razmještaj"

#: pro/fields/class-acf-field-flexible-content.php:416,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Click to toggle"
msgstr "Klikni za uključivanje/isključivanje"

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder Layout"
msgstr "Presloži polja povlačenjem"

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder"
msgstr "Presloži"

#: pro/fields/class-acf-field-flexible-content.php:552
msgid "Delete Layout"
msgstr "Obriši"

#: pro/fields/class-acf-field-flexible-content.php:552,
#: includes/admin/views/field-group-field.php:59
msgid "Delete"
msgstr "Obriši"

#: pro/fields/class-acf-field-flexible-content.php:553
msgid "Duplicate Layout"
msgstr "Dupliciraj razmještaj"

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Add New Layout"
msgstr "Dodaj novi razmještaj"

#: pro/fields/class-acf-field-flexible-content.php:635
msgid "Min"
msgstr "Minimum"

#: pro/fields/class-acf-field-flexible-content.php:650
msgid "Max"
msgstr "Maksimum"

#: pro/fields/class-acf-field-flexible-content.php:679,
#: pro/fields/class-acf-field-repeater.php:469
msgid "Button Label"
msgstr "Tekst gumba"

#: pro/fields/class-acf-field-flexible-content.php:690
msgid "Minimum Layouts"
msgstr "Najmanje"

#: pro/fields/class-acf-field-flexible-content.php:701
msgid "Maximum Layouts"
msgstr "Najviše"

#: pro/fields/class-acf-field-flexible-content.php:1704,
#: pro/fields/class-acf-field-repeater.php:1108
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1715
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: pro/fields/class-acf-field-flexible-content.php:1731
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: pro/fields/class-acf-field-gallery.php:25,
#: includes/admin/views/html-notice-upgrade.php:15
msgid "Gallery"
msgstr "Galerija"

#: pro/fields/class-acf-field-gallery.php:73
msgid "Add Image to Gallery"
msgstr "Dodaj sliku u galeriju"

#: pro/fields/class-acf-field-gallery.php:74
msgid "Maximum selection reached"
msgstr "Već ste dodali najviše dozovoljenih polja"

#: pro/fields/class-acf-field-gallery.php:320
msgid "Length"
msgstr "Dužina"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr "Potpis"

#: pro/fields/class-acf-field-gallery.php:376
msgid "Alt Text"
msgstr "Alternativni tekst"

#: pro/fields/class-acf-field-gallery.php:500
msgid "Add to gallery"
msgstr "Dodaj u galeriju"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Bulk actions"
msgstr "Grupne akcije"

#: pro/fields/class-acf-field-gallery.php:505
msgid "Sort by date uploaded"
msgstr "Razvrstaj po datumu dodavanja"

#: pro/fields/class-acf-field-gallery.php:506
msgid "Sort by date modified"
msgstr "Razvrstaj po datumu zadnje promjene"

#: pro/fields/class-acf-field-gallery.php:507
msgid "Sort by title"
msgstr "Razvrstaj po naslovu"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Reverse current order"
msgstr "Obrnuti redosljed"

#: pro/fields/class-acf-field-gallery.php:520
msgid "Close"
msgstr "Zatvori"

#: pro/fields/class-acf-field-gallery.php:602
msgid "Insert"
msgstr "Umetni"

#: pro/fields/class-acf-field-gallery.php:603
msgid "Specify where new attachments are added"
msgstr "Precizirajte gdje se dodaju novi prilozi"

#: pro/fields/class-acf-field-gallery.php:607
msgid "Append to the end"
msgstr "Umetni na kraj"

#: pro/fields/class-acf-field-gallery.php:608
msgid "Prepend to the beginning"
msgstr "Umetni na početak"

#: pro/fields/class-acf-field-gallery.php:633
msgid "Minimum Selection"
msgstr "Minimalni odabri"

#: pro/fields/class-acf-field-gallery.php:644
msgid "Maximum Selection"
msgstr "Maksimalni odabir"

#: pro/fields/class-acf-field-repeater.php:25,
#: includes/admin/views/html-notice-upgrade.php:9
msgid "Repeater"
msgstr "Ponavljajuće polje"

#: pro/fields/class-acf-field-repeater.php:64,
#: pro/fields/class-acf-field-repeater.php:659
msgid "Minimum rows reached ({min} rows)"
msgstr "Minimalni broj redova je već odabran ({min})"

#: pro/fields/class-acf-field-repeater.php:65
msgid "Maximum rows reached ({max} rows)"
msgstr "Maksimalni broj redova je već odabran ({max})"

#: pro/fields/class-acf-field-repeater.php:334
msgid "Add row"
msgstr "Dodaj red"

#: pro/fields/class-acf-field-repeater.php:335
#, fuzzy
#| msgid "Duplicate"
msgid "Duplicate row"
msgstr "Dupliciraj"

#: pro/fields/class-acf-field-repeater.php:336
msgid "Remove row"
msgstr "Ukloni red"

#: pro/fields/class-acf-field-repeater.php:414
msgid "Collapsed"
msgstr "Sklopljeno"

#: pro/fields/class-acf-field-repeater.php:415
msgid "Select a sub field to show when row is collapsed"
msgstr "Odaberite pod polje koje će biti prikazano dok je red sklopljen"

#: pro/fields/class-acf-field-repeater.php:427
msgid "Minimum Rows"
msgstr "Minimalno redova"

#: pro/fields/class-acf-field-repeater.php:439
msgid "Maximum Rows"
msgstr "Maksimalno redova"

#: pro/locations/class-acf-location-block.php:71
#, fuzzy
#| msgid "No options pages exist"
msgid "No block types exist"
msgstr "Ne postoji stranica sa postavkama"

#: pro/locations/class-acf-location-options-page.php:22,
#: includes/admin/views/html-notice-upgrade.php:18
msgid "Options Page"
msgstr "Postavke"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Ne postoji stranica sa postavkama"

#: tests/basic/test-blocks.php:456
msgid "Hero"
msgstr ""

#: tests/basic/test-blocks.php:457
msgid "Display a random hero image."
msgstr ""

#: tests/basic/test-blocks.php:630
msgid "Test JS"
msgstr ""

#: tests/basic/test-blocks.php:631
msgid "A block for testing JS."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:35
msgid "Export Field Groups"
msgstr "Izvezi skup polja"

#: includes/admin/tools/class-acf-admin-tool-export.php:39,
#: includes/admin/tools/class-acf-admin-tool-export.php:335,
#: includes/admin/tools/class-acf-admin-tool-export.php:364
msgid "Generate PHP"
msgstr "Generiraj PHP kod"

#: includes/admin/tools/class-acf-admin-tool-export.php:96,
#: includes/admin/tools/class-acf-admin-tool-export.php:131
msgid "No field groups selected"
msgstr "Niste odabrali polje"

#: includes/admin/tools/class-acf-admin-tool-export.php:167
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/tools/class-acf-admin-tool-export.php:233,
#: includes/admin/tools/class-acf-admin-tool-export.php:262
msgid "Select Field Groups"
msgstr "Odaberite skup polja"

#: includes/admin/tools/class-acf-admin-tool-export.php:329
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Odaberite polja koja želite izvesti i zatim odaberite željeni format. Klikom "
"na gumb “preuzimanje”, preuzmite .json datoteku sa poljima koju zatim možete "
"uvesti u drugu ACF instalaciju.\n"
"Klikom na “generiraj” gumb, izvezite PHP kod koji možete uključiti u "
"WordPress temu."

#: includes/admin/tools/class-acf-admin-tool-export.php:334
msgid "Export File"
msgstr "Datoteka za izvoz"

#: includes/admin/tools/class-acf-admin-tool-export.php:405
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Navedeni kod možete koristiti kako bi registrirali lokalnu verziju odabranih "
"polja ili skupine polja. Lokalna polje pružaju dodatne mogućnosti kao što je "
"brže očitavanje, verzioniranje i dinamičke postavke polja. Jednostavno "
"kopirajte navedeni kod u functions.php datoteku u vašoj temi ili uključite "
"ih kao vanjsku datoteku."

#: includes/admin/tools/class-acf-admin-tool-export.php:435
msgid "Copy to clipboard"
msgstr "Kopiraj u međuspremnik"

#: includes/admin/tools/class-acf-admin-tool-export.php:472
msgid "Copied"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:28
msgid "Import Field Groups"
msgstr "Uvoz skupa polja"

#: includes/admin/tools/class-acf-admin-tool-import.php:49
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Odaberite ACF JSON datoteku koju želite uvesti. Nakon što kliknete ‘Uvezi’ "
"gumb, ACF će uvesti sva polja iz odabrane datoteke."

#: includes/admin/tools/class-acf-admin-tool-import.php:66
msgid "Import File"
msgstr "Datoteka za uvoz"

#: includes/admin/tools/class-acf-admin-tool-import.php:97
msgid "Error uploading file. Please try again"
msgstr "Greška prilikom prijenosa datoteke, molimo pokušaj ponovno"

#: includes/admin/tools/class-acf-admin-tool-import.php:102
msgid "Incorrect file type"
msgstr "Nedozvoljeni format datoteke"

#: includes/admin/tools/class-acf-admin-tool-import.php:111
msgid "Import file empty"
msgstr "Odabrana datoteka za uvoz ne sadrži"

#: includes/admin/tools/class-acf-admin-tool-import.php:142
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "Uvjet za prikaz"

#: includes/admin/views/field-group-field-conditional-logic.php:60
msgid "Show this field if"
msgstr "Prikaži polje ako"

#: includes/admin/views/field-group-field-conditional-logic.php:172,
#: includes/admin/views/field-group-locations.php:38
msgid "Add rule group"
msgstr "Dodaj skup pravila"

#: includes/admin/views/field-group-field.php:53,
#: includes/admin/views/field-group-field.php:56
msgid "Edit field"
msgstr "Uredi polje"

#: includes/admin/views/field-group-field.php:57
msgid "Duplicate field"
msgstr "Dupliciraj polje"

#: includes/admin/views/field-group-field.php:58
msgid "Move field to another group"
msgstr "Premjeti polje u drugu skupinu"

#: includes/admin/views/field-group-field.php:58
msgid "Move"
msgstr "Premjesti"

#: includes/admin/views/field-group-field.php:59
msgid "Delete field"
msgstr "Obriši polje"

#: includes/admin/views/field-group-field.php:78
msgid "Field Label"
msgstr "Naziv polja"

#: includes/admin/views/field-group-field.php:79
msgid "This is the name which will appear on the EDIT page"
msgstr "Naziv koji se prikazuje prilikom uređivanja stranice"

#: includes/admin/views/field-group-field.php:92
msgid "Field Name"
msgstr "Naziv polja"

#: includes/admin/views/field-group-field.php:93
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Jedna riječ, bez razmaka. Povlaka i donja crta su dozvoljeni"

#: includes/admin/views/field-group-field.php:106
msgid "Field Type"
msgstr "Tip polja"

#: includes/admin/views/field-group-field.php:121
msgid "Instructions"
msgstr "Upute"

#: includes/admin/views/field-group-field.php:122
msgid "Instructions for authors. Shown when submitting data"
msgstr "Upute priliko uređivanja. Vidljivo prilikom spremanja podataka"

#: includes/admin/views/field-group-field.php:135
msgid "Required?"
msgstr "Obavezno?"

#: includes/admin/views/field-group-field.php:161
msgid "Wrapper Attributes"
msgstr "Značajke prethodnog elementa"

#: includes/admin/views/field-group-field.php:167
msgid "width"
msgstr "širina"

#: includes/admin/views/field-group-field.php:185
msgid "class"
msgstr "klasa"

#: includes/admin/views/field-group-field.php:201
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:215,
#: includes/admin/views/field-group-field.php:215
msgid "Close Field"
msgstr "Zatvori polje"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Redni broj"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Tip"

#: includes/admin/views/field-group-fields.php:19
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Nema polja. Kliknite gumb <strong>+ Dodaj polje</strong> da bi kreirali "
"polje."

#: includes/admin/views/field-group-fields.php:44
msgid "+ Add Field"
msgstr "Dodaj polje"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Pravila"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr "Odaberite pravila koja određuju koji prikaz će koristiti ACF polja"

#: includes/admin/views/field-group-options.php:10
msgid "Active"
msgstr "Aktivan"

#: includes/admin/views/field-group-options.php:27
msgid "Show in REST API"
msgstr ""

#: includes/admin/views/field-group-options.php:44
msgid "Style"
msgstr "Stil"

#: includes/admin/views/field-group-options.php:51
msgid "Standard (WP metabox)"
msgstr "Zadano (WP metabox)"

#: includes/admin/views/field-group-options.php:52
msgid "Seamless (no metabox)"
msgstr "Bez"

#: includes/admin/views/field-group-options.php:61
msgid "Position"
msgstr "Pozicija"

#: includes/admin/views/field-group-options.php:68
msgid "High (after title)"
msgstr "Visoko (nakon naslova)"

#: includes/admin/views/field-group-options.php:69
msgid "Normal (after content)"
msgstr "Normalno (nakon saržaja)"

#: includes/admin/views/field-group-options.php:70
msgid "Side"
msgstr "Desni stupac"

#: includes/admin/views/field-group-options.php:80
msgid "Label placement"
msgstr "Pozicija oznake"

#: includes/admin/views/field-group-options.php:97
msgid "Instruction placement"
msgstr "Pozicija uputa"

#: includes/admin/views/field-group-options.php:104
msgid "Below labels"
msgstr "Ispod oznake"

#: includes/admin/views/field-group-options.php:105
msgid "Below fields"
msgstr "Iznad oznake"

#: includes/admin/views/field-group-options.php:114
msgid "Order No."
msgstr "Redni broj."

#: includes/admin/views/field-group-options.php:115
msgid "Field groups with a lower order will appear first"
msgstr "Skup polja sa nižim brojem će biti više pozicioniran"

#: includes/admin/views/field-group-options.php:128
msgid "Shown in field group list"
msgstr "Vidljivo u popisu"

#: includes/admin/views/field-group-options.php:139
msgid "Permalink"
msgstr "Stalna veza"

#: includes/admin/views/field-group-options.php:140
msgid "Content Editor"
msgstr "Uređivač sadržaja"

#: includes/admin/views/field-group-options.php:141
msgid "Excerpt"
msgstr "Izvadak"

#: includes/admin/views/field-group-options.php:143
msgid "Discussion"
msgstr "Rasprava"

#: includes/admin/views/field-group-options.php:145
msgid "Revisions"
msgstr "Revizija"

#: includes/admin/views/field-group-options.php:146
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/field-group-options.php:147
msgid "Author"
msgstr "Autor"

#: includes/admin/views/field-group-options.php:148
msgid "Format"
msgstr "Format"

#: includes/admin/views/field-group-options.php:149
msgid "Page Attributes"
msgstr "Atributi stranice"

#: includes/admin/views/field-group-options.php:151
msgid "Categories"
msgstr "Kategorije"

#: includes/admin/views/field-group-options.php:152
msgid "Tags"
msgstr "Oznake"

#: includes/admin/views/field-group-options.php:153
msgid "Send Trackbacks"
msgstr "Pošalji povratnu vezu"

#: includes/admin/views/field-group-options.php:161
msgid "Hide on screen"
msgstr "Sakrij"

#: includes/admin/views/field-group-options.php:162
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Odaberite</b> koje grupe želite <b>sakriti</b> prilikom uređivanja."

#: includes/admin/views/field-group-options.php:162
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Ukoliko je više skupova polja prikazano na istom ekranu, postavke prvog "
"skupa polja će biti korištene (postavke polja sa nižim brojem u redosljedu)"

#: includes/admin/views/html-admin-navigation.php:89
#, fuzzy
#| msgid "Upgrade Sites"
msgid "Upgrade to Pro"
msgstr "Ažuriraj stranice"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Ažuriranje baze podatak dovršeno. Provjerite koje web stranice u svojoj "
"mreži želite nadograditi i zatim kliknite %s."

#: includes/admin/views/html-admin-page-upgrade-network.php:26,
#: includes/admin/views/html-admin-page-upgrade-network.php:27,
#: includes/admin/views/html-admin-page-upgrade-network.php:96
msgid "Upgrade Sites"
msgstr "Ažuriraj stranice"

#: includes/admin/views/html-admin-page-upgrade-network.php:36,
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Web stranica"

#: includes/admin/views/html-admin-page-upgrade-network.php:80
msgid "Site is up to date"
msgstr "Nema novih ažuriranja za web stranica"

#: includes/admin/views/html-admin-page-upgrade-network.php:78
#, fuzzy
#| msgid "Site requires database upgrade from %s to %s"
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr ""
"Za web stranicu je potrebna nadogradnja baze podataka iz %s na verziju %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:97
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Baza podataka je nadograđena. <a href=“%s”>Kliknite ovdje za povratak na "
"administraciju WordPress mreže</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:117
msgid "Please select at least one site to upgrade."
msgstr ""

#: includes/admin/views/html-admin-page-upgrade-network.php:121,
#: includes/admin/views/html-notice-upgrade.php:45
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Prije nego nastavite preporučamo da napravite sigurnosnu kopiju baze "
"podataka. Jeste li sigurni da želite nastaviti ažuriranje?"

#: includes/admin/views/html-admin-page-upgrade-network.php:148,
#: includes/admin/views/html-admin-page-upgrade.php:31
msgid "Upgrading data to version %s"
msgstr "Nadogradnja na verziju %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:162
#, fuzzy
#| msgid "Upgrade complete"
msgid "Upgrade complete."
msgstr "Nadogradnja završena"

#: includes/admin/views/html-admin-page-upgrade-network.php:165,
#: includes/admin/views/html-admin-page-upgrade.php:65
#, fuzzy
#| msgid "Upgrade Sites"
msgid "Upgrade failed."
msgstr "Ažuriraj stranice"

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Učitavam podatke za nadogradnju…"

#: includes/admin/views/html-admin-page-upgrade.php:33
#, fuzzy
#| msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Nadogradnja baze je dovršena. <a href=\"%s\">Pogledajte što je novo</a>"

#: includes/admin/views/html-admin-tools.php:24
msgid "Back to all tools"
msgstr ""

#: includes/admin/views/html-notice-upgrade.php:28
msgid "Database Upgrade Required"
msgstr "Potrebno je nadograditi bazu podataka"

#: includes/admin/views/html-notice-upgrade.php:29
#, fuzzy
#| msgid "Thank you for updating to %s v%s!"
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Hvala što ste nadogradili %s na v%s!"

#: includes/admin/views/html-notice-upgrade.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""

#: includes/admin/views/html-notice-upgrade.php:31
#, fuzzy
#| msgid ""
#| "Please also ensure any premium add-ons (%s) have first been updated to "
#| "the latest version."
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Molimo provjerite da su svi premium dodaci (%s) ažurirani na najnoviju "
"verziju."

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Deaktiviraj licencu"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Aktiviraj licencu"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Informacije o licenci"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Da bi omogućili ažuriranje, molimo unesite vašu licencu i polje ispod. "
"Ukoliko ne posjedujete licencu, molimo posjetite <a href=“%s” "
"target=“_blank”>detalji i cijene</a>."

#: pro/admin/views/html-settings-updates.php:41
msgid "License Key"
msgstr "Licenca"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
#, fuzzy
#| msgid "Better Validation"
msgid "Retry Activation"
msgstr "Bolja verifikacija polja"

#: pro/admin/views/html-settings-updates.php:76
msgid "Update Information"
msgstr "Ažuriraj informacije"

#: pro/admin/views/html-settings-updates.php:83
msgid "Current Version"
msgstr "Trenutna vezija"

#: pro/admin/views/html-settings-updates.php:91
msgid "Latest Version"
msgstr "Posljednja dostupna verzija"

#: pro/admin/views/html-settings-updates.php:99
msgid "Update Available"
msgstr "Dostupna nadogradnja"

#: pro/admin/views/html-settings-updates.php:111
msgid "Please enter your license key above to unlock updates"
msgstr "Unesite licencu kako bi mogli izvršiti nadogradnju"

#: pro/admin/views/html-settings-updates.php:109
msgid "Update Plugin"
msgstr "Nadogradi dodatak"

#: pro/admin/views/html-settings-updates.php:107
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Please reactivate your license to unlock updates"
msgstr "Unesite licencu kako bi mogli izvršiti nadogradnju"

#: pro/admin/views/html-settings-updates.php:124
msgid "Changelog"
msgstr "Popis izmjena"

#: pro/admin/views/html-settings-updates.php:134
msgid "Upgrade Notice"
msgstr "Obavijest od nadogradnjama"

#~ msgid "Inactive"
#~ msgstr "Neaktivno"

#, php-format
#~ msgid "Inactive <span class=\"count\">(%s)</span>"
#~ msgid_plural "Inactive <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Neaktivno <span class=“count”>(%s)</span>"
#~ msgstr[1] "Neaktivnih: <span class=“count”>(%s)</span>"
#~ msgstr[2] "Neaktivnih: <span class=“count”>(%s)</span>"

#~ msgid "Parent fields"
#~ msgstr "Matično polje"

#~ msgid "Sibling fields"
#~ msgstr "Slična polja"

#, php-format
#~ msgid "%s field group synchronised."
#~ msgid_plural "%s field groups synchronised."
#~ msgstr[0] "Polja sinkronizirana (%s)."
#~ msgstr[1] "Polja sinkronizirana (%s)."
#~ msgstr[2] "Polja sinkronizirana (%s)."

#~ msgid "Status"
#~ msgstr "Status"

#, php-format
#~ msgid "See what's new in <a href=\"%s\">version %s</a>."
#~ msgstr "Pogledaj što je novo u <a href=\"%s\">%s verziji</a>."

#~ msgid "Resources"
#~ msgstr "Materijali"

#~ msgid "Documentation"
#~ msgstr "Dokumentacija"

#~ msgid "Pro"
#~ msgstr "Pro"

#, php-format
#~ msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
#~ msgstr "Hvala što koristite <a href=\"%s\">ACF</a>."

#~ msgid "Synchronise field group"
#~ msgstr "Sinkroniziraj skup polja"

#~ msgid "Apply"
#~ msgstr "Prijavi"

#~ msgid "Bulk Actions"
#~ msgstr "Skupne akcije"

#~ msgid "Add-ons"
#~ msgstr "Dodaci"

#~ msgid "<b>Error</b>. Could not load add-ons list"
#~ msgstr "<b>Greška</b>. Greška prilikom učitavanja dodataka"

#~ msgid "Info"
#~ msgstr "Info"

#~ msgid "What's New"
#~ msgstr "Što je novo"

#~ msgid "Advanced Custom Fields Database Upgrade"
#~ msgstr "Nadogradnja baze ACF"

#~ msgid ""
#~ "Before you start using the new awesome features, please update your "
#~ "database to the newest version."
#~ msgstr ""
#~ "Prije nego što počnete koristiti nove mogućnosti, molimo ažurirajte bazu "
#~ "podataka na posljednju verziju."

#~ msgid "Download & Install"
#~ msgstr "Preuzimam datoteke"

#~ msgid "Installed"
#~ msgstr "Instalirano"

#~ msgid "Welcome to Advanced Custom Fields"
#~ msgstr "Advanced Custom Fields vam želi dobrodošlicu"

#, php-format
#~ msgid ""
#~ "Thank you for updating! ACF %s is bigger and better than ever before. We "
#~ "hope you like it."
#~ msgstr ""
#~ "Ažuriranje dovršeno, hvala! ACF %s je veći i bolji nego ikad prije. "
#~ "Nadamo se da će vam se svidjet."

#~ msgid "A smoother custom field experience"
#~ msgstr "Bolje korisničko iskustvo korištenja prilagođenih polja"

#~ msgid "Improved Usability"
#~ msgstr "Poboljšana uporabljivost"

#~ msgid ""
#~ "Including the popular Select2 library has improved both usability and "
#~ "speed across a number of field types including post object, page link, "
#~ "taxonomy and select."
#~ msgstr ""
#~ "Uključivanje popularne biblioteke Select2 poboljšano je korisničko "
#~ "iskustvo i brzina na velikom broju polja."

#~ msgid "Improved Design"
#~ msgstr "Unaprijeđen dizajn"

#~ msgid ""
#~ "Many fields have undergone a visual refresh to make ACF look better than "
#~ "ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
#~ "(new) fields!"
#~ msgstr ""
#~ "Mnoga polja su vizualno osvježena te time ACF sada izgleda bolje nego "
#~ "ikad prije!"

#~ msgid "Improved Data"
#~ msgstr "Unaprijeđeno upravljanje podacima"

#~ msgid ""
#~ "Redesigning the data architecture has allowed sub fields to live "
#~ "independently from their parents. This allows you to drag and drop fields "
#~ "in and out of parent fields!"
#~ msgstr ""
#~ "Nova arhitektura polja omogućuje pod poljima da budu korištena zasebno "
#~ "bez obzira kojem skupu polja pripadaju. Ovo vam omogućuje premještanje "
#~ "polja iz jednog skupa u drugi!"

#~ msgid "Goodbye Add-ons. Hello PRO"
#~ msgstr "Doviđenja dodaci, upoznajte PRO verziju"

#~ msgid "Introducing ACF PRO"
#~ msgstr "Predstavljamo ACF PRO"

#~ msgid ""
#~ "We're changing the way premium functionality is delivered in an exciting "
#~ "way!"
#~ msgstr ""
#~ "Mijanjamo način funkcioniranja premium dodataka, od sada mnogo "
#~ "jednostavnije!"

#, php-format
#~ msgid ""
#~ "All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
#~ "version of ACF</a>. With both personal and developer licenses available, "
#~ "premium functionality is more affordable and accessible than ever before!"
#~ msgstr ""
#~ "Sva 4 premium dodakta od sada su ukomponiranu u novu <a href=“%s”>Pro "
#~ "verziju ACF</a>. Sa novim osobnom i razvojnom opcijom licenciranja, "
#~ "premium funkcionalnost je dosupnija i povoljnija nego prije!"

#~ msgid "Powerful Features"
#~ msgstr "Super mogućnosti"

#~ msgid ""
#~ "ACF PRO contains powerful features such as repeatable data, flexible "
#~ "content layouts, a beautiful gallery field and the ability to create "
#~ "extra admin options pages!"
#~ msgstr ""
#~ "ACF PRO uključuje napredne funkcionalnosti kao ponavljajuća polja, "
#~ "modularni raspored, galerija slika i mogućnost dodavanja novih stranica u "
#~ "postavkama administracije!"

#, php-format
#~ msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
#~ msgstr "Pročitajte više o <a href=“%s”>mogućnostima ACF PRO</a>."

#~ msgid "Easy Upgrading"
#~ msgstr "Jednostavno ažuriranje"

#, php-format
#~ msgid ""
#~ "To help make upgrading easy, <a href=\"%s\">login to your store account</"
#~ "a> and claim a free copy of ACF PRO!"
#~ msgstr ""
#~ "Kako bi pojednostavili ažuriranje, <a href=“%s”>prijavite se s vašim "
#~ "računom</a> i osigurajte besplatnu verziju ACF PRO!"

#, php-format
#~ msgid ""
#~ "We also wrote an <a href=\"%s\">upgrade guide</a> to answer any "
#~ "questions, but if you do have one, please contact our support team via "
#~ "the <a href=\"%s\">help desk</a>"
#~ msgstr ""
#~ "Provjeriti <a href=“%s”>upute za ažuriranje</a> ako imate dodatnih "
#~ "pitanja, ili kontaktirajte našu <a href=“%s”>tim za podršku</a>"

#~ msgid "Under the Hood"
#~ msgstr "Ispod haube"

#~ msgid "Smarter field settings"
#~ msgstr "Pametnije postavke"

#~ msgid "ACF now saves its field settings as individual post objects"
#~ msgstr "ACF od sada sprema postavke polja kao objekt"

#~ msgid "More AJAX"
#~ msgstr "Više AJAX-a"

#~ msgid "More fields use AJAX powered search to speed up page loading"
#~ msgstr ""
#~ "Više polja koristi asinkrono pretraživanje kako bi učitavanje stranice "
#~ "bilo brže"

#~ msgid "New auto export to JSON feature improves speed"
#~ msgstr "Nova mogućnost automatskog izvoza u JSON obliku"

#~ msgid "Better version control"
#~ msgstr "Bolje upravljanje verzijama"

#~ msgid ""
#~ "New auto export to JSON feature allows field settings to be version "
#~ "controlled"
#~ msgstr "Nova opcija izvoza u JSON omogućuje verziranje"

#~ msgid "Swapped XML for JSON"
#~ msgstr "JSON umjesto XML"

#~ msgid "Import / Export now uses JSON in favour of XML"
#~ msgstr "Uvoz / Izvoz sada koristi JSON umjesto XML"

#~ msgid "New Forms"
#~ msgstr "Nove forme"

#~ msgid "Fields can now be mapped to comments, widgets and all user forms!"
#~ msgstr ""
#~ "Od sada je moguće dodati polja na sve stranice, uključujući komentare, "
#~ "stranice za uređivanje korisnika i widgete!"

#~ msgid "A new field for embedding content has been added"
#~ msgstr "Novo polje za ugnježdeni sadržaj"

#~ msgid "New Gallery"
#~ msgstr "Nova galerija"

#~ msgid "The gallery field has undergone a much needed facelift"
#~ msgstr "Polje Galerija je dobilo novi izgled"

#~ msgid "New Settings"
#~ msgstr "Nove postavke"

#~ msgid ""
#~ "Field group settings have been added for label placement and instruction "
#~ "placement"
#~ msgstr ""
#~ "Postavke svakog polja uključuju dodatna polja, polje za opis i polje za "
#~ "upute namjenjene korisniku"

#~ msgid "Better Front End Forms"
#~ msgstr "Bolji prikaz formi na web stranici"

#~ msgid "acf_form() can now create a new post on submission"
#~ msgstr ""
#~ "acf_form() funkcija od sada omogućuje dodavanje nove objave prilikom "
#~ "spremanja"

#~ msgid "Form validation is now done via PHP + AJAX in favour of only JS"
#~ msgstr ""
#~ "Verifikacija polja se sada obavlja asinkrono (PHP + AJAX) umjesto "
#~ "dosadašnjeg načina (Javascript)"

#~ msgid "Relationship Field"
#~ msgstr "Polje za povezivanje objekta"

#~ msgid ""
#~ "New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
#~ msgstr ""
#~ "Novo postavke polja Veza za filter (pretraga, tip objekta, taksonomija)"

#~ msgid "Moving Fields"
#~ msgstr "Premještanje polja"

#~ msgid ""
#~ "New field group functionality allows you to move a field between groups & "
#~ "parents"
#~ msgstr ""
#~ "Nova funkcionalnost polja omogućuje premještanje polja i skupa polja"

#~ msgid "New archives group in page_link field selection"
#~ msgstr "Nova skupina ‘arhiva’ prilikom odabira polja page_link"

#~ msgid "Better Options Pages"
#~ msgstr "Bolja upravljanje stranica sa postavkama"

#~ msgid ""
#~ "New functions for options page allow creation of both parent and child "
#~ "menu pages"
#~ msgstr ""
#~ "Nova funkcionalnost kod dodavanja stranica za postavke omogućuju "
#~ "dodavanje izvornih i pod stranica izbornika"

#, php-format
#~ msgid "We think you'll love the changes in %s."
#~ msgstr "Mislimo da će vam se svidjeti promjene u %s."

#~ msgid "Current Color"
#~ msgstr "Trenutna boja"

#~ msgid "Locating"
#~ msgstr "Lociranje u tijeku"

#~ msgid "Shown when entering data"
#~ msgstr "Prikazuje se prilikom unosa podataka"

#~ msgid "Error."
#~ msgstr "Greška."

#~ msgid "No embed found for the given URL."
#~ msgstr "Nije pronađen nijedan umetak za unesenu adresu."

#~ msgid "Minimum values reached ( {min} values )"
#~ msgstr "Minimalna vrijednost je {min}"

#~ msgid "None"
#~ msgstr "Bez odabira"

#~ msgid "Taxonomy Term"
#~ msgstr "Pojam takosnomije"

#~ msgid "remove {layout}?"
#~ msgstr "ukloni {layout}?"

#~ msgid "This field requires at least {min} {identifier}"
#~ msgstr "Polje mora sadržavati najmanje {min} {identifier}"

#~ msgid "Maximum {label} limit reached ({max} {identifier})"
#~ msgstr "Polje {label} smije sadržavati najviše {max} {identifier}"

#~ msgid "Elliot Condon"
#~ msgstr "Elliot Condon"

#~ msgid "http://www.elliotcondon.com/"
#~ msgstr "http://www.elliotcondon.com/"

#~ msgid "Getting Started"
#~ msgstr "Kako početi"

#~ msgid "Field Types"
#~ msgstr "Tipovi polja"

#~ msgid "Functions"
#~ msgstr "Funkcije"

#~ msgid "Actions"
#~ msgstr "Akcije"

#~ msgid "Features"
#~ msgstr "Mogućnosti"

#~ msgid "How to"
#~ msgstr "Pomoć"

#~ msgid "Tutorials"
#~ msgstr "Tutorijali"

#~ msgid "FAQ"
#~ msgstr "Česta pitanja"

#~ msgid "Error"
#~ msgstr "Greška"

#~ msgid "Export Field Groups to PHP"
#~ msgstr "Izvoz polja u PHP obliku"

#~ msgid "Download export file"
#~ msgstr "Preuzmi datoteku"

#~ msgid "Generate export code"
#~ msgstr "Stvori kod za izvoz"

#~ msgid "Term meta upgrade not possible (termmeta table does not exist)"
#~ msgstr ""
#~ "Nije moguće dovrišti nadogradnju tablice 'termmeta', tablica ne postoji u "
#~ "bazi"
