# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2022-12-13T15:07:38+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: ko_KR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/api/api-template.php:867
msgid "[ACF shortcode value disabled for preview]"
msgstr ""

#: includes/admin/admin-field-group.php:672
msgid "Close Modal"
msgstr ""

#: includes/admin/admin-field-group.php:162
#: assets/build/js/acf-field-group.js:1304
#: assets/build/js/acf-field-group.js:1468
msgid "Field moved to other group"
msgstr ""

#: includes/admin/admin-field-group.php:161 assets/build/js/acf.js:1397
#: assets/build/js/acf.js:1463
msgid "Close modal"
msgstr ""

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr ""

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr ""

#: includes/fields/class-acf-field-select.php:450
#: includes/fields/class-acf-field-true_false.php:197
msgid "Use a stylized checkbox using select2"
msgstr ""

#: includes/fields/class-acf-field-radio.php:257
msgid "Save Other Choice"
msgstr ""

#: includes/fields/class-acf-field-radio.php:246
msgid "Allow Other Choice"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:446
msgid "Add Toggle All"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:405
msgid "Save Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:394
msgid "Allow Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:144
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""

#: includes/admin/views/html-admin-navigation.php:111
msgid "Updates"
msgstr ""

#: includes/admin/views/html-admin-navigation.php:79
msgid "Advanced Custom Fields logo"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:22
msgid "Save Changes"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:16
msgid "Field Group Title"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:3
msgid "Add title"
msgstr ""

#. translators: %s url to getting started guide
#: includes/admin/views/field-groups-empty.php:20
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""

#: includes/admin/views/field-groups-empty.php:15
msgid "Add Field Group"
msgstr ""

#. translators: %s url to creating a field group page
#: includes/admin/views/field-groups-empty.php:10
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""

#: includes/admin/views/field-groups-empty.php:5
msgid "Add Your First Field Group"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:16
msgid "Upgrade Now"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:11
msgid "Options Pages"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:10
msgid "ACF Blocks"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:8
msgid "Gallery Field"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:7
msgid "Flexible Content Field"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:6
msgid "Repeater Field"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:4
#: includes/admin/views/html-admin-navigation.php:97
msgid "Unlock Extra Features with ACF PRO"
msgstr ""

#: includes/admin/views/field-group-options.php:242
msgid "Delete Field Group"
msgstr ""

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/field-group-options.php:236
msgid "Created on %1$s at %2$s"
msgstr ""

#: includes/admin/views/field-group-options.php:179
msgid "Group Settings"
msgstr ""

#: includes/admin/views/field-group-options.php:28
msgid "Location Rules"
msgstr ""

#. translators: %s url to field types list
#: includes/admin/views/field-group-fields.php:61
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""

#: includes/admin/views/field-group-fields.php:54
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""

#: includes/admin/views/field-group-fields.php:53
msgid "Add Your First Field"
msgstr ""

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/field-group-fields.php:32
msgid "#"
msgstr ""

#: includes/admin/views/field-group-fields.php:22
#: includes/admin/views/field-group-fields.php:56
#: includes/admin/views/field-group-fields.php:92
#: includes/admin/views/html-admin-form-top.php:21
msgid "Add Field"
msgstr ""

#: includes/admin/views/field-group-field.php:191
#: includes/admin/views/field-group-options.php:40
msgid "Presentation"
msgstr ""

#: includes/admin/views/field-group-field.php:159
msgid "Validation"
msgstr ""

#: includes/admin/views/field-group-field.php:93
msgid "General"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:70
msgid "Import JSON"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:361
msgid "Export Field Groups - Generate PHP"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid "Export As JSON"
msgstr ""

#: includes/admin/admin-field-groups.php:638
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] ""

#: includes/admin/admin-field-groups.php:585
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] ""

#: includes/admin/admin-field-groups.php:537
#: includes/admin/admin-field-groups.php:558
msgid "Deactivate"
msgstr ""

#: includes/admin/admin-field-groups.php:537
msgid "Deactivate this item"
msgstr ""

#: includes/admin/admin-field-groups.php:533
#: includes/admin/admin-field-groups.php:557
msgid "Activate"
msgstr ""

#: includes/admin/admin-field-groups.php:533
msgid "Activate this item"
msgstr ""

#: includes/admin/admin-field-group.php:158
#: assets/build/js/acf-field-group.js:2366
#: assets/build/js/acf-field-group.js:2635
msgid "Move field group to trash?"
msgstr ""

#: acf.php:448 includes/admin/admin-field-group.php:353
#: includes/admin/admin-field-groups.php:232
msgctxt "post status"
msgid "Inactive"
msgstr ""

#. Author of the plugin
msgid "WP Engine"
msgstr ""

#: acf.php:506
msgid ""
"Advanced Custom Fields and Advanced Custom Fields should not be active at "
"the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""

#: acf.php:504
msgid ""
"Advanced Custom Fields and Advanced Custom Fields should not be active at "
"the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""

#: includes/acf-value-functions.php:374
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" target=\"_blank"
"\">Learn how to fix this</a>."
msgstr ""

#: includes/fields/class-acf-field-user.php:537
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] ""

#: includes/fields/class-acf-field-user.php:528
msgid "%1$s must have a valid user ID."
msgstr ""

#: includes/fields/class-acf-field-user.php:366
msgid "Invalid request."
msgstr ""

#: includes/fields/class-acf-field-select.php:683
msgid "%1$s is not one of %2$s"
msgstr ""

#: includes/fields/class-acf-field-post_object.php:669
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] ""

#: includes/fields/class-acf-field-post_object.php:653
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] ""

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must have a valid post ID."
msgstr ""

#: includes/fields/class-acf-field-file.php:472
msgid "%s requires a valid attachment ID."
msgstr ""

#: includes/admin/views/field-group-options.php:205
msgid "Show in REST API"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:167
msgid "Enable Transparency"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:186
msgid "RGBA Array"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:96
msgid "RGBA String"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:95
#: includes/fields/class-acf-field-color_picker.php:185
msgid "Hex String"
msgstr ""

#: includes/admin/admin-field-group.php:187
#: assets/build/js/acf-field-group.js:837
#: assets/build/js/acf-field-group.js:933
msgid "Gallery (Pro only)"
msgstr ""

#: includes/admin/admin-field-group.php:186
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:923
msgid "Clone (Pro only)"
msgstr ""

#: includes/admin/admin-field-group.php:185
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:920
msgid "Flexible Content (Pro only)"
msgstr ""

#: includes/admin/admin-field-group.php:184
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:917
msgid "Repeater (Pro only)"
msgstr ""

#: includes/admin/admin-field-group.php:353
msgctxt "post status"
msgid "Active"
msgstr ""

#: includes/fields/class-acf-field-email.php:178
msgid "'%s' is not a valid email address"
msgstr "‘%s’은(는) 유효한 이매일 주소가 아닙니다"

#: includes/fields/class-acf-field-color_picker.php:74
msgid "Color value"
msgstr "색상 값"

#: includes/fields/class-acf-field-color_picker.php:72
msgid "Select default color"
msgstr "기본 색상 선택하기"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Clear color"
msgstr "색상 지우기"

#: includes/acf-wp-functions.php:87
msgid "Blocks"
msgstr "블록"

#: includes/acf-wp-functions.php:83
msgid "Options"
msgstr "옵션"

#: includes/acf-wp-functions.php:79
msgid "Users"
msgstr "사용자"

#: includes/acf-wp-functions.php:75
msgid "Menu items"
msgstr "메뉴 항목"

#: includes/acf-wp-functions.php:67
msgid "Widgets"
msgstr "위젯"

#: includes/acf-wp-functions.php:59
msgid "Attachments"
msgstr "첨부"

#: includes/acf-wp-functions.php:54
msgid "Taxonomies"
msgstr "택소노미"

#: includes/acf-wp-functions.php:41
msgid "Posts"
msgstr "글"

#: includes/ajax/class-acf-ajax-local-json-diff.php:68
msgid "JSON field group (newer)"
msgstr "JSON 필드 그룹 (최신)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:64
msgid "Original field group"
msgstr "원본 필드 그룹"

#: includes/ajax/class-acf-ajax-local-json-diff.php:59
msgid "Last updated: %s"
msgstr "최근 업대이트: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:53
msgid "Sorry, this field group is unavailable for diff comparison."
msgstr "죄송합니다. 이 필드 그룹은 차이점 비교를 사용할 수 없어요."

#: includes/ajax/class-acf-ajax-local-json-diff.php:43
msgid "Invalid field group ID."
msgstr "유효하지 않은 필드 그룹 ID에요."

#: includes/ajax/class-acf-ajax-local-json-diff.php:36
msgid "Invalid field group parameter(s)."
msgstr "유효하지 않은 필드 그룹 매개변수에요."

#: includes/admin/admin-field-groups.php:506
msgid "Awaiting save"
msgstr "저장 대기 중"

#: includes/admin/admin-field-groups.php:503
msgid "Saved"
msgstr "저장했어요"

#: includes/admin/admin-field-groups.php:499
msgid "Import"
msgstr "가져오기"

#: includes/admin/admin-field-groups.php:495
msgid "Review changes"
msgstr "변경사항 검토하기"

#: includes/admin/admin-field-groups.php:471
msgid "Located in: %s"
msgstr "위치해요: %s"

#: includes/admin/admin-field-groups.php:467
msgid "Located in plugin: %s"
msgstr "플러그인에 위치해요: %s"

#: includes/admin/admin-field-groups.php:463
msgid "Located in theme: %s"
msgstr "테마에 위치해요: %s"

#: includes/admin/admin-field-groups.php:441
msgid "Various"
msgstr "변형"

#: includes/admin/admin-field-groups.php:200
#: includes/admin/admin-field-groups.php:565
msgid "Sync changes"
msgstr "변경사항 동기화하기"

#: includes/admin/admin-field-groups.php:199
msgid "Loading diff"
msgstr "차이 부르는 중"

#: includes/admin/admin-field-groups.php:198
msgid "Review local JSON changes"
msgstr "지역 JSON 변경 검토하기"

#: includes/admin/admin.php:172
msgid "Visit website"
msgstr "웹 사이트 방문하기"

#: includes/admin/admin.php:171
msgid "View details"
msgstr "세부 정보보기"

#: includes/admin/admin.php:170
msgid "Version %s"
msgstr "버전 %s"

#: includes/admin/admin.php:169
msgid "Information"
msgstr "정보"

#: includes/admin/admin.php:160
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">상담 창구</a>가 있어요. 상담 창구의 지원 전"
"문가가 보다 깊이 있고 기술적인 문제에 대한 도움을 드릴 거에요."

#: includes/admin/admin.php:156
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the ‘how-tos’ of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">토론</a>이 있어요. 고사필 세계의 ‘방법’을 파"
"악하는데 도움을 줄 수 있는 활발하고 친근한 커뮤니티가 커뮤니티 포럼에 있어요."

#: includes/admin/admin.php:152
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">문서</a>가 있어요. 광범위한 문서에는 발생할 "
"수 있는 대부분의 상황에 대한 참조와 지침을 포함하고 있어요."

#: includes/admin/admin.php:149
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"지원에 열정적이고, 고사필을 통해 웹사이트를 최대한 활용하시기 원해요. 어떤 어"
"려움이라도 겪고 있다면, 도움을 얻을 수 있는 여러 곳이 있어요:"

#: includes/admin/admin.php:146 includes/admin/admin.php:148
msgid "Help & Support"
msgstr "도움말 & 지원"

#: includes/admin/admin.php:137
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr "지원이 필요하다면 도움말 & 지원탭을 이용하여 연락하기 바래요."

#: includes/admin/admin.php:134
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"첫 필드 그룹을 만들기 전에, 플러그인의 철학과 모범 사례로 친숙해질 수 있는 "
"<a href=\"%s\" target=\"_blank\">시작하기</a> 지침을 먼저 읽으시기를 권해요."

#: includes/admin/admin.php:132
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"고급 사용자 정의 필드 플러그인은 워드프레스 편집 화면을 추가 필드로 사용자 정"
"의할 수 있는 비주얼 양식 제작기와 어떤 테마 템플릿 파일에서든지 사용자 정의 "
"필드 값을 보여주는 직관적인 API를 제공해요."

#: includes/admin/admin.php:129 includes/admin/admin.php:131
msgid "Overview"
msgstr "전체 보기"

#: includes/locations.php:36
msgid "Location type \"%s\" is already registered."
msgstr "위치 유형 “%s”을(를) 이미 등록했어요."

#: includes/locations.php:25
msgid "Class \"%s\" does not exist."
msgstr "클래스 “%s”(이)가 없어요."

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "유효하지 않은 논스에요."

#: includes/fields/class-acf-field-user.php:361
msgid "Error loading field."
msgstr "필드를 부르는 중에 오류가 있어요."

#: assets/build/js/acf-input.js:2742 assets/build/js/acf-input.js:2811
#: assets/build/js/acf-input.js:2913 assets/build/js/acf-input.js:2987
msgid "Location not found: %s"
msgstr "위치를 찾을 수 없어요: %s"

#: includes/forms/form-user.php:353
msgid "<strong>Error</strong>: %s"
msgstr ""

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "위젯"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "사용자 역할"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "댓글"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "글 형식"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "메뉴 항목"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "글 상태"

#: includes/acf-wp-functions.php:71
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "메뉴"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "메뉴 위치"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "메뉴"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "글 분류"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "자식 패이지 (부모가 있습니다)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "부모 패이지 (자식이 있습니다)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "최상위 패이지 (부모가 없습니다)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "글 패이지"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "프론트 패이지"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "패이지 유형"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "끝을 다시보기"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "보기"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "로그인했습니다"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "현재 사용자"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "패이지 템플릿"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "등록하기"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "추가하기 / 편집하기"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "사용자 양식"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "패이지 부모"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "최고 관리자"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "현재 사용자 역할"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "기본 템플릿"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "글 템플릿"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "글 카테고리"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "모든 %s의 형식"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "첨부"

#: includes/validation.php:365
msgid "%s value is required"
msgstr "%s 값이 필요합니다"

#: includes/admin/views/field-group-field-conditional-logic.php:59
msgid "Show this field if"
msgstr "이 필드를 보이는 경우"

#: includes/admin/views/field-group-field-conditional-logic.php:26
#: includes/admin/views/field-group-field.php:279
msgid "Conditional Logic"
msgstr "조건부 논리"

#: includes/admin/admin.php:207
#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/html-location-rule.php:92
msgid "and"
msgstr "그리고"

#: includes/admin/admin-field-groups.php:290
msgid "Local JSON"
msgstr "로컬 JSON"

#: includes/admin/views/field-group-pro-features.php:9
msgid "Clone Field"
msgstr "복제 필드"

#: includes/admin/views/html-notice-upgrade.php:30
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr "최신 버전 프리미엄 애드온 (%s)의 업대이트를 확인하기 바랍니다."

#: includes/admin/views/html-notice-upgrade.php:28
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr "이 버전은 대이터배이스 개선을 포함하고 있어 업그래이드가 필요합니다."

#: includes/admin/views/html-notice-upgrade.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr ""

#: includes/admin/views/html-notice-upgrade.php:27
msgid "Database Upgrade Required"
msgstr "대이터배이스 업그래이드가 필요합니다"

#: includes/admin/views/html-notice-upgrade.php:18
msgid "Options Page"
msgstr "옵션 패이지"

#: includes/admin/views/html-notice-upgrade.php:15
msgid "Gallery"
msgstr "갤러리"

#: includes/admin/views/html-notice-upgrade.php:12
msgid "Flexible Content"
msgstr "유연한 콘텐츠"

#: includes/admin/views/html-notice-upgrade.php:9
msgid "Repeater"
msgstr "리피터"

#: includes/admin/views/html-admin-tools.php:24
msgid "Back to all tools"
msgstr "모든 도구로 돌아감"

#: includes/admin/views/field-group-options.php:160
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"편집 화면에 여러 필드 그룹을 보이면 첫 번째 필드 그룹 옵션을 사용합니다 (가"
"장 낮은 정렬 번호인 것)"

#: includes/admin/views/field-group-options.php:160
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "편집 화면에서 <b>숨기려면</b> 항목을 <b>선택</b>하세요."

#: includes/admin/views/field-group-options.php:159
msgid "Hide on screen"
msgstr "화면 숨기기"

#: includes/admin/views/field-group-options.php:151
msgid "Send Trackbacks"
msgstr "트랙백 보내기"

#: includes/admin/views/field-group-options.php:150
msgid "Tags"
msgstr "태그"

#: includes/admin/views/field-group-options.php:149
msgid "Categories"
msgstr "카테고리"

#: includes/admin/views/field-group-options.php:147
msgid "Page Attributes"
msgstr "패이지 속성"

#: includes/admin/views/field-group-options.php:146
msgid "Format"
msgstr "형식"

#: includes/admin/views/field-group-options.php:145
msgid "Author"
msgstr "글쓴이"

#: includes/admin/views/field-group-options.php:144
msgid "Slug"
msgstr "슬러그"

#: includes/admin/views/field-group-options.php:143
msgid "Revisions"
msgstr "리비전"

#: includes/acf-wp-functions.php:63
#: includes/admin/views/field-group-options.php:142
msgid "Comments"
msgstr "댓글"

#: includes/admin/views/field-group-options.php:141
msgid "Discussion"
msgstr "토론"

#: includes/admin/views/field-group-options.php:139
msgid "Excerpt"
msgstr "요약"

#: includes/admin/views/field-group-options.php:138
msgid "Content Editor"
msgstr "콘텐츠 편집기"

#: includes/admin/views/field-group-options.php:137
msgid "Permalink"
msgstr "퍼머링크"

#: includes/admin/views/field-group-options.php:222
msgid "Shown in field group list"
msgstr "필드 그룹 목록에 보이기"

#: includes/admin/views/field-group-options.php:122
msgid "Field groups with a lower order will appear first"
msgstr "순서가 낮은 필드 그룹이 먼저 나타납니다"

#: includes/admin/views/field-group-options.php:121
msgid "Order No."
msgstr "정렬 번호"

#: includes/admin/views/field-group-options.php:112
msgid "Below fields"
msgstr "필드 아래"

#: includes/admin/views/field-group-options.php:111
msgid "Below labels"
msgstr "라벨 아래"

#: includes/admin/views/field-group-options.php:104
msgid "Instruction placement"
msgstr "지침 배치"

#: includes/admin/views/field-group-options.php:87
msgid "Label placement"
msgstr "라벨 배치"

#: includes/admin/views/field-group-options.php:77
msgid "Side"
msgstr "측면"

#: includes/admin/views/field-group-options.php:76
msgid "Normal (after content)"
msgstr "일반 (콘텐츠 다음)"

#: includes/admin/views/field-group-options.php:75
msgid "High (after title)"
msgstr "상단 (제목 다음)"

#: includes/admin/views/field-group-options.php:68
msgid "Position"
msgstr "위치"

#: includes/admin/views/field-group-options.php:59
msgid "Seamless (no metabox)"
msgstr "매끄럽게 (메타 상자가 없습니다)"

#: includes/admin/views/field-group-options.php:58
msgid "Standard (WP metabox)"
msgstr "표준 (워프 메타 상자)"

#: includes/admin/views/field-group-options.php:51
msgid "Style"
msgstr "스타일"

#: includes/admin/views/field-group-fields.php:44
msgid "Type"
msgstr "유형"

#: includes/admin/admin-field-groups.php:285
#: includes/admin/views/field-group-fields.php:43
msgid "Key"
msgstr "키"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/field-group-fields.php:37
msgid "Order"
msgstr "정렬하기"

#: includes/admin/views/field-group-field.php:294
msgid "Close Field"
msgstr "필드 닫기"

#: includes/admin/views/field-group-field.php:235
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:219
msgid "class"
msgstr "클래스"

#: includes/admin/views/field-group-field.php:256
msgid "width"
msgstr "넓이"

#: includes/admin/views/field-group-field.php:250
msgid "Wrapper Attributes"
msgstr "덮개 속성"

#: includes/admin/views/field-group-field.php:171
msgid "Required"
msgstr ""

#: includes/admin/views/field-group-field.php:203
msgid "Instructions for authors. Shown when submitting data"
msgstr "글쓴이를 위한 지침입니다. 자료를 제출할 때 보이기"

#: includes/admin/views/field-group-field.php:202
msgid "Instructions"
msgstr "지침"

#: includes/admin/views/field-group-field.php:106
msgid "Field Type"
msgstr "필드 유형"

#: includes/admin/views/field-group-field.php:134
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "공백이 없는 단일 단어입니다. 밑줄과 대시를 허용했습니다."

#: includes/admin/views/field-group-field.php:133
msgid "Field Name"
msgstr "필드 이름"

#: includes/admin/views/field-group-field.php:121
msgid "This is the name which will appear on the EDIT page"
msgstr "이것은 편집하기 패이지에 보일 이름입니다."

#: includes/admin/views/field-group-field.php:120
msgid "Field Label"
msgstr "필드 레이블"

#: includes/admin/views/field-group-field.php:70
msgid "Delete"
msgstr "지우기"

#: includes/admin/views/field-group-field.php:70
msgid "Delete field"
msgstr "필드 지우기"

#: includes/admin/views/field-group-field.php:68
msgid "Move"
msgstr "이동하기"

#: includes/admin/views/field-group-field.php:68
msgid "Move field to another group"
msgstr "다름 그룹으로 필드 이동하기"

#: includes/admin/views/field-group-field.php:66
msgid "Duplicate field"
msgstr "필드 복제하기"

#: includes/admin/views/field-group-field.php:62
#: includes/admin/views/field-group-field.php:65
msgid "Edit field"
msgstr "필드 편집하기"

#: includes/admin/views/field-group-field.php:58
msgid "Drag to reorder"
msgstr "재정렬하려면 끌기"

#: includes/admin/admin-field-group.php:168
#: includes/admin/views/html-location-group.php:3
#: assets/build/js/acf-field-group.js:1935
#: assets/build/js/acf-field-group.js:2166
msgid "Show this field group if"
msgstr "이 필드 그룹이면 보이기"

#: includes/admin/views/html-admin-page-upgrade.php:94
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "사용 가능한 업대이트가 없습니다."

#: includes/admin/views/html-admin-page-upgrade.php:33
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"대이터배이스 업그래이드를 완료했습니다. <a href=\"%s\"> 새로운 기능 보기 </a>"

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "업그래이드 작업을 읽기 ..."

#: includes/admin/views/html-admin-page-upgrade-network.php:165
#: includes/admin/views/html-admin-page-upgrade.php:65
msgid "Upgrade failed."
msgstr "업그래이드에 실패했습니다."

#: includes/admin/views/html-admin-page-upgrade-network.php:162
msgid "Upgrade complete."
msgstr "업그래이드를 완료했습니다."

#: includes/admin/views/html-admin-page-upgrade-network.php:148
#: includes/admin/views/html-admin-page-upgrade.php:31
msgid "Upgrading data to version %s"
msgstr "버전 %s(으)로 자료를 업그래이드하기"

#: includes/admin/views/html-admin-page-upgrade-network.php:121
#: includes/admin/views/html-notice-upgrade.php:44
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"계속하기 전에 대이터배이스를 백업하는 것이 좋습니다. 지금 업데이트 도구를 실"
"행하기 원하는게 확실한가요?"

#: includes/admin/views/html-admin-page-upgrade-network.php:117
msgid "Please select at least one site to upgrade."
msgstr "업그래이드 할 사이트를 하나 이상 선택하기 바랍니다."

#: includes/admin/views/html-admin-page-upgrade-network.php:97
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"대이터배이스 업그래이드를 완료했습니다. <a href=\"%s\"> 네트워크 알림판으로 "
"돌아 가기 </a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:80
msgid "Site is up to date"
msgstr "사이트가 최신 상태입니다"

#: includes/admin/views/html-admin-page-upgrade-network.php:78
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr ""

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "사이트"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:96
msgid "Upgrade Sites"
msgstr "사이트 업그래이드하기"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"다음 사이트는 대배 업그래이드가 필요합니다. 업대이트하려는 항목을 확인한 다"
"음 %s을(를) 누르세요."

#: includes/admin/views/field-group-field-conditional-logic.php:171
#: includes/admin/views/field-group-locations.php:38
msgid "Add rule group"
msgstr "그룹 규칙 추가하기"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr "고급 사용자 정의 필드를 사용할 편집 화면을 결정하는 규칙 세트 만들기"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "규칙"

#: includes/admin/tools/class-acf-admin-tool-export.php:478
msgid "Copied"
msgstr "복사했습니다"

#: includes/admin/tools/class-acf-admin-tool-export.php:441
msgid "Copy to clipboard"
msgstr "클립보드에 복사하기"

#: includes/admin/tools/class-acf-admin-tool-export.php:362
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"다음 코드를 사용하여 선택한 필드 그룹의 로컬 버전을 등록 할 수 있습니다. 로"
"컬 필드 그룹은 빠른 로드 시간, 버전 관리와 동적 필드 설정과 같은 많은 이점을 "
"제공 할 수 있습니다. 다음 코드를 복사하여 테마의 “functions.php” 파일에 붙여 "
"넣거나 외부 파일에 포함하ㅋㅊ뇨."

#: includes/admin/tools/class-acf-admin-tool-export.php:329
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:233
#: includes/admin/tools/class-acf-admin-tool-export.php:262
msgid "Select Field Groups"
msgstr "필드 그룹 선택하기"

#: includes/admin/tools/class-acf-admin-tool-export.php:167
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "%s개의 필드 그룹을 내보냈습니다."

#: includes/admin/tools/class-acf-admin-tool-export.php:96
#: includes/admin/tools/class-acf-admin-tool-export.php:131
msgid "No field groups selected"
msgstr "선택한 필드 그룹이 없습니다"

#: includes/admin/tools/class-acf-admin-tool-export.php:39
#: includes/admin/tools/class-acf-admin-tool-export.php:337
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "PHP 만들기"

#: includes/admin/tools/class-acf-admin-tool-export.php:35
msgid "Export Field Groups"
msgstr "필드 그룹 내보내기"

#: includes/admin/tools/class-acf-admin-tool-import.php:146
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "%s개의 필드 그룹을 가져왔습니다"

#: includes/admin/tools/class-acf-admin-tool-import.php:115
msgid "Import file empty"
msgstr "빈 파일 가져 오기"

#: includes/admin/tools/class-acf-admin-tool-import.php:106
msgid "Incorrect file type"
msgstr "잘못된 파일 형식"

#: includes/admin/tools/class-acf-admin-tool-import.php:101
msgid "Error uploading file. Please try again"
msgstr "파일 업로드 중 오류가 발생하였습니다. 다시 시도하기 바랍니다"

#: includes/admin/tools/class-acf-admin-tool-import.php:51
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:28
#: includes/admin/tools/class-acf-admin-tool-import.php:50
msgid "Import Field Groups"
msgstr "필드 그룹 가져오기"

#: includes/admin/admin-field-groups.php:494
msgid "Sync"
msgstr "동기화하기"

#: includes/admin/admin-field-groups.php:942
msgid "Select %s"
msgstr "%s 선택하기"

#: includes/admin/admin-field-groups.php:527
#: includes/admin/admin-field-groups.php:556
#: includes/admin/views/field-group-field.php:66
msgid "Duplicate"
msgstr "복제하기"

#: includes/admin/admin-field-groups.php:527
msgid "Duplicate this item"
msgstr "이 항목 복제하기"

#: includes/admin/admin-field-groups.php:284
#: includes/admin/views/field-group-options.php:221
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
msgid "Description"
msgstr "설명"

#: includes/admin/admin-field-groups.php:491
#: includes/admin/admin-field-groups.php:829
msgid "Sync available"
msgstr "동기화를 사용할 수 있습니다"

#: includes/admin/admin-field-groups.php:754
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] ""
"필드 그룹을 동기화했습니다.\n"
"%s개의 필드 그룹을 동기화했습니다."

#: includes/admin/admin-field-groups.php:696
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] ""
"필드 그룹을 복사했습니다.\n"
"%s개의 필드 그룹을 복사했습니다."

#: includes/admin/admin-field-groups.php:118
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "활성 <span class=\"count\"> (%s) </span>"

#: includes/admin/admin-upgrade.php:237
msgid "Review sites & upgrade"
msgstr "사이트 검토하기 & 업그래이드하기"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:93
#: includes/admin/admin-upgrade.php:94 includes/admin/admin-upgrade.php:213
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "대이터배이스 업그래이드하기"

#: includes/admin/admin.php:49 includes/admin/views/field-group-options.php:140
msgid "Custom Fields"
msgstr "필드 꾸미기"

#: includes/admin/admin-field-group.php:718
msgid "Move Field"
msgstr "필드 이동하기"

#: includes/admin/admin-field-group.php:707
#: includes/admin/admin-field-group.php:711
msgid "Please select the destination for this field"
msgstr "이 필드의 목적지를 선택하기 바랍니다"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/admin-field-group.php:668
msgid "The %1$s field can now be found in the %2$s field group"
msgstr ""

#: includes/admin/admin-field-group.php:665
msgid "Move Complete."
msgstr "이동을 완료했습니다."

#: includes/admin/views/field-group-field.php:273
#: includes/admin/views/field-group-options.php:189
msgid "Active"
msgstr "활성화하기"

#: includes/admin/admin-field-group.php:325
msgid "Field Keys"
msgstr "필드 키"

#: includes/admin/admin-field-group.php:224
#: includes/admin/tools/class-acf-admin-tool-export.php:286
msgid "Settings"
msgstr "설정"

#: includes/admin/admin-field-groups.php:286
msgid "Location"
msgstr "위치"

#: includes/admin/admin-field-group.php:169 assets/build/js/acf-input.js:983
#: assets/build/js/acf-input.js:1075
msgid "Null"
msgstr "Null"

#: includes/acf-field-group-functions.php:846
#: includes/admin/admin-field-group.php:166
#: assets/build/js/acf-field-group.js:1158
#: assets/build/js/acf-field-group.js:1310
msgid "copy"
msgstr "복사하기"

#: includes/admin/admin-field-group.php:165
#: assets/build/js/acf-field-group.js:344
#: assets/build/js/acf-field-group.js:389
msgid "(this field)"
msgstr "(이 필드)"

#: includes/admin/admin-field-group.php:163 assets/build/js/acf-input.js:918
#: assets/build/js/acf-input.js:943 assets/build/js/acf-input.js:1002
#: assets/build/js/acf-input.js:1030
msgid "Checked"
msgstr "확인했습니다"

#: includes/admin/admin-field-group.php:160
#: assets/build/js/acf-field-group.js:1249
#: assets/build/js/acf-field-group.js:1408
msgid "Move Custom Field"
msgstr "사용자 필드 이동하기"

#: includes/admin/admin-field-group.php:159
#: assets/build/js/acf-field-group.js:370
#: assets/build/js/acf-field-group.js:415
msgid "No toggle fields available"
msgstr "토글 필드를 사용할 수 없습니다"

#: includes/admin/admin-field-group.php:157
msgid "Field group title is required"
msgstr "필드 그룹 제목이 필요합니다"

#: includes/admin/admin-field-group.php:156
#: assets/build/js/acf-field-group.js:1238
#: assets/build/js/acf-field-group.js:1394
msgid "This field cannot be moved until its changes have been saved"
msgstr "변경 사항을 저장할 때까지 이 필드를 이동할 수 없습니다"

#: includes/admin/admin-field-group.php:155
#: assets/build/js/acf-field-group.js:1049
#: assets/build/js/acf-field-group.js:1192
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "“field_”는 필드 이름의 시작에 사용할 수 없습니다"

#: includes/admin/admin-field-group.php:82
msgid "Field group draft updated."
msgstr "필드 그룹 초안을 업대이트했습니다."

#: includes/admin/admin-field-group.php:81
msgid "Field group scheduled for."
msgstr "예정한 필드 그룹입니다."

#: includes/admin/admin-field-group.php:80
msgid "Field group submitted."
msgstr "필드 그룹을 등록했습니다."

#: includes/admin/admin-field-group.php:79
msgid "Field group saved."
msgstr "필드 그룹을 저장했습니다."

#: includes/admin/admin-field-group.php:78
msgid "Field group published."
msgstr "필드 그룹을 발행했습니다."

#: includes/admin/admin-field-group.php:75
msgid "Field group deleted."
msgstr "필드 그룹을 삭제했습니다."

#: includes/admin/admin-field-group.php:73
#: includes/admin/admin-field-group.php:74
#: includes/admin/admin-field-group.php:76
msgid "Field group updated."
msgstr "필드 그룹을 업대이트했습니다."

#: includes/admin/admin-tools.php:119
#: includes/admin/views/html-admin-navigation.php:109
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "도구"

#: includes/locations/abstract-acf-location.php:106
msgid "is not equal to"
msgstr "같지 않습니다"

#: includes/locations/abstract-acf-location.php:105
msgid "is equal to"
msgstr "같습니다"

#: includes/locations.php:102
msgid "Forms"
msgstr "양식"

#: includes/locations.php:100 includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "패이지"

#: includes/locations.php:99 includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "글"

#: includes/fields.php:358
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:357
msgid "Relational"
msgstr "관계형"

#: includes/fields.php:356
msgid "Choice"
msgstr "선택하기"

#: includes/fields.php:354
msgid "Basic"
msgstr "기본"

#: includes/fields.php:313
msgid "Unknown"
msgstr "알 수 없습니다"

#: includes/fields.php:313
msgid "Field type does not exist"
msgstr "필드 유형이 존재하지 않습니다"

#: includes/forms/form-front.php:236
msgid "Spam Detected"
msgstr "스팸을 감지했습니다"

#: includes/forms/form-front.php:107
msgid "Post updated"
msgstr "글 업대이트"

#: includes/forms/form-front.php:106
msgid "Update"
msgstr "업대이트하기"

#: includes/forms/form-front.php:57
msgid "Validate Email"
msgstr "이매일 확인하기"

#: includes/fields.php:355 includes/forms/form-front.php:49
msgid "Content"
msgstr "콘텐츠"

#: includes/forms/form-front.php:40
msgid "Title"
msgstr "제목"

#: includes/assets.php:371 includes/forms/form-comment.php:160
#: assets/build/js/acf-input.js:7310 assets/build/js/acf-input.js:7876
msgid "Edit field group"
msgstr "필드 그룹 편집하기"

#: includes/admin/admin-field-group.php:181 assets/build/js/acf-input.js:1125
#: assets/build/js/acf-input.js:1230
msgid "Selection is less than"
msgstr "작게 선택했습니다"

#: includes/admin/admin-field-group.php:180 assets/build/js/acf-input.js:1106
#: assets/build/js/acf-input.js:1202
msgid "Selection is greater than"
msgstr "크게 선택했습니다"

#: includes/admin/admin-field-group.php:179 assets/build/js/acf-input.js:1075
#: assets/build/js/acf-input.js:1170
msgid "Value is less than"
msgstr "작은 값을 선택했습니다"

#: includes/admin/admin-field-group.php:178 assets/build/js/acf-input.js:1045
#: assets/build/js/acf-input.js:1139
msgid "Value is greater than"
msgstr "큰 값을 선택했습니다"

#: includes/admin/admin-field-group.php:177 assets/build/js/acf-input.js:888
#: assets/build/js/acf-input.js:960
msgid "Value contains"
msgstr "값을 포함합니다"

#: includes/admin/admin-field-group.php:176 assets/build/js/acf-input.js:862
#: assets/build/js/acf-input.js:926
msgid "Value matches pattern"
msgstr "값 일치 패턴"

#: includes/admin/admin-field-group.php:175 assets/build/js/acf-input.js:840
#: assets/build/js/acf-input.js:1023 assets/build/js/acf-input.js:903
#: assets/build/js/acf-input.js:1116
msgid "Value is not equal to"
msgstr "값이 같지 않습니다"

#: includes/admin/admin-field-group.php:174 assets/build/js/acf-input.js:810
#: assets/build/js/acf-input.js:964 assets/build/js/acf-input.js:864
#: assets/build/js/acf-input.js:1053
msgid "Value is equal to"
msgstr "값이 동일합니다"

#: includes/admin/admin-field-group.php:173 assets/build/js/acf-input.js:788
#: assets/build/js/acf-input.js:841
msgid "Has no value"
msgstr "값이 없습니다"

#: includes/admin/admin-field-group.php:172 assets/build/js/acf-input.js:758
#: assets/build/js/acf-input.js:783
msgid "Has any value"
msgstr "값이 있습니다"

#: includes/assets.php:352 assets/build/js/acf.js:1524
#: assets/build/js/acf.js:1604
msgid "Cancel"
msgstr "취소하기"

#: includes/assets.php:348 assets/build/js/acf.js:1698
#: assets/build/js/acf.js:1801
msgid "Are you sure?"
msgstr "확실한가요?"

#: includes/assets.php:368 assets/build/js/acf-input.js:9343
#: assets/build/js/acf-input.js:10172
msgid "%d fields require attention"
msgstr "%d 필드는 주의가 필요합니다"

#: includes/assets.php:367 assets/build/js/acf-input.js:9341
#: assets/build/js/acf-input.js:10168
msgid "1 field requires attention"
msgstr "1 필드 주의 필요"

#: includes/assets.php:366 includes/validation.php:287
#: includes/validation.php:297 assets/build/js/acf-input.js:9336
#: assets/build/js/acf-input.js:10163
msgid "Validation failed"
msgstr "검증을 실패했습니다"

#: includes/assets.php:365 assets/build/js/acf-input.js:9499
#: assets/build/js/acf-input.js:10346
msgid "Validation successful"
msgstr "검증을 성공했습니다"

#: includes/media.php:54 assets/build/js/acf-input.js:7138
#: assets/build/js/acf-input.js:7680
msgid "Restricted"
msgstr "제한했습니다"

#: includes/media.php:53 assets/build/js/acf-input.js:6953
#: assets/build/js/acf-input.js:7444
msgid "Collapse Details"
msgstr "세부정보 접기"

#: includes/media.php:52 assets/build/js/acf-input.js:6953
#: assets/build/js/acf-input.js:7441
msgid "Expand Details"
msgstr "세부정보 확장하기"

#: includes/media.php:51 assets/build/js/acf-input.js:6820
#: assets/build/js/acf-input.js:7289
msgid "Uploaded to this post"
msgstr "글에 업로드"

#: includes/media.php:50 assets/build/js/acf-input.js:6859
#: assets/build/js/acf-input.js:7328
msgctxt "verb"
msgid "Update"
msgstr "업대이트하기"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "편집하기"

#: includes/assets.php:362 assets/build/js/acf-input.js:9113
#: assets/build/js/acf-input.js:9934
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "패이지를 벗어나면 변경 한 내용이 손실 됩니다"

#: includes/api/api-helpers.php:3395
msgid "File type must be %s."
msgstr "파일 형식은 %s 여야 합니다."

#: includes/admin/admin-field-group.php:167
#: includes/admin/views/field-group-field-conditional-logic.php:59
#: includes/admin/views/field-group-field-conditional-logic.php:169
#: includes/admin/views/field-group-locations.php:36
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3391 assets/build/js/acf-field-group.js:492
#: assets/build/js/acf-field-group.js:1973
#: assets/build/js/acf-field-group.js:544
#: assets/build/js/acf-field-group.js:2210
msgid "or"
msgstr "또는"

#: includes/api/api-helpers.php:3364
msgid "File size must not exceed %s."
msgstr "파일 크기는 %s을(를) 초과하지 않아야 합니다."

#: includes/api/api-helpers.php:3359
msgid "File size must be at least %s."
msgstr "파일 크기는 %s 이상 이어야 합니다."

#: includes/api/api-helpers.php:3344
msgid "Image height must not exceed %dpx."
msgstr "이미지 높이는 %d픽셀 를 초과하지 않아야 합니다."

#: includes/api/api-helpers.php:3339
msgid "Image height must be at least %dpx."
msgstr "이미지 높이는 %d픽셀 이상 이어야 합니다."

#: includes/api/api-helpers.php:3325
msgid "Image width must not exceed %dpx."
msgstr "이미지 넓이는 %d픽셀을 초과하지 않아야 합니다."

#: includes/api/api-helpers.php:3320
msgid "Image width must be at least %dpx."
msgstr "이미지 넓이는 %d픽셀 이상이어야 합니다."

#: includes/api/api-helpers.php:1566 includes/api/api-term.php:147
msgid "(no title)"
msgstr "(제목이 없습니다)"

#: includes/api/api-helpers.php:861
msgid "Full Size"
msgstr "전체 크기"

#: includes/api/api-helpers.php:820
msgid "Large"
msgstr "대형"

#: includes/api/api-helpers.php:819
msgid "Medium"
msgstr "중간"

#: includes/api/api-helpers.php:818
msgid "Thumbnail"
msgstr "썸내일"

#: includes/acf-field-functions.php:846
#: includes/admin/admin-field-group.php:164
#: assets/build/js/acf-field-group.js:789
#: assets/build/js/acf-field-group.js:859
msgid "(no label)"
msgstr "(라벨이 없습니다)"

#: includes/fields/class-acf-field-textarea.php:142
msgid "Sets the textarea height"
msgstr "문자 영역의 높이를 설정하기"

#: includes/fields/class-acf-field-textarea.php:141
msgid "Rows"
msgstr "행"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "문자 영역"

#: includes/fields/class-acf-field-checkbox.php:447
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "모든 선택 항목을 전환하려면 추가 확인란을 추가하십시오"

#: includes/fields/class-acf-field-checkbox.php:409
msgid "Save 'custom' values to the field's choices"
msgstr "선택한 필드에 ‘사용자 정의’값 저장하기"

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Allow 'custom' values to be added"
msgstr "추가한 ‘사용자 정의’ 값 허용하기"

#: includes/fields/class-acf-field-checkbox.php:233
msgid "Add new choice"
msgstr "새로운 선택 추가하기"

#: includes/fields/class-acf-field-checkbox.php:170
msgid "Toggle All"
msgstr "모두 토글하기"

#: includes/fields/class-acf-field-page_link.php:477
msgid "Allow Archives URLs"
msgstr "보관소 URLs 허용하기"

#: includes/fields/class-acf-field-page_link.php:165
msgid "Archives"
msgstr "보관소"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "패이지 링크"

#: includes/fields/class-acf-field-taxonomy.php:945
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "추가하기"

#: includes/admin/views/field-group-fields.php:42
#: includes/fields/class-acf-field-taxonomy.php:910
msgid "Name"
msgstr "이름"

#: includes/fields/class-acf-field-taxonomy.php:894
msgid "%s added"
msgstr "%s을(를) 추가했습니다"

#: includes/fields/class-acf-field-taxonomy.php:858
msgid "%s already exists"
msgstr "%s이(가) 이미 존재합니다"

#: includes/fields/class-acf-field-taxonomy.php:846
msgid "User unable to add new %s"
msgstr "사용자가 새로운 %s를 추가 할 수 없습니다"

#: includes/fields/class-acf-field-taxonomy.php:756
msgid "Term ID"
msgstr "조건 ID"

#: includes/fields/class-acf-field-taxonomy.php:755
msgid "Term Object"
msgstr "조건 개체"

#: includes/fields/class-acf-field-taxonomy.php:740
msgid "Load value from posts terms"
msgstr "글 용어에서 값 로드하기"

#: includes/fields/class-acf-field-taxonomy.php:739
msgid "Load Terms"
msgstr "용어 로드하기"

#: includes/fields/class-acf-field-taxonomy.php:729
msgid "Connect selected terms to the post"
msgstr "글에 선택한 조건을 연결하기"

#: includes/fields/class-acf-field-taxonomy.php:728
msgid "Save Terms"
msgstr "조건 저장하기"

#: includes/fields/class-acf-field-taxonomy.php:718
msgid "Allow new terms to be created whilst editing"
msgstr "편집하는 동안 생성할 새로운 조건을 허용하기"

#: includes/fields/class-acf-field-taxonomy.php:717
msgid "Create Terms"
msgstr "조건 만들기"

#: includes/fields/class-acf-field-taxonomy.php:776
msgid "Radio Buttons"
msgstr "라디오 버튼"

#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Single Value"
msgstr "단일 값"

#: includes/fields/class-acf-field-taxonomy.php:773
msgid "Multi Select"
msgstr "다중 선택"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Checkbox"
msgstr "확인상자"

#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Multiple Values"
msgstr "복수 값"

#: includes/fields/class-acf-field-taxonomy.php:766
msgid "Select the appearance of this field"
msgstr "이 필드의 외관 선택하기"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Appearance"
msgstr "외관"

#: includes/fields/class-acf-field-taxonomy.php:707
msgid "Select the taxonomy to be displayed"
msgstr "보일 할 분류를 선택하기"

#: includes/fields/class-acf-field-taxonomy.php:668
msgctxt "No Terms"
msgid "No %s"
msgstr ""

#: includes/fields/class-acf-field-number.php:263
msgid "Value must be equal to or lower than %d"
msgstr "값은 %d와(과) 같거나 작아야 합니다."

#: includes/fields/class-acf-field-number.php:256
msgid "Value must be equal to or higher than %d"
msgstr "값은 %d와(과) 같거나 큰 값이어야만 합니다"

#: includes/fields/class-acf-field-number.php:241
msgid "Value must be a number"
msgstr "값은 숫자여야만 합니다"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "번호"

#: includes/fields/class-acf-field-radio.php:261
msgid "Save 'other' values to the field's choices"
msgstr "선택한 필드에 ‘다른’ 값 저장하기"

#: includes/fields/class-acf-field-radio.php:250
msgid "Add 'other' choice to allow for custom values"
msgstr "사용자 정의 값을 허용하는 ‘기타’ 선택 사항 추가하기"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "라디오 버튼"

#: includes/fields/class-acf-field-accordion.php:104
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"중지한 이전 아코디언의 엔드 포인트를 정의합니다. 이 아코디언은 보이지 않을 것"
"입니다."

#: includes/fields/class-acf-field-accordion.php:93
msgid "Allow this accordion to open without closing others."
msgstr "다른 것을 닫지 않고 열 수 있도록 아코디언을 허용합니다."

#: includes/fields/class-acf-field-accordion.php:92
msgid "Multi-expand"
msgstr "다중 확장"

#: includes/fields/class-acf-field-accordion.php:82
msgid "Display this accordion as open on page load."
msgstr "이 아코디언은 패이지 로드시 열린 것으로 보입니다."

#: includes/fields/class-acf-field-accordion.php:81
msgid "Open"
msgstr "열기"

#: includes/fields/class-acf-field-accordion.php:25
msgid "Accordion"
msgstr "아코디언"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-file.php:276
msgid "Restrict which files can be uploaded"
msgstr "업로드 할 수 있는 파일 제한하기"

#: includes/fields/class-acf-field-file.php:217
msgid "File ID"
msgstr "파일 ID"

#: includes/fields/class-acf-field-file.php:216
msgid "File URL"
msgstr "파일 URL"

#: includes/fields/class-acf-field-file.php:215
msgid "File Array"
msgstr "파일 배열"

#: includes/fields/class-acf-field-file.php:183
msgid "Add File"
msgstr "파일 추가하기"

#: includes/admin/tools/class-acf-admin-tool-import.php:94
#: includes/fields/class-acf-field-file.php:183
msgid "No file selected"
msgstr "선택한 파일이 없습니다"

#: includes/fields/class-acf-field-file.php:147
msgid "File name"
msgstr "파일 이름"

#: includes/fields/class-acf-field-file.php:60
#: assets/build/js/acf-input.js:2466 assets/build/js/acf-input.js:2612
msgid "Update File"
msgstr "파일 업대이트하기"

#: includes/fields/class-acf-field-file.php:59
#: assets/build/js/acf-input.js:2465 assets/build/js/acf-input.js:2611
msgid "Edit File"
msgstr "파일 편집하기"

#: includes/admin/tools/class-acf-admin-tool-import.php:59
#: includes/fields/class-acf-field-file.php:58
#: assets/build/js/acf-input.js:2439 assets/build/js/acf-input.js:2584
msgid "Select File"
msgstr "파일 선택하기"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "파일"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "비밀번호"

#: includes/fields/class-acf-field-select.php:391
msgid "Specify the value returned"
msgstr "반환할 값 지정하기"

#: includes/fields/class-acf-field-select.php:460
msgid "Use AJAX to lazy load choices?"
msgstr "지연 선택에 AJAX를 사용할까요?"

#: includes/fields/class-acf-field-checkbox.php:358
#: includes/fields/class-acf-field-select.php:380
msgid "Enter each default value on a new line"
msgstr "새로운 줄에 기본 값 입력하기"

#: includes/fields/class-acf-field-select.php:255 includes/media.php:48
#: assets/build/js/acf-input.js:6718 assets/build/js/acf-input.js:7174
msgctxt "verb"
msgid "Select"
msgstr "선택하기"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "로드를 실패했습니다"

#: includes/fields/class-acf-field-select.php:117
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "검색하기&hellip;"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "더 많은 결과 부르기&hellip;"

#: includes/fields/class-acf-field-select.php:115
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "%d 항목만 선택할 수 있습니다"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "1 항목만 선택할 수 있습니다"

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "%d글자를 지우기 바랍니다"

#: includes/fields/class-acf-field-select.php:112
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "1글자를 지우기 바랍니다"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "%d 또는 이상의 글자를 입력하기 바랍니다"

#: includes/fields/class-acf-field-select.php:110
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "1 또는 이상의 글자를 입력하기 바랍니다"

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "일치하는 항목을 찾을 수 없습니다"

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d개의 결과를 사용할 수 있습니다. 탐색하려면 위와 아래 방향키를 사용하세요."

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "하나의 결과를 사용가능할 수 있습니다. 선택하려면 엔터를 누르세요."

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:777
msgctxt "noun"
msgid "Select"
msgstr "선택하기"

#: includes/fields/class-acf-field-user.php:74
msgid "User ID"
msgstr "사용자 ID"

#: includes/fields/class-acf-field-user.php:73
msgid "User Object"
msgstr "사용자 개체"

#: includes/fields/class-acf-field-user.php:72
msgid "User Array"
msgstr "사용자 배열"

#: includes/fields/class-acf-field-user.php:60
msgid "All user roles"
msgstr "모든 사용자 역할"

#: includes/fields/class-acf-field-user.php:52
msgid "Filter by role"
msgstr "역할로 필터하기"

#: includes/fields/class-acf-field-user.php:20 includes/locations.php:101
msgid "User"
msgstr "사용자"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "구분자"

#: includes/fields/class-acf-field-color_picker.php:73
msgid "Select Color"
msgstr "색상 선택하기"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Default"
msgstr "기본"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Clear"
msgstr "초기화"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "색상 선택 도구"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "후"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "오후"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "전"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "오전"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "선택하기"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "완료"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "지금"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "시간대"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "마이크로초"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "밀리초"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "초"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "분"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "시간"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "시간"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "시간 선택하기"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "일시 선택 도구"

#: includes/fields/class-acf-field-accordion.php:103
msgid "Endpoint"
msgstr "엔드포인트"

#: includes/admin/views/field-group-options.php:95
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "왼쪽에 정렬합니다"

#: includes/admin/views/field-group-options.php:94
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "상단에 정렬합니다"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "배치"

#: includes/fields/class-acf-field-tab.php:26
msgid "Tab"
msgstr "탭"

#: includes/fields/class-acf-field-url.php:159
msgid "Value must be a valid URL"
msgstr "값은 유효한 URL 이어야 합니다"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "URL"

#: includes/fields/class-acf-field-link.php:174
msgid "Link URL"
msgstr "링크 URL"

#: includes/fields/class-acf-field-link.php:173
msgid "Link Array"
msgstr "링크 배열"

#: includes/fields/class-acf-field-link.php:142
msgid "Opens in a new window/tab"
msgstr "새로운 창/탭에서 열립니다"

#: includes/fields/class-acf-field-link.php:137
msgid "Select Link"
msgstr "링크 선택하기"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "링크"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "이매일"

#: includes/fields/class-acf-field-number.php:185
#: includes/fields/class-acf-field-range.php:214
msgid "Step Size"
msgstr "크기 단계"

#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-range.php:192
msgid "Maximum Value"
msgstr "최대 값"

#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-range.php:181
msgid "Minimum Value"
msgstr "최소 값"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "범위"

#: includes/fields/class-acf-field-button-group.php:172
#: includes/fields/class-acf-field-checkbox.php:375
#: includes/fields/class-acf-field-radio.php:217
#: includes/fields/class-acf-field-select.php:398
msgid "Both (Array)"
msgstr "둘 다(배열)"

#: includes/admin/views/field-group-fields.php:41
#: includes/fields/class-acf-field-button-group.php:171
#: includes/fields/class-acf-field-checkbox.php:374
#: includes/fields/class-acf-field-radio.php:216
#: includes/fields/class-acf-field-select.php:397
msgid "Label"
msgstr "라벨"

#: includes/fields/class-acf-field-button-group.php:170
#: includes/fields/class-acf-field-checkbox.php:373
#: includes/fields/class-acf-field-radio.php:215
#: includes/fields/class-acf-field-select.php:396
msgid "Value"
msgstr "값"

#: includes/fields/class-acf-field-button-group.php:219
#: includes/fields/class-acf-field-checkbox.php:437
#: includes/fields/class-acf-field-radio.php:289
msgid "Vertical"
msgstr "수직"

#: includes/fields/class-acf-field-button-group.php:218
#: includes/fields/class-acf-field-checkbox.php:438
#: includes/fields/class-acf-field-radio.php:290
msgid "Horizontal"
msgstr "수평"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "red : Red"
msgstr "적색 : 적색"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "For more control, you may specify both a value and label like this:"
msgstr "더 많은 제어를 위해 다음과 같이 값과 레이블을 모두 지정할 수 있습니다:"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "Enter each choice on a new line."
msgstr "새 행에 각 선택을 입력합니다."

#: includes/fields/class-acf-field-button-group.php:144
#: includes/fields/class-acf-field-checkbox.php:347
#: includes/fields/class-acf-field-radio.php:189
#: includes/fields/class-acf-field-select.php:368
msgid "Choices"
msgstr "선택하기"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "버튼 그룹"

#: includes/fields/class-acf-field-page_link.php:488
#: includes/fields/class-acf-field-post_object.php:404
#: includes/fields/class-acf-field-select.php:406
#: includes/fields/class-acf-field-user.php:83
msgid "Select multiple values?"
msgstr "여러 값을 선택할까요?"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-page_link.php:509
#: includes/fields/class-acf-field-post_object.php:426
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:428
#: includes/fields/class-acf-field-taxonomy.php:786
#: includes/fields/class-acf-field-user.php:104
msgid "Allow Null?"
msgstr "Null을 허용할까요?"

#: includes/fields/class-acf-field-page_link.php:249
#: includes/fields/class-acf-field-post_object.php:250
#: includes/fields/class-acf-field-taxonomy.php:932
msgid "Parent"
msgstr "부모"

#: includes/fields/class-acf-field-wysiwyg.php:394
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE는 필드를 누를 때까지 초기화하지 않을 것입니다"

#: includes/fields/class-acf-field-wysiwyg.php:393
msgid "Delay initialization?"
msgstr "초기화를 지연할까요?"

#: includes/fields/class-acf-field-wysiwyg.php:382
msgid "Show Media Upload Buttons?"
msgstr "미디어 업로드 버튼을 보일까요?"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Toolbar"
msgstr "도구모음"

#: includes/fields/class-acf-field-wysiwyg.php:358
msgid "Text Only"
msgstr "텍스트 만"

#: includes/fields/class-acf-field-wysiwyg.php:357
msgid "Visual Only"
msgstr "비주얼 만"

#: includes/fields/class-acf-field-wysiwyg.php:356
msgid "Visual & Text"
msgstr "비주얼 & 텍스트"

#: includes/fields/class-acf-field-wysiwyg.php:351
msgid "Tabs"
msgstr "탭"

#: includes/fields/class-acf-field-wysiwyg.php:289
msgid "Click to initialize TinyMCE"
msgstr "TinyMCS 초기화하려면 누릅니다"

#: includes/fields/class-acf-field-wysiwyg.php:283
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "텍스트"

#: includes/fields/class-acf-field-wysiwyg.php:282
msgid "Visual"
msgstr "비주얼"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "위지윅 편집기"

#: includes/fields/class-acf-field-text.php:180
#: includes/fields/class-acf-field-textarea.php:233
msgid "Value must not exceed %d characters"
msgstr "%d자를 초과하지 않아야 합니다"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "무제한으로 비워두기"

#: includes/fields/class-acf-field-text.php:114
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "글자 수 제한"

#: includes/fields/class-acf-field-email.php:155
#: includes/fields/class-acf-field-number.php:206
#: includes/fields/class-acf-field-password.php:102
#: includes/fields/class-acf-field-range.php:236
#: includes/fields/class-acf-field-text.php:155
msgid "Appears after the input"
msgstr "입력란 뒤에 보입니다."

#: includes/fields/class-acf-field-email.php:154
#: includes/fields/class-acf-field-number.php:205
#: includes/fields/class-acf-field-password.php:101
#: includes/fields/class-acf-field-range.php:235
#: includes/fields/class-acf-field-text.php:154
msgid "Append"
msgstr "추가하기"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:196
#: includes/fields/class-acf-field-password.php:92
#: includes/fields/class-acf-field-range.php:226
#: includes/fields/class-acf-field-text.php:145
msgid "Appears before the input"
msgstr "입력란 전에 보입니다"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:195
#: includes/fields/class-acf-field-password.php:91
#: includes/fields/class-acf-field-range.php:225
#: includes/fields/class-acf-field-text.php:144
msgid "Prepend"
msgstr "머리말"

#: includes/fields/class-acf-field-email.php:135
#: includes/fields/class-acf-field-number.php:176
#: includes/fields/class-acf-field-password.php:82
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:153
#: includes/fields/class-acf-field-url.php:119
msgid "Appears within the input"
msgstr "입력란 내에 보입니다"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:175
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-text.php:134
#: includes/fields/class-acf-field-textarea.php:152
#: includes/fields/class-acf-field-url.php:118
msgid "Placeholder Text"
msgstr "텍스트 자리 표시자"

#: includes/fields/class-acf-field-button-group.php:155
#: includes/fields/class-acf-field-email.php:115
#: includes/fields/class-acf-field-number.php:126
#: includes/fields/class-acf-field-radio.php:200
#: includes/fields/class-acf-field-range.php:162
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:101
#: includes/fields/class-acf-field-url.php:99
#: includes/fields/class-acf-field-wysiwyg.php:316
msgid "Appears when creating a new post"
msgstr "새 글을 만들 때 보입니다."

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "텍스트"

#: includes/fields/class-acf-field-relationship.php:760
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] ""

#: includes/fields/class-acf-field-post_object.php:395
#: includes/fields/class-acf-field-relationship.php:622
msgid "Post ID"
msgstr "글 ID"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:394
#: includes/fields/class-acf-field-relationship.php:621
msgid "Post Object"
msgstr "글 개체"

#: includes/fields/class-acf-field-relationship.php:654
msgid "Maximum posts"
msgstr "최대 글"

#: includes/fields/class-acf-field-relationship.php:644
msgid "Minimum posts"
msgstr "최소 글"

#: includes/admin/views/field-group-options.php:148
#: includes/fields/class-acf-field-relationship.php:679
msgid "Featured Image"
msgstr "특성 이미지"

#: includes/fields/class-acf-field-relationship.php:675
msgid "Selected elements will be displayed in each result"
msgstr "선택한 요소를 각 결과에 표시합니다"

#: includes/fields/class-acf-field-relationship.php:674
msgid "Elements"
msgstr "요소"

#: includes/fields/class-acf-field-relationship.php:608
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:706
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "분류"

#: includes/fields/class-acf-field-relationship.php:607
#: includes/locations/class-acf-location-post-type.php:22
msgid "Post Type"
msgstr "글 유형"

#: includes/fields/class-acf-field-relationship.php:601
msgid "Filters"
msgstr "필터"

#: includes/fields/class-acf-field-page_link.php:470
#: includes/fields/class-acf-field-post_object.php:382
#: includes/fields/class-acf-field-relationship.php:594
msgid "All taxonomies"
msgstr "모든 분류"

#: includes/fields/class-acf-field-page_link.php:462
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:586
msgid "Filter by Taxonomy"
msgstr "분류로 필터하기"

#: includes/fields/class-acf-field-page_link.php:455
#: includes/fields/class-acf-field-post_object.php:367
#: includes/fields/class-acf-field-relationship.php:579
msgid "All post types"
msgstr "모든 글 유형"

#: includes/fields/class-acf-field-page_link.php:447
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:571
msgid "Filter by Post Type"
msgstr "글 유형으로 필터하기"

#: includes/fields/class-acf-field-relationship.php:469
msgid "Search..."
msgstr "검색하기..."

#: includes/fields/class-acf-field-relationship.php:399
msgid "Select taxonomy"
msgstr "분류 선택하기"

#: includes/fields/class-acf-field-relationship.php:390
msgid "Select post type"
msgstr "글 유형 선택하기"

#: includes/fields/class-acf-field-relationship.php:65
#: assets/build/js/acf-input.js:3917 assets/build/js/acf-input.js:4195
msgid "No matches found"
msgstr "일치하는 항목을 찾을 수 없습니다"

#: includes/fields/class-acf-field-relationship.php:64
#: assets/build/js/acf-input.js:3900 assets/build/js/acf-input.js:4174
msgid "Loading"
msgstr "로드중"

#: includes/fields/class-acf-field-relationship.php:63
#: assets/build/js/acf-input.js:3809 assets/build/js/acf-input.js:4070
msgid "Maximum values reached ( {max} values )"
msgstr "최대 값에 도달했습니다 ({max} 값)"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "관계"

#: includes/fields/class-acf-field-file.php:288
#: includes/fields/class-acf-field-image.php:314
msgid "Comma separated list. Leave blank for all types"
msgstr "쉼표로 구분하는 목록입니다. 모든 유형을 빈 값으로 처리했습니다."

#: includes/fields/class-acf-field-file.php:287
#: includes/fields/class-acf-field-image.php:313
msgid "Allowed file types"
msgstr "허용한 파일 형식"

#: includes/fields/class-acf-field-file.php:275
#: includes/fields/class-acf-field-image.php:277
msgid "Maximum"
msgstr "최대"

#: includes/fields/class-acf-field-file.php:151
#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-file.php:279
#: includes/fields/class-acf-field-image.php:268
#: includes/fields/class-acf-field-image.php:304
msgid "File size"
msgstr "파일 크기"

#: includes/fields/class-acf-field-image.php:242
#: includes/fields/class-acf-field-image.php:278
msgid "Restrict which images can be uploaded"
msgstr "업로드 할 수 있는 이미지 제한하기"

#: includes/fields/class-acf-field-file.php:263
#: includes/fields/class-acf-field-image.php:241
msgid "Minimum"
msgstr "최소"

#: includes/fields/class-acf-field-file.php:232
#: includes/fields/class-acf-field-image.php:207
msgid "Uploaded to post"
msgstr "글을 업로드했습니다"

#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:206
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "모두"

#: includes/fields/class-acf-field-file.php:226
#: includes/fields/class-acf-field-image.php:201
msgid "Limit the media library choice"
msgstr "미디어 라이브러리 선택 제한하기"

#: includes/fields/class-acf-field-file.php:225
#: includes/fields/class-acf-field-image.php:200
msgid "Library"
msgstr "라이브러리"

#: includes/fields/class-acf-field-image.php:333
msgid "Preview Size"
msgstr "미리보기 크기"

#: includes/fields/class-acf-field-image.php:192
msgid "Image ID"
msgstr "이미지 ID"

#: includes/fields/class-acf-field-image.php:191
msgid "Image URL"
msgstr "이미지 URL"

#: includes/fields/class-acf-field-image.php:190
msgid "Image Array"
msgstr "이미지 배열"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:368
#: includes/fields/class-acf-field-file.php:210
#: includes/fields/class-acf-field-link.php:168
#: includes/fields/class-acf-field-radio.php:210
msgid "Specify the returned value on front end"
msgstr "프론트 엔드에 반환 값 지정하기"

#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:367
#: includes/fields/class-acf-field-file.php:209
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-taxonomy.php:750
msgid "Return Value"
msgstr "값 반환하기"

#: includes/fields/class-acf-field-image.php:159
msgid "Add Image"
msgstr "이미지 추가하기"

#: includes/fields/class-acf-field-image.php:159
msgid "No image selected"
msgstr "선택한 이미지가 없습니다"

#: includes/assets.php:351 includes/fields/class-acf-field-file.php:159
#: includes/fields/class-acf-field-image.php:139
#: includes/fields/class-acf-field-link.php:142 assets/build/js/acf.js:1523
#: assets/build/js/acf.js:1603
msgid "Remove"
msgstr "제거하기"

#: includes/admin/views/field-group-field.php:65
#: includes/fields/class-acf-field-file.php:157
#: includes/fields/class-acf-field-image.php:137
#: includes/fields/class-acf-field-link.php:142
msgid "Edit"
msgstr "편집하기"

#: includes/fields/class-acf-field-image.php:67 includes/media.php:55
#: assets/build/js/acf-input.js:6765 assets/build/js/acf-input.js:7228
msgid "All images"
msgstr "모든 이미지"

#: includes/fields/class-acf-field-image.php:66
#: assets/build/js/acf-input.js:3173 assets/build/js/acf-input.js:3386
msgid "Update Image"
msgstr "이미지 업대이트하기"

#: includes/fields/class-acf-field-image.php:65
#: assets/build/js/acf-input.js:3172 assets/build/js/acf-input.js:3385
msgid "Edit Image"
msgstr "이미지 편집하기"

#: includes/fields/class-acf-field-image.php:64
#: assets/build/js/acf-input.js:3148 assets/build/js/acf-input.js:3360
msgid "Select Image"
msgstr "이미지 선택하기"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "이미지"

#: includes/fields/class-acf-field-message.php:123
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "렌더링 대신 보이는 텍스트로 HTML 마크 업을 허용하기"

#: includes/fields/class-acf-field-message.php:122
msgid "Escape HTML"
msgstr "HTML 이탈하기"

#: includes/fields/class-acf-field-message.php:114
#: includes/fields/class-acf-field-textarea.php:169
msgid "No Formatting"
msgstr "서식이 없습니다"

#: includes/fields/class-acf-field-message.php:113
#: includes/fields/class-acf-field-textarea.php:168
msgid "Automatically add &lt;br&gt;"
msgstr "&lt;br&gt; 자동 추가하기"

#: includes/fields/class-acf-field-message.php:112
#: includes/fields/class-acf-field-textarea.php:167
msgid "Automatically add paragraphs"
msgstr "단락 자동 추가하기"

#: includes/fields/class-acf-field-message.php:108
#: includes/fields/class-acf-field-textarea.php:163
msgid "Controls how new lines are rendered"
msgstr "새 줄을 렌더링하는 방법을 제어합니다"

#: includes/fields/class-acf-field-message.php:107
#: includes/fields/class-acf-field-textarea.php:162
msgid "New Lines"
msgstr "새로운 줄"

#: includes/fields/class-acf-field-date_picker.php:229
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "한 주의 시작일"

#: includes/fields/class-acf-field-date_picker.php:198
msgid "The format used when saving a value"
msgstr "값을 저장할 때 사용하는 형식입니다"

#: includes/fields/class-acf-field-date_picker.php:197
msgid "Save Format"
msgstr "형식 저장하기"

#: includes/fields/class-acf-field-date_picker.php:64
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "주"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "이전"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "다음"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "오늘"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "완료"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "날짜 선택 도구"

#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:281
#: includes/fields/class-acf-field-oembed.php:265
msgid "Width"
msgstr "넓이"

#: includes/fields/class-acf-field-oembed.php:262
#: includes/fields/class-acf-field-oembed.php:274
msgid "Embed Size"
msgstr "임베드 크기"

#: includes/fields/class-acf-field-oembed.php:219
msgid "Enter URL"
msgstr "입력 URL"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:181
msgid "Text shown when inactive"
msgstr "비활성 텍스트 표시"

#: includes/fields/class-acf-field-true_false.php:180
msgid "Off Text"
msgstr "텍스트 끄기"

#: includes/fields/class-acf-field-true_false.php:165
msgid "Text shown when active"
msgstr "활성일 때 보이는 텍스트"

#: includes/fields/class-acf-field-true_false.php:164
msgid "On Text"
msgstr "텍스트 켜기"

#: includes/fields/class-acf-field-select.php:449
#: includes/fields/class-acf-field-true_false.php:196
msgid "Stylized UI"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:154
#: includes/fields/class-acf-field-checkbox.php:357
#: includes/fields/class-acf-field-color_picker.php:155
#: includes/fields/class-acf-field-email.php:114
#: includes/fields/class-acf-field-number.php:125
#: includes/fields/class-acf-field-radio.php:199
#: includes/fields/class-acf-field-range.php:161
#: includes/fields/class-acf-field-select.php:379
#: includes/fields/class-acf-field-text.php:94
#: includes/fields/class-acf-field-textarea.php:100
#: includes/fields/class-acf-field-true_false.php:144
#: includes/fields/class-acf-field-url.php:98
#: includes/fields/class-acf-field-wysiwyg.php:315
msgid "Default Value"
msgstr "기본 값"

#: includes/fields/class-acf-field-true_false.php:135
msgid "Displays text alongside the checkbox"
msgstr "확인란과 함께 텍스트를 표시합니다"

#: includes/fields/class-acf-field-message.php:26
#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-true_false.php:134
msgid "Message"
msgstr "안내문"

#: includes/assets.php:350 includes/fields/class-acf-field-true_false.php:83
#: includes/fields/class-acf-field-true_false.php:184
#: assets/build/js/acf.js:1700 assets/build/js/acf.js:1803
msgid "No"
msgstr "아니오"

#: includes/assets.php:349 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:168
#: assets/build/js/acf.js:1699 assets/build/js/acf.js:1802
msgid "Yes"
msgstr "예"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "참 / 거짓"

#: includes/fields/class-acf-field-group.php:471
msgid "Row"
msgstr "열"

#: includes/fields/class-acf-field-group.php:470
msgid "Table"
msgstr "태이블"

#: includes/fields/class-acf-field-group.php:469
msgid "Block"
msgstr "블록"

#: includes/fields/class-acf-field-group.php:464
msgid "Specify the style used to render the selected fields"
msgstr "선택한 필드를 렌더링하는 데 사용하는 스타일 지정하기"

#: includes/fields.php:359 includes/fields/class-acf-field-button-group.php:212
#: includes/fields/class-acf-field-checkbox.php:431
#: includes/fields/class-acf-field-group.php:463
#: includes/fields/class-acf-field-radio.php:283
msgid "Layout"
msgstr "래이아웃"

#: includes/fields/class-acf-field-group.php:447
msgid "Sub Fields"
msgstr "하위 필드"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "그룹"

#: includes/fields/class-acf-field-google-map.php:232
msgid "Customize the map height"
msgstr "사용자 정의 지도 높이"

#: includes/fields/class-acf-field-google-map.php:231
#: includes/fields/class-acf-field-image.php:256
#: includes/fields/class-acf-field-image.php:292
#: includes/fields/class-acf-field-oembed.php:277
msgid "Height"
msgstr "높이"

#: includes/fields/class-acf-field-google-map.php:220
msgid "Set the initial zoom level"
msgstr "초기화 줌 레벨 설정하기"

#: includes/fields/class-acf-field-google-map.php:219
msgid "Zoom"
msgstr "줌"

#: includes/fields/class-acf-field-google-map.php:193
#: includes/fields/class-acf-field-google-map.php:206
msgid "Center the initial map"
msgstr "초기화 지도 중앙"

#: includes/fields/class-acf-field-google-map.php:192
#: includes/fields/class-acf-field-google-map.php:205
msgid "Center"
msgstr "중앙"

#: includes/fields/class-acf-field-google-map.php:160
msgid "Search for address..."
msgstr "주소를 검색하기..."

#: includes/fields/class-acf-field-google-map.php:157
msgid "Find current location"
msgstr "현재 위치 찾기"

#: includes/fields/class-acf-field-google-map.php:156
msgid "Clear location"
msgstr "위치 지우기"

#: includes/fields/class-acf-field-google-map.php:155
#: includes/fields/class-acf-field-relationship.php:606
msgid "Search"
msgstr "검색하기"

#: includes/fields/class-acf-field-google-map.php:60
#: assets/build/js/acf-input.js:2832 assets/build/js/acf-input.js:3013
msgid "Sorry, this browser does not support geolocation"
msgstr "죄송합니다. 이 브라우저는 위치 정보를 지원하지 않습니다"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "구글 지도"

#: includes/fields/class-acf-field-date_picker.php:209
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:129
msgid "The format returned via template functions"
msgstr "템플릿 함수를 통해 반환되는 형식"

#: includes/fields/class-acf-field-color_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:197
#: includes/fields/class-acf-field-image.php:184
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:616
#: includes/fields/class-acf-field-select.php:390
#: includes/fields/class-acf-field-time_picker.php:128
#: includes/fields/class-acf-field-user.php:67
msgid "Return Format"
msgstr "반환 형식"

#: includes/fields/class-acf-field-date_picker.php:187
#: includes/fields/class-acf-field-date_picker.php:218
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-date_time_picker.php:207
#: includes/fields/class-acf-field-time_picker.php:120
#: includes/fields/class-acf-field-time_picker.php:136
msgid "Custom:"
msgstr "사용자 정의:"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-time_picker.php:113
msgid "The format displayed when editing a post"
msgstr "글을 편집 할 때 보이는 형식"

#: includes/fields/class-acf-field-date_picker.php:178
#: includes/fields/class-acf-field-date_time_picker.php:179
#: includes/fields/class-acf-field-time_picker.php:112
msgid "Display Format"
msgstr "보이는 형식"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "시간 선택 도구"

#. translators: counts for inactive field groups
#: acf.php:454
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] ""

#: acf.php:413
msgid "No Fields found in Trash"
msgstr "휴지통에 필드가 없습니다"

#: acf.php:412
msgid "No Fields found"
msgstr "필드를 찾을 수 없습니다"

#: acf.php:411
msgid "Search Fields"
msgstr "필드 검색하기"

#: acf.php:410
msgid "View Field"
msgstr "필드 보기"

#: acf.php:409 includes/admin/views/field-group-fields.php:104
msgid "New Field"
msgstr "새로운 필드"

#: acf.php:408
msgid "Edit Field"
msgstr "필드 편집하기"

#: acf.php:407
msgid "Add New Field"
msgstr "새로운 필드 추가하기"

#: acf.php:405
msgid "Field"
msgstr "필드"

#: acf.php:404 includes/admin/admin-field-group.php:220
#: includes/admin/admin-field-groups.php:287
#: includes/admin/views/field-group-fields.php:21
msgid "Fields"
msgstr "필드"

#: acf.php:379
msgid "No Field Groups found in Trash"
msgstr "휴지통에서 필드 그룹을 찾을 수 없습니다"

#: acf.php:378
msgid "No Field Groups found"
msgstr "필드 그룹을 찾을 수 없습니다"

#: acf.php:377
msgid "Search Field Groups"
msgstr "필드 그룹 검색하기"

#: acf.php:376
msgid "View Field Group"
msgstr "필드 그룹 보기"

#: acf.php:375
msgid "New Field Group"
msgstr "새 필드 그룹"

#: acf.php:374
msgid "Edit Field Group"
msgstr "필드 그룹 편집하기"

#: acf.php:373
msgid "Add New Field Group"
msgstr "새 필드 그룹 추가하기"

#: acf.php:372 acf.php:406 includes/admin/admin.php:51
msgid "Add New"
msgstr "새로 추가하기"

#: acf.php:371
msgid "Field Group"
msgstr "필드 그룹"

#: acf.php:370 includes/admin/admin.php:50
msgid "Field Groups"
msgstr "필드 그룹"

#. Description of the plugin
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "강력하고, 전문적이며 직관적인 필드로 워드프레스를 사용자 정의합니다."

#. Plugin URI of the plugin
#. Author URI of the plugin
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com/"

#. Plugin Name of the plugin
#: acf.php:91
msgid "Advanced Custom Fields"
msgstr "고급 사용자 정의 필드"
