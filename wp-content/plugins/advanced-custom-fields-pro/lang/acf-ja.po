# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2022-12-13T15:07:38+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: ja\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/api/api-template.php:867
msgid "[ACF shortcode value disabled for preview]"
msgstr ""

#: includes/admin/admin-field-group.php:672
msgid "Close Modal"
msgstr ""

#: includes/admin/admin-field-group.php:162
#: assets/build/js/acf-field-group.js:1304
#: assets/build/js/acf-field-group.js:1468
msgid "Field moved to other group"
msgstr ""

#: includes/admin/admin-field-group.php:161 assets/build/js/acf.js:1397
#: assets/build/js/acf.js:1463
msgid "Close modal"
msgstr ""

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr ""

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr ""

#: includes/fields/class-acf-field-select.php:450
#: includes/fields/class-acf-field-true_false.php:197
msgid "Use a stylized checkbox using select2"
msgstr ""

#: includes/fields/class-acf-field-radio.php:257
msgid "Save Other Choice"
msgstr ""

#: includes/fields/class-acf-field-radio.php:246
msgid "Allow Other Choice"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:446
msgid "Add Toggle All"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:405
msgid "Save Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:394
msgid "Allow Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:144
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""

#: pro/admin/admin-updates.php:122,
#: pro/admin/views/html-settings-updates.php:12
msgid "Updates"
msgstr "アップデート"

#: includes/admin/views/html-admin-navigation.php:79
msgid "Advanced Custom Fields logo"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:22
msgid "Save Changes"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:16
msgid "Field Group Title"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:3
msgid "Add title"
msgstr ""

#. translators: %s url to getting started guide
#: includes/admin/views/field-groups-empty.php:20
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""

#: includes/admin/views/field-groups-empty.php:15
msgid "Add Field Group"
msgstr ""

#. translators: %s url to creating a field group page
#: includes/admin/views/field-groups-empty.php:10
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""

#: includes/admin/views/field-groups-empty.php:5
msgid "Add Your First Field Group"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:16
msgid "Upgrade Now"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:11
msgid "Options Pages"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:10
msgid "ACF Blocks"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:4
#: includes/admin/views/html-admin-navigation.php:97
msgid "Unlock Extra Features with ACF PRO"
msgstr ""

#: includes/admin/views/field-group-options.php:242
msgid "Delete Field Group"
msgstr ""

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/field-group-options.php:236
msgid "Created on %1$s at %2$s"
msgstr ""

#: includes/admin/views/field-group-options.php:179
msgid "Group Settings"
msgstr ""

#: includes/admin/views/field-group-options.php:28
msgid "Location Rules"
msgstr ""

#. translators: %s url to field types list
#: includes/admin/views/field-group-fields.php:61
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""

#: includes/admin/views/field-group-fields.php:54
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""

#: includes/admin/views/field-group-fields.php:53
msgid "Add Your First Field"
msgstr ""

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/field-group-fields.php:32
msgid "#"
msgstr ""

#: includes/admin/views/field-group-fields.php:22
#: includes/admin/views/field-group-fields.php:56
#: includes/admin/views/field-group-fields.php:92
#: includes/admin/views/html-admin-form-top.php:21
msgid "Add Field"
msgstr ""

#: includes/admin/views/field-group-field.php:191
#: includes/admin/views/field-group-options.php:40
msgid "Presentation"
msgstr ""

#: includes/admin/views/field-group-field.php:159
msgid "Validation"
msgstr ""

#: includes/admin/views/field-group-field.php:93
msgid "General"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:70
msgid "Import JSON"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:361
msgid "Export Field Groups - Generate PHP"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid "Export As JSON"
msgstr ""

#: includes/admin/admin-field-groups.php:638
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] ""

#: includes/admin/admin-field-groups.php:585
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] ""

#: includes/admin/admin-field-groups.php:537
#: includes/admin/admin-field-groups.php:558
msgid "Deactivate"
msgstr ""

#: includes/admin/admin-field-groups.php:537
msgid "Deactivate this item"
msgstr ""

#: includes/admin/admin-field-groups.php:533
#: includes/admin/admin-field-groups.php:557
msgid "Activate"
msgstr ""

#: includes/admin/admin-field-groups.php:533
msgid "Activate this item"
msgstr ""

#: includes/admin/admin-field-group.php:158
#: assets/build/js/acf-field-group.js:2366
#: assets/build/js/acf-field-group.js:2635
msgid "Move field group to trash?"
msgstr ""

#: acf.php:448 includes/admin/admin-field-group.php:353
#: includes/admin/admin-field-groups.php:232
msgctxt "post status"
msgid "Inactive"
msgstr ""

#. Author of the plugin
msgid "WP Engine"
msgstr ""

#: acf.php:506
msgid ""
"Advanced Custom Fields and Advanced Custom Fields should not be active at "
"the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""

#: acf.php:504
msgid ""
"Advanced Custom Fields and Advanced Custom Fields should not be active at "
"the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""

#: includes/acf-value-functions.php:374
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" target=\"_blank"
"\">Learn how to fix this</a>."
msgstr ""

#: includes/fields/class-acf-field-user.php:537
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] ""

#: includes/fields/class-acf-field-user.php:528
msgid "%1$s must have a valid user ID."
msgstr ""

#: includes/fields/class-acf-field-user.php:366
msgid "Invalid request."
msgstr ""

#: includes/fields/class-acf-field-select.php:683
msgid "%1$s is not one of %2$s"
msgstr ""

#: includes/fields/class-acf-field-post_object.php:669
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] ""

#: includes/fields/class-acf-field-post_object.php:653
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] ""

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must have a valid post ID."
msgstr ""

#: includes/fields/class-acf-field-file.php:472
msgid "%s requires a valid attachment ID."
msgstr ""

#: includes/admin/views/field-group-options.php:205
msgid "Show in REST API"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:167
msgid "Enable Transparency"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:186
msgid "RGBA Array"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:96
msgid "RGBA String"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:95
#: includes/fields/class-acf-field-color_picker.php:185
msgid "Hex String"
msgstr ""

#: includes/admin/admin-field-group.php:187
#: assets/build/js/acf-field-group.js:837
#: assets/build/js/acf-field-group.js:933
msgid "Gallery (Pro only)"
msgstr "ギャラリー (プロ版のみ)"

#: includes/admin/admin-field-group.php:186
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:923
msgid "Clone (Pro only)"
msgstr "複製 (プロ版のみ)"

#: includes/admin/admin-field-group.php:185
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:920
msgid "Flexible Content (Pro only)"
msgstr "フレキシブルコンテンツ (プロ版のみ)"

#: includes/admin/admin-field-group.php:184
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:917
msgid "Repeater (Pro only)"
msgstr "リピーター (プロ版のみ)"

#: includes/admin/admin-field-group.php:353
msgctxt "post status"
msgid "Active"
msgstr "有効"

#: includes/fields/class-acf-field-email.php:178
msgid "'%s' is not a valid email address"
msgstr "'%s' は有効なメールアドレスではありません"

#: includes/fields/class-acf-field-color_picker.php:74
msgid "Color value"
msgstr "明度"

#: includes/fields/class-acf-field-color_picker.php:72
msgid "Select default color"
msgstr "デフォルトの色を選択"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Clear color"
msgstr "色をクリア"

#: includes/acf-wp-functions.php:87
msgid "Blocks"
msgstr "ブロック"

#: includes/acf-wp-functions.php:83
msgid "Options"
msgstr "オプション"

#: includes/acf-wp-functions.php:79
msgid "Users"
msgstr "ユーザー"

#: includes/acf-wp-functions.php:75
msgid "Menu items"
msgstr "メニュー項目"

#: includes/acf-wp-functions.php:67
msgid "Widgets"
msgstr "ウィジェット"

#: includes/acf-wp-functions.php:59
msgid "Attachments"
msgstr "添付ファイル"

#: includes/acf-wp-functions.php:54
msgid "Taxonomies"
msgstr "タクソノミー"

#: includes/acf-wp-functions.php:41
msgid "Posts"
msgstr "投稿"

#: includes/ajax/class-acf-ajax-local-json-diff.php:68
msgid "JSON field group (newer)"
msgstr "JSON フィールドグループ (新しい方)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:64
msgid "Original field group"
msgstr "元のフィールドグループ"

#: includes/ajax/class-acf-ajax-local-json-diff.php:59
msgid "Last updated: %s"
msgstr "最終更新日: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:53
msgid "Sorry, this field group is unavailable for diff comparison."
msgstr "このフィールドグループは diff 比較に使用できません。"

#: includes/ajax/class-acf-ajax-local-json-diff.php:43
msgid "Invalid field group ID."
msgstr "無効なフィールドグループ ID。"

#: includes/ajax/class-acf-ajax-local-json-diff.php:36
msgid "Invalid field group parameter(s)."
msgstr "無効なフィールドグループパラメータ。"

#: includes/admin/admin-field-groups.php:506
msgid "Awaiting save"
msgstr "保存待ち"

#: includes/admin/admin-field-groups.php:503
msgid "Saved"
msgstr "保存しました"

#: includes/admin/admin-field-groups.php:499
msgid "Import"
msgstr "インポート"

#: includes/admin/admin-field-groups.php:495
msgid "Review changes"
msgstr "変更をレビュー"

#: includes/admin/admin-field-groups.php:471
msgid "Located in: %s"
msgstr "位置: %s"

#: includes/admin/admin-field-groups.php:467
msgid "Located in plugin: %s"
msgstr "プラグイン中の位置: %s"

#: includes/admin/admin-field-groups.php:463
msgid "Located in theme: %s"
msgstr "テーマ内の位置: %s"

#: includes/admin/admin-field-groups.php:441
msgid "Various"
msgstr "各種"

#: includes/admin/admin-field-groups.php:200
#: includes/admin/admin-field-groups.php:565
msgid "Sync changes"
msgstr "変更を同期"

#: includes/admin/admin-field-groups.php:199
msgid "Loading diff"
msgstr "差分を読み込み中"

#: includes/admin/admin-field-groups.php:198
msgid "Review local JSON changes"
msgstr "ローカルの JSON 変更をレビュー"

#: includes/admin/admin.php:172
msgid "Visit website"
msgstr "サイトへ移動"

#: includes/admin/admin.php:171
msgid "View details"
msgstr "詳細を表示"

#: includes/admin/admin.php:170
msgid "Version %s"
msgstr "バージョン %s"

#: includes/admin/admin.php:169
msgid "Information"
msgstr "情報"

#: includes/admin/admin.php:160
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">ヘルプデスク</a>。サポートの専門家がお客様の"
"より詳細な技術的課題をサポートします。"

#: includes/admin/admin.php:156
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the ‘how-tos’ of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">ディスカッション</a>。コミュニティフォーラム"
"には、活発でフレンドリーなコミュニティがあり、ACF の世界の「ハウツー」を理解"
"する手助けをしてくれるかもしれません。"

#: includes/admin/admin.php:152
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">ドキュメンテーション</a>。私たちの豊富なド"
"キュメントには、お客様が遭遇する可能性のあるほとんどの状況に対するリファレン"
"スやガイドが含まれています。"

#: includes/admin/admin.php:149
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"私たちはサポートを非常に重要視しており、ACF を使ったサイトを最大限に活用して"
"いただきたいと考えています。何か問題が発生した場合には、複数の場所でサポート"
"を受けることができます:"

#: includes/admin/admin.php:146 includes/admin/admin.php:148
msgid "Help & Support"
msgstr "ヘルプとサポート"

#: includes/admin/admin.php:137
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr "お困りの際は「ヘルプとサポート」タブからお問い合わせください。"

#: includes/admin/admin.php:134
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"初めてフィールドグループを作成する前にまず<a href=\"%s\" target=\"_blank\">ス"
"タートガイド</a>に目を通して、プラグインの理念やベストプラクティスを理解する"
"ことをおすすめします。"

#: includes/admin/admin.php:132
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Advanced Custom Fields プラグインは、WordPress の編集画面を追加フィールドでカ"
"スタマイズするためのビジュアルフォームビルダーと、カスタムフィールドの値を任"
"意のテーマテンプレートファイルに表示するための直感的な API を提供します。"

#: includes/admin/admin.php:129 includes/admin/admin.php:131
msgid "Overview"
msgstr "概要"

#: includes/locations.php:36
msgid "Location type \"%s\" is already registered."
msgstr "位置タイプ「%s」はすでに登録されています。"

#: includes/locations.php:25
msgid "Class \"%s\" does not exist."
msgstr "クラス \"%s\" は存在しません。"

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "無効な nonce。"

#: includes/fields/class-acf-field-user.php:361
msgid "Error loading field."
msgstr "フィールドの読み込みエラー。"

#: assets/build/js/acf-input.js:2742 assets/build/js/acf-input.js:2811
#: assets/build/js/acf-input.js:2913 assets/build/js/acf-input.js:2987
msgid "Location not found: %s"
msgstr "位置情報が見つかりません: %s"

#: includes/forms/form-user.php:353
msgid "<strong>Error</strong>: %s"
msgstr "<strong>エラー</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "ウィジェット"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "ユーザー権限グループ"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "コメント"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "投稿フォーマット"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "メニュー項目"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "投稿ステータス"

#: includes/acf-wp-functions.php:71
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "メニュー"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "メニューの位置"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "メニュー"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "投稿タクソノミー"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "子ページ (親ページあり)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "親ページ (子ページあり)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "最上位レベルのページ (親ページなし)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "投稿ページ"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "フロントページ"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "ページタイプ"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "バックエンドで表示"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "フロントエンドで表示"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "ログイン済み"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "現在のユーザー"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "固定ページテンプレート"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "登録"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "追加 / 編集"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "ユーザーフォーム"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "親ページ"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "特権管理者"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "現在のユーザー権限グループ"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "デフォルトテンプレート"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "投稿テンプレート"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "投稿カテゴリー"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "すべての%sフォーマット"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "添付ファイル"

#: includes/validation.php:365
msgid "%s value is required"
msgstr "%s の値は必須です"

#: includes/admin/views/field-group-field-conditional-logic.php:59
msgid "Show this field if"
msgstr "このフィールドグループの表示条件"

#: includes/admin/views/field-group-field-conditional-logic.php:26
#: includes/admin/views/field-group-field.php:279
msgid "Conditional Logic"
msgstr "条件判定"

#: includes/admin/admin.php:207
#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/html-location-rule.php:92
msgid "and"
msgstr "と"

#: includes/admin/admin-field-groups.php:290
msgid "Local JSON"
msgstr "ローカル JSON"

#: includes/admin/views/field-group-pro-features.php:9
msgid "Clone Field"
msgstr "フィールドを複製"

#: includes/admin/views/html-notice-upgrade.php:30
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"また、すべてのプレミアムアドオン ( %s) が最新版に更新されていることを確認して"
"ください。"

#: includes/admin/views/html-notice-upgrade.php:28
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"このバージョンにはデータベースの改善が含まれており、アップグレードが必要で"
"す。"

#: includes/admin/views/html-notice-upgrade.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "%1$s v%2$sへの更新をありがとうございます。"

#: includes/admin/views/html-notice-upgrade.php:27
msgid "Database Upgrade Required"
msgstr "データベースのアップグレードが必要"

#: includes/admin/views/html-notice-upgrade.php:18
msgid "Options Page"
msgstr "オプションページ"

#: includes/admin/views/html-notice-upgrade.php:15
msgid "Gallery"
msgstr "ギャラリー"

#: includes/admin/views/html-notice-upgrade.php:12
msgid "Flexible Content"
msgstr "柔軟なコンテンツ"

#: includes/admin/views/html-notice-upgrade.php:9
msgid "Repeater"
msgstr "繰り返し"

#: includes/admin/views/html-admin-tools.php:24
msgid "Back to all tools"
msgstr "すべてのツールに戻る"

#: includes/admin/views/field-group-options.php:160
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"複数のフィールドグループが編集画面に表示される場合、最初のフィールドグループ "
"(最小の番号を持つもの) のオプションが使用されます"

#: includes/admin/views/field-group-options.php:160
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "編集画面で<b>非表示</b>にする項目を<b>選択して</b>ください。"

#: includes/admin/views/field-group-options.php:159
msgid "Hide on screen"
msgstr "画面上で非表示"

#: includes/admin/views/field-group-options.php:151
msgid "Send Trackbacks"
msgstr "トラックバック送信"

#: includes/admin/views/field-group-options.php:150
msgid "Tags"
msgstr "タグ"

#: includes/admin/views/field-group-options.php:149
msgid "Categories"
msgstr "カテゴリー"

#: includes/admin/views/field-group-options.php:147
msgid "Page Attributes"
msgstr "ページ属性"

#: includes/admin/views/field-group-options.php:146
msgid "Format"
msgstr "フォーマット"

#: includes/admin/views/field-group-options.php:145
msgid "Author"
msgstr "投稿者"

#: includes/admin/views/field-group-options.php:144
msgid "Slug"
msgstr "スラッグ"

#: includes/admin/views/field-group-options.php:143
msgid "Revisions"
msgstr "リビジョン"

#: includes/acf-wp-functions.php:63
#: includes/admin/views/field-group-options.php:142
msgid "Comments"
msgstr "コメント"

#: includes/admin/views/field-group-options.php:141
msgid "Discussion"
msgstr "ディスカッション"

#: includes/admin/views/field-group-options.php:139
msgid "Excerpt"
msgstr "抜粋"

#: includes/admin/views/field-group-options.php:138
msgid "Content Editor"
msgstr "コンテンツエディター"

#: includes/admin/views/field-group-options.php:137
msgid "Permalink"
msgstr "パーマリンク"

#: includes/admin/views/field-group-options.php:222
msgid "Shown in field group list"
msgstr "フィールドグループリストに表示"

#: includes/admin/views/field-group-options.php:122
msgid "Field groups with a lower order will appear first"
msgstr "下位のフィールドグループを最初に表示"

#: includes/admin/views/field-group-options.php:121
msgid "Order No."
msgstr "注文番号"

#: includes/admin/views/field-group-options.php:112
msgid "Below fields"
msgstr "フィールドの下"

#: includes/admin/views/field-group-options.php:111
msgid "Below labels"
msgstr "ラベルの下"

#: includes/admin/views/field-group-options.php:104
msgid "Instruction placement"
msgstr "手順の配置"

#: includes/admin/views/field-group-options.php:87
msgid "Label placement"
msgstr "ラベルの配置"

#: includes/admin/views/field-group-options.php:77
msgid "Side"
msgstr "サイド"

#: includes/admin/views/field-group-options.php:76
msgid "Normal (after content)"
msgstr "通常 (コンテンツの後)"

#: includes/admin/views/field-group-options.php:75
msgid "High (after title)"
msgstr "高 (タイトルの後)"

#: includes/admin/views/field-group-options.php:68
msgid "Position"
msgstr "位置"

#: includes/admin/views/field-group-options.php:59
msgid "Seamless (no metabox)"
msgstr "シームレス (メタボックスなし)"

#: includes/admin/views/field-group-options.php:58
msgid "Standard (WP metabox)"
msgstr "標準 (WP メタボックス)"

#: includes/admin/views/field-group-options.php:51
msgid "Style"
msgstr "スタイル"

#: includes/admin/views/field-group-fields.php:44
msgid "Type"
msgstr "タイプ"

#: includes/admin/admin-field-groups.php:285
#: includes/admin/views/field-group-fields.php:43
msgid "Key"
msgstr "キー"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/field-group-fields.php:37
msgid "Order"
msgstr "順序"

#: includes/admin/views/field-group-field.php:294
msgid "Close Field"
msgstr "フィールドを閉じる"

#: includes/admin/views/field-group-field.php:235
msgid "id"
msgstr "ID"

#: includes/admin/views/field-group-field.php:219
msgid "class"
msgstr "クラス"

#: includes/admin/views/field-group-field.php:256
msgid "width"
msgstr "横幅"

#: includes/admin/views/field-group-field.php:250
msgid "Wrapper Attributes"
msgstr "ラッパー属性"

#: includes/admin/views/field-group-field.php:171
msgid "Required"
msgstr ""

#: includes/admin/views/field-group-field.php:203
msgid "Instructions for authors. Shown when submitting data"
msgstr "投稿者向けの手順。データ送信時に表示されます"

#: includes/admin/views/field-group-field.php:202
msgid "Instructions"
msgstr "手順"

#: includes/admin/views/field-group-field.php:106
msgid "Field Type"
msgstr "フィールドタイプ"

#: includes/admin/views/field-group-field.php:134
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "スペースは不可、アンダースコアとダッシュは使用可能"

#: includes/admin/views/field-group-field.php:133
msgid "Field Name"
msgstr "フィールド名"

#: includes/admin/views/field-group-field.php:121
msgid "This is the name which will appear on the EDIT page"
msgstr "これは、編集ページに表示される名前です"

#: includes/admin/views/field-group-field.php:120
msgid "Field Label"
msgstr "フィールドラベル"

#: includes/admin/views/field-group-field.php:70
msgid "Delete"
msgstr "削除"

#: includes/admin/views/field-group-field.php:70
msgid "Delete field"
msgstr "フィールドを削除"

#: includes/admin/views/field-group-field.php:68
msgid "Move"
msgstr "移動"

#: includes/admin/views/field-group-field.php:68
msgid "Move field to another group"
msgstr "フィールドを別のグループへ移動"

#: includes/admin/views/field-group-field.php:66
msgid "Duplicate field"
msgstr "フィールドを複製"

#: includes/admin/views/field-group-field.php:62
#: includes/admin/views/field-group-field.php:65
msgid "Edit field"
msgstr "フィールドを編集"

#: includes/admin/views/field-group-field.php:58
msgid "Drag to reorder"
msgstr "ドラッグして順序を変更"

#: includes/admin/admin-field-group.php:168
#: includes/admin/views/html-location-group.php:3
#: assets/build/js/acf-field-group.js:1935
#: assets/build/js/acf-field-group.js:2166
msgid "Show this field group if"
msgstr "このフィールドグループを表示する条件"

#: includes/admin/views/html-admin-page-upgrade.php:94
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "利用可能な更新はありません。"

#: includes/admin/views/html-admin-page-upgrade.php:33
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"データベースのアップグレードが完了しました。<a href=\"%s\">変更点を表示</a>"

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "アップグレードタスクを読み込んでいます..."

#: includes/admin/views/html-admin-page-upgrade-network.php:165
#: includes/admin/views/html-admin-page-upgrade.php:65
msgid "Upgrade failed."
msgstr "アップグレードに失敗しました。"

#: includes/admin/views/html-admin-page-upgrade-network.php:162
msgid "Upgrade complete."
msgstr "アップグレードが完了しました。"

#: includes/admin/views/html-admin-page-upgrade-network.php:148
#: includes/admin/views/html-admin-page-upgrade.php:31
msgid "Upgrading data to version %s"
msgstr "データをバージョン%sへアップグレード中"

#: includes/admin/views/html-admin-page-upgrade-network.php:121
#: includes/admin/views/html-notice-upgrade.php:44
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"続行する前にデータベースをバックアップすることを強くおすすめします。本当に更"
"新ツールを今すぐ実行してもよいですか ?"

#: includes/admin/views/html-admin-page-upgrade-network.php:117
msgid "Please select at least one site to upgrade."
msgstr "アップグレードするサイトを1つ以上選択してください。"

#: includes/admin/views/html-admin-page-upgrade-network.php:97
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"データベースのアップグレードが完了しました。<a href=\"%s\">ネットワークダッ"
"シュボードに戻る</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:80
msgid "Site is up to date"
msgstr "サイトは最新状態です"

#: includes/admin/views/html-admin-page-upgrade-network.php:78
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "%1$s から %2$s へのデータベースのアップグレードが必要です"

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "サイト"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:96
msgid "Upgrade Sites"
msgstr "サイトをアップグレード"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"以下のサイトはデータベースのアップグレードが必要です。更新したいものにチェッ"
"クを入れて、%s をクリックしてください。"

#: includes/admin/views/field-group-field-conditional-logic.php:171
#: includes/admin/views/field-group-locations.php:38
msgid "Add rule group"
msgstr "ルールグループを追加"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"どの編集画面でカスタムフィールドを表示するかを決定するルールを作成します"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "ルール"

#: includes/admin/tools/class-acf-admin-tool-export.php:478
msgid "Copied"
msgstr "コピーしました"

#: includes/admin/tools/class-acf-admin-tool-export.php:441
msgid "Copy to clipboard"
msgstr "クリップボードにコピー"

#: includes/admin/tools/class-acf-admin-tool-export.php:362
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"以下のコードは選択したフィールドグループのローカルバージョンとして登録に使え"
"ます。ローカルフィールドグループは読み込み時間の短縮やバージョンコントロー"
"ル、動的なフィールド/設定など多くの利点があります。以下のコードをテーマの "
"functions.php や外部ファイルにコピー & ペーストしてください。"

#: includes/admin/tools/class-acf-admin-tool-export.php:329
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:233
#: includes/admin/tools/class-acf-admin-tool-export.php:262
msgid "Select Field Groups"
msgstr "フィールドグループを選択"

#: includes/admin/tools/class-acf-admin-tool-export.php:167
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "%s個のフィールドグループをエクスポートしました。"

#: includes/admin/tools/class-acf-admin-tool-export.php:96
#: includes/admin/tools/class-acf-admin-tool-export.php:131
msgid "No field groups selected"
msgstr "フィールド未選択"

#: includes/admin/tools/class-acf-admin-tool-export.php:39
#: includes/admin/tools/class-acf-admin-tool-export.php:337
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "PHP を生成"

#: includes/admin/tools/class-acf-admin-tool-export.php:35
msgid "Export Field Groups"
msgstr "フィールドグループをエクスポート"

#: includes/admin/tools/class-acf-admin-tool-import.php:146
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "%s個のフィールドグループをインポートしました"

#: includes/admin/tools/class-acf-admin-tool-import.php:115
msgid "Import file empty"
msgstr "空ファイルのインポート"

#: includes/admin/tools/class-acf-admin-tool-import.php:106
msgid "Incorrect file type"
msgstr "不正なファイルの種類"

#: includes/admin/tools/class-acf-admin-tool-import.php:101
msgid "Error uploading file. Please try again"
msgstr "ファイルアップロードエラー。もう一度お試しください"

#: includes/admin/tools/class-acf-admin-tool-import.php:51
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:28
#: includes/admin/tools/class-acf-admin-tool-import.php:50
msgid "Import Field Groups"
msgstr "フィールドグループをインポート"

#: includes/admin/admin-field-groups.php:494
msgid "Sync"
msgstr "同期"

#: includes/admin/admin-field-groups.php:942
msgid "Select %s"
msgstr "%sを選択"

#: includes/admin/admin-field-groups.php:527
#: includes/admin/admin-field-groups.php:556
#: includes/admin/views/field-group-field.php:66
msgid "Duplicate"
msgstr "複製"

#: includes/admin/admin-field-groups.php:527
msgid "Duplicate this item"
msgstr "この項目を複製"

#: includes/admin/admin-field-groups.php:284
#: includes/admin/views/field-group-options.php:221
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
msgid "Description"
msgstr "説明"

#: includes/admin/admin-field-groups.php:491
#: includes/admin/admin-field-groups.php:829
msgid "Sync available"
msgstr "同期が利用できます"

#: includes/admin/admin-field-groups.php:754
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s件のフィールドグループを同期しました。"

#: includes/admin/admin-field-groups.php:696
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s件のフィールドグループを複製しました。"

#: includes/admin/admin-field-groups.php:118
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "使用中 <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:237
msgid "Review sites & upgrade"
msgstr "サイトをレビューしてアップグレード"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:93
#: includes/admin/admin-upgrade.php:94 includes/admin/admin-upgrade.php:213
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "データベースをアップグレード"

#: includes/admin/admin.php:49 includes/admin/views/field-group-options.php:140
msgid "Custom Fields"
msgstr "カスタムフィールド"

#: includes/admin/admin-field-group.php:718
msgid "Move Field"
msgstr "フィールドを移動"

#: includes/admin/admin-field-group.php:707
#: includes/admin/admin-field-group.php:711
msgid "Please select the destination for this field"
msgstr "このフィールドの移動先を選択してください"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/admin-field-group.php:668
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "%1$s フィールドは現在 %2$s フィールドグループにあります"

#: includes/admin/admin-field-group.php:665
msgid "Move Complete."
msgstr "移動が完了しました。"

#: includes/admin/views/field-group-field.php:273
#: includes/admin/views/field-group-options.php:189
msgid "Active"
msgstr "有効"

#: includes/admin/admin-field-group.php:325
msgid "Field Keys"
msgstr "フィールドキー"

#: includes/admin/admin-field-group.php:224
#: includes/admin/tools/class-acf-admin-tool-export.php:286
msgid "Settings"
msgstr "設定"

#: includes/admin/admin-field-groups.php:286
msgid "Location"
msgstr "所在地"

#: includes/admin/admin-field-group.php:169 assets/build/js/acf-input.js:983
#: assets/build/js/acf-input.js:1075
msgid "Null"
msgstr "Null"

#: includes/acf-field-group-functions.php:846
#: includes/admin/admin-field-group.php:166
#: assets/build/js/acf-field-group.js:1158
#: assets/build/js/acf-field-group.js:1310
msgid "copy"
msgstr "コピー"

#: includes/admin/admin-field-group.php:165
#: assets/build/js/acf-field-group.js:344
#: assets/build/js/acf-field-group.js:389
msgid "(this field)"
msgstr "(このフィールド)"

#: includes/admin/admin-field-group.php:163 assets/build/js/acf-input.js:918
#: assets/build/js/acf-input.js:943 assets/build/js/acf-input.js:1002
#: assets/build/js/acf-input.js:1030
msgid "Checked"
msgstr "チェック済み"

#: includes/admin/admin-field-group.php:160
#: assets/build/js/acf-field-group.js:1249
#: assets/build/js/acf-field-group.js:1408
msgid "Move Custom Field"
msgstr "カスタムフィールドを移動"

#: includes/admin/admin-field-group.php:159
#: assets/build/js/acf-field-group.js:370
#: assets/build/js/acf-field-group.js:415
msgid "No toggle fields available"
msgstr "利用可能な切り替えフィールドがありません"

#: includes/admin/admin-field-group.php:157
msgid "Field group title is required"
msgstr "フィールドグループのタイトルは必須です"

#: includes/admin/admin-field-group.php:156
#: assets/build/js/acf-field-group.js:1238
#: assets/build/js/acf-field-group.js:1394
msgid "This field cannot be moved until its changes have been saved"
msgstr "変更を保存するまでこのフィールドは移動できません"

#: includes/admin/admin-field-group.php:155
#: assets/build/js/acf-field-group.js:1049
#: assets/build/js/acf-field-group.js:1192
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "\"field_\" という文字列はフィールド名の先頭に使うことはできません"

#: includes/admin/admin-field-group.php:82
msgid "Field group draft updated."
msgstr "フィールドグループ下書きを更新しました。"

#: includes/admin/admin-field-group.php:81
msgid "Field group scheduled for."
msgstr "フィールドグループを公開予約しました。"

#: includes/admin/admin-field-group.php:80
msgid "Field group submitted."
msgstr "フィールドグループを送信しました。"

#: includes/admin/admin-field-group.php:79
msgid "Field group saved."
msgstr "フィールドグループを保存しました。"

#: includes/admin/admin-field-group.php:78
msgid "Field group published."
msgstr "フィールドグループを公開しました。"

#: includes/admin/admin-field-group.php:75
msgid "Field group deleted."
msgstr "フィールドグループを削除しました。"

#: includes/admin/admin-field-group.php:73
#: includes/admin/admin-field-group.php:74
#: includes/admin/admin-field-group.php:76
msgid "Field group updated."
msgstr "フィールドグループを更新しました。"

#: includes/admin/admin-tools.php:119
#: includes/admin/views/html-admin-navigation.php:109
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "ツール"

#: includes/locations/abstract-acf-location.php:106
msgid "is not equal to"
msgstr "等しくない"

#: includes/locations/abstract-acf-location.php:105
msgid "is equal to"
msgstr "等しい"

#: includes/locations.php:102
msgid "Forms"
msgstr "フォーム"

#: includes/locations.php:100 includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "固定ページ"

#: includes/locations.php:99 includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "投稿"

#: includes/fields.php:358
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:357
msgid "Relational"
msgstr "関連"

#: includes/fields.php:356
msgid "Choice"
msgstr "選択"

#: includes/fields.php:354
msgid "Basic"
msgstr "基本"

#: includes/fields.php:313
msgid "Unknown"
msgstr "不明"

#: includes/fields.php:313
msgid "Field type does not exist"
msgstr "フィールドタイプが存在しません"

#: includes/forms/form-front.php:236
msgid "Spam Detected"
msgstr "スパムを検出しました"

#: includes/forms/form-front.php:107
msgid "Post updated"
msgstr "投稿を更新しました"

#: includes/forms/form-front.php:106
msgid "Update"
msgstr "更新"

#: includes/forms/form-front.php:57
msgid "Validate Email"
msgstr "メールを確認"

#: includes/fields.php:355 includes/forms/form-front.php:49
msgid "Content"
msgstr "コンテンツ"

#: includes/forms/form-front.php:40
msgid "Title"
msgstr "タイトル"

#: includes/assets.php:371 includes/forms/form-comment.php:160
#: assets/build/js/acf-input.js:7310 assets/build/js/acf-input.js:7876
msgid "Edit field group"
msgstr "フィールドグループを編集"

#: includes/admin/admin-field-group.php:181 assets/build/js/acf-input.js:1125
#: assets/build/js/acf-input.js:1230
msgid "Selection is less than"
msgstr "選択範囲が以下より小さい場合"

#: includes/admin/admin-field-group.php:180 assets/build/js/acf-input.js:1106
#: assets/build/js/acf-input.js:1202
msgid "Selection is greater than"
msgstr "選択範囲が以下より大きい場合"

#: includes/admin/admin-field-group.php:179 assets/build/js/acf-input.js:1075
#: assets/build/js/acf-input.js:1170
msgid "Value is less than"
msgstr "値が以下より小さい場合"

#: includes/admin/admin-field-group.php:178 assets/build/js/acf-input.js:1045
#: assets/build/js/acf-input.js:1139
msgid "Value is greater than"
msgstr "値が以下より大きい場合"

#: includes/admin/admin-field-group.php:177 assets/build/js/acf-input.js:888
#: assets/build/js/acf-input.js:960
msgid "Value contains"
msgstr "以下の値が含まれる場合"

#: includes/admin/admin-field-group.php:176 assets/build/js/acf-input.js:862
#: assets/build/js/acf-input.js:926
msgid "Value matches pattern"
msgstr "値が以下のパターンに一致する場合"

#: includes/admin/admin-field-group.php:175 assets/build/js/acf-input.js:840
#: assets/build/js/acf-input.js:1023 assets/build/js/acf-input.js:903
#: assets/build/js/acf-input.js:1116
msgid "Value is not equal to"
msgstr "値が以下に等しくない場合"

#: includes/admin/admin-field-group.php:174 assets/build/js/acf-input.js:810
#: assets/build/js/acf-input.js:964 assets/build/js/acf-input.js:864
#: assets/build/js/acf-input.js:1053
msgid "Value is equal to"
msgstr "値が以下に等しい場合"

#: includes/admin/admin-field-group.php:173 assets/build/js/acf-input.js:788
#: assets/build/js/acf-input.js:841
msgid "Has no value"
msgstr "値がない場合"

#: includes/admin/admin-field-group.php:172 assets/build/js/acf-input.js:758
#: assets/build/js/acf-input.js:783
msgid "Has any value"
msgstr "任意の値あり"

#: includes/assets.php:352 assets/build/js/acf.js:1524
#: assets/build/js/acf.js:1604
msgid "Cancel"
msgstr "キャンセル"

#: includes/assets.php:348 assets/build/js/acf.js:1698
#: assets/build/js/acf.js:1801
msgid "Are you sure?"
msgstr "本当に実行しますか ?"

#: includes/assets.php:368 assets/build/js/acf-input.js:9343
#: assets/build/js/acf-input.js:10172
msgid "%d fields require attention"
msgstr "%d個のフィールドで確認が必要です"

#: includes/assets.php:367 assets/build/js/acf-input.js:9341
#: assets/build/js/acf-input.js:10168
msgid "1 field requires attention"
msgstr "1つのフィールドで確認が必要です"

#: includes/assets.php:366 includes/validation.php:287
#: includes/validation.php:297 assets/build/js/acf-input.js:9336
#: assets/build/js/acf-input.js:10163
msgid "Validation failed"
msgstr "検証失敗"

#: includes/assets.php:365 assets/build/js/acf-input.js:9499
#: assets/build/js/acf-input.js:10346
msgid "Validation successful"
msgstr "検証成功"

#: includes/media.php:54 assets/build/js/acf-input.js:7138
#: assets/build/js/acf-input.js:7680
msgid "Restricted"
msgstr "制限"

#: includes/media.php:53 assets/build/js/acf-input.js:6953
#: assets/build/js/acf-input.js:7444
msgid "Collapse Details"
msgstr "詳細を折りたたむ"

#: includes/media.php:52 assets/build/js/acf-input.js:6953
#: assets/build/js/acf-input.js:7441
msgid "Expand Details"
msgstr "詳細を展開"

#: includes/media.php:51 assets/build/js/acf-input.js:6820
#: assets/build/js/acf-input.js:7289
msgid "Uploaded to this post"
msgstr "この投稿へのアップロード"

#: includes/media.php:50 assets/build/js/acf-input.js:6859
#: assets/build/js/acf-input.js:7328
msgctxt "verb"
msgid "Update"
msgstr "更新"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "編集"

#: includes/assets.php:362 assets/build/js/acf-input.js:9113
#: assets/build/js/acf-input.js:9934
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "このページから移動した場合、変更は失われます"

#: includes/api/api-helpers.php:3395
msgid "File type must be %s."
msgstr "ファイル形式は %s である必要があります。"

#: includes/admin/admin-field-group.php:167
#: includes/admin/views/field-group-field-conditional-logic.php:59
#: includes/admin/views/field-group-field-conditional-logic.php:169
#: includes/admin/views/field-group-locations.php:36
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3391 assets/build/js/acf-field-group.js:492
#: assets/build/js/acf-field-group.js:1973
#: assets/build/js/acf-field-group.js:544
#: assets/build/js/acf-field-group.js:2210
msgid "or"
msgstr "または"

#: includes/api/api-helpers.php:3364
msgid "File size must not exceed %s."
msgstr "ファイルサイズは %s 以下である必要があります。"

#: includes/api/api-helpers.php:3359
msgid "File size must be at least %s."
msgstr "ファイルサイズは %s 以上である必要があります。"

#: includes/api/api-helpers.php:3344
msgid "Image height must not exceed %dpx."
msgstr "画像の高さは %dpx 以下である必要があります。"

#: includes/api/api-helpers.php:3339
msgid "Image height must be at least %dpx."
msgstr "画像の高さは %dpx 以上である必要があります。"

#: includes/api/api-helpers.php:3325
msgid "Image width must not exceed %dpx."
msgstr "画像の幅は %dpx 以下である必要があります。"

#: includes/api/api-helpers.php:3320
msgid "Image width must be at least %dpx."
msgstr "画像の幅は %dpx 以上である必要があります。"

#: includes/api/api-helpers.php:1566 includes/api/api-term.php:147
msgid "(no title)"
msgstr "(タイトルなし)"

#: includes/api/api-helpers.php:861
msgid "Full Size"
msgstr "フルサイズ"

#: includes/api/api-helpers.php:820
msgid "Large"
msgstr "大"

#: includes/api/api-helpers.php:819
msgid "Medium"
msgstr "中"

#: includes/api/api-helpers.php:818
msgid "Thumbnail"
msgstr "サムネイル"

#: includes/acf-field-functions.php:846
#: includes/admin/admin-field-group.php:164
#: assets/build/js/acf-field-group.js:789
#: assets/build/js/acf-field-group.js:859
msgid "(no label)"
msgstr "(ラベルなし)"

#: includes/fields/class-acf-field-textarea.php:142
msgid "Sets the textarea height"
msgstr "テキストエリアの高さを設定"

#: includes/fields/class-acf-field-textarea.php:141
msgid "Rows"
msgstr "行"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "テキストエリア"

#: includes/fields/class-acf-field-checkbox.php:447
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "追加のチェックボックスを先頭に追加して、すべての選択肢を切り替えます"

#: includes/fields/class-acf-field-checkbox.php:409
msgid "Save 'custom' values to the field's choices"
msgstr "フィールドの選択肢として「カスタム」を保存する"

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Allow 'custom' values to be added"
msgstr "「カスタム」値の追加を許可する"

#: includes/fields/class-acf-field-checkbox.php:233
msgid "Add new choice"
msgstr "新規選択肢を追加"

#: includes/fields/class-acf-field-checkbox.php:170
msgid "Toggle All"
msgstr "すべて切り替え"

#: includes/fields/class-acf-field-page_link.php:477
msgid "Allow Archives URLs"
msgstr "アーカイブ URL を許可"

#: includes/fields/class-acf-field-page_link.php:165
msgid "Archives"
msgstr "アーカイブ"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "ページリンク"

#: includes/fields/class-acf-field-taxonomy.php:945
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "追加"

#: includes/admin/views/field-group-fields.php:42
#: includes/fields/class-acf-field-taxonomy.php:910
msgid "Name"
msgstr "名前"

#: includes/fields/class-acf-field-taxonomy.php:894
msgid "%s added"
msgstr "%s を追加しました"

#: includes/fields/class-acf-field-taxonomy.php:858
msgid "%s already exists"
msgstr "%s はすでに存在しています"

#: includes/fields/class-acf-field-taxonomy.php:846
msgid "User unable to add new %s"
msgstr "ユーザーが新規 %s を追加できません"

#: includes/fields/class-acf-field-taxonomy.php:756
msgid "Term ID"
msgstr "ターム ID"

#: includes/fields/class-acf-field-taxonomy.php:755
msgid "Term Object"
msgstr "タームオブジェクト"

#: includes/fields/class-acf-field-taxonomy.php:740
msgid "Load value from posts terms"
msgstr "投稿タームから値を読み込む"

#: includes/fields/class-acf-field-taxonomy.php:739
msgid "Load Terms"
msgstr "タームを読み込む"

#: includes/fields/class-acf-field-taxonomy.php:729
msgid "Connect selected terms to the post"
msgstr "選択したタームを投稿に関連付ける"

#: includes/fields/class-acf-field-taxonomy.php:728
msgid "Save Terms"
msgstr "タームを保存"

#: includes/fields/class-acf-field-taxonomy.php:718
msgid "Allow new terms to be created whilst editing"
msgstr "編集中に新しいタームを作成できるようにする"

#: includes/fields/class-acf-field-taxonomy.php:717
msgid "Create Terms"
msgstr "タームを追加"

#: includes/fields/class-acf-field-taxonomy.php:776
msgid "Radio Buttons"
msgstr "ラジオボタン"

#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Single Value"
msgstr "単一値"

#: includes/fields/class-acf-field-taxonomy.php:773
msgid "Multi Select"
msgstr "複数選択"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Checkbox"
msgstr "チェックボックス"

#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Multiple Values"
msgstr "複数値"

#: includes/fields/class-acf-field-taxonomy.php:766
msgid "Select the appearance of this field"
msgstr "このフィールドの外観を選択"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Appearance"
msgstr "外観"

#: includes/fields/class-acf-field-taxonomy.php:707
msgid "Select the taxonomy to be displayed"
msgstr "表示するタクソノミーを選択"

#: includes/fields/class-acf-field-taxonomy.php:668
msgctxt "No Terms"
msgid "No %s"
msgstr ""

#: includes/fields/class-acf-field-number.php:263
msgid "Value must be equal to or lower than %d"
msgstr "値は%d文字以下である必要があります"

#: includes/fields/class-acf-field-number.php:256
msgid "Value must be equal to or higher than %d"
msgstr "値は%d文字以上である必要があります"

#: includes/fields/class-acf-field-number.php:241
msgid "Value must be a number"
msgstr "値は数字である必要があります"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "番号"

#: includes/fields/class-acf-field-radio.php:261
msgid "Save 'other' values to the field's choices"
msgstr "フィールドの選択肢として「その他」を保存する"

#: includes/fields/class-acf-field-radio.php:250
msgid "Add 'other' choice to allow for custom values"
msgstr "「その他」の選択肢を追加してカスタム値を許可"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "ラジオボタン"

#: includes/fields/class-acf-field-accordion.php:104
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"前のアコーディオンを停止するエンドポイントを定義します。このアコーディオンは"
"表示されません。"

#: includes/fields/class-acf-field-accordion.php:93
msgid "Allow this accordion to open without closing others."
msgstr ""
"他のアコーディオンを閉じずにこのアコーディオンを開くことができるようにする。"

#: includes/fields/class-acf-field-accordion.php:92
msgid "Multi-expand"
msgstr "マルチ展開"

#: includes/fields/class-acf-field-accordion.php:82
msgid "Display this accordion as open on page load."
msgstr "このアコーディオンをページの読み込み時に開いた状態で表示します。"

#: includes/fields/class-acf-field-accordion.php:81
msgid "Open"
msgstr "受付中"

#: includes/fields/class-acf-field-accordion.php:25
msgid "Accordion"
msgstr "アコーディオン"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-file.php:276
msgid "Restrict which files can be uploaded"
msgstr "アップロード可能なファイルを制限"

#: includes/fields/class-acf-field-file.php:217
msgid "File ID"
msgstr "ファイル ID"

#: includes/fields/class-acf-field-file.php:216
msgid "File URL"
msgstr "ファイルの URL"

#: includes/fields/class-acf-field-file.php:215
msgid "File Array"
msgstr "ファイル配列"

#: includes/fields/class-acf-field-file.php:183
msgid "Add File"
msgstr "ファイルを追加"

#: includes/admin/tools/class-acf-admin-tool-import.php:94
#: includes/fields/class-acf-field-file.php:183
msgid "No file selected"
msgstr "ファイルが選択されていません"

#: includes/fields/class-acf-field-file.php:147
msgid "File name"
msgstr "ファイル名"

#: includes/fields/class-acf-field-file.php:60
#: assets/build/js/acf-input.js:2466 assets/build/js/acf-input.js:2612
msgid "Update File"
msgstr "ファイルを更新"

#: includes/fields/class-acf-field-file.php:59
#: assets/build/js/acf-input.js:2465 assets/build/js/acf-input.js:2611
msgid "Edit File"
msgstr "ファイルを編集"

#: includes/admin/tools/class-acf-admin-tool-import.php:59
#: includes/fields/class-acf-field-file.php:58
#: assets/build/js/acf-input.js:2439 assets/build/js/acf-input.js:2584
msgid "Select File"
msgstr "ファイルを選択"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "ファイル"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "パスワード"

#: includes/fields/class-acf-field-select.php:391
msgid "Specify the value returned"
msgstr "戻り値を指定します"

#: includes/fields/class-acf-field-select.php:460
msgid "Use AJAX to lazy load choices?"
msgstr "AJAX を使用して選択肢を遅延読み込みしますか ?"

#: includes/fields/class-acf-field-checkbox.php:358
#: includes/fields/class-acf-field-select.php:380
msgid "Enter each default value on a new line"
msgstr "新しい行に各デフォルト値を入力してください"

#: includes/fields/class-acf-field-select.php:255 includes/media.php:48
#: assets/build/js/acf-input.js:6718 assets/build/js/acf-input.js:7174
msgctxt "verb"
msgid "Select"
msgstr "選択"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "読み込み失敗"

#: includes/fields/class-acf-field-select.php:117
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "検索中&hellip;"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "結果をさらに読み込み中&hellip;"

#: includes/fields/class-acf-field-select.php:115
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "%d項目のみ選択可能です"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "1項目のみ選択可能です"

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "%d文字を削除してください"

#: includes/fields/class-acf-field-select.php:112
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "1文字削除してください"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "%d文字以上を入力してください"

#: includes/fields/class-acf-field-select.php:110
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "1つ以上の文字を入力してください"

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "一致する項目がありません"

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d件の結果が見つかりました。上下矢印キーを使って移動してください。"

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "1件の結果が利用可能です。Enter を押して選択してください。"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:777
msgctxt "noun"
msgid "Select"
msgstr "選択"

#: includes/fields/class-acf-field-user.php:74
msgid "User ID"
msgstr "ユーザー ID"

#: includes/fields/class-acf-field-user.php:73
msgid "User Object"
msgstr "ユーザーオブジェクト"

#: includes/fields/class-acf-field-user.php:72
msgid "User Array"
msgstr "ユーザー配列"

#: includes/fields/class-acf-field-user.php:60
msgid "All user roles"
msgstr "すべてのユーザー権限グループ"

#: includes/fields/class-acf-field-user.php:52
msgid "Filter by role"
msgstr "権限グループで絞り込む"

#: includes/fields/class-acf-field-user.php:20 includes/locations.php:101
msgid "User"
msgstr "ユーザー"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "区切り"

#: includes/fields/class-acf-field-color_picker.php:73
msgid "Select Color"
msgstr "色を選択"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Default"
msgstr "デフォルト"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Clear"
msgstr "クリア"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "カラーピッカー"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "選択"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "完了"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "現在"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "タイムゾーン"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "マイクロ秒"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "ミリ秒"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "秒"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "分"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "時"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "時間"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "時間を選択"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "日時選択ツール"

#: includes/fields/class-acf-field-accordion.php:103
msgid "Endpoint"
msgstr "エンドポイント"

#: includes/admin/views/field-group-options.php:95
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "左揃え"

#: includes/admin/views/field-group-options.php:94
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "上揃え"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "配置"

#: includes/fields/class-acf-field-tab.php:26
msgid "Tab"
msgstr "タブ"

#: includes/fields/class-acf-field-url.php:159
msgid "Value must be a valid URL"
msgstr "値は有効な URL である必要があります"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "URL"

#: includes/fields/class-acf-field-link.php:174
msgid "Link URL"
msgstr "リンク URL"

#: includes/fields/class-acf-field-link.php:173
msgid "Link Array"
msgstr "リンク配列"

#: includes/fields/class-acf-field-link.php:142
msgid "Opens in a new window/tab"
msgstr "新しいウィンドウまたはタブで開く"

#: includes/fields/class-acf-field-link.php:137
msgid "Select Link"
msgstr "リンクを選択"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "リンク"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "メール"

#: includes/fields/class-acf-field-number.php:185
#: includes/fields/class-acf-field-range.php:214
msgid "Step Size"
msgstr "ステップサイズ"

#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-range.php:192
msgid "Maximum Value"
msgstr "最大値"

#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-range.php:181
msgid "Minimum Value"
msgstr "最小値"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "範囲"

#: includes/fields/class-acf-field-button-group.php:172
#: includes/fields/class-acf-field-checkbox.php:375
#: includes/fields/class-acf-field-radio.php:217
#: includes/fields/class-acf-field-select.php:398
msgid "Both (Array)"
msgstr "両方 (配列)"

#: includes/admin/views/field-group-fields.php:41
#: includes/fields/class-acf-field-button-group.php:171
#: includes/fields/class-acf-field-checkbox.php:374
#: includes/fields/class-acf-field-radio.php:216
#: includes/fields/class-acf-field-select.php:397
msgid "Label"
msgstr "ラベル"

#: includes/fields/class-acf-field-button-group.php:170
#: includes/fields/class-acf-field-checkbox.php:373
#: includes/fields/class-acf-field-radio.php:215
#: includes/fields/class-acf-field-select.php:396
msgid "Value"
msgstr "値"

#: includes/fields/class-acf-field-button-group.php:219
#: includes/fields/class-acf-field-checkbox.php:437
#: includes/fields/class-acf-field-radio.php:289
msgid "Vertical"
msgstr "垂直"

#: includes/fields/class-acf-field-button-group.php:218
#: includes/fields/class-acf-field-checkbox.php:438
#: includes/fields/class-acf-field-radio.php:290
msgid "Horizontal"
msgstr "水平"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "red : Red"
msgstr "red : Red"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "For more control, you may specify both a value and label like this:"
msgstr "下記のように記述すると、値とラベルの両方を制御することができます:"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "Enter each choice on a new line."
msgstr "選択肢を改行で区切って入力してください。"

#: includes/fields/class-acf-field-button-group.php:144
#: includes/fields/class-acf-field-checkbox.php:347
#: includes/fields/class-acf-field-radio.php:189
#: includes/fields/class-acf-field-select.php:368
msgid "Choices"
msgstr "選択肢"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "ボタングループ"

#: includes/fields/class-acf-field-page_link.php:488
#: includes/fields/class-acf-field-post_object.php:404
#: includes/fields/class-acf-field-select.php:406
#: includes/fields/class-acf-field-user.php:83
msgid "Select multiple values?"
msgstr "複数値を選択可能にしますか ?"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-page_link.php:509
#: includes/fields/class-acf-field-post_object.php:426
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:428
#: includes/fields/class-acf-field-taxonomy.php:786
#: includes/fields/class-acf-field-user.php:104
msgid "Allow Null?"
msgstr "空の値を許可しますか ?"

#: includes/fields/class-acf-field-page_link.php:249
#: includes/fields/class-acf-field-post_object.php:250
#: includes/fields/class-acf-field-taxonomy.php:932
msgid "Parent"
msgstr "親"

#: includes/fields/class-acf-field-wysiwyg.php:394
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "フィールドがクリックされるまで TinyMCE は初期化されません"

#: includes/fields/class-acf-field-wysiwyg.php:393
msgid "Delay initialization?"
msgstr "初期化を遅延させますか ?"

#: includes/fields/class-acf-field-wysiwyg.php:382
msgid "Show Media Upload Buttons?"
msgstr "メディアアップロードボタンを表示しますか ?"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Toolbar"
msgstr "ツールバー"

#: includes/fields/class-acf-field-wysiwyg.php:358
msgid "Text Only"
msgstr "テキストのみ"

#: includes/fields/class-acf-field-wysiwyg.php:357
msgid "Visual Only"
msgstr "ビジュアルのみ"

#: includes/fields/class-acf-field-wysiwyg.php:356
msgid "Visual & Text"
msgstr "ビジュアルとテキスト"

#: includes/fields/class-acf-field-wysiwyg.php:351
msgid "Tabs"
msgstr "タブ"

#: includes/fields/class-acf-field-wysiwyg.php:289
msgid "Click to initialize TinyMCE"
msgstr "クリックして TinyMCE を初期化"

#: includes/fields/class-acf-field-wysiwyg.php:283
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "テキスト"

#: includes/fields/class-acf-field-wysiwyg.php:282
msgid "Visual"
msgstr "ビジュアル"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "WYSIWYG エディター"

#: includes/fields/class-acf-field-text.php:180
#: includes/fields/class-acf-field-textarea.php:233
msgid "Value must not exceed %d characters"
msgstr "値は%d文字以内である必要があります"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "制限しない場合は空白にする"

#: includes/fields/class-acf-field-text.php:114
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "文字数制限"

#: includes/fields/class-acf-field-email.php:155
#: includes/fields/class-acf-field-number.php:206
#: includes/fields/class-acf-field-password.php:102
#: includes/fields/class-acf-field-range.php:236
#: includes/fields/class-acf-field-text.php:155
msgid "Appears after the input"
msgstr "入力内容の後に表示"

#: includes/fields/class-acf-field-email.php:154
#: includes/fields/class-acf-field-number.php:205
#: includes/fields/class-acf-field-password.php:101
#: includes/fields/class-acf-field-range.php:235
#: includes/fields/class-acf-field-text.php:154
msgid "Append"
msgstr "追加"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:196
#: includes/fields/class-acf-field-password.php:92
#: includes/fields/class-acf-field-range.php:226
#: includes/fields/class-acf-field-text.php:145
msgid "Appears before the input"
msgstr "入力内容の前に表示"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:195
#: includes/fields/class-acf-field-password.php:91
#: includes/fields/class-acf-field-range.php:225
#: includes/fields/class-acf-field-text.php:144
msgid "Prepend"
msgstr "先頭に追加"

#: includes/fields/class-acf-field-email.php:135
#: includes/fields/class-acf-field-number.php:176
#: includes/fields/class-acf-field-password.php:82
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:153
#: includes/fields/class-acf-field-url.php:119
msgid "Appears within the input"
msgstr "入力内容の中に表示"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:175
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-text.php:134
#: includes/fields/class-acf-field-textarea.php:152
#: includes/fields/class-acf-field-url.php:118
msgid "Placeholder Text"
msgstr "プレースホルダーテキスト"

#: includes/fields/class-acf-field-button-group.php:155
#: includes/fields/class-acf-field-email.php:115
#: includes/fields/class-acf-field-number.php:126
#: includes/fields/class-acf-field-radio.php:200
#: includes/fields/class-acf-field-range.php:162
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:101
#: includes/fields/class-acf-field-url.php:99
#: includes/fields/class-acf-field-wysiwyg.php:316
msgid "Appears when creating a new post"
msgstr "新規投稿作成時に表示"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "テキスト"

#: includes/fields/class-acf-field-relationship.php:760
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$sは%2$s個以上選択する必要があります"

#: includes/fields/class-acf-field-post_object.php:395
#: includes/fields/class-acf-field-relationship.php:622
msgid "Post ID"
msgstr "投稿 ID"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:394
#: includes/fields/class-acf-field-relationship.php:621
msgid "Post Object"
msgstr "投稿オブジェクト"

#: includes/fields/class-acf-field-relationship.php:654
msgid "Maximum posts"
msgstr "最大投稿"

#: includes/fields/class-acf-field-relationship.php:644
msgid "Minimum posts"
msgstr "最小投稿"

#: includes/admin/views/field-group-options.php:148
#: includes/fields/class-acf-field-relationship.php:679
msgid "Featured Image"
msgstr "アイキャッチ画像"

#: includes/fields/class-acf-field-relationship.php:675
msgid "Selected elements will be displayed in each result"
msgstr "選択した要素がそれぞれの結果に表示されます"

#: includes/fields/class-acf-field-relationship.php:674
msgid "Elements"
msgstr "要素"

#: includes/fields/class-acf-field-relationship.php:608
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:706
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "タクソノミー"

#: includes/fields/class-acf-field-relationship.php:607
#: includes/locations/class-acf-location-post-type.php:22
msgid "Post Type"
msgstr "投稿タイプ"

#: includes/fields/class-acf-field-relationship.php:601
msgid "Filters"
msgstr "フィルター"

#: includes/fields/class-acf-field-page_link.php:470
#: includes/fields/class-acf-field-post_object.php:382
#: includes/fields/class-acf-field-relationship.php:594
msgid "All taxonomies"
msgstr "すべてのタクソノミー"

#: includes/fields/class-acf-field-page_link.php:462
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:586
msgid "Filter by Taxonomy"
msgstr "タクソノミーで絞り込み"

#: includes/fields/class-acf-field-page_link.php:455
#: includes/fields/class-acf-field-post_object.php:367
#: includes/fields/class-acf-field-relationship.php:579
msgid "All post types"
msgstr "すべての投稿タイプ"

#: includes/fields/class-acf-field-page_link.php:447
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:571
msgid "Filter by Post Type"
msgstr "投稿タイプでフィルター"

#: includes/fields/class-acf-field-relationship.php:469
msgid "Search..."
msgstr "検索…"

#: includes/fields/class-acf-field-relationship.php:399
msgid "Select taxonomy"
msgstr "タクソノミーを選択"

#: includes/fields/class-acf-field-relationship.php:390
msgid "Select post type"
msgstr "投稿タイプを選択"

#: includes/fields/class-acf-field-relationship.php:65
#: assets/build/js/acf-input.js:3917 assets/build/js/acf-input.js:4195
msgid "No matches found"
msgstr "一致する項目がありません"

#: includes/fields/class-acf-field-relationship.php:64
#: assets/build/js/acf-input.js:3900 assets/build/js/acf-input.js:4174
msgid "Loading"
msgstr "読み込み中"

#: includes/fields/class-acf-field-relationship.php:63
#: assets/build/js/acf-input.js:3809 assets/build/js/acf-input.js:4070
msgid "Maximum values reached ( {max} values )"
msgstr "最大値 ({max}) に達しました"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "関係"

#: includes/fields/class-acf-field-file.php:288
#: includes/fields/class-acf-field-image.php:314
msgid "Comma separated list. Leave blank for all types"
msgstr "カンマ区切りのリスト。すべてのタイプを許可する場合は空白のままにします"

#: includes/fields/class-acf-field-file.php:287
#: includes/fields/class-acf-field-image.php:313
msgid "Allowed file types"
msgstr "許可されたファイルの種類"

#: includes/fields/class-acf-field-file.php:275
#: includes/fields/class-acf-field-image.php:277
msgid "Maximum"
msgstr "最大"

#: includes/fields/class-acf-field-file.php:151
#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-file.php:279
#: includes/fields/class-acf-field-image.php:268
#: includes/fields/class-acf-field-image.php:304
msgid "File size"
msgstr "ファイルサイズ"

#: includes/fields/class-acf-field-image.php:242
#: includes/fields/class-acf-field-image.php:278
msgid "Restrict which images can be uploaded"
msgstr "アップロード可能な画像を制限"

#: includes/fields/class-acf-field-file.php:263
#: includes/fields/class-acf-field-image.php:241
msgid "Minimum"
msgstr "最小"

#: includes/fields/class-acf-field-file.php:232
#: includes/fields/class-acf-field-image.php:207
msgid "Uploaded to post"
msgstr "投稿にアップロード"

#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:206
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "すべて"

#: includes/fields/class-acf-field-file.php:226
#: includes/fields/class-acf-field-image.php:201
msgid "Limit the media library choice"
msgstr "メディアライブラリの選択肢を制限"

#: includes/fields/class-acf-field-file.php:225
#: includes/fields/class-acf-field-image.php:200
msgid "Library"
msgstr "ライブラリ"

#: includes/fields/class-acf-field-image.php:333
msgid "Preview Size"
msgstr "プレビューサイズ"

#: includes/fields/class-acf-field-image.php:192
msgid "Image ID"
msgstr "画像 ID"

#: includes/fields/class-acf-field-image.php:191
msgid "Image URL"
msgstr "画像 URL"

#: includes/fields/class-acf-field-image.php:190
msgid "Image Array"
msgstr "画像配列"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:368
#: includes/fields/class-acf-field-file.php:210
#: includes/fields/class-acf-field-link.php:168
#: includes/fields/class-acf-field-radio.php:210
msgid "Specify the returned value on front end"
msgstr "フロントエンドへの返り値を指定してください"

#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:367
#: includes/fields/class-acf-field-file.php:209
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-taxonomy.php:750
msgid "Return Value"
msgstr "返り値"

#: includes/fields/class-acf-field-image.php:159
msgid "Add Image"
msgstr "画像を追加"

#: includes/fields/class-acf-field-image.php:159
msgid "No image selected"
msgstr "画像が選択されていません"

#: includes/assets.php:351 includes/fields/class-acf-field-file.php:159
#: includes/fields/class-acf-field-image.php:139
#: includes/fields/class-acf-field-link.php:142 assets/build/js/acf.js:1523
#: assets/build/js/acf.js:1603
msgid "Remove"
msgstr "削除"

#: includes/admin/views/field-group-field.php:65
#: includes/fields/class-acf-field-file.php:157
#: includes/fields/class-acf-field-image.php:137
#: includes/fields/class-acf-field-link.php:142
msgid "Edit"
msgstr "編集"

#: includes/fields/class-acf-field-image.php:67 includes/media.php:55
#: assets/build/js/acf-input.js:6765 assets/build/js/acf-input.js:7228
msgid "All images"
msgstr "すべての画像"

#: includes/fields/class-acf-field-image.php:66
#: assets/build/js/acf-input.js:3173 assets/build/js/acf-input.js:3386
msgid "Update Image"
msgstr "画像を更新"

#: includes/fields/class-acf-field-image.php:65
#: assets/build/js/acf-input.js:3172 assets/build/js/acf-input.js:3385
msgid "Edit Image"
msgstr "画像を編集"

#: includes/fields/class-acf-field-image.php:64
#: assets/build/js/acf-input.js:3148 assets/build/js/acf-input.js:3360
msgid "Select Image"
msgstr "画像を選択"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "画像"

#: includes/fields/class-acf-field-message.php:123
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "HTML マークアップのコードとして表示を許可"

#: includes/fields/class-acf-field-message.php:122
msgid "Escape HTML"
msgstr "HTML をエスケープ"

#: includes/fields/class-acf-field-message.php:114
#: includes/fields/class-acf-field-textarea.php:169
msgid "No Formatting"
msgstr "書式設定なし"

#: includes/fields/class-acf-field-message.php:113
#: includes/fields/class-acf-field-textarea.php:168
msgid "Automatically add &lt;br&gt;"
msgstr "自動的に &lt;br&gt; を追加"

#: includes/fields/class-acf-field-message.php:112
#: includes/fields/class-acf-field-textarea.php:167
msgid "Automatically add paragraphs"
msgstr "自動的に段落追加する"

#: includes/fields/class-acf-field-message.php:108
#: includes/fields/class-acf-field-textarea.php:163
msgid "Controls how new lines are rendered"
msgstr "改行をどのように表示するか制御"

#: includes/fields/class-acf-field-message.php:107
#: includes/fields/class-acf-field-textarea.php:162
msgid "New Lines"
msgstr "改行"

#: includes/fields/class-acf-field-date_picker.php:229
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "週の始まり"

#: includes/fields/class-acf-field-date_picker.php:198
msgid "The format used when saving a value"
msgstr "値を保存するときに使用される形式"

#: includes/fields/class-acf-field-date_picker.php:197
msgid "Save Format"
msgstr "書式を保存"

#: includes/fields/class-acf-field-date_picker.php:64
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Wk"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "前へ"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "次へ"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "今日"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "完了"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "日付選択ツール"

#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:281
#: includes/fields/class-acf-field-oembed.php:265
msgid "Width"
msgstr "幅"

#: includes/fields/class-acf-field-oembed.php:262
#: includes/fields/class-acf-field-oembed.php:274
msgid "Embed Size"
msgstr "埋め込みサイズ"

#: includes/fields/class-acf-field-oembed.php:219
msgid "Enter URL"
msgstr "URL を入力"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:181
msgid "Text shown when inactive"
msgstr "無効化時に表示されるテキスト"

#: includes/fields/class-acf-field-true_false.php:180
msgid "Off Text"
msgstr "無効化時のテキスト"

#: includes/fields/class-acf-field-true_false.php:165
msgid "Text shown when active"
msgstr "有効時に表示するテキスト"

#: includes/fields/class-acf-field-true_false.php:164
msgid "On Text"
msgstr "アクティブ時のテキスト"

#: includes/fields/class-acf-field-select.php:449
#: includes/fields/class-acf-field-true_false.php:196
msgid "Stylized UI"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:154
#: includes/fields/class-acf-field-checkbox.php:357
#: includes/fields/class-acf-field-color_picker.php:155
#: includes/fields/class-acf-field-email.php:114
#: includes/fields/class-acf-field-number.php:125
#: includes/fields/class-acf-field-radio.php:199
#: includes/fields/class-acf-field-range.php:161
#: includes/fields/class-acf-field-select.php:379
#: includes/fields/class-acf-field-text.php:94
#: includes/fields/class-acf-field-textarea.php:100
#: includes/fields/class-acf-field-true_false.php:144
#: includes/fields/class-acf-field-url.php:98
#: includes/fields/class-acf-field-wysiwyg.php:315
msgid "Default Value"
msgstr "初期値"

#: includes/fields/class-acf-field-true_false.php:135
msgid "Displays text alongside the checkbox"
msgstr "チェックボックスの横にテキストを表示"

#: includes/fields/class-acf-field-message.php:26
#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-true_false.php:134
msgid "Message"
msgstr "メッセージ"

#: includes/assets.php:350 includes/fields/class-acf-field-true_false.php:83
#: includes/fields/class-acf-field-true_false.php:184
#: assets/build/js/acf.js:1700 assets/build/js/acf.js:1803
msgid "No"
msgstr "いいえ"

#: includes/assets.php:349 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:168
#: assets/build/js/acf.js:1699 assets/build/js/acf.js:1802
msgid "Yes"
msgstr "はい"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "真/偽"

#: includes/fields/class-acf-field-group.php:471
msgid "Row"
msgstr "行"

#: includes/fields/class-acf-field-group.php:470
msgid "Table"
msgstr "テーブル"

#: includes/fields/class-acf-field-group.php:469
msgid "Block"
msgstr "ブロック"

#: includes/fields/class-acf-field-group.php:464
msgid "Specify the style used to render the selected fields"
msgstr "選択したフィールドのレンダリングに使用されるスタイルを指定します"

#: includes/fields.php:359 includes/fields/class-acf-field-button-group.php:212
#: includes/fields/class-acf-field-checkbox.php:431
#: includes/fields/class-acf-field-group.php:463
#: includes/fields/class-acf-field-radio.php:283
msgid "Layout"
msgstr "レイアウト"

#: includes/fields/class-acf-field-group.php:447
msgid "Sub Fields"
msgstr "サブフィールド"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "グループ"

#: includes/fields/class-acf-field-google-map.php:232
msgid "Customize the map height"
msgstr "地図の高さをカスタマイズ"

#: includes/fields/class-acf-field-google-map.php:231
#: includes/fields/class-acf-field-image.php:256
#: includes/fields/class-acf-field-image.php:292
#: includes/fields/class-acf-field-oembed.php:277
msgid "Height"
msgstr "高さ"

#: includes/fields/class-acf-field-google-map.php:220
msgid "Set the initial zoom level"
msgstr "地図のデフォルトズームレベルを設定"

#: includes/fields/class-acf-field-google-map.php:219
msgid "Zoom"
msgstr "ズーム"

#: includes/fields/class-acf-field-google-map.php:193
#: includes/fields/class-acf-field-google-map.php:206
msgid "Center the initial map"
msgstr "地図のデフォルト中心位置を設定"

#: includes/fields/class-acf-field-google-map.php:192
#: includes/fields/class-acf-field-google-map.php:205
msgid "Center"
msgstr "中央"

#: includes/fields/class-acf-field-google-map.php:160
msgid "Search for address..."
msgstr "住所を検索…"

#: includes/fields/class-acf-field-google-map.php:157
msgid "Find current location"
msgstr "現在の場所を検索"

#: includes/fields/class-acf-field-google-map.php:156
msgid "Clear location"
msgstr "位置情報をクリア"

#: includes/fields/class-acf-field-google-map.php:155
#: includes/fields/class-acf-field-relationship.php:606
msgid "Search"
msgstr "検索"

#: includes/fields/class-acf-field-google-map.php:60
#: assets/build/js/acf-input.js:2832 assets/build/js/acf-input.js:3013
msgid "Sorry, this browser does not support geolocation"
msgstr "お使いのブラウザーは位置情報機能に対応していません"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google マップ"

#: includes/fields/class-acf-field-date_picker.php:209
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:129
msgid "The format returned via template functions"
msgstr "テンプレート関数で返されるフォーマット"

#: includes/fields/class-acf-field-color_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:197
#: includes/fields/class-acf-field-image.php:184
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:616
#: includes/fields/class-acf-field-select.php:390
#: includes/fields/class-acf-field-time_picker.php:128
#: includes/fields/class-acf-field-user.php:67
msgid "Return Format"
msgstr "戻り値の形式"

#: includes/fields/class-acf-field-date_picker.php:187
#: includes/fields/class-acf-field-date_picker.php:218
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-date_time_picker.php:207
#: includes/fields/class-acf-field-time_picker.php:120
#: includes/fields/class-acf-field-time_picker.php:136
msgid "Custom:"
msgstr "カスタム:"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-time_picker.php:113
msgid "The format displayed when editing a post"
msgstr "投稿編集時に表示される書式"

#: includes/fields/class-acf-field-date_picker.php:178
#: includes/fields/class-acf-field-date_time_picker.php:179
#: includes/fields/class-acf-field-time_picker.php:112
msgid "Display Format"
msgstr "表示形式"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "時間選択ツール"

#. translators: counts for inactive field groups
#: acf.php:454
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] ""

#: acf.php:413
msgid "No Fields found in Trash"
msgstr "ゴミ箱にフィールドが見つかりません"

#: acf.php:412
msgid "No Fields found"
msgstr "フィールドが見つかりません"

#: acf.php:411
msgid "Search Fields"
msgstr "フィールドを検索"

#: acf.php:410
msgid "View Field"
msgstr "フィールドを表示"

#: acf.php:409 includes/admin/views/field-group-fields.php:104
msgid "New Field"
msgstr "新規フィールド"

#: acf.php:408
msgid "Edit Field"
msgstr "フィールドを編集"

#: acf.php:407
msgid "Add New Field"
msgstr "新規フィールドを追加"

#: acf.php:405
msgid "Field"
msgstr "フィールド"

#: acf.php:404 includes/admin/admin-field-group.php:220
#: includes/admin/admin-field-groups.php:287
#: includes/admin/views/field-group-fields.php:21
msgid "Fields"
msgstr "フィールド"

#: acf.php:379
msgid "No Field Groups found in Trash"
msgstr "ゴミ箱にフィールドグループが見つかりません"

#: acf.php:378
msgid "No Field Groups found"
msgstr "フィールドグループが見つかりません"

#: acf.php:377
msgid "Search Field Groups"
msgstr "フィールドグループを検索"

#: acf.php:376
msgid "View Field Group"
msgstr "フィールドグループを表示"

#: acf.php:375
msgid "New Field Group"
msgstr "新規フィールドグループ"

#: acf.php:374
msgid "Edit Field Group"
msgstr "フィールドグループを編集"

#: acf.php:373
msgid "Add New Field Group"
msgstr "新規フィールドグループを追加"

#: acf.php:372 acf.php:406 includes/admin/admin.php:51
msgid "Add New"
msgstr "新規追加"

#: acf.php:371
msgid "Field Group"
msgstr "フィールドグループ"

#: acf.php:370 includes/admin/admin.php:50
msgid "Field Groups"
msgstr "フィールドグループ"

#. Description of the plugin
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"パワフル、プロフェッショナル、直感的なフィールドで WordPress をカスタマイズ。"

#. Plugin URI of the plugin
#. Author URI of the plugin
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php:91
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:166
msgid "Block type name is required."
msgstr ""

#: pro/blocks.php:173
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:731
msgid "Switch to Edit"
msgstr ""

#: pro/blocks.php:732
msgid "Switch to Preview"
msgstr ""

#: pro/blocks.php:733
msgid "Change content alignment"
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:736
msgid "%s settings"
msgstr ""

#: pro/blocks.php:949
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:955
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "オプションを更新しました"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a href=\"%1$s"
"\">Updates</a> page. If you don't have a licence key, please see <a href="
"\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""

#: pro/updates.php:279, pro/admin/views/html-settings-updates.php:117
msgid "Check Again"
msgstr "再確認"

#: pro/updates.php:561
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "公開"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"このオプションページにカスタムフィールドグループがありません. <a href=\"%s\">"
"カスタムフィールドグループを作成</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>エラー</b> 更新サーバーに接続できません"

#: pro/admin/admin-updates.php:209
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""

#: pro/admin/admin-updates.php:196
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:815
msgid "Select one or more fields you wish to clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:834
msgid "Display"
msgstr "表示"

#: pro/fields/class-acf-field-clone.php:835
msgid "Specify the style used to render the clone field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:840
msgid "Group (displays selected fields in a group within this field)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:841
msgid "Seamless (replaces this field with selected fields)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:864
msgid "Labels will be displayed as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:869
msgid "Prefix Field Labels"
msgstr ""

#: pro/fields/class-acf-field-clone.php:880
msgid "Values will be saved as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:885
msgid "Prefix Field Names"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1001
msgid "Unknown field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1038
msgid "Unknown field group"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1042
msgid "All fields from %s field group"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:31,
#: pro/fields/class-acf-field-repeater.php:79,
#: pro/fields/class-acf-field-repeater.php:263
msgid "Add Row"
msgstr "行を追加"

#: pro/fields/class-acf-field-flexible-content.php:71,
#: pro/fields/class-acf-field-flexible-content.php:917,
#: pro/fields/class-acf-field-flexible-content.php:996
#, fuzzy
#| msgid "layout"
msgid "layout"
msgid_plural "layouts"
msgstr[0] "レイアウト"

#: pro/fields/class-acf-field-flexible-content.php:72
msgid "layouts"
msgstr "レイアウト"

#: pro/fields/class-acf-field-flexible-content.php:75,
#: pro/fields/class-acf-field-flexible-content.php:916,
#: pro/fields/class-acf-field-flexible-content.php:995
msgid "This field requires at least {min} {label} {identifier}"
msgstr "{identifier}に{label}は最低{min}個必要です"

#: pro/fields/class-acf-field-flexible-content.php:76
msgid "This field has a limit of {max} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:79
msgid "{available} {label} {identifier} available (max {max})"
msgstr ""
"あと{available}個 {identifier}には {label} を利用できます（最大 {max}個）"

#: pro/fields/class-acf-field-flexible-content.php:80
msgid "{required} {label} {identifier} required (min {min})"
msgstr ""
"あと{required}個 {identifier}には {label} を利用する必要があります（最小 "
"{max}個）"

#: pro/fields/class-acf-field-flexible-content.php:83
msgid "Flexible Content requires at least 1 layout"
msgstr "柔軟コンテンツは少なくとも1個のレイアウトが必要です"

#: pro/fields/class-acf-field-flexible-content.php:276
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "下の \"%s\" ボタンをクリックしてレイアウトの作成を始めてください"

#: pro/fields/class-acf-field-flexible-content.php:413
msgid "Add layout"
msgstr "レイアウトを追加"

#: pro/fields/class-acf-field-flexible-content.php:414
msgid "Duplicate layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:415
msgid "Remove layout"
msgstr "レイアウトを削除"

#: pro/fields/class-acf-field-flexible-content.php:416,
#: pro/fields/class-acf-repeater-table.php:369
msgid "Click to toggle"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder Layout"
msgstr "レイアウトを並べ替え"

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder"
msgstr "並べ替え"

#: pro/fields/class-acf-field-flexible-content.php:552
msgid "Delete Layout"
msgstr "レイアウトを削除"

#: pro/fields/class-acf-field-flexible-content.php:553
msgid "Duplicate Layout"
msgstr "レイアウトを複製"

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Add New Layout"
msgstr "新しいレイアウトを追加"

#: pro/fields/class-acf-field-flexible-content.php:635
msgid "Min"
msgstr "最小数"

#: pro/fields/class-acf-field-flexible-content.php:650
msgid "Max"
msgstr "最大数"

#: pro/fields/class-acf-field-flexible-content.php:679,
#: pro/fields/class-acf-field-repeater.php:259
msgid "Button Label"
msgstr "ボタンのラベル"

#: pro/fields/class-acf-field-flexible-content.php:690
msgid "Minimum Layouts"
msgstr "レイアウトの最小数"

#: pro/fields/class-acf-field-flexible-content.php:701
msgid "Maximum Layouts"
msgstr "レイアウトの最大数"

#: pro/fields/class-acf-field-flexible-content.php:1704,
#: pro/fields/class-acf-field-repeater.php:861
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1715
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""

#: pro/fields/class-acf-field-flexible-content.php:1731
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""

#: pro/fields/class-acf-field-gallery.php:73
msgid "Add Image to Gallery"
msgstr "ギャラリーに画像を追加"

#: pro/fields/class-acf-field-gallery.php:74
msgid "Maximum selection reached"
msgstr "選択の最大数に到達しました"

#: pro/fields/class-acf-field-gallery.php:320
msgid "Length"
msgstr "長さ"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:376
msgid "Alt Text"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:500
msgid "Add to gallery"
msgstr "ギャラリーを追加"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Bulk actions"
msgstr "一括操作"

#: pro/fields/class-acf-field-gallery.php:505
msgid "Sort by date uploaded"
msgstr "アップロード日で並べ替え"

#: pro/fields/class-acf-field-gallery.php:506
msgid "Sort by date modified"
msgstr "変更日で並び替え"

#: pro/fields/class-acf-field-gallery.php:507
msgid "Sort by title"
msgstr "タイトルで並び替え"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Reverse current order"
msgstr "並び順を逆にする"

#: pro/fields/class-acf-field-gallery.php:520
msgid "Close"
msgstr "閉じる"

#: pro/fields/class-acf-field-gallery.php:602
msgid "Insert"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:603
msgid "Specify where new attachments are added"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:607
msgid "Append to the end"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:608
msgid "Prepend to the beginning"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:633
msgid "Minimum Selection"
msgstr "最小選択数"

#: pro/fields/class-acf-field-gallery.php:644
msgid "Maximum Selection"
msgstr "最大選択数"

#: pro/fields/class-acf-field-repeater.php:53,
#: pro/fields/class-acf-field-repeater.php:423
msgid "Minimum rows reached ({min} rows)"
msgstr "最小行数に達しました（{min} 行）"

#: pro/fields/class-acf-field-repeater.php:54
msgid "Maximum rows reached ({max} rows)"
msgstr "最大行数に達しました（{max} 行）"

#: pro/fields/class-acf-field-repeater.php:55
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:174
msgid "Collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:175
msgid "Select a sub field to show when row is collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:187
msgid "Minimum Rows"
msgstr "最小行数"

#: pro/fields/class-acf-field-repeater.php:199
msgid "Maximum Rows"
msgstr "最大行数"

#: pro/fields/class-acf-field-repeater.php:228
msgid "Pagination"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:229
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:240
msgid "Rows Per Page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:241
msgid "Set the number of rows to be displayed on a page."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:959
msgid "Invalid field key."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:968
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:389
msgid "Add row"
msgstr "行を追加"

#: pro/fields/class-acf-repeater-table.php:390
msgid "Duplicate row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:391
msgid "Remove row"
msgstr "行を削除"

#: pro/fields/class-acf-repeater-table.php:435,
#: pro/fields/class-acf-repeater-table.php:452
msgid "Current Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:444
msgid "First page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:448
msgid "Previous page"
msgstr ""

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:457
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:465
msgid "Next page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:469
msgid "Last page"
msgstr ""

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr ""

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "オプションページはありません"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "ライセンスのアクティベートを解除"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "ライセンスをアクティベート"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr ""

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""

#: pro/admin/views/html-settings-updates.php:41
msgid "License Key"
msgstr "ライセンスキー"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr ""

#: pro/admin/views/html-settings-updates.php:76
msgid "Update Information"
msgstr "アップデート情報"

#: pro/admin/views/html-settings-updates.php:83
msgid "Current Version"
msgstr "現在のバージョン"

#: pro/admin/views/html-settings-updates.php:91
msgid "Latest Version"
msgstr "最新のバージョン"

#: pro/admin/views/html-settings-updates.php:99
msgid "Update Available"
msgstr "利用可能なアップデート"

#: pro/admin/views/html-settings-updates.php:111
msgid "Please enter your license key above to unlock updates"
msgstr "アップデートのロックを解除するためにライセンスキーを入力してください"

#: pro/admin/views/html-settings-updates.php:109
msgid "Update Plugin"
msgstr "プラグインをアップデート"

#: pro/admin/views/html-settings-updates.php:107
msgid "Please reactivate your license to unlock updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:124
msgid "Changelog"
msgstr "更新履歴"

#: pro/admin/views/html-settings-updates.php:134
msgid "Upgrade Notice"
msgstr "アップグレード通知"

#~ msgid "Gallery Field"
#~ msgstr "ギャラリーフィールド"

#~ msgid "Flexible Content Field"
#~ msgstr "柔軟コンテンツフィールド"

#~ msgid "Repeater Field"
#~ msgstr "繰り返しフィールド"

#~ msgid "Disabled"
#~ msgstr "無効状態"

#, php-format
#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "無効状態 <span class=\"count\">(%s)</span>"

#~ msgid "Move to trash. Are you sure?"
#~ msgstr "ゴミ箱に移動します。よろしいですか？"

#~ msgid "checked"
#~ msgstr "チェック済み"

#~ msgid "Parent fields"
#~ msgstr "親フィールド"

#~ msgid "Sibling fields"
#~ msgstr "兄弟フィールド"

#, php-format
#~ msgid "The %s field can now be found in the %s field group"
#~ msgstr "この %s フィールドは今 %s フィールドグループにあります"

#~ msgid "Close Window"
#~ msgstr "ウィンドウを閉じる"

#, php-format
#~ msgid "Field group duplicated. %s"
#~ msgstr "フィールドグループを複製しました。 %s"

#, php-format
#~ msgid "%s field group duplicated."
#~ msgid_plural "%s field groups duplicated."
#~ msgstr[0] "%s個 のフィールドグループを複製しました。"

#, php-format
#~ msgid "Field group synchronised. %s"
#~ msgstr "フィールドグループを同期しました。%s"

#, php-format
#~ msgid "%s field group synchronised."
#~ msgid_plural "%s field groups synchronised."
#~ msgstr[0] "%s個 のフィールドグループを同期しました。"

#~ msgid "Status"
#~ msgstr "状態"

#~ msgid "See what's new in"
#~ msgstr "新着情報を見る"

#~ msgid "version"
#~ msgstr "バージョン"

#~ msgid "Resources"
#~ msgstr "リソース"

#~ msgid "Getting Started"
#~ msgstr "はじめに"

#~ msgid "Field Types"
#~ msgstr "フィールドタイプ"

#~ msgid "Functions"
#~ msgstr "ファンクション"

#~ msgid "Actions"
#~ msgstr "アクション"

#~ msgid "'How to' guides"
#~ msgstr "使い方ガイド"

#~ msgid "Tutorials"
#~ msgstr "チュートリアル"

#~ msgid "Created by"
#~ msgstr "作成"

#~ msgid "Synchronise field group"
#~ msgstr "フィールドグループを同期する"

#~ msgid "Add-ons"
#~ msgstr "アドオン"

#~ msgid "<b>Error</b>. Could not load add-ons list"
#~ msgstr "<b>エラー</b> アドオンのリストを読み込めませんでした"

#~ msgid "Info"
#~ msgstr "お知らせ"

#~ msgid "What's New"
#~ msgstr "新着情報"

#, php-format
#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr ""
#~ "<b>成功</b> インポートツールは %s個 のフィールドグループを追加しました：%s"

#, php-format
#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>警告</b> インポートツールは %s個 のフィールドグループが既に存在している"
#~ "のを検出したため無視しました：%s"

#~ msgid "Upgrade ACF"
#~ msgstr "ACFをアップグレード"

#~ msgid "Upgrade"
#~ msgstr "アップグレード"

#~ msgid "Error"
#~ msgstr "エラー"

#~ msgid "Error."
#~ msgstr "エラー."

#~ msgid "Required?"
#~ msgstr "必須か？"

#~ msgid ""
#~ "No fields. Click the <strong>+ Add Field</strong> button to create your "
#~ "first field."
#~ msgstr ""
#~ "フィールドはありません。<strong>+ 新規追加</strong>ボタンをクリックして最"
#~ "初のフィールドを作成してください。"

#~ msgid "Drag and drop to reorder"
#~ msgstr "ドラッグアンドドロップで並べ替える"

#~ msgid "+ Add Field"
#~ msgstr "+ フィールドを追加"

#~ msgid "Taxonomy Term"
#~ msgstr "タクソノミーターム"

#~ msgid "Download & Install"
#~ msgstr "ダウンロードしてインストール"

#~ msgid "Installed"
#~ msgstr "インストール済み"

#~ msgid "Welcome to Advanced Custom Fields"
#~ msgstr "ようこそ Advanced Custom Fields"

#, php-format
#~ msgid ""
#~ "Thank you for updating! ACF %s is bigger and better than ever before. We "
#~ "hope you like it."
#~ msgstr ""
#~ "アップグレードありがとうございます！ACF %s は規模、質ともに向上していま"
#~ "す。気に入ってもらえたら幸いです。"

#~ msgid "A smoother custom field experience"
#~ msgstr "もっとも快適なカスタムフィールド体験"

#~ msgid "Improved Usability"
#~ msgstr "改良されたユーザビリティ"

#~ msgid ""
#~ "Including the popular Select2 library has improved both usability and "
#~ "speed across a number of field types including post object, page link, "
#~ "taxonomy and select."
#~ msgstr ""
#~ "内蔵した人気のSelect2ライブラリによって、投稿オブジェクトやページリンク、"
#~ "タクソノミーなど多くのフィールドタイプにおける選択のユーザビリティと速度の"
#~ "両方を改善しました。"

#~ msgid "Improved Design"
#~ msgstr "改良されたデザイン"

#~ msgid ""
#~ "Many fields have undergone a visual refresh to make ACF look better than "
#~ "ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
#~ "(new) fields!"
#~ msgstr ""
#~ "ACFがより良くなるよう、多くのフィールドのデザインを一新しました！目立った"
#~ "変化は、ギャラリーフィールドや関連フィールド、（新しい）oEmbedフィールドで"
#~ "わかるでしょう！"

#~ msgid "Improved Data"
#~ msgstr "改良されたデータ"

#~ msgid ""
#~ "Redesigning the data architecture has allowed sub fields to live "
#~ "independently from their parents. This allows you to drag and drop fields "
#~ "in and out of parent fields!"
#~ msgstr ""
#~ "データ構造を再設計したことでサブフィールドは親フィールドから独立して存在で"
#~ "きるようになりました。これによって親フィールドの内外にフィールドをドラッグ"
#~ "アンドドロップできます!"

#~ msgid "Goodbye Add-ons. Hello PRO"
#~ msgstr "さようならアドオン、こんにちはPRO"

#~ msgid "Introducing ACF PRO"
#~ msgstr "ACF PRO紹介"

#~ msgid ""
#~ "We're changing the way premium functionality is delivered in an exciting "
#~ "way!"
#~ msgstr "我々はエキサイティングな方法で有料機能を提供することにしました！"

#, php-format
#~ msgid ""
#~ "All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
#~ "version of ACF</a>. With both personal and developer licenses available, "
#~ "premium functionality is more affordable and accessible than ever before!"
#~ msgstr ""
#~ "4つのアドオンを<a href=\"%s\">ACFのPROバージョン</a>として組み合わせまし"
#~ "た。個人または開発者ライセンスによって、以前よりお手頃な価格で有料機能を利"
#~ "用できます！"

#~ msgid "Powerful Features"
#~ msgstr "パワフルな機能"

#~ msgid ""
#~ "ACF PRO contains powerful features such as repeatable data, flexible "
#~ "content layouts, a beautiful gallery field and the ability to create "
#~ "extra admin options pages!"
#~ msgstr ""
#~ "ACF PROには、繰り返し可能なデータ、柔軟なコンテンツレイアウト、美しいギャ"
#~ "ラリーフィールド、オプションページを作成するなど、パワフルな機能が含まれて"
#~ "います！"

#, php-format
#~ msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
#~ msgstr "もっと<a href=\"%s\">ACF PRO の機能</a>を見る。"

#~ msgid "Easy Upgrading"
#~ msgstr "簡単なアップグレード"

#, php-format
#~ msgid ""
#~ "To help make upgrading easy, <a href=\"%s\">login to your store account</"
#~ "a> and claim a free copy of ACF PRO!"
#~ msgstr ""
#~ "アップグレードを簡単にするために、<a href=\"%s\">ストアアカウントにログイ"
#~ "ン</a>してACF PROの無料版を請求してください!"

#, php-format
#~ msgid ""
#~ "We also wrote an <a href=\"%s\">upgrade guide</a> to answer any "
#~ "questions, but if you do have one, please contact our support team via "
#~ "the <a href=\"%s\">help desk</a>"
#~ msgstr ""
#~ "我々は多くの質問に応えるために<a href=\"%s\">アップグレードガイド</a>を用"
#~ "意していますが、もし質問がある場合は<a href=\"%s\">ヘルプデスク</a>からサ"
#~ "ポートチームに連絡をしてください"

#~ msgid "Under the Hood"
#~ msgstr "その内部では"

#~ msgid "Smarter field settings"
#~ msgstr "よりスマートなフィールド設定"

#~ msgid "ACF now saves its field settings as individual post objects"
#~ msgstr ""
#~ "ACFはそれぞれのフィールドを独立した投稿オブジェクトとして保存するようにな"
#~ "りました"

#~ msgid "More AJAX"
#~ msgstr "いっそうAJAXに"

#~ msgid "More fields use AJAX powered search to speed up page loading"
#~ msgstr ""
#~ "ページの読み込み速度を高速化するために、より多くのフィールドがAJAXを利用す"
#~ "るようになりました"

#~ msgid "New auto export to JSON feature improves speed"
#~ msgstr "新しいJSON形式の自動エクスポート機能の速度を改善"

#~ msgid "Better version control"
#~ msgstr "より良いバージョンコントロール"

#~ msgid ""
#~ "New auto export to JSON feature allows field settings to be version "
#~ "controlled"
#~ msgstr ""
#~ "新しいJSON形式の自動エクスポート機能は、フィールド設定のバージョンコント"
#~ "ロールを可能にします"

#~ msgid "Swapped XML for JSON"
#~ msgstr "XMLからJSONへ"

#~ msgid "Import / Export now uses JSON in favour of XML"
#~ msgstr "インポート / エクスポートにXML形式より優れているJSON形式が使えます"

#~ msgid "New Forms"
#~ msgstr "新しいフォーム"

#~ msgid "Fields can now be mapped to comments, widgets and all user forms!"
#~ msgstr ""
#~ "コメントとウィジェット、全てのユーザーのフォームにフィールドを追加できるよ"
#~ "うになりました!"

#~ msgid "A new field for embedding content has been added"
#~ msgstr "新しいフィールドに「oEmbed（埋め込みコンテンツ）」を追加しています"

#~ msgid "New Gallery"
#~ msgstr "新しいギャラリー"

#~ msgid "The gallery field has undergone a much needed facelift"
#~ msgstr "ギャラリーフィールドは多くのマイナーチェンジをしています"

#~ msgid "New Settings"
#~ msgstr "新しい設定"

#~ msgid ""
#~ "Field group settings have been added for label placement and instruction "
#~ "placement"
#~ msgstr ""
#~ "フィールドグループの設定に「ラベルの配置」と「説明の配置」を追加しています"

#~ msgid "Better Front End Forms"
#~ msgstr "より良いフロントエンドフォーム"

#~ msgid "acf_form() can now create a new post on submission"
#~ msgstr ""
#~ "acf_form()は新しい投稿をフロントエンドから作成できるようになりました"

#~ msgid "Better Validation"
#~ msgstr "より良いバリデーション"

#~ msgid "Form validation is now done via PHP + AJAX in favour of only JS"
#~ msgstr ""
#~ "フォームバリデーションは、JSのみより優れているPHP + AJAXで行われます"

#~ msgid "Relationship Field"
#~ msgstr "関連フィールド"

#~ msgid ""
#~ "New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
#~ msgstr ""
#~ "関連フィールドの新しい設定「フィルター」（検索、投稿タイプ、タクソノ"
#~ "ミー）。"

#~ msgid "Moving Fields"
#~ msgstr "フィールド移動"

#~ msgid ""
#~ "New field group functionality allows you to move a field between groups & "
#~ "parents"
#~ msgstr ""
#~ "新しいフィールドグループでは、フィールドが親フィールドやフィールドグループ"
#~ "間を移動することができます"

#~ msgid "New archives group in page_link field selection"
#~ msgstr "新しいページリンクの選択肢に「アーカイブグループ」を追加しています"

#~ msgid "Better Options Pages"
#~ msgstr "より良いオプションページ"

#~ msgid ""
#~ "New functions for options page allow creation of both parent and child "
#~ "menu pages"
#~ msgstr ""
#~ "オプションページの新しい機能として、親と子の両方のメニューページを作ること"
#~ "ができます"

#, php-format
#~ msgid "We think you'll love the changes in %s."
#~ msgstr "%s の変更は、きっと気に入っていただけるでしょう。"

#~ msgid "Export Field Groups to PHP"
#~ msgstr "フィールドグループを PHP形式 でエクスポートする"

#~ msgid ""
#~ "Select the field groups you would like to export and then select your "
#~ "export method. Use the download button to export to a .json file which "
#~ "you can then import to another ACF installation. Use the generate button "
#~ "to export to PHP code which you can place in your theme."
#~ msgstr ""
#~ "エクスポートしたいフィールドグループとエクスポート方法を選んでください。ダ"
#~ "ウンロードボタンでは別のACFをインストールした環境でインポートできるJSON"
#~ "ファイルがエクスポートされます。生成ボタンではテーマ内で利用できるPHPコー"
#~ "ドが生成されます。"

#~ msgid "Download export file"
#~ msgstr "エクスポートファイルをダウンロード"

#~ msgid "Generate export code"
#~ msgstr "エクスポートコードを生成"

#~ msgid ""
#~ "Select the Advanced Custom Fields JSON file you would like to import. "
#~ "When you click the import button below, ACF will import the field groups."
#~ msgstr ""
#~ "インポートしたいACFのJSONファイルを選択してください。下のインポートボタン"
#~ "をクリックすると、ACFはフィールドグループをインポートします。"

#~ msgid "Advanced Custom Fields Database Upgrade"
#~ msgstr "Advanced Custom Fields データベースのアップグレード"

#~ msgid ""
#~ "The following sites require a DB upgrade. Check the ones you want to "
#~ "update and then click “Upgrade Database”."
#~ msgstr ""
#~ "下記のサイトはデータベースのアップグレードが必要です。アップデートしたいサ"
#~ "イトにチェックを入れ、「データベースをアップグレード」をクリックしてくださ"
#~ "い。"

#, php-format
#~ msgid "Site requires database upgrade from %s to %s"
#~ msgstr "%s から %s へのデータベースアップグレードが必要なサイト"

#~ msgid "Upgrade complete"
#~ msgstr "更新完了"

#~ msgid "Upgrading data to"
#~ msgstr "データをアップグレード"

#, php-format
#~ msgid "Thank you for updating to %s v%s!"
#~ msgstr "%s v%sへのアップグレードありがとうございます!"

#~ msgid ""
#~ "Before you start using the new awesome features, please update your "
#~ "database to the newest version."
#~ msgstr ""
#~ "素晴らしい新機能を利用する前にデータベースを最新バージョンに更新してくださ"
#~ "い。"

#~ msgid "See what's new"
#~ msgstr "新着情報を見る"

#, php-format
#~ msgid "File size must must not exceed %s."
#~ msgstr "ファイルサイズは %s を超えてはいけません。"

#~ msgid "Toggle"
#~ msgstr "トグル"

#~ msgid "Done"
#~ msgstr "完了"

#~ msgid "Today"
#~ msgstr "本日"

#~ msgid "Show a different month"
#~ msgstr "別の月を表示する"

#~ msgid "Return format"
#~ msgstr "返り値"

#~ msgid "uploaded to this post"
#~ msgstr "この投稿にアップロードされる"

#~ msgid "File Name"
#~ msgstr "ファイルネーム"

#~ msgid "File Size"
#~ msgstr "ファイルサイズ"

#~ msgid "No File selected"
#~ msgstr "ファイルが選択されていません"

#~ msgid "Locating"
#~ msgstr "場所"

#~ msgid "Customise the map height"
#~ msgstr "マップの高さを調整"

#~ msgid "Shown when entering data"
#~ msgstr "投稿編集中に表示されます"

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr ""
#~ "すべてのテキストが最初にWordPressの関数を通過しますのでご注意ください"

#~ msgid "No embed found for the given URL."
#~ msgstr "指定されたURLには埋め込む内容がありません."

#~ msgid "Other"
#~ msgstr "その他"

#~ msgid "Save Other"
#~ msgstr "その他を保存"

#~ msgid "Minimum values reached ( {min} values )"
#~ msgstr "最小値 ( {min} ) に達しました"

#~ msgid "Select"
#~ msgstr "セレクトボックス"

#~ msgid "Stylised UI"
#~ msgstr "スタイリッシュなUI"

#~ msgid "Warning"
#~ msgstr "注意"

#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "このタブは、テーブルスタイルの繰り返しフィールドか柔軟コンテンツフィールド"
#~ "が追加された場合、正しく表示されません"

#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr ""
#~ "\"タブ\" を使うとフィールドのグループ化によって編集画面をより整理できま"
#~ "す。"

#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "この\"タブ\" の後に続く（または別の \"タブ\" が定義されるまでの）全ての"
#~ "フィールドは、このフィールドのラベルがタブの見出しとなりグループ化されま"
#~ "す。"

#~ msgid "End-point"
#~ msgstr "エンドポイント"

#~ msgid "Use this field as an end-point and start a new group of tabs"
#~ msgstr ""
#~ "このフィールドをエンドポイントとして使用し、新規のタブグループを開始する"

#, php-format
#~ msgid "Add new %s "
#~ msgstr "新しい %s を追加"

#~ msgid "None"
#~ msgstr "無"

#~ msgid "eg. Show extra content"
#~ msgstr "例：追加コンテンツを表示する"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>接続エラー</b> すみません、もう一度試してみてください"

#~ msgid "Save Options"
#~ msgstr "オプションを保存"

#~ msgid "License"
#~ msgstr "ライセンス"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "アップデートのロックを解除するには、以下にライセンスキーを入力してくださ"
#~ "い。ライセンスキーを持っていない場合は、こちらを参照してください"

#~ msgid "details & pricing"
#~ msgstr "価格と詳細"

#, php-format
#~ msgid ""
#~ "To enable updates, please enter your license key on the <a href=\"%s"
#~ "\">Updates</a> page. If you don't have a licence key, please see <a href="
#~ "\"%s\">details & pricing</a>"
#~ msgstr ""
#~ "アップデートを有効にするには、<a href=\"%s\">アップデート</a>ページにライ"
#~ "センスキーを入力してください。ライセンスキーを持っていない場合は、こちらを"
#~ "<a href=\"%s\">詳細と価格</a>参照してください"

#~ msgid "remove {layout}?"
#~ msgstr "{layout} を削除しますか？"

#~ msgid "This field requires at least {min} {identifier}"
#~ msgstr "このフィールドは{identifier}が最低{min}個は必要です"

#~ msgid "This field has a limit of {max} {identifier}"
#~ msgstr "このフィールドは{identifier}が最高{max}個までです"

#~ msgid "Maximum {label} limit reached ({max} {identifier})"
#~ msgstr "{label}は最大数に達しました（{max} {identifier}）"

#, php-format
#~ msgid "%s requires at least %s selection"
#~ msgid_plural "%s requires at least %s selections"
#~ msgstr[0] "%s は少なくとも %s個 選択してください"

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "Advanced Custom Fields Pro"

#~ msgid "http://www.advancedcustomfields.com/"
#~ msgstr "http://www.advancedcustomfields.com/"

#~ msgid ""
#~ "Customise WordPress with powerful, professional and intuitive fields."
#~ msgstr ""
#~ "強力でプロフェッショナル、そして直感的なフィールドで WordPress をカスタマ"
#~ "イズ。"

#~ msgid "elliot condon"
#~ msgstr "エリオット・コンドン"

#~ msgid "http://www.elliotcondon.com/"
#~ msgstr "http://www.elliotcondon.com/"

#~ msgid "Hide / Show All"
#~ msgstr "全て 非表示 / 表示"

#~ msgid "Show Field Keys"
#~ msgstr "フィールドキーを表示"

#~ msgid "Pending Review"
#~ msgstr "レビュー待ち"

#~ msgid "Draft"
#~ msgstr "下書き"

#~ msgid "Future"
#~ msgstr "予約投稿"

#~ msgid "Private"
#~ msgstr "非公開"

#~ msgid "Revision"
#~ msgstr "リビジョン"

#~ msgid "Trash"
#~ msgstr "ゴミ箱"

#~ msgid "Import / Export"
#~ msgstr "インポート / エクスポート"

#~ msgid "Field groups are created in order <br />from lowest to highest"
#~ msgstr "フィールドグループは、順番が小さいほうから大きいほうへ作成されます"

#~ msgid ""
#~ "If multiple field groups appear on an edit screen, the first field "
#~ "group's options will be used. (the one with the lowest order number)"
#~ msgstr ""
#~ "編集画面に複数のフィールドグループが表示される場合、最初の（=順番の最も小"
#~ "さい）フィールドグループのオプションが使用されます。"

#~ msgid "ACF PRO Required"
#~ msgstr "ACF PROが必要です"

#~ msgid ""
#~ "We have detected an issue which requires your attention: This website "
#~ "makes use of premium add-ons (%s) which are no longer compatible with ACF."
#~ msgstr ""
#~ "あなたに注意すべき問題があります：有料アドオン（%s）を利用したこのウェブサ"
#~ "イトにACFはもはや対応していません。"

#~ msgid ""
#~ "Don't panic, you can simply roll back the plugin and continue using ACF "
#~ "as you know it!"
#~ msgstr ""
#~ "慌てないでください、プラグインをロールバックすることで今までどおりACFを使"
#~ "用し続けることができます！"

#~ msgid "Roll back to ACF v%s"
#~ msgstr "ACF v%sにロールバックする"

#~ msgid "Learn why ACF PRO is required for my site"
#~ msgstr "なぜ自分のサイトにACF PROが必要なのか学ぶ"

#~ msgid "Update Database"
#~ msgstr "データベースを更新"

#~ msgid "Data Upgrade"
#~ msgstr "データアップグレード"

#~ msgid "Data upgraded successfully."
#~ msgstr "データアップグレード成功"

#~ msgid "Data is at the latest version."
#~ msgstr "データは最新バージョンです"

#~ msgid "1 required field below is empty"
#~ msgid_plural "%s required fields below are empty"
#~ msgstr[0] "下記の %s個 の必須フィールドが空です"

#~ msgid "Load & Save Terms to Post"
#~ msgstr "ターム情報の読込／保存"

#~ msgid ""
#~ "Load value based on the post's terms and update the post's terms on save"
#~ msgstr "投稿ターム情報を読み込み、保存時に反映させる"

#~ msgid "Top Level Page (parent of 0)"
#~ msgstr "一番上の階層（親ページがない）"

#~ msgid "Logged in User Type"
#~ msgstr "ログインしているユーザーのタイプ"

#~ msgid "Field&nbsp;Groups"
#~ msgstr "フィールドグループ"

#~ msgid "Custom field updated."
#~ msgstr "カスタムフィールドを更新しました"

#~ msgid "Custom field deleted."
#~ msgstr "カスタムフィールドを削除しました"

#~ msgid "Field group restored to revision from %s"
#~ msgstr "リビジョン %s からフィールドグループを復元しました"

#~ msgid "Full"
#~ msgstr "フルサイズ"

#~ msgid "No ACF groups selected"
#~ msgstr "ACF グループが選択されていません"

#~ msgid ""
#~ "Create infinite rows of repeatable data with this versatile interface!"
#~ msgstr ""
#~ "繰り返し挿入可能なフォームを、すてきなインターフェースで作成します。"

#~ msgid "Create image galleries in a simple and intuitive interface!"
#~ msgstr "画像ギャラリーを、シンプルで直感的なインターフェースで作成します。"

#~ msgid "Create global data to use throughout your website!"
#~ msgstr "ウェブサイト全体で使用できるグローバルデータを作成します。"

#~ msgid "Create unique designs with a flexible content layout manager!"
#~ msgstr ""
#~ "柔軟なコンテンツレイアウト管理により、すてきなデザインを作成します。"

#~ msgid "Gravity Forms Field"
#~ msgstr "Gravity Forms フィールド"

#~ msgid "Creates a select field populated with Gravity Forms!"
#~ msgstr "Creates a select field populated with Gravity Forms!"

#, fuzzy
#~ msgid "Date & Time Picker"
#~ msgstr "デイトピッカー"

#~ msgid "jQuery date & time picker"
#~ msgstr "jQuery デイトタイムピッカー"

#, fuzzy
#~ msgid "Location Field"
#~ msgstr "位置"

#~ msgid "Find addresses and coordinates of a desired location"
#~ msgstr "Find addresses and coordinates of a desired location"

#, fuzzy
#~ msgid "Contact Form 7 Field"
#~ msgstr "カスタムフィールド"

#~ msgid "Assign one or more contact form 7 forms to a post"
#~ msgstr "Assign one or more contact form 7 forms to a post"

#, fuzzy
#~ msgid "Advanced Custom Fields Add-Ons"
#~ msgstr "Advanced Custom Fields"

#~ msgid ""
#~ "The following Add-ons are available to increase the functionality of the "
#~ "Advanced Custom Fields plugin."
#~ msgstr ""
#~ "Advanced Custom Fields プラグインに機能を追加するアドオンが利用できます。"

#~ msgid ""
#~ "Each Add-on can be installed as a separate plugin (receives updates) or "
#~ "included in your theme (does not receive updates)."
#~ msgstr ""
#~ "それぞれのアドオンは、個別のプラグインとしてインストールする(管理画面で更"
#~ "新できる)か、テーマに含める(管理画面で更新できない)かしてください。"

#~ msgid "Purchase & Install"
#~ msgstr "購入してインストールする"

#~ msgid "Download"
#~ msgstr "ダウンロードする"

#, fuzzy
#~ msgid "Export"
#~ msgstr "XML をエクスポートする"

#, fuzzy
#~ msgid "Select the field groups to be exported"
#~ msgstr ""
#~ "一覧からフィールドグループを選択し、\"XML をエクスポートする\" をクリック"
#~ "してください"

#, fuzzy
#~ msgid "Export to XML"
#~ msgstr "XML をエクスポートする"

#, fuzzy
#~ msgid "Export to PHP"
#~ msgstr "フィールドグループを PHP 形式でエクスポートする"

#~ msgid ""
#~ "ACF will create a .xml export file which is compatible with the native WP "
#~ "import plugin."
#~ msgstr ""
#~ "ACF は .xml 形式のエクスポートファイルを作成します。WP のインポートプラグ"
#~ "インと互換性があります。"

#~ msgid ""
#~ "Imported field groups <b>will</b> appear in the list of editable field "
#~ "groups. This is useful for migrating fields groups between Wp websites."
#~ msgstr ""
#~ "インポートしたフィールドグループは、編集可能なフィールドグループの一覧に表"
#~ "示されます。WP ウェブサイト間でフィールドグループを移行するのに役立ちま"
#~ "す。"

#~ msgid "Select field group(s) from the list and click \"Export XML\""
#~ msgstr ""
#~ "一覧からフィールドグループを選択し、\"XML をエクスポートする\" をクリック"
#~ "してください"

#~ msgid "Save the .xml file when prompted"
#~ msgstr "指示に従って .xml ファイルを保存してください"

#~ msgid "Navigate to Tools &raquo; Import and select WordPress"
#~ msgstr "ツール &raquo; インポートと進み、WordPress を選択してください"

#~ msgid "Install WP import plugin if prompted"
#~ msgstr ""
#~ "(インストールを促された場合は) WP インポートプラグインをインストールしてく"
#~ "ださい"

#~ msgid "Upload and import your exported .xml file"
#~ msgstr "エクスポートした .xml ファイルをアップロードし、インポートする"

#~ msgid "Select your user and ignore Import Attachments"
#~ msgstr "ユーザーを選択するが、Import Attachments を選択しない"

#~ msgid "That's it! Happy WordPressing"
#~ msgstr "これで OK です。WordPress をお楽しみください"

#~ msgid "ACF will create the PHP code to include in your theme."
#~ msgstr "ACF は、テーマに含める PHP コードを作成します"

#~ msgid ""
#~ "Registered field groups <b>will not</b> appear in the list of editable "
#~ "field groups. This is useful for including fields in themes."
#~ msgstr ""
#~ "登録したフィールドグループは、編集可能なフィールドグループの一覧に<b>表示"
#~ "されません</b>。テーマにフィールドを含めるときに役立ちます。"

#~ msgid ""
#~ "Please note that if you export and register field groups within the same "
#~ "WP, you will see duplicate fields on your edit screens. To fix this, "
#~ "please move the original field group to the trash or remove the code from "
#~ "your functions.php file."
#~ msgstr ""
#~ "同一の WP でフィールドグループをエクスポートして登録する場合は、編集画面で"
#~ "重複フィールドになることに注意してください。これを修正するには、元のフィー"
#~ "ルドグループをゴミ箱へ移動するか、functions.php ファイルからこのコードを除"
#~ "去してください。"

#~ msgid "Select field group(s) from the list and click \"Create PHP\""
#~ msgstr ""
#~ "一覧からフィールドグループを選択し、\"PHP 形式のデータを作成する\" をク"
#~ "リックしてください。"

#~ msgid "Copy the PHP code generated"
#~ msgstr "生成された PHP コードをコピーし、"

#~ msgid "Paste into your functions.php file"
#~ msgstr "functions.php に貼り付けてください"

#~ msgid ""
#~ "To activate any Add-ons, edit and use the code in the first few lines."
#~ msgstr ""
#~ "アドオンを有効化するには、最初の何行かのコードを編集して使用してください"

#~ msgid "Notes"
#~ msgstr "注意"

#~ msgid "Include in theme"
#~ msgstr "テーマに含める"

#~ msgid ""
#~ "The Advanced Custom Fields plugin can be included within a theme. To do "
#~ "so, move the ACF plugin inside your theme and add the following code to "
#~ "your functions.php file:"
#~ msgstr ""
#~ "Advanced Custom Fields プラグインは、テーマに含めることができます。プラグ"
#~ "インをテーマ内に移動し、functions.php に下記コードを追加してください。"

#~ msgid ""
#~ "To remove all visual interfaces from the ACF plugin, you can use a "
#~ "constant to enable lite mode. Add the following code to your functions."
#~ "php file <b>before</b> the include_once code:"
#~ msgstr ""
#~ "Advanced Custom Fields プラグインのビジュアルインターフェースを取り除くに"
#~ "は、定数を利用して「ライトモード」を有効にすることができます。functions."
#~ "php の include_once よりも<b>前</b>に下記のコードを追加してください。"

#, fuzzy
#~ msgid "Back to export"
#~ msgstr "設定に戻る"

#~ msgid ""
#~ "/**\n"
#~ " *  Install Add-ons\n"
#~ " *  \n"
#~ " *  The following code will include all 4 premium Add-Ons in your theme.\n"
#~ " *  Please do not attempt to include a file which does not exist. This "
#~ "will produce an error.\n"
#~ " *  \n"
#~ " *  All fields must be included during the 'acf/register_fields' action.\n"
#~ " *  Other types of Add-ons (like the options page) can be included "
#~ "outside of this action.\n"
#~ " *  \n"
#~ " *  The following code assumes you have a folder 'add-ons' inside your "
#~ "theme.\n"
#~ " *\n"
#~ " *  IMPORTANT\n"
#~ " *  Add-ons may be included in a premium theme as outlined in the terms "
#~ "and conditions.\n"
#~ " *  However, they are NOT to be included in a premium / free plugin.\n"
#~ " *  For more information, please read http://www.advancedcustomfields.com/"
#~ "terms-conditions/\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " *  Install Add-ons\n"
#~ " *  \n"
#~ " *  The following code will include all 4 premium Add-Ons in your theme.\n"
#~ " *  Please do not attempt to include a file which does not exist. This "
#~ "will produce an error.\n"
#~ " *  \n"
#~ " *  All fields must be included during the 'acf/register_fields' action.\n"
#~ " *  Other types of Add-ons (like the options page) can be included "
#~ "outside of this action.\n"
#~ " *  \n"
#~ " *  The following code assumes you have a folder 'add-ons' inside your "
#~ "theme.\n"
#~ " *\n"
#~ " *  IMPORTANT\n"
#~ " *  Add-ons may be included in a premium theme as outlined in the terms "
#~ "and conditions.\n"
#~ " *  However, they are NOT to be included in a premium / free plugin.\n"
#~ " *  For more information, please read http://www.advancedcustomfields.com/"
#~ "terms-conditions/\n"
#~ " */"

#, fuzzy
#~ msgid ""
#~ "/**\n"
#~ " *  Register Field Groups\n"
#~ " *\n"
#~ " *  The register_field_group function accepts 1 array which holds the "
#~ "relevant data to register a field group\n"
#~ " *  You may edit the array as you see fit. However, this may result in "
#~ "errors if the array is not compatible with ACF\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " * フィールドグループを登録する\n"
#~ " * register_field_group 関数は、フィールドグループを登録するのに関係する"
#~ "データを持っている一つの配列を受け付けます。\n"
#~ " * 配列を好きなように編集することができます。しかし、配列が ACF と互換性の"
#~ "無い場合、エラーになってしまいます。\n"
#~ " * このコードは、functions.php ファイルを読み込む度に実行する必要がありま"
#~ "す。\n"
#~ " */"

#~ msgid "No field groups were selected"
#~ msgstr "フィールドグループが選択されていません"

#, fuzzy
#~ msgid "Show Field Key:"
#~ msgstr "フィールドキー"

#~ msgid "Vote"
#~ msgstr "投票"

#~ msgid "Follow"
#~ msgstr "フォロー"

#~ msgid "Thank you for updating to the latest version!"
#~ msgstr "最新版への更新ありがとうございます。"

#~ msgid ""
#~ "is more polished and enjoyable than ever before. We hope you like it."
#~ msgstr ""
#~ "は以前よりも洗練され、より良くなりました。気に入ってもらえると嬉しいです。"

#~ msgid "What’s New"
#~ msgstr "更新情報"

#, fuzzy
#~ msgid "Download Add-ons"
#~ msgstr "アドオンを探す"

#~ msgid "Activation codes have grown into plugins!"
#~ msgstr "アクティベーションコードから、プラグインに変更されました。"

#~ msgid ""
#~ "Add-ons are now activated by downloading and installing individual "
#~ "plugins. Although these plugins will not be hosted on the wordpress.org "
#~ "repository, each Add-on will continue to receive updates in the usual way."
#~ msgstr ""
#~ "アドオンは、個別のプラグインをダウンロードしてインストールしてください。"
#~ "wordpress.org リポジトリにはありませんが、管理画面でこれらのアドオンの更新"
#~ "を行う事が出来ます。"

#~ msgid "All previous Add-ons have been successfully installed"
#~ msgstr "今まで使用していたアドオンがインストールされました。"

#~ msgid "This website uses premium Add-ons which need to be downloaded"
#~ msgstr ""
#~ "このウェブサイトではプレミアムアドオンが使用されており、アドオンをダウン"
#~ "ロードする必要があります。"

#, fuzzy
#~ msgid "Download your activated Add-ons"
#~ msgstr "アドオンを有効化する"

#~ msgid ""
#~ "This website does not use premium Add-ons and will not be affected by "
#~ "this change."
#~ msgstr ""
#~ "このウェブサイトではプレミアムアドオンを使用しておらず、この変更に影響され"
#~ "ません。"

#~ msgid "Easier Development"
#~ msgstr "開発を容易に"

#, fuzzy
#~ msgid "New Field Types"
#~ msgstr "フィールドタイプ"

#, fuzzy
#~ msgid "Taxonomy Field"
#~ msgstr "タクソノミー"

#, fuzzy
#~ msgid "User Field"
#~ msgstr "フィールドを閉じる"

#, fuzzy
#~ msgid "Email Field"
#~ msgstr "ギャラリーフィールド"

#, fuzzy
#~ msgid "Password Field"
#~ msgstr "新規フィールド"

#, fuzzy
#~ msgid "Custom Field Types"
#~ msgstr "カスタムフィールド"

#~ msgid ""
#~ "Creating your own field type has never been easier! Unfortunately, "
#~ "version 3 field types are not compatible with version 4."
#~ msgstr ""
#~ "独自のフィールドタイプが簡単に作成できます。残念ですが、バージョン 3 と"
#~ "バージョン 4 には互換性がありません。"

#~ msgid "Migrating your field types is easy, please"
#~ msgstr "フィールドタイプをマイグレーションするのは簡単です。"

#~ msgid "follow this tutorial"
#~ msgstr "このチュートリアルに従ってください。"

#~ msgid "to learn more."
#~ msgstr "詳細を見る"

#~ msgid "Actions &amp; Filters"
#~ msgstr "アクションとフィルター"

#~ msgid ""
#~ "All actions & filters have received a major facelift to make customizing "
#~ "ACF even easier! Please"
#~ msgstr ""
#~ "カスタマイズを簡単にするため、すべてのアクションとフィルターを改装しまし"
#~ "た。"

#, fuzzy
#~ msgid "read this guide"
#~ msgstr "このフィールドを編集する"

#~ msgid "to find the updated naming convention."
#~ msgstr "新しい命名規則をごらんください。"

#~ msgid "Preview draft is now working!"
#~ msgstr "プレビューが有効になりました。"

#~ msgid "This bug has been squashed along with many other little critters!"
#~ msgstr "このバグを修正しました。"

#~ msgid "See the full changelog"
#~ msgstr "全ての更新履歴を見る"

#~ msgid "Important"
#~ msgstr "重要"

#~ msgid "Database Changes"
#~ msgstr "データベース更新"

#~ msgid ""
#~ "Absolutely <strong>no</strong> changes have been made to the database "
#~ "between versions 3 and 4. This means you can roll back to version 3 "
#~ "without any issues."
#~ msgstr ""
#~ "バージョン 3 と 4 でデータベースの更新はありません。問題が発生した場合、"
#~ "バージョン 3 へのロールバックを行うことができます。"

#~ msgid "Potential Issues"
#~ msgstr "潜在的な問題"

#~ msgid ""
#~ "Do to the sizable changes surounding Add-ons, field types and action/"
#~ "filters, your website may not operate correctly. It is important that you "
#~ "read the full"
#~ msgstr ""
#~ "アドオン、フィールドタイプ、アクション／フィルターに関する変更のため、ウェ"
#~ "ブサイトが正常に動作しない可能性があります。"

#~ msgid "Migrating from v3 to v4"
#~ msgstr "バージョン 3 から 4 への移行をごらんください。"

#~ msgid "guide to view the full list of changes."
#~ msgstr "変更の一覧を見ることができます。"

#~ msgid "Really Important!"
#~ msgstr "非常に重要"

#~ msgid ""
#~ "If you updated the ACF plugin without prior knowledge of such changes, "
#~ "please roll back to the latest"
#~ msgstr "予備知識無しに更新してしまった場合は、"

#~ msgid "version 3"
#~ msgstr "バージョン 3 "

#~ msgid "of this plugin."
#~ msgstr "にロールバックしてください。"

#~ msgid "Thank You"
#~ msgstr "ありがとうございます"

#~ msgid ""
#~ "A <strong>BIG</strong> thank you to everyone who has helped test the "
#~ "version 4 beta and for all the support I have received."
#~ msgstr ""
#~ "バージョン 4 ベータのテストに協力してくださった皆さん、サポートしてくだ"
#~ "さった皆さんに感謝します。"

#~ msgid "Without you all, this release would not have been possible!"
#~ msgstr "皆さんの助けが無ければ、リリースすることはできなかったでしょう。"

#, fuzzy
#~ msgid "Changelog for"
#~ msgstr "更新履歴"

#~ msgid "Learn more"
#~ msgstr "詳細を見る"

#~ msgid ""
#~ "Previously, all Add-ons were unlocked via an activation code (purchased "
#~ "from the ACF Add-ons store). New to v4, all Add-ons act as separate "
#~ "plugins which need to be individually downloaded, installed and updated."
#~ msgstr ""
#~ "今までは、アドオンはアクティベーションコードでロック解除していました。バー"
#~ "ジョン 4 では、アドオンは個別のプラグインとしてダウンロードしてインストー"
#~ "ルする必要があります。"

#~ msgid ""
#~ "This page will assist you in downloading and installing each available "
#~ "Add-on."
#~ msgstr "このページは、アドオンのダウンロードやインストールを手助けします。"

#, fuzzy
#~ msgid "Available Add-ons"
#~ msgstr "アドオンを有効化する"

#~ msgid ""
#~ "The following Add-ons have been detected as activated on this website."
#~ msgstr "以下のアドオンがこのウェブサイトで有効になっています。"

#~ msgid "Activation Code"
#~ msgstr "アクティベーションコード"

#, fuzzy
#~ msgid "Installation"
#~ msgstr "説明"

#~ msgid "For each Add-on available, please perform the following:"
#~ msgstr "それぞれのアドオンについて、下記を実行してください。"

#~ msgid "Download the Add-on plugin (.zip file) to your desktop"
#~ msgstr "アドオン(.zip ファイル)をダウンロードする"

#~ msgid "Navigate to"
#~ msgstr "管理画面で"

#~ msgid "Plugins > Add New > Upload"
#~ msgstr "プラグイン > 新規追加 > アップロード"

#~ msgid ""
#~ "Use the uploader to browse, select and install your Add-on (.zip file)"
#~ msgstr "アドオンのファイルを選択してインストールする"

#~ msgid ""
#~ "Once the plugin has been uploaded and installed, click the 'Activate "
#~ "Plugin' link"
#~ msgstr "アップロードできたら、有効化をクリックする"

#~ msgid "The Add-on is now installed and activated!"
#~ msgstr "アドオンがインストールされ、有効化されました。"

#~ msgid "Awesome. Let's get to work"
#~ msgstr "素晴らしい。作業に戻ります。"

#~ msgid "Validation Failed. One or more fields below are required."
#~ msgstr "検証に失敗しました。下記のフィールドの少なくとも一つが必須です。"

#, fuzzy
#~ msgid "What's new"
#~ msgstr "新着情報で見る"

#~ msgid "credits"
#~ msgstr "クレジット"

#~ msgid "Modifying field group options 'show on page'"
#~ msgstr "フィールドグループオプション「ページで表示する」を変更"

#~ msgid "Modifying field option 'taxonomy'"
#~ msgstr "フィールドオプション「タクソノミー」を変更"

#~ msgid "Moving user custom fields from wp_options to wp_usermeta'"
#~ msgstr ""
#~ "ユーザーのカスタムフィールドを wp_options から wp_usermeta に変更する"

#~ msgid "blue : Blue"
#~ msgstr "blue : 青"

#~ msgid "eg: #ffffff"
#~ msgstr "例: #ffffff"

#~ msgid "Save format"
#~ msgstr "フォーマットを保存する"

#~ msgid ""
#~ "This format will determin the value saved to the database and returned "
#~ "via the API"
#~ msgstr ""
#~ "このフォーマットは、値をデータベースに保存し、API で返す形式を決定します"

#~ msgid "\"yymmdd\" is the most versatile save format. Read more about"
#~ msgstr "最も良く用いられるフォーマットは \"yymmdd\" です。詳細は"

#~ msgid "jQuery date formats"
#~ msgstr "jQuery 日付フォーマット"

#~ msgid "This format will be seen by the user when entering a value"
#~ msgstr "ユーザーが値を入力するときのフォーマット"

#~ msgid ""
#~ "\"dd/mm/yy\" or \"mm/dd/yy\" are the most used Display Formats. Read more "
#~ "about"
#~ msgstr "よく使用されるのは、\"dd/mm/yy\" や \"mm/dd/yy\" です。詳細は"

#~ msgid "Dummy"
#~ msgstr "ダミー"

#~ msgid "No File Selected"
#~ msgstr "ファイルが選択されていません"

#~ msgid "File Object"
#~ msgstr "ファイルオブジェクト"

#~ msgid "File Updated."
#~ msgstr "ファイルを更新しました"

#~ msgid "Media attachment updated."
#~ msgstr "メディアアタッチメントを更新しました"

#~ msgid "No files selected"
#~ msgstr "ファイルが選択されていません"

#~ msgid "Add Selected Files"
#~ msgstr "選択されたファイルを追加する"

#~ msgid "Image Object"
#~ msgstr "画像オブジェクト"

#~ msgid "Image Updated."
#~ msgstr "画像を更新しました"

#~ msgid "No images selected"
#~ msgstr "画像が選択されていません"

#, fuzzy
#~ msgid "Add Selected Images"
#~ msgstr "選択した画像を追加する"

#~ msgid "Text &amp; HTML entered here will appear inline with the fields"
#~ msgstr "ここに記述したテキストと HTML がインラインで表示されます。"

#~ msgid "Specifies the minimum value allowed"
#~ msgstr "最小値を指定します。"

#~ msgid "Specifies the maximim value allowed"
#~ msgstr "最大値を指定します。"

#~ msgid "Step"
#~ msgstr "Step"

#~ msgid "Specifies the legal number intervals"
#~ msgstr "入力値の間隔を指定します。"

#~ msgid "Filter from Taxonomy"
#~ msgstr "タクソノミーでフィルタする"

#~ msgid "Enter your choices one per line"
#~ msgstr "選択肢を一行ずつ入力してください"

#~ msgid "Red"
#~ msgstr "赤"

#~ msgid "Blue"
#~ msgstr "青"

#~ msgid "Filter by post type"
#~ msgstr "投稿タイプでフィルタする"

#, fuzzy
#~ msgid "Post Type Select"
#~ msgstr "投稿タイプ"

#, fuzzy
#~ msgid "Post Title"
#~ msgstr "投稿タイプ"

#~ msgid ""
#~ "All fields proceeding this \"tab field\" (or until another \"tab field\"  "
#~ "is defined) will appear grouped on the edit screen."
#~ msgstr "タブフィールドでフィールドを区切り、グループ化して表示します。"

#~ msgid "You can use multiple tabs to break up your fields into sections."
#~ msgstr "複数のタブを使用することができます。"

#~ msgid "Formatting"
#~ msgstr "フォーマット"

#~ msgid "Define how to render html tags"
#~ msgstr "html タグの表示を決定する"

#~ msgid "HTML"
#~ msgstr "HTML"

#~ msgid "Define how to render html tags / new lines"
#~ msgstr "html タグ/新しい行の表示を決定する"

#~ msgid "auto &lt;br /&gt;"
#~ msgstr "自動 &lt;br /&gt;"

#~ msgid "Field Order"
#~ msgstr "フィールド順序"

#~ msgid "Field Key"
#~ msgstr "フィールドキー"

#~ msgid "Edit this Field"
#~ msgstr "このフィールドを編集する"

#~ msgid "Read documentation for this field"
#~ msgstr "このフィールドのドキュメントを読む"

#~ msgid "Docs"
#~ msgstr "ドキュメント"

#~ msgid "Duplicate this Field"
#~ msgstr "このフィールドを複製する"

#~ msgid "Delete this Field"
#~ msgstr "このフィールドを削除する"

#~ msgid "Field Instructions"
#~ msgstr "フィールド記入のヒント"

#~ msgid "Show this field when"
#~ msgstr "表示する条件"

#~ msgid "all"
#~ msgstr "全て"

#~ msgid "any"
#~ msgstr "任意"

#~ msgid "these rules are met"
#~ msgstr "これらの条件を満たす"

#, fuzzy
#~ msgid "Taxonomy Term (Add / Edit)"
#~ msgstr "タクソノミー(追加/編集)"

#~ msgid "User (Add / Edit)"
#~ msgstr "ユーザー(追加/編集)"

#, fuzzy
#~ msgid "Media Attachment (Edit)"
#~ msgstr "メディアアタッチメントを更新しました"

#~ msgid "Normal"
#~ msgstr "Normal"

#~ msgid "No Metabox"
#~ msgstr "メタボックス無"

#~ msgid "Standard Metabox"
#~ msgstr "標準メタボックス"
