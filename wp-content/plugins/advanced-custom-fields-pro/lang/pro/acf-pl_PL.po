msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.2.9\n"
"Report-Msgid-Bugs-To: https://support.advancedcustomfields.com\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: 2022-02-27 13:58+0100\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON> <<EMAIL>>\n"
"Language: pl_PL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 "
"|| n%100>=20) ? 1 : 2);\n"
"X-Generator: Poedit 3.0.1\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:3, pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: acf.php:4, acf.php:8
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#: acf.php:5
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Dostosuj WordPress za pomocą potężnych, profesjonalnych i intuicyjnych pól."

#: acf.php:7
msgid "Delicious Brains"
msgstr "Delicious Brains"

#: acf.php:71
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:360, includes/admin/admin.php:50, includes/admin/admin.php:50
msgid "Field Groups"
msgstr "Grupy pól"

#: acf.php:361
msgid "Field Group"
msgstr "Grupa pól"

#: acf.php:362, acf.php:396, includes/admin/admin.php:51,
#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Add New"
msgstr "Dodaj nową"

#: acf.php:363
msgid "Add New Field Group"
msgstr "Dodaj nową grupę pól"

#: acf.php:364
msgid "Edit Field Group"
msgstr "Edytuj grupę pól"

#: acf.php:365
msgid "New Field Group"
msgstr "Nowa grupa pól"

#: acf.php:366
msgid "View Field Group"
msgstr "Zobacz grupę pól"

#: acf.php:367
msgid "Search Field Groups"
msgstr "Szukaj grup pól"

#: acf.php:368
msgid "No Field Groups found"
msgstr "Nie znaleziono grupy pól"

#: acf.php:369
msgid "No Field Groups found in Trash"
msgstr "Brak grup pól w koszu"

#: acf.php:394, includes/admin/admin-field-group.php:233,
#: includes/admin/admin-field-groups.php:266,
#: pro/fields/class-acf-field-clone.php:814
msgid "Fields"
msgstr "Pola"

#: acf.php:395
msgid "Field"
msgstr "Pole"

#: acf.php:397
msgid "Add New Field"
msgstr "Dodaj nowe pole"

#: acf.php:398
msgid "Edit Field"
msgstr "Edytuj pole"

#: acf.php:399, includes/admin/views/field-group-fields.php:56
msgid "New Field"
msgstr "Nowe pole"

#: acf.php:400
msgid "View Field"
msgstr "Zobacz pole"

#: acf.php:401
msgid "Search Fields"
msgstr "Szukaj pól"

#: acf.php:402
msgid "No Fields found"
msgstr "Nie znaleziono pól"

#: acf.php:403
msgid "No Fields found in Trash"
msgstr "Nie znaleziono pól w koszu"

#: acf.php:441, includes/admin/admin-field-group.php:385,
#: includes/admin/admin-field-groups.php:230
msgctxt "post status"
msgid "Disabled"
msgstr "Wyłączone"

#: acf.php:446
msgid "Disabled <span class=\"count\">(%s)</span>"
msgid_plural "Disabled <span class=\"count\">(%s)</span>"
msgstr[0] "Wyłączony: <span class=\"count\">(%s)</span>"
msgstr[1] "Wyłączonych: <span class=\"count\">(%s)</span>"
msgstr[2] "Wyłączonych: <span class=\"count\">(%s)</span>"

#: acf.php:496
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields i Advanced Custom Fields PRO nie powinny być aktywne "
"w tym samym czasie. Automatycznie dezaktywowaliśmy Advanced Custom Fields."

#: acf.php:498
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields i Advanced Custom Fields PRO nie powinny być aktywne "
"w tym samym czasie. Automatycznie dezaktywowaliśmy Advanced Custom Fields "
"PRO."

#: includes/acf-field-functions.php:841,
#: includes/admin/admin-field-group.php:171
msgid "(no label)"
msgstr "(brak etykiety)"

#: includes/acf-field-group-functions.php:846,
#: includes/admin/admin-field-group.php:173
msgid "copy"
msgstr "kopia"

#: includes/acf-value-functions.php:353
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" target=\"_blank"
"\">Learn how to fix this</a>."
msgstr ""
"<strong>%1$s</strong> - Wykryliśmy jedno lub więcej wywołań, które pobierają "
"wartości pól ACF przed inicjalizacją ACF. Nie są one obsłużone i może "
"powodować nieprawidłowe lub brakujące dane. <a href=\"%2$s\" target=\"_blank"
"\">Dowiedz się, jak to naprawić</a>."

#: includes/acf-wp-functions.php:41
msgid "Posts"
msgstr "Wpisy"

#: includes/acf-wp-functions.php:54
msgid "Taxonomies"
msgstr "Taksonomie"

#: includes/acf-wp-functions.php:59
msgid "Attachments"
msgstr "Załączniki"

#: includes/acf-wp-functions.php:63,
#: includes/admin/views/field-group-options.php:144
msgid "Comments"
msgstr "Komentarze"

#: includes/acf-wp-functions.php:67
msgid "Widgets"
msgstr "Widgety"

#: includes/acf-wp-functions.php:71,
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Wiele menu"

#: includes/acf-wp-functions.php:75
msgid "Menu items"
msgstr "Elementy menu"

#: includes/acf-wp-functions.php:79
msgid "Users"
msgstr "Użytkownicy"

#: includes/acf-wp-functions.php:83, pro/options-page.php:47
msgid "Options"
msgstr "Opcje"

#: includes/acf-wp-functions.php:87
msgid "Blocks"
msgstr "Bloki"

#: includes/assets.php:348
msgid "Are you sure?"
msgstr "Czy na pewno?"

#: includes/assets.php:349, includes/fields/class-acf-field-true_false.php:80,
#: includes/fields/class-acf-field-true_false.php:176,
#: pro/admin/views/html-settings-updates.php:104
msgid "Yes"
msgstr "Tak"

#: includes/assets.php:350, includes/fields/class-acf-field-true_false.php:83,
#: includes/fields/class-acf-field-true_false.php:193,
#: pro/admin/views/html-settings-updates.php:116
msgid "No"
msgstr "Nie"

#: includes/assets.php:351, includes/fields/class-acf-field-file.php:159,
#: includes/fields/class-acf-field-image.php:139,
#: includes/fields/class-acf-field-link.php:142,
#: pro/fields/class-acf-field-gallery.php:336,
#: pro/fields/class-acf-field-gallery.php:491
msgid "Remove"
msgstr "Usuń"

#: includes/assets.php:352
msgid "Cancel"
msgstr "Anuluj"

#: includes/assets.php:362
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Wprowadzone przez Ciebie zmiany przepadną jeśli przejdziesz do innej strony"

#: includes/assets.php:365
msgid "Validation successful"
msgstr "Walidacja zakończona sukcesem"

#: includes/assets.php:366, includes/validation.php:286,
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Walidacja nie powiodła się"

#: includes/assets.php:367
msgid "1 field requires attention"
msgstr "1 pole wymaga uwagi"

#: includes/assets.php:368
msgid "%d fields require attention"
msgstr "%d pól wymaga uwagi"

#: includes/assets.php:371, includes/forms/form-comment.php:160,
#: pro/admin/admin-options-page.php:309
msgid "Edit field group"
msgstr "Edytuj grupę pól"

#: includes/fields.php:313
msgid "Field type does not exist"
msgstr "Typ pola nie istnieje"

#: includes/fields.php:313
msgid "Unknown"
msgstr "Nieznane"

#: includes/fields.php:354
msgid "Basic"
msgstr "Podstawowe"

#: includes/fields.php:355, includes/forms/form-front.php:49
msgid "Content"
msgstr "Treść"

#: includes/fields.php:356
msgid "Choice"
msgstr "Wybór"

#: includes/fields.php:357
msgid "Relational"
msgstr "Relacyjne"

#: includes/fields.php:358
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:359,
#: includes/fields/class-acf-field-button-group.php:181,
#: includes/fields/class-acf-field-checkbox.php:377,
#: includes/fields/class-acf-field-group.php:462,
#: includes/fields/class-acf-field-radio.php:256,
#: pro/fields/class-acf-field-clone.php:850,
#: pro/fields/class-acf-field-flexible-content.php:549,
#: pro/fields/class-acf-field-flexible-content.php:604,
#: pro/fields/class-acf-field-repeater.php:451
msgid "Layout"
msgstr "Układ"

#: includes/locations.php:25
msgid "Class \"%s\" does not exist."
msgstr "Klasa \"%s\" nie istnieje."

#: includes/locations.php:36
msgid "Location type \"%s\" is already registered."
msgstr "Typ lokalizacji \"%s\" jest już zarejestrowany."

#: includes/locations.php:99, includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Wpis"

#: includes/locations.php:100,
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Strona"

#: includes/locations.php:101, includes/fields/class-acf-field-user.php:20
msgid "User"
msgstr "Użytkownik"

#: includes/locations.php:102
msgid "Forms"
msgstr "Formularze"

#: includes/media.php:48, includes/fields/class-acf-field-select.php:255
msgctxt "verb"
msgid "Select"
msgstr "Wybierz"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Edytuj"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "Aktualizuj"

#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "Przesłane do tego wpisu"

#: includes/media.php:52
msgid "Expand Details"
msgstr "Rozwiń szczegóły"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "Zwiń szczegóły"

#: includes/media.php:54
msgid "Restricted"
msgstr "Ograniczone"

#: includes/media.php:55, includes/fields/class-acf-field-image.php:67
msgid "All images"
msgstr "Wszystkie obrazy"

#: includes/validation.php:364
msgid "%s value is required"
msgstr "%s wartość jest wymagana"

#: pro/blocks.php:37
msgid "Block type name is required."
msgstr "Nazwa typu bloku jest wymagana."

#: pro/blocks.php:44
msgid "Block type \"%s\" is already registered."
msgstr "Typ bloku \"%s\" jest już zarejestrowany."

#: pro/blocks.php:495
msgid "Switch to Edit"
msgstr "Przejdź do Edytuj"

#: pro/blocks.php:496
msgid "Switch to Preview"
msgstr "Przejdź do Podglądu"

#: pro/blocks.php:497
msgid "Change content alignment"
msgstr "Zmień wyrównanie treści"

#. translators: %s: Block type title
#: pro/blocks.php:500
msgid "%s settings"
msgstr "Ustawienia  %s"

#: pro/options-page.php:77, includes/forms/form-front.php:106,
#: pro/fields/class-acf-field-gallery.php:523
msgid "Update"
msgstr "Aktualizuj"

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Ustawienia zostały zaktualizowane"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a href=\"%1$s"
"\">Updates</a> page. If you don't have a licence key, please see <a href="
"\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Aby włączyć aktualizacje, należy wprowadzić klucz licencyjny na stronie <a "
"href=\"%1$s\">Aktualizacje</a>. Jeśli nie posiadasz klucza licencyjnego, "
"zapoznaj się z <a href=\"%2$s\" target=\"_blank\">informacjami szczegółowymi "
"i cenami</a>."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""
"<b>Błąd aktywacji ACF</b>. Zdefiniowany przez Państwa klucz licencyjny uległ "
"zmianie, ale podczas dezaktywacji starej licencji wystąpił błąd"

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""
"<b>Błąd aktywacji ACF</b>. Twój zdefiniowany klucz licencyjny uległ zmianie, "
"ale wystąpił błąd podczas łączenia się z serwerem aktywacyjnym"

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr "<b>ACF Błąd aktywacji</b>"

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""
"<b>Błąd aktywacji ACF</b>. Wystąpił błąd podczas łączenia się z serwerem "
"aktywacyjnym"

#: pro/updates.php:279, pro/admin/views/html-settings-updates.php:117
msgid "Check Again"
msgstr "Sprawdź ponownie"

#: pro/updates.php:561
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""
"<b>Błąd aktywacji ACF</b>. Nie można połączyć się z serwerem aktywacyjnym"

#: includes/admin/admin-field-group.php:84,
#: includes/admin/admin-field-group.php:85,
#: includes/admin/admin-field-group.php:87
msgid "Field group updated."
msgstr "Grupa pól została zaktualizowana."

#: includes/admin/admin-field-group.php:86
msgid "Field group deleted."
msgstr "Grupa pól została usunięta."

#: includes/admin/admin-field-group.php:89
msgid "Field group published."
msgstr "Grupa pól została opublikowana."

#: includes/admin/admin-field-group.php:90
msgid "Field group saved."
msgstr "Grupa pól została zapisana."

#: includes/admin/admin-field-group.php:91
msgid "Field group submitted."
msgstr "Grupa pól została dodana."

#: includes/admin/admin-field-group.php:92
msgid "Field group scheduled for."
msgstr "Grupa pól została zaplanowana na."

#: includes/admin/admin-field-group.php:93
msgid "Field group draft updated."
msgstr "Szkic grupy pól został zaktualizowany."

#: includes/admin/admin-field-group.php:164
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Ciąg znaków \"field_\" nie może zostać użyty na początku nazwy pola"

#: includes/admin/admin-field-group.php:165
msgid "This field cannot be moved until its changes have been saved"
msgstr "To pole nie może zostać przeniesione zanim zmiany nie zostaną zapisane"

#: includes/admin/admin-field-group.php:166
msgid "Field group title is required"
msgstr "Tytuł grupy pól jest wymagany"

#: includes/admin/admin-field-group.php:167
msgid "Move to trash. Are you sure?"
msgstr "Przenieś do kosza. Jesteś pewny?"

#: includes/admin/admin-field-group.php:168
msgid "No toggle fields available"
msgstr "Pola przełączania niedostępne"

#: includes/admin/admin-field-group.php:169
msgid "Move Custom Field"
msgstr "Przenieś pole"

#: includes/admin/admin-field-group.php:170
msgid "Checked"
msgstr "Zaznaczone"

#: includes/admin/admin-field-group.php:172
msgid "(this field)"
msgstr "(to pole)"

#: includes/admin/admin-field-group.php:174, includes/api/api-helpers.php:3409,
#: includes/admin/views/field-group-field-conditional-logic.php:60,
#: includes/admin/views/field-group-field-conditional-logic.php:170,
#: includes/admin/views/field-group-locations.php:36,
#: includes/admin/views/html-location-group.php:3
msgid "or"
msgstr "lub"

#: includes/admin/admin-field-group.php:175,
#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Pokaż tą grupę pól jeśli"

#: includes/admin/admin-field-group.php:176
msgid "Null"
msgstr "Null"

#: includes/admin/admin-field-group.php:179
msgid "Has any value"
msgstr "Ma dowolną wartość"

#: includes/admin/admin-field-group.php:180
msgid "Has no value"
msgstr "Nie ma wartości"

#: includes/admin/admin-field-group.php:181
msgid "Value is equal to"
msgstr "Wartość jest równa"

#: includes/admin/admin-field-group.php:182
msgid "Value is not equal to"
msgstr "Wartość nie jest równa"

#: includes/admin/admin-field-group.php:183
msgid "Value matches pattern"
msgstr "Wartość musi pasować do wzoru"

#: includes/admin/admin-field-group.php:184
msgid "Value contains"
msgstr "Wartość zawiera"

#: includes/admin/admin-field-group.php:185
msgid "Value is greater than"
msgstr "Wartość jest większa niż"

#: includes/admin/admin-field-group.php:186
msgid "Value is less than"
msgstr "Wartość jest mniejsza niż"

#: includes/admin/admin-field-group.php:187
msgid "Selection is greater than"
msgstr "Wybór jest większy niż"

#: includes/admin/admin-field-group.php:188
msgid "Selection is less than"
msgstr "Wybór jest mniejszy niż"

#: includes/admin/admin-field-group.php:191
msgid "Repeater (Pro only)"
msgstr "Pole powtarzalne (tylko Pro)"

#: includes/admin/admin-field-group.php:192
msgid "Flexible Content (Pro only)"
msgstr "Elastyczna zawartość (tylko Pro)"

#: includes/admin/admin-field-group.php:193
msgid "Clone (Pro only)"
msgstr "Klon (tylko Pro)"

#: includes/admin/admin-field-group.php:194
msgid "Gallery (Pro only)"
msgstr "Galeria (tylko Pro)"

#: includes/admin/admin-field-group.php:234,
#: includes/admin/admin-field-groups.php:265
msgid "Location"
msgstr "Lokacja"

#: includes/admin/admin-field-group.php:235,
#: includes/admin/tools/class-acf-admin-tool-export.php:288
msgid "Settings"
msgstr "Ustawienia"

#: includes/admin/admin-field-group.php:361
msgid "Field Keys"
msgstr "Klucze pola"

#: includes/admin/admin-field-group.php:385
msgctxt "post status"
msgid "Active"
msgstr "Aktywne"

#: includes/admin/admin-field-group.php:752
msgid "Move Complete."
msgstr "Przenoszenie zakończone."

#: includes/admin/admin-field-group.php:754
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "Pole %1$s można teraz znaleźć w grupie pól %2$s"

#: includes/admin/admin-field-group.php:758
msgid "Close Window"
msgstr "Zamknij okno"

#: includes/admin/admin-field-group.php:797
msgid "Please select the destination for this field"
msgstr "Proszę wybrać miejsce przeznaczenia dla tego pola"

#: includes/admin/admin-field-group.php:804
msgid "Move Field"
msgstr "Przenieś pole"

#: includes/admin/admin-field-groups.php:116
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Aktywny <span class=\"count\">(%s)</span>"
msgstr[1] "Aktywne <span class=\"count\">(%s)</span>"
msgstr[2] "Aktywnych <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:196
msgid "Review local JSON changes"
msgstr "Przegląd lokalnych zmian JSON"

#: includes/admin/admin-field-groups.php:197
msgid "Loading diff"
msgstr "Ładowanie różnic"

#: includes/admin/admin-field-groups.php:198,
#: includes/admin/admin-field-groups.php:533
msgid "Sync changes"
msgstr "Synchronizuj zmiany"

#: includes/admin/admin-field-groups.php:263,
#: pro/fields/class-acf-field-gallery.php:388,
#: includes/admin/views/field-group-options.php:127,
#: includes/admin/views/html-admin-page-upgrade-network.php:38,
#: includes/admin/views/html-admin-page-upgrade-network.php:49
msgid "Description"
msgstr "Opis"

#: includes/admin/admin-field-groups.php:264,
#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Klucz"

#: includes/admin/admin-field-groups.php:269
msgid "Local JSON"
msgstr "Lokalny JSON"

#: includes/admin/admin-field-groups.php:419
msgid "Various"
msgstr "Różne"

#: includes/admin/admin-field-groups.php:449
msgid "Located in: %s"
msgstr "Znajduje się w: %s"

#: includes/admin/admin-field-groups.php:445
msgid "Located in plugin: %s"
msgstr "Znalezione we wtyczce: %s"

#: includes/admin/admin-field-groups.php:441
msgid "Located in theme: %s"
msgstr "Znalezione w motywie: %s"

#: includes/admin/admin-field-groups.php:484
msgid "Awaiting save"
msgstr "Oczekiwanie na zapis"

#: includes/admin/admin-field-groups.php:481
msgid "Saved"
msgstr "Zapisane"

#: includes/admin/admin-field-groups.php:469,
#: includes/admin/admin-field-groups.php:687
msgid "Sync available"
msgstr "Synchronizacja możliwa"

#: includes/admin/admin-field-groups.php:477
msgid "Import"
msgstr "Import"

#: includes/admin/admin-field-groups.php:472
msgid "Sync"
msgstr "Synchronizacja"

#: includes/admin/admin-field-groups.php:473
msgid "Review changes"
msgstr "Przejrzyj zmiany"

#: includes/admin/admin-field-groups.php:505
msgid "Duplicate this item"
msgstr "Duplikuj to pole"

#: includes/admin/admin-field-groups.php:505,
#: includes/admin/admin-field-groups.php:525,
#: pro/fields/class-acf-field-flexible-content.php:553,
#: includes/admin/views/field-group-field.php:57
msgid "Duplicate"
msgstr "Duplikuj"

#: includes/admin/admin-field-groups.php:555
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Grupa pól została powielona."
msgstr[1] "%s grupy pól zostało zduplikowanych."
msgstr[2] "%s grup pól zostało zduplikowanych."

#: includes/admin/admin-field-groups.php:612
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "Grupa pól została zsynchronizowana."
msgstr[1] "%s grupy pól zostały zsynchronizowane."
msgstr[2] "%s grup pól zostało zsynchronizowanych."

#: includes/admin/admin-field-groups.php:800
msgid "Select %s"
msgstr "Wybierz %s"

#: includes/admin/admin-tools.php:119,
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Narzędzia"

#: includes/admin/admin-upgrade.php:51, includes/admin/admin-upgrade.php:113,
#: includes/admin/admin-upgrade.php:114, includes/admin/admin-upgrade.php:177,
#: includes/admin/views/html-admin-page-upgrade-network.php:24,
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Aktualizuj bazę danych"

#: includes/admin/admin-upgrade.php:201
msgid "Review sites & upgrade"
msgstr "Strona opinii i aktualizacji"

#: includes/admin/admin.php:49,
#: includes/admin/views/field-group-options.php:142
msgid "Custom Fields"
msgstr "Własne pola"

#: includes/admin/admin.php:129, includes/admin/admin.php:131
msgid "Overview"
msgstr "Podsumowanie"

#: includes/admin/admin.php:132
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Wtyczka Advanced Custom Fields zapewnia wizualny kreator formularzy do "
"dostosowywania ekranów edycji WordPress z dodatkowymi polami oraz intuicyjny "
"interfejs API do wyświetlania niestandardowych wartości pól w dowolnym pliku "
"szablonu motywu."

#: includes/admin/admin.php:134
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Przed utworzeniem pierwszej grupy pól zalecamy najpierw przeczytanie naszego "
"przewodnika <a href=\"%s\" target=\"_blank\"> Pierwsze kroki </a>, aby "
"zapoznać się z filozofią wtyczki i sprawdzonymi metodami."

#: includes/admin/admin.php:137
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Skorzystaj z zakładki Pomoc i wsparcie, aby skontaktować się, jeśli "
"potrzebujesz pomocy."

#: includes/admin/admin.php:146, includes/admin/admin.php:148
msgid "Help & Support"
msgstr "Pomoc & Wsparcie"

#: includes/admin/admin.php:149
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Jesteśmy fanatyczni, jeśli chodzi o wsparcie i chcemy, abyś w pełni "
"wykorzystał swoją stronę internetową dzięki ACF. Jeśli napotkasz "
"jakiekolwiek trudności, jest kilka miejsc, w których możesz znaleźć pomoc:"

#: includes/admin/admin.php:152
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Dokumentacja</a>. Nasza obszerna "
"dokumentacja zawiera opisy i przewodniki dotyczące większości sytuacji, "
"które możesz napotkać."

#: includes/admin/admin.php:156
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the ‘how-tos’ of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Dyskusje</a>. Mamy aktywną i przyjazną "
"społeczność na naszych forach społecznościowych, która pomoże Ci poznać "
"tajniki świata ACF."

#: includes/admin/admin.php:160
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Pomoc</a>. Nasi pracownicy pomocy "
"technicznej pomogą w bardziej dogłębnych wyzwaniach technicznych."

#: includes/admin/admin.php:169
msgid "Information"
msgstr "Informacja"

#: includes/admin/admin.php:170
msgid "Version %s"
msgstr "Wersja %s"

#: includes/admin/admin.php:171
msgid "View details"
msgstr "Pokaż szczegóły"

#: includes/admin/admin.php:172
msgid "Visit website"
msgstr "Odwiedź stronę"

#: includes/admin/admin.php:201,
#: includes/admin/views/field-group-field-conditional-logic.php:157,
#: includes/admin/views/html-location-rule.php:92
msgid "and"
msgstr "oraz"

#: includes/ajax/class-acf-ajax-local-json-diff.php:36
msgid "Invalid field group parameter(s)."
msgstr "Nieprawidłowy parametr (parametry) grupy pól."

#: includes/ajax/class-acf-ajax-local-json-diff.php:43
msgid "Invalid field group ID."
msgstr "Nieprawidłowy identyfikator grupy pól."

#: includes/ajax/class-acf-ajax-local-json-diff.php:53
msgid "Sorry, this field group is unavailable for diff comparison."
msgstr "Przepraszamy, ta grupa pól jest niedostępna dla porównania różnic."

#: includes/ajax/class-acf-ajax-local-json-diff.php:59
msgid "Last updated: %s"
msgstr "Ostatnia aktualizacja: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:64
msgid "Original field group"
msgstr "Oryginalna grupa pól"

#: includes/ajax/class-acf-ajax-local-json-diff.php:68
msgid "JSON field group (newer)"
msgstr "Grupa pól JSON (nowsze)"

#: includes/ajax/class-acf-ajax-upgrade.php:34,
#: includes/admin/views/html-admin-page-upgrade.php:94
msgid "No updates available."
msgstr "Brak dostępnych aktualizacji."

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Nieprawidłowy identyfikator jednorazowy."

#: includes/api/api-helpers.php:821
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/api/api-helpers.php:822
msgid "Medium"
msgstr "Średni"

#: includes/api/api-helpers.php:823
msgid "Large"
msgstr "Duży"

#: includes/api/api-helpers.php:864
msgid "Full Size"
msgstr "Pełny rozmiar"

#: includes/api/api-helpers.php:1569, includes/api/api-term.php:147,
#: pro/fields/class-acf-field-clone.php:1005
msgid "(no title)"
msgstr "(brak tytułu)"

#: includes/api/api-helpers.php:3343
msgid "Image width must not exceed %dpx."
msgstr "Szerokość obrazu nie może przekraczać %dpx."

#: includes/api/api-helpers.php:3338
msgid "Image width must be at least %dpx."
msgstr "Szerokość obrazu musi mieć co najmniej %dpx."

#: includes/api/api-helpers.php:3362
msgid "Image height must not exceed %dpx."
msgstr "Wysokość obrazu nie może przekraczać %dpx."

#: includes/api/api-helpers.php:3357
msgid "Image height must be at least %dpx."
msgstr "Wysokość obrazu musi mieć co najmniej %dpx."

#: includes/api/api-helpers.php:3382
msgid "File size must not exceed %s."
msgstr "Rozmiar pliku nie może przekraczać %s."

#: includes/api/api-helpers.php:3377
msgid "File size must be at least %s."
msgstr "Rozmiar pliku musi wynosić co najmniej %s."

#: includes/api/api-helpers.php:3413
msgid "File type must be %s."
msgstr "Plik musi spełniać kryteria typu %s."

#: includes/fields/class-acf-field-accordion.php:25
msgid "Accordion"
msgstr "Zwijane panele"

#: includes/fields/class-acf-field-accordion.php:102
msgid "Open"
msgstr "Otwarte"

#: includes/fields/class-acf-field-accordion.php:103
msgid "Display this accordion as open on page load."
msgstr "Pokaż ten zwijany panel jako otwarty po załadowaniu strony."

#: includes/fields/class-acf-field-accordion.php:114
msgid "Multi-expand"
msgstr "Multi-expand"

#: includes/fields/class-acf-field-accordion.php:115
msgid "Allow this accordion to open without closing others."
msgstr "Zezwól, aby ten zwijany panel otwierał się bez zamykania innych."

#: includes/fields/class-acf-field-accordion.php:126,
#: includes/fields/class-acf-field-tab.php:117
msgid "Endpoint"
msgstr "Punkt końcowy"

#: includes/fields/class-acf-field-accordion.php:127
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Zdefiniuj punkt końcowy dla zatrzymania poprzedniego panelu zwijanego. Ten "
"panel zwijany nie będzie widoczny."

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Grupa przycisków"

#: includes/fields/class-acf-field-button-group.php:147,
#: includes/fields/class-acf-field-checkbox.php:324,
#: includes/fields/class-acf-field-radio.php:191,
#: includes/fields/class-acf-field-select.php:364
msgid "Choices"
msgstr "Wybory"

#: includes/fields/class-acf-field-button-group.php:148,
#: includes/fields/class-acf-field-checkbox.php:325,
#: includes/fields/class-acf-field-radio.php:192,
#: includes/fields/class-acf-field-select.php:365
msgid "Enter each choice on a new line."
msgstr "Wpisz każdy z wyborów w osobnej linii."

#: includes/fields/class-acf-field-button-group.php:148,
#: includes/fields/class-acf-field-checkbox.php:325,
#: includes/fields/class-acf-field-radio.php:192,
#: includes/fields/class-acf-field-select.php:365
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Aby uzyskać większą kontrolę, można określić zarówno wartość i etykietę w "
"niniejszy sposób:"

#: includes/fields/class-acf-field-button-group.php:148,
#: includes/fields/class-acf-field-checkbox.php:325,
#: includes/fields/class-acf-field-radio.php:192,
#: includes/fields/class-acf-field-select.php:365
msgid "red : Red"
msgstr "czerwony : Czerwony"

#: includes/fields/class-acf-field-button-group.php:158,
#: includes/fields/class-acf-field-page_link.php:482,
#: includes/fields/class-acf-field-post_object.php:394,
#: includes/fields/class-acf-field-radio.php:202,
#: includes/fields/class-acf-field-select.php:386,
#: includes/fields/class-acf-field-taxonomy.php:748,
#: includes/fields/class-acf-field-user.php:68
msgid "Allow Null?"
msgstr "Zezwolić na pustą wartość Null?"

#: includes/fields/class-acf-field-button-group.php:170,
#: includes/fields/class-acf-field-checkbox.php:366,
#: includes/fields/class-acf-field-color_picker.php:155,
#: includes/fields/class-acf-field-email.php:117,
#: includes/fields/class-acf-field-number.php:125,
#: includes/fields/class-acf-field-radio.php:245,
#: includes/fields/class-acf-field-range.php:163,
#: includes/fields/class-acf-field-select.php:375,
#: includes/fields/class-acf-field-text.php:97,
#: includes/fields/class-acf-field-textarea.php:103,
#: includes/fields/class-acf-field-true_false.php:148,
#: includes/fields/class-acf-field-url.php:101,
#: includes/fields/class-acf-field-wysiwyg.php:334
msgid "Default Value"
msgstr "Domyślna wartość"

#: includes/fields/class-acf-field-button-group.php:171,
#: includes/fields/class-acf-field-email.php:118,
#: includes/fields/class-acf-field-number.php:126,
#: includes/fields/class-acf-field-radio.php:246,
#: includes/fields/class-acf-field-range.php:164,
#: includes/fields/class-acf-field-text.php:98,
#: includes/fields/class-acf-field-textarea.php:104,
#: includes/fields/class-acf-field-url.php:102,
#: includes/fields/class-acf-field-wysiwyg.php:335
msgid "Appears when creating a new post"
msgstr "Wyświetlane podczas tworzenia nowego wpisu"

#: includes/fields/class-acf-field-button-group.php:187,
#: includes/fields/class-acf-field-checkbox.php:384,
#: includes/fields/class-acf-field-radio.php:263
msgid "Horizontal"
msgstr "Poziomy"

#: includes/fields/class-acf-field-button-group.php:188,
#: includes/fields/class-acf-field-checkbox.php:383,
#: includes/fields/class-acf-field-radio.php:262
msgid "Vertical"
msgstr "Pionowy"

#: includes/fields/class-acf-field-button-group.php:197,
#: includes/fields/class-acf-field-checkbox.php:405,
#: includes/fields/class-acf-field-file.php:227,
#: includes/fields/class-acf-field-link.php:170,
#: includes/fields/class-acf-field-radio.php:272,
#: includes/fields/class-acf-field-taxonomy.php:801
msgid "Return Value"
msgstr "Zwracana wartość"

#: includes/fields/class-acf-field-button-group.php:198,
#: includes/fields/class-acf-field-checkbox.php:406,
#: includes/fields/class-acf-field-file.php:228,
#: includes/fields/class-acf-field-link.php:171,
#: includes/fields/class-acf-field-radio.php:273
msgid "Specify the returned value on front end"
msgstr "Określ zwracaną wartość na stronie (front-end)"

#: includes/fields/class-acf-field-button-group.php:203,
#: includes/fields/class-acf-field-checkbox.php:411,
#: includes/fields/class-acf-field-radio.php:278,
#: includes/fields/class-acf-field-select.php:444
msgid "Value"
msgstr "Wartość"

#: includes/fields/class-acf-field-button-group.php:204,
#: includes/fields/class-acf-field-checkbox.php:412,
#: includes/fields/class-acf-field-radio.php:279,
#: includes/fields/class-acf-field-select.php:445,
#: pro/fields/class-acf-field-flexible-content.php:581,
#: includes/admin/views/field-group-fields.php:5
msgid "Label"
msgstr "Etykieta"

#: includes/fields/class-acf-field-button-group.php:205,
#: includes/fields/class-acf-field-checkbox.php:413,
#: includes/fields/class-acf-field-radio.php:280,
#: includes/fields/class-acf-field-select.php:446
msgid "Both (Array)"
msgstr "Oba (Array)"

#: includes/fields/class-acf-field-checkbox.php:25,
#: includes/fields/class-acf-field-taxonomy.php:733
msgid "Checkbox"
msgstr "Wybór (checkbox)"

#: includes/fields/class-acf-field-checkbox.php:144
msgid "Toggle All"
msgstr "Przełącz wszystko"

#: includes/fields/class-acf-field-checkbox.php:207
msgid "Add new choice"
msgstr "Dodaj nowy wybór"

#: includes/fields/class-acf-field-checkbox.php:335
msgid "Allow Custom"
msgstr "Zezwól na niestandardowe"

#: includes/fields/class-acf-field-checkbox.php:340
msgid "Allow 'custom' values to be added"
msgstr "Zezwalaj na dodawanie \"niestandardowych\" wartości"

#: includes/fields/class-acf-field-checkbox.php:348
msgid "Save Custom"
msgstr "Zapisz niestandardowe"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Save 'custom' values to the field's choices"
msgstr "Zapisz \"niestandardowe\" wartości tego pola wyboru"

#: includes/fields/class-acf-field-checkbox.php:367,
#: includes/fields/class-acf-field-select.php:376
msgid "Enter each default value on a new line"
msgstr "Wpisz każdą domyślną wartość w osobnej linii"

#: includes/fields/class-acf-field-checkbox.php:393
msgid "Toggle"
msgstr "Przełącznik (Toggle)"

#: includes/fields/class-acf-field-checkbox.php:394
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"Dołącz dodatkowe pole wyboru, aby grupowo włączać/wyłączać wszystkie pola "
"wyboru"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Wybór koloru"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Clear"
msgstr "Wyczyść"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Clear color"
msgstr "Usuń kolor"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Default"
msgstr "Domyślna wartość"

#: includes/fields/class-acf-field-color_picker.php:72
msgid "Select default color"
msgstr "Ustaw kolor domyślny"

#: includes/fields/class-acf-field-color_picker.php:73
msgid "Select Color"
msgstr "Wybierz kolor"

#: includes/fields/class-acf-field-color_picker.php:74
msgid "Color value"
msgstr "Wartość koloru"

#: includes/fields/class-acf-field-color_picker.php:95,
#: includes/fields/class-acf-field-color_picker.php:185
msgid "Hex String"
msgstr "Hex String"

#: includes/fields/class-acf-field-color_picker.php:96
msgid "RGBA String"
msgstr "Ciąg RGBA"

#: includes/fields/class-acf-field-color_picker.php:167
msgid "Enable Transparency"
msgstr "Włącz transparencję"

#: includes/fields/class-acf-field-color_picker.php:179,
#: includes/fields/class-acf-field-date_picker.php:216,
#: includes/fields/class-acf-field-date_time_picker.php:201,
#: includes/fields/class-acf-field-image.php:204,
#: includes/fields/class-acf-field-post_object.php:418,
#: includes/fields/class-acf-field-relationship.php:662,
#: includes/fields/class-acf-field-select.php:439,
#: includes/fields/class-acf-field-time_picker.php:131,
#: includes/fields/class-acf-field-user.php:90,
#: pro/fields/class-acf-field-gallery.php:573
msgid "Return Format"
msgstr "Zwracany format"

#: includes/fields/class-acf-field-color_picker.php:186
msgid "RGBA Array"
msgstr "Tablica RGBA"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Wybór daty"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Gotowe"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Dzisiaj"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Dalej"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Wstecz"

#: includes/fields/class-acf-field-date_picker.php:64
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Tydz"

#: includes/fields/class-acf-field-date_picker.php:181,
#: includes/fields/class-acf-field-date_time_picker.php:182,
#: includes/fields/class-acf-field-time_picker.php:114
msgid "Display Format"
msgstr "Format wyświetlania"

#: includes/fields/class-acf-field-date_picker.php:182,
#: includes/fields/class-acf-field-date_time_picker.php:183,
#: includes/fields/class-acf-field-time_picker.php:115
msgid "The format displayed when editing a post"
msgstr "Wyświetlany format przy edycji wpisu"

#: includes/fields/class-acf-field-date_picker.php:190,
#: includes/fields/class-acf-field-date_picker.php:226,
#: includes/fields/class-acf-field-date_time_picker.php:192,
#: includes/fields/class-acf-field-date_time_picker.php:211,
#: includes/fields/class-acf-field-time_picker.php:122,
#: includes/fields/class-acf-field-time_picker.php:139
msgid "Custom:"
msgstr "Niestandardowe:"

#: includes/fields/class-acf-field-date_picker.php:217,
#: includes/fields/class-acf-field-date_time_picker.php:202,
#: includes/fields/class-acf-field-time_picker.php:132
msgid "The format returned via template functions"
msgstr "Wartość zwracana przez funkcje w szablonie"

#: includes/fields/class-acf-field-date_picker.php:202
msgid "Save Format"
msgstr "Zapisz format"

#: includes/fields/class-acf-field-date_picker.php:203
msgid "The format used when saving a value"
msgstr "Format używany podczas zapisywania wartości"

#: includes/fields/class-acf-field-date_picker.php:237,
#: includes/fields/class-acf-field-date_time_picker.php:220
msgid "Week Starts On"
msgstr "Tydzień zaczyna się od"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Wybieranie daty i godziny"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Wybierz czas"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Czas"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Godzina"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuta"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Sekunda"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milisekunda"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosekunda"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Strefa czasu"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Teraz"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Gotowe"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Wybierz"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "E-mail"

#: includes/fields/class-acf-field-email.php:128,
#: includes/fields/class-acf-field-number.php:136,
#: includes/fields/class-acf-field-password.php:73,
#: includes/fields/class-acf-field-text.php:108,
#: includes/fields/class-acf-field-textarea.php:114,
#: includes/fields/class-acf-field-url.php:112
msgid "Placeholder Text"
msgstr "Placeholder (tekst zastępczy)"

#: includes/fields/class-acf-field-email.php:129,
#: includes/fields/class-acf-field-number.php:137,
#: includes/fields/class-acf-field-password.php:74,
#: includes/fields/class-acf-field-text.php:109,
#: includes/fields/class-acf-field-textarea.php:115,
#: includes/fields/class-acf-field-url.php:113
msgid "Appears within the input"
msgstr "Pojawia się w polu formularza"

#: includes/fields/class-acf-field-email.php:139,
#: includes/fields/class-acf-field-number.php:147,
#: includes/fields/class-acf-field-password.php:84,
#: includes/fields/class-acf-field-range.php:210,
#: includes/fields/class-acf-field-text.php:119
msgid "Prepend"
msgstr "Przed polem (prefiks)"

#: includes/fields/class-acf-field-email.php:140,
#: includes/fields/class-acf-field-number.php:148,
#: includes/fields/class-acf-field-password.php:85,
#: includes/fields/class-acf-field-range.php:211,
#: includes/fields/class-acf-field-text.php:120
msgid "Appears before the input"
msgstr "Pojawia się przed polem formularza"

#: includes/fields/class-acf-field-email.php:150,
#: includes/fields/class-acf-field-number.php:158,
#: includes/fields/class-acf-field-password.php:95,
#: includes/fields/class-acf-field-range.php:221,
#: includes/fields/class-acf-field-text.php:130
msgid "Append"
msgstr "Za polem (sufiks)"

#: includes/fields/class-acf-field-email.php:151,
#: includes/fields/class-acf-field-number.php:159,
#: includes/fields/class-acf-field-password.php:96,
#: includes/fields/class-acf-field-range.php:222,
#: includes/fields/class-acf-field-text.php:131
msgid "Appears after the input"
msgstr "Pojawia się za polem formularza"

#: includes/fields/class-acf-field-email.php:175
msgid "'%s' is not a valid email address"
msgstr "'%s' nie jest prawidłowym adresem e-mail"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Plik"

#: includes/fields/class-acf-field-file.php:58,
#: includes/admin/tools/class-acf-admin-tool-import.php:55
msgid "Select File"
msgstr "Wybierz plik"

#: includes/fields/class-acf-field-file.php:59
msgid "Edit File"
msgstr "Edytuj plik"

#: includes/fields/class-acf-field-file.php:60
msgid "Update File"
msgstr "Aktualizuj plik"

#: includes/fields/class-acf-field-file.php:147
msgid "File name"
msgstr "Nazwa pliku"

#: includes/fields/class-acf-field-file.php:151,
#: includes/fields/class-acf-field-file.php:264,
#: includes/fields/class-acf-field-file.php:277,
#: includes/fields/class-acf-field-image.php:276,
#: includes/fields/class-acf-field-image.php:313,
#: pro/fields/class-acf-field-gallery.php:682,
#: pro/fields/class-acf-field-gallery.php:719
msgid "File size"
msgstr "Wielkość pliku"

#: includes/fields/class-acf-field-file.php:157,
#: includes/fields/class-acf-field-image.php:137,
#: includes/fields/class-acf-field-link.php:142,
#: pro/fields/class-acf-field-gallery.php:335,
#: includes/admin/views/field-group-field.php:56
msgid "Edit"
msgstr "Edytuj"

#: includes/fields/class-acf-field-file.php:182,
#: includes/admin/tools/class-acf-admin-tool-import.php:89
msgid "No file selected"
msgstr "Nie zaznaczono żadnego pliku"

#: includes/fields/class-acf-field-file.php:182
msgid "Add File"
msgstr "Dodaj plik"

#: includes/fields/class-acf-field-file.php:233
msgid "File Array"
msgstr "Tablica pliku (Array)"

#: includes/fields/class-acf-field-file.php:234
msgid "File URL"
msgstr "Adres URL pliku"

#: includes/fields/class-acf-field-file.php:235
msgid "File ID"
msgstr "ID pliku"

#: includes/fields/class-acf-field-file.php:244,
#: includes/fields/class-acf-field-image.php:233,
#: pro/fields/class-acf-field-gallery.php:617
msgid "Library"
msgstr "Biblioteka"

#: includes/fields/class-acf-field-file.php:245,
#: includes/fields/class-acf-field-image.php:234,
#: pro/fields/class-acf-field-gallery.php:618
msgid "Limit the media library choice"
msgstr "Ograniczenie wyborów z biblioteki"

#: includes/fields/class-acf-field-file.php:250,
#: includes/fields/class-acf-field-image.php:239,
#: includes/locations/class-acf-location-attachment.php:73,
#: includes/locations/class-acf-location-comment.php:61,
#: includes/locations/class-acf-location-nav-menu.php:74,
#: includes/locations/class-acf-location-taxonomy.php:63,
#: includes/locations/class-acf-location-user-form.php:71,
#: includes/locations/class-acf-location-user-role.php:78,
#: includes/locations/class-acf-location-widget.php:65,
#: pro/fields/class-acf-field-gallery.php:623,
#: pro/locations/class-acf-location-block.php:66
msgid "All"
msgstr "Wszystkie"

#: includes/fields/class-acf-field-file.php:251,
#: includes/fields/class-acf-field-image.php:240,
#: pro/fields/class-acf-field-gallery.php:624
msgid "Uploaded to post"
msgstr "Przesłane do wpisu"

#: includes/fields/class-acf-field-file.php:260,
#: includes/fields/class-acf-field-image.php:249,
#: pro/fields/class-acf-field-gallery.php:655
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:261,
#: includes/fields/class-acf-field-file.php:274
msgid "Restrict which files can be uploaded"
msgstr "Określ jakie pliki mogą być przesyłane"

#: includes/fields/class-acf-field-file.php:273,
#: includes/fields/class-acf-field-image.php:286,
#: pro/fields/class-acf-field-gallery.php:692
msgid "Maximum"
msgstr "Maksimum"

#: includes/fields/class-acf-field-file.php:286,
#: includes/fields/class-acf-field-image.php:323,
#: pro/fields/class-acf-field-gallery.php:729
msgid "Allowed file types"
msgstr "Dozwolone typy plików"

#: includes/fields/class-acf-field-file.php:287,
#: includes/fields/class-acf-field-image.php:324,
#: pro/fields/class-acf-field-gallery.php:730
msgid "Comma separated list. Leave blank for all types"
msgstr "Lista rozdzielana przecinkami. Pozostaw puste dla wszystkich typów"

#: includes/fields/class-acf-field-file.php:469
msgid "%s requires a valid attachment ID."
msgstr "%s wymaga prawidłowego ID załącznika."

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Mapa Google"

#: includes/fields/class-acf-field-google-map.php:60
msgid "Sorry, this browser does not support geolocation"
msgstr "Przepraszamy, ta przeglądarka nie obsługuje geolokalizacji"

#: includes/fields/class-acf-field-google-map.php:155,
#: includes/fields/class-acf-field-relationship.php:615
msgid "Search"
msgstr "Szukaj"

#: includes/fields/class-acf-field-google-map.php:156
msgid "Clear location"
msgstr "Wyczyść lokalizację"

#: includes/fields/class-acf-field-google-map.php:157
msgid "Find current location"
msgstr "Znajdź aktualną lokalizację"

#: includes/fields/class-acf-field-google-map.php:160
msgid "Search for address..."
msgstr "Szukaj adresu..."

#: includes/fields/class-acf-field-google-map.php:192,
#: includes/fields/class-acf-field-google-map.php:205
msgid "Center"
msgstr "Wyśrodkuj"

#: includes/fields/class-acf-field-google-map.php:193,
#: includes/fields/class-acf-field-google-map.php:206
msgid "Center the initial map"
msgstr "Wyśrodkuj początkową mapę"

#: includes/fields/class-acf-field-google-map.php:219
msgid "Zoom"
msgstr "Zbliżenie"

#: includes/fields/class-acf-field-google-map.php:220
msgid "Set the initial zoom level"
msgstr "Ustaw początkowe zbliżenie"

#: includes/fields/class-acf-field-google-map.php:231,
#: includes/fields/class-acf-field-image.php:264,
#: includes/fields/class-acf-field-image.php:301,
#: includes/fields/class-acf-field-oembed.php:292,
#: pro/fields/class-acf-field-gallery.php:670,
#: pro/fields/class-acf-field-gallery.php:707
msgid "Height"
msgstr "Wysokość"

#: includes/fields/class-acf-field-google-map.php:232
msgid "Customize the map height"
msgstr "Dostosuj wysokość mapy"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Grupa"

#: includes/fields/class-acf-field-group.php:446,
#: pro/fields/class-acf-field-repeater.php:381
msgid "Sub Fields"
msgstr "Pola podrzędne"

#: includes/fields/class-acf-field-group.php:463,
#: pro/fields/class-acf-field-clone.php:851
msgid "Specify the style used to render the selected fields"
msgstr "Określ style stosowane to renderowania wybranych pól"

#: includes/fields/class-acf-field-group.php:468,
#: pro/fields/class-acf-field-clone.php:856,
#: pro/fields/class-acf-field-flexible-content.php:617,
#: pro/fields/class-acf-field-repeater.php:459,
#: pro/locations/class-acf-location-block.php:22
msgid "Block"
msgstr "Blok"

#: includes/fields/class-acf-field-group.php:469,
#: pro/fields/class-acf-field-clone.php:857,
#: pro/fields/class-acf-field-flexible-content.php:616,
#: pro/fields/class-acf-field-repeater.php:458
msgid "Table"
msgstr "Tabela"

#: includes/fields/class-acf-field-group.php:470,
#: pro/fields/class-acf-field-clone.php:858,
#: pro/fields/class-acf-field-flexible-content.php:618,
#: pro/fields/class-acf-field-repeater.php:460
msgid "Row"
msgstr "Wiersz"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Obraz"

#: includes/fields/class-acf-field-image.php:64
msgid "Select Image"
msgstr "Wybierz obraz"

#: includes/fields/class-acf-field-image.php:65
msgid "Edit Image"
msgstr "Edytuj obraz"

#: includes/fields/class-acf-field-image.php:66
msgid "Update Image"
msgstr "Aktualizuj obraz"

#: includes/fields/class-acf-field-image.php:158
msgid "No image selected"
msgstr "Nie wybrano obrazu"

#: includes/fields/class-acf-field-image.php:158
msgid "Add Image"
msgstr "Dodaj obraz"

#: includes/fields/class-acf-field-image.php:210,
#: pro/fields/class-acf-field-gallery.php:579
msgid "Image Array"
msgstr "Tablica obrazów (Array)"

#: includes/fields/class-acf-field-image.php:211,
#: pro/fields/class-acf-field-gallery.php:580
msgid "Image URL"
msgstr "Adres URL obrazu"

#: includes/fields/class-acf-field-image.php:212,
#: pro/fields/class-acf-field-gallery.php:581
msgid "Image ID"
msgstr "ID obrazu"

#: includes/fields/class-acf-field-image.php:221,
#: pro/fields/class-acf-field-gallery.php:590
msgid "Preview Size"
msgstr "Rozmiar podglądu"

#: includes/fields/class-acf-field-image.php:250,
#: includes/fields/class-acf-field-image.php:287,
#: pro/fields/class-acf-field-gallery.php:656,
#: pro/fields/class-acf-field-gallery.php:693
msgid "Restrict which images can be uploaded"
msgstr "Określ jakie obrazy mogą być przesyłane"

#: includes/fields/class-acf-field-image.php:253,
#: includes/fields/class-acf-field-image.php:290,
#: includes/fields/class-acf-field-oembed.php:279,
#: pro/fields/class-acf-field-gallery.php:659,
#: pro/fields/class-acf-field-gallery.php:696
msgid "Width"
msgstr "Szerokość"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Link"

#: includes/fields/class-acf-field-link.php:137
msgid "Select Link"
msgstr "Wybierz link"

#: includes/fields/class-acf-field-link.php:142
msgid "Opens in a new window/tab"
msgstr "Otwiera się w nowym oknie/karcie"

#: includes/fields/class-acf-field-link.php:176
msgid "Link Array"
msgstr "Tablica linków (Array)"

#: includes/fields/class-acf-field-link.php:177
msgid "Link URL"
msgstr "Adres URL linku"

#: includes/fields/class-acf-field-message.php:26,
#: includes/fields/class-acf-field-message.php:100,
#: includes/fields/class-acf-field-true_false.php:137
msgid "Message"
msgstr "Wiadomość"

#: includes/fields/class-acf-field-message.php:111,
#: includes/fields/class-acf-field-textarea.php:148
msgid "New Lines"
msgstr "Nowe linie"

#: includes/fields/class-acf-field-message.php:112,
#: includes/fields/class-acf-field-textarea.php:149
msgid "Controls how new lines are rendered"
msgstr "Kontroluje jak nowe linie są renderowane"

#: includes/fields/class-acf-field-message.php:116,
#: includes/fields/class-acf-field-textarea.php:153
msgid "Automatically add paragraphs"
msgstr "Automatycznie dodaj akapity"

#: includes/fields/class-acf-field-message.php:117,
#: includes/fields/class-acf-field-textarea.php:154
msgid "Automatically add &lt;br&gt;"
msgstr "Automatycznie dodaj &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:118,
#: includes/fields/class-acf-field-textarea.php:155
msgid "No Formatting"
msgstr "Brak formatowania"

#: includes/fields/class-acf-field-message.php:127
msgid "Escape HTML"
msgstr "Dodawaj znaki ucieczki do HTML (escape HTML)"

#: includes/fields/class-acf-field-message.php:128
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Zezwól aby znaczniki HTML były wyświetlane jako widoczny tekst, a nie "
"renderowane"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Liczba"

#: includes/fields/class-acf-field-number.php:169,
#: includes/fields/class-acf-field-range.php:174
msgid "Minimum Value"
msgstr "Minimalna wartość"

#: includes/fields/class-acf-field-number.php:180,
#: includes/fields/class-acf-field-range.php:186
msgid "Maximum Value"
msgstr "Maksymalna wartość"

#: includes/fields/class-acf-field-number.php:191,
#: includes/fields/class-acf-field-range.php:198
msgid "Step Size"
msgstr "Wielkość kroku"

#: includes/fields/class-acf-field-number.php:229
msgid "Value must be a number"
msgstr "Wartość musi być liczbą"

#: includes/fields/class-acf-field-number.php:244
msgid "Value must be equal to or higher than %d"
msgstr "Wartość musi być równa lub wyższa od %d"

#: includes/fields/class-acf-field-number.php:251
msgid "Value must be equal to or lower than %d"
msgstr "Wartość musi być równa lub niższa od %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:230
msgid "Enter URL"
msgstr "Wprowadź adres URL"

#: includes/fields/class-acf-field-oembed.php:276,
#: includes/fields/class-acf-field-oembed.php:289
msgid "Embed Size"
msgstr "Rozmiar osadzenia"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Link do strony"

#: includes/fields/class-acf-field-page_link.php:165
msgid "Archives"
msgstr "Archiwa"

#: includes/fields/class-acf-field-page_link.php:249,
#: includes/fields/class-acf-field-post_object.php:250,
#: includes/fields/class-acf-field-taxonomy.php:948
msgid "Parent"
msgstr "Rodzic"

#: includes/fields/class-acf-field-page_link.php:450,
#: includes/fields/class-acf-field-post_object.php:362,
#: includes/fields/class-acf-field-relationship.php:578
msgid "Filter by Post Type"
msgstr "Filtruj wg typu wpisu"

#: includes/fields/class-acf-field-page_link.php:458,
#: includes/fields/class-acf-field-post_object.php:370,
#: includes/fields/class-acf-field-relationship.php:586
msgid "All post types"
msgstr "Wszystkie typy wpisów"

#: includes/fields/class-acf-field-page_link.php:466,
#: includes/fields/class-acf-field-post_object.php:378,
#: includes/fields/class-acf-field-relationship.php:594
msgid "Filter by Taxonomy"
msgstr "Filtruj wg taksonomii"

#: includes/fields/class-acf-field-page_link.php:474,
#: includes/fields/class-acf-field-post_object.php:386,
#: includes/fields/class-acf-field-relationship.php:602
msgid "All taxonomies"
msgstr "Wszystkie taksonomie"

#: includes/fields/class-acf-field-page_link.php:494
msgid "Allow Archives URLs"
msgstr "Pozwól na adresy URL archiwów"

#: includes/fields/class-acf-field-page_link.php:506,
#: includes/fields/class-acf-field-post_object.php:406,
#: includes/fields/class-acf-field-select.php:398,
#: includes/fields/class-acf-field-user.php:79
msgid "Select multiple values?"
msgstr "Możliwość wyboru wielu wartości?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Hasło"

#: includes/fields/class-acf-field-post_object.php:25,
#: includes/fields/class-acf-field-post_object.php:423,
#: includes/fields/class-acf-field-relationship.php:667
msgid "Post Object"
msgstr "Obiekt wpisu"

#: includes/fields/class-acf-field-post_object.php:424,
#: includes/fields/class-acf-field-relationship.php:668
msgid "Post ID"
msgstr "ID wpisu"

#: includes/fields/class-acf-field-post_object.php:642
msgid "%1$s must have a valid post ID."
msgstr "%1$s musi mieć poprawny identyfikator wpisu."

#: includes/fields/class-acf-field-post_object.php:651
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s musi należeć do typu wpisu %2$s."
msgstr[1] "%1$s musi należeć do następujących typów wpisów: %2$s"
msgstr[2] "%1$s musi należeć do następujących typów wpisów: %2$s"

#: includes/fields/class-acf-field-post_object.php:667
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s musi należeć do taksonomii %2$s."
msgstr[1] "%1$s musi należeć do następujących taksonomii: %2$s"
msgstr[2] "%1$s musi należeć do następujących taksonomii: %2$s"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Przycisk opcji (radio)"

#: includes/fields/class-acf-field-radio.php:214
msgid "Other"
msgstr "Inne"

#: includes/fields/class-acf-field-radio.php:219
msgid "Add 'other' choice to allow for custom values"
msgstr ""
"Dodaj pole \"inne\" aby zezwolić na wartości definiowane przez użytkownika"

#: includes/fields/class-acf-field-radio.php:227
msgid "Save Other"
msgstr "Zapisz inne"

#: includes/fields/class-acf-field-radio.php:232
msgid "Save 'other' values to the field's choices"
msgstr "Dopisz zapisaną wartość pola \"inne\" do wyborów tego pola"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Zakres"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Relacja"

#: includes/fields/class-acf-field-relationship.php:63
msgid "Maximum values reached ( {max} values )"
msgstr "Maksymalna liczba wartości została przekroczona ( {max} wartości )"

#: includes/fields/class-acf-field-relationship.php:64
msgid "Loading"
msgstr "Ładowanie"

#: includes/fields/class-acf-field-relationship.php:65
msgid "No matches found"
msgstr "Nie znaleziono pasujących wyników"

#: includes/fields/class-acf-field-relationship.php:390
msgid "Select post type"
msgstr "Wybierz typ wpisu"

#: includes/fields/class-acf-field-relationship.php:399
msgid "Select taxonomy"
msgstr "Wybierz taksonomię"

#: includes/fields/class-acf-field-relationship.php:469
msgid "Search..."
msgstr "Szukaj..."

#: includes/fields/class-acf-field-relationship.php:610
msgid "Filters"
msgstr "Filtry"

#: includes/fields/class-acf-field-relationship.php:616,
#: includes/locations/class-acf-location-post-type.php:22
msgid "Post Type"
msgstr "Typ wpisu"

#: includes/fields/class-acf-field-relationship.php:617,
#: includes/fields/class-acf-field-taxonomy.php:28,
#: includes/fields/class-acf-field-taxonomy.php:714,
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taksonomia"

#: includes/fields/class-acf-field-relationship.php:626
msgid "Elements"
msgstr "Elementy"

#: includes/fields/class-acf-field-relationship.php:627
msgid "Selected elements will be displayed in each result"
msgstr "Wybrane elementy będą wyświetlone przy każdym wyniku"

#: includes/fields/class-acf-field-relationship.php:631,
#: includes/admin/views/field-group-options.php:150
msgid "Featured Image"
msgstr "Obrazek wyróżniający"

#: includes/fields/class-acf-field-relationship.php:640
msgid "Minimum posts"
msgstr "Minimum wpisów"

#: includes/fields/class-acf-field-relationship.php:651
msgid "Maximum posts"
msgstr "Maksimum wpisów"

#: includes/fields/class-acf-field-relationship.php:752,
#: pro/fields/class-acf-field-gallery.php:832
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s wymaga co najmniej %2$s wyboru"
msgstr[1] "%1$s wymaga co najmniej %2$s wyborów"
msgstr[2] "%1$s wymaga co najmniej %2$s wyborów"

#: includes/fields/class-acf-field-select.php:25,
#: includes/fields/class-acf-field-taxonomy.php:738
msgctxt "noun"
msgid "Select"
msgstr "Wybór"

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Dostępny jest jeden wynik. Aby go wybrać, wciśnij klawisz enter."

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "Dostępnych wyników - %d. Użyj strzałek w górę i w dół, aby nawigować."

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Nie znaleziono wyników"

#: includes/fields/class-acf-field-select.php:110
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Wpisz 1 lub więcej znaków"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Wpisz %d lub więcej znaków"

#: includes/fields/class-acf-field-select.php:112
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Proszę usunąć 1 znak"

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Proszę usunąć %d znaki/ów"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Możesz wybrać tylko 1 element"

#: includes/fields/class-acf-field-select.php:115
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Możesz wybrać tylko %d elementy/ów"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Ładuję więcej wyników&hellip;"

#: includes/fields/class-acf-field-select.php:117
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Szukam&hellip;"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Ładowanie zakończone niepowodzeniem"

#: includes/fields/class-acf-field-select.php:410,
#: includes/fields/class-acf-field-true_false.php:159
msgid "Stylised UI"
msgstr "Ostylowany interfejs użytkownika"

#: includes/fields/class-acf-field-select.php:422
msgid "Use AJAX to lazy load choices?"
msgstr "Użyć technologii AJAX do wczytywania wyników?"

#: includes/fields/class-acf-field-select.php:440
msgid "Specify the value returned"
msgstr "Określ zwracaną wartość"

#: includes/fields/class-acf-field-select.php:663
msgid "%1$s is not one of %2$s"
msgstr "%1$s nie jest jednym z %2$s"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Separator"

#: includes/fields/class-acf-field-tab.php:26
msgid "Tab"
msgstr "Zakładka"

#: includes/fields/class-acf-field-tab.php:103
msgid "Placement"
msgstr "Położenie"

#: includes/fields/class-acf-field-tab.php:107,
#: includes/admin/views/field-group-options.php:87
msgid "Top aligned"
msgstr "Wyrównanie do góry"

#: includes/fields/class-acf-field-tab.php:108,
#: includes/admin/views/field-group-options.php:88
msgid "Left aligned"
msgstr "Wyrównanie do lewej"

#: includes/fields/class-acf-field-tab.php:118
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr "Użyj tego pola jako punkt końcowy i zacznij nową grupę zakładek."

#: includes/fields/class-acf-field-taxonomy.php:673
msgctxt "No terms"
msgid "No %s"
msgstr "Brak %s"

#: includes/fields/class-acf-field-taxonomy.php:715
msgid "Select the taxonomy to be displayed"
msgstr "Wybierz taksonomię do wyświetlenia"

#: includes/fields/class-acf-field-taxonomy.php:726
msgid "Appearance"
msgstr "Wygląd"

#: includes/fields/class-acf-field-taxonomy.php:727
msgid "Select the appearance of this field"
msgstr "Określ wygląd tego pola"

#: includes/fields/class-acf-field-taxonomy.php:732
msgid "Multiple Values"
msgstr "Wiele wartości"

#: includes/fields/class-acf-field-taxonomy.php:734
msgid "Multi Select"
msgstr "Wybór wielokrotny"

#: includes/fields/class-acf-field-taxonomy.php:736
msgid "Single Value"
msgstr "Pojedyncza wartość"

#: includes/fields/class-acf-field-taxonomy.php:737
msgid "Radio Buttons"
msgstr "Przycisk opcji (radio)"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Create Terms"
msgstr "Tworzenie terminów taksonomii"

#: includes/fields/class-acf-field-taxonomy.php:766
msgid "Allow new terms to be created whilst editing"
msgstr "Pozwól na tworzenie nowych terminów taksonomii podczas edycji"

#: includes/fields/class-acf-field-taxonomy.php:777
msgid "Save Terms"
msgstr "Zapisz terminy taksonomii"

#: includes/fields/class-acf-field-taxonomy.php:778
msgid "Connect selected terms to the post"
msgstr "Przypisz wybrane terminy taksonomii do wpisu"

#: includes/fields/class-acf-field-taxonomy.php:789
msgid "Load Terms"
msgstr "Wczytaj terminy taksonomii"

#: includes/fields/class-acf-field-taxonomy.php:790
msgid "Load value from posts terms"
msgstr "Wczytaj wartości z terminów taksonomii z wpisu"

#: includes/fields/class-acf-field-taxonomy.php:806
msgid "Term Object"
msgstr "Obiekt terminu (WP_Term)"

#: includes/fields/class-acf-field-taxonomy.php:807
msgid "Term ID"
msgstr "ID terminu"

#: includes/fields/class-acf-field-taxonomy.php:862
msgid "User unable to add new %s"
msgstr "Użytkownik nie może dodać nowych %s"

#: includes/fields/class-acf-field-taxonomy.php:874
msgid "%s already exists"
msgstr "%s już istnieje"

#: includes/fields/class-acf-field-taxonomy.php:910
msgid "%s added"
msgstr "Dodano %s"

#: includes/fields/class-acf-field-taxonomy.php:926,
#: pro/fields/class-acf-field-flexible-content.php:597,
#: includes/admin/views/field-group-fields.php:6
msgid "Name"
msgstr "Nazwa"

#: includes/fields/class-acf-field-taxonomy.php:961,
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Dodaj"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Tekst"

#: includes/fields/class-acf-field-text.php:141,
#: includes/fields/class-acf-field-textarea.php:125
msgid "Character Limit"
msgstr "Limit znaków"

#: includes/fields/class-acf-field-text.php:142,
#: includes/fields/class-acf-field-textarea.php:126
msgid "Leave blank for no limit"
msgstr "Pozostaw puste w przypadku braku limitu"

#: includes/fields/class-acf-field-text.php:168,
#: includes/fields/class-acf-field-textarea.php:221
msgid "Value must not exceed %d characters"
msgstr "Wartość nie może przekraczać %d znaków"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Obszar tekstowy"

#: includes/fields/class-acf-field-textarea.php:136
msgid "Rows"
msgstr "Wiersze"

#: includes/fields/class-acf-field-textarea.php:137
msgid "Sets the textarea height"
msgstr "Określa wysokość obszaru tekstowego"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Wybieranie daty i godziny"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Prawda / Fałsz"

#: includes/fields/class-acf-field-true_false.php:138
msgid "Displays text alongside the checkbox"
msgstr "Wyświetla tekst obok pola wyboru (checkbox)"

#: includes/fields/class-acf-field-true_false.php:172
msgid "On Text"
msgstr "Tekst, gdy włączone"

#: includes/fields/class-acf-field-true_false.php:173
msgid "Text shown when active"
msgstr "Tekst wyświetlany, gdy jest aktywne"

#: includes/fields/class-acf-field-true_false.php:189
msgid "Off Text"
msgstr "Tekst, gdy wyłączone"

#: includes/fields/class-acf-field-true_false.php:190
msgid "Text shown when inactive"
msgstr "Tekst wyświetlany, gdy jest nieaktywne"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Url"

#: includes/fields/class-acf-field-url.php:154
msgid "Value must be a valid URL"
msgstr "Wartość musi być poprawnym adresem URL"

#: includes/fields/class-acf-field-user.php:53
msgid "Filter by role"
msgstr "Filtruj wg roli"

#: includes/fields/class-acf-field-user.php:61
msgid "All user roles"
msgstr "Wszystkie role użytkownika"

#: includes/fields/class-acf-field-user.php:95
msgid "User Array"
msgstr "Tablica użytkowników (Array)"

#: includes/fields/class-acf-field-user.php:96
msgid "User Object"
msgstr "Obiekt użytkownika"

#: includes/fields/class-acf-field-user.php:97
msgid "User ID"
msgstr "ID użytkownika"

#: includes/fields/class-acf-field-user.php:350
msgid "Error loading field."
msgstr "Błąd ładowania pola."

#: includes/fields/class-acf-field-user.php:355
msgid "Invalid request."
msgstr "Nieprawidłowe żądanie."

#: includes/fields/class-acf-field-user.php:517
msgid "%1$s must have a valid user ID."
msgstr "%1$s musi mieć ważny poprawny ID użytkownika."

#: includes/fields/class-acf-field-user.php:526
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s musi mieć użytkownika z rolą %2$s."
msgstr[1] "%1$s musi mieć użytkowników z następującymi rolami: %2$s"
msgstr[2] "%1$s musi mieć użytkowników z następującymi rolami: %2$s"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Edytor WYSIWYG"

#: includes/fields/class-acf-field-wysiwyg.php:282
msgid "Visual"
msgstr "Wizualny"

#: includes/fields/class-acf-field-wysiwyg.php:283
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Tekstowy"

#: includes/fields/class-acf-field-wysiwyg.php:289
msgid "Click to initialize TinyMCE"
msgstr "Kliknij, aby zainicjować TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:345
msgid "Tabs"
msgstr "Zakładki"

#: includes/fields/class-acf-field-wysiwyg.php:350
msgid "Visual & Text"
msgstr "Wizualna i Tekstowa"

#: includes/fields/class-acf-field-wysiwyg.php:351
msgid "Visual Only"
msgstr "Tylko wizualna"

#: includes/fields/class-acf-field-wysiwyg.php:352
msgid "Text Only"
msgstr "Tylko tekstowa"

#: includes/fields/class-acf-field-wysiwyg.php:361
msgid "Toolbar"
msgstr "Pasek narzędzi"

#: includes/fields/class-acf-field-wysiwyg.php:378
msgid "Show Media Upload Buttons?"
msgstr "Wyświetlić przyciski Dodawania mediów?"

#: includes/fields/class-acf-field-wysiwyg.php:390
msgid "Delay initialization?"
msgstr "Opóźnić inicjowanie?"

#: includes/fields/class-acf-field-wysiwyg.php:391
msgid "TinyMCE will not be initialized until field is clicked"
msgstr ""
"TinyMCE nie zostanie zainicjowany, dopóki to pole nie zostanie kliknięte"

#: includes/forms/form-front.php:40, pro/fields/class-acf-field-gallery.php:352
msgid "Title"
msgstr "Tytuł"

#: includes/forms/form-front.php:57
msgid "Validate Email"
msgstr "Waliduj E-mail"

#: includes/forms/form-front.php:107
msgid "Post updated"
msgstr "Wpis zaktualizowany"

#: includes/forms/form-front.php:236
msgid "Spam Detected"
msgstr "Wykryto Spam"

#: includes/forms/form-user.php:353
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Błąd</strong>: %s"

#: includes/locations/abstract-acf-location.php:105
msgid "is equal to"
msgstr "jest równe"

#: includes/locations/abstract-acf-location.php:106
msgid "is not equal to"
msgstr "jest inne niż"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Załącznik"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Wszystkie formaty %s"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Komentarz"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Rola bieżącego użytkownika"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super Administrator"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Bieżący użytkownik"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Zalogowany"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Wyświetla stronę (front-end)"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Wyświetla kokpit (back-end)"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Element menu"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Pozycje menu"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Rodzic strony"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Szablon strony"

#: includes/locations/class-acf-location-page-template.php:73,
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Domyślny szablon"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Typ strony"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Strona główna"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Strona wpisów"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Strona najwyższego poziomu (brak rodzica)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Strona będąca rodzicem (posiada potomne)"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Strona będąca potomną (ma rodziców)"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Kategoria wpisu"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Format wpisu"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Status wpisu"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Taksonomia wpisu"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Szablon wpisu"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Formularz użytkownika"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Dodaj / Edytuj"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Zarejestruj"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Rola użytkownika"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widżet"

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Opublikuj"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Żadna grupa pól nie została dodana do tej strony opcji. <a href=\"%s"
"\">Utwórz grupę własnych pól</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Błąd</b>. Nie można połączyć z serwerem aktualizacji"

#: pro/admin/admin-updates.php:122,
#: pro/admin/views/html-settings-updates.php:12
msgid "Updates"
msgstr "Aktualizacje"

#: pro/admin/admin-updates.php:209
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Error</b>. Nie można uwierzytelnić pakietu aktualizacyjnego. Proszę "
"sprawdzić ponownie lub dezaktywować i ponownie uaktywnić licencję ACF PRO."

#: pro/admin/admin-updates.php:196
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""
"<b>Błąd</b>. Twoja licencja dla tej strony wygasła lub została "
"dezaktywowana. Proszę ponownie aktywować licencję ACF PRO."

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Klon"

#: pro/fields/class-acf-field-clone.php:815
msgid "Select one or more fields you wish to clone"
msgstr "Wybierz jedno lub więcej pól które chcesz sklonować"

#: pro/fields/class-acf-field-clone.php:834
msgid "Display"
msgstr "Wyświetl"

#: pro/fields/class-acf-field-clone.php:835
msgid "Specify the style used to render the clone field"
msgstr "Określ styl wykorzystywany do stosowania w klonowanych polach"

#: pro/fields/class-acf-field-clone.php:840
msgid "Group (displays selected fields in a group within this field)"
msgstr "Grupuj (wyświetla wybrane pola w grupie)"

#: pro/fields/class-acf-field-clone.php:841
msgid "Seamless (replaces this field with selected fields)"
msgstr "Ujednolicenie (zastępuje to pole wybranymi polami)"

#: pro/fields/class-acf-field-clone.php:864
msgid "Labels will be displayed as %s"
msgstr "Etykiety będą wyświetlane jako %s"

#: pro/fields/class-acf-field-clone.php:869
msgid "Prefix Field Labels"
msgstr "Prefiks Etykiet Pól"

#: pro/fields/class-acf-field-clone.php:880
msgid "Values will be saved as %s"
msgstr "Wartości będą zapisane jako %s"

#: pro/fields/class-acf-field-clone.php:885
msgid "Prefix Field Names"
msgstr "Prefiks Nazw Pól"

#: pro/fields/class-acf-field-clone.php:1001
msgid "Unknown field"
msgstr "Nieznane pole"

#: pro/fields/class-acf-field-clone.php:1038
msgid "Unknown field group"
msgstr "Nieznana grupa pól"

#: pro/fields/class-acf-field-clone.php:1042
msgid "All fields from %s field group"
msgstr "Wszystkie pola z grupy pola %s"

#: pro/fields/class-acf-field-flexible-content.php:25,
#: includes/admin/views/html-notice-upgrade.php:12
msgid "Flexible Content"
msgstr "Elastyczne treść"

#: pro/fields/class-acf-field-flexible-content.php:31,
#: pro/fields/class-acf-field-repeater.php:182,
#: pro/fields/class-acf-field-repeater.php:473
msgid "Add Row"
msgstr "Dodaj wiersz"

#: pro/fields/class-acf-field-flexible-content.php:71,
#: pro/fields/class-acf-field-flexible-content.php:917,
#: pro/fields/class-acf-field-flexible-content.php:996
msgid "layout"
msgid_plural "layouts"
msgstr[0] "układ"
msgstr[1] "układy"
msgstr[2] "układów"

#: pro/fields/class-acf-field-flexible-content.php:72
msgid "layouts"
msgstr "układy"

#: pro/fields/class-acf-field-flexible-content.php:75,
#: pro/fields/class-acf-field-flexible-content.php:916,
#: pro/fields/class-acf-field-flexible-content.php:995
msgid "This field requires at least {min} {label} {identifier}"
msgstr "To pole wymaga przynajmniej {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:76
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "To pole ma ograniczenie {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:79
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} dostępne (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:80
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} wymagane (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:83
msgid "Flexible Content requires at least 1 layout"
msgstr "Elastyczne pole wymaga przynajmniej 1 układu"

#: pro/fields/class-acf-field-flexible-content.php:276
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Kliknij przycisk \"%s\" poniżej, aby zacząć tworzyć nowy układ"

#: pro/fields/class-acf-field-flexible-content.php:410,
#: pro/fields/class-acf-field-repeater.php:295,
#: includes/admin/views/field-group-field.php:49
msgid "Drag to reorder"
msgstr "Przeciągnij aby zmienić kolejność"

#: pro/fields/class-acf-field-flexible-content.php:413
msgid "Add layout"
msgstr "Dodaj układ"

#: pro/fields/class-acf-field-flexible-content.php:414
msgid "Duplicate layout"
msgstr "Powiel układ"

#: pro/fields/class-acf-field-flexible-content.php:415
msgid "Remove layout"
msgstr "Usuń układ"

#: pro/fields/class-acf-field-flexible-content.php:416,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Click to toggle"
msgstr "Kliknij, aby przełączyć"

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder Layout"
msgstr "Zmień kolejność układów"

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder"
msgstr "Zmień kolejność"

#: pro/fields/class-acf-field-flexible-content.php:552
msgid "Delete Layout"
msgstr "Usuń układ"

#: pro/fields/class-acf-field-flexible-content.php:552,
#: includes/admin/views/field-group-field.php:59
msgid "Delete"
msgstr "Usuń"

#: pro/fields/class-acf-field-flexible-content.php:553
msgid "Duplicate Layout"
msgstr "Duplikuj układ"

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Add New Layout"
msgstr "Dodaj nowy układ"

#: pro/fields/class-acf-field-flexible-content.php:635
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:650
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:679,
#: pro/fields/class-acf-field-repeater.php:469
msgid "Button Label"
msgstr "Etykieta przycisku"

#: pro/fields/class-acf-field-flexible-content.php:690
msgid "Minimum Layouts"
msgstr "Minimalna liczba układów"

#: pro/fields/class-acf-field-flexible-content.php:701
msgid "Maximum Layouts"
msgstr "Maksymalna liczba układów"

#: pro/fields/class-acf-field-flexible-content.php:1704,
#: pro/fields/class-acf-field-repeater.php:1108
msgid "%s must be of type array or null."
msgstr "%s musi być typu tablicy lub null."

#: pro/fields/class-acf-field-flexible-content.php:1715
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] "%1$s musi zawierać co najmniej %2$s %3$s układ."
msgstr[1] "%1$s musi zawierać co najmniej %2$s %3$s układy."
msgstr[2] "%1$s musi zawierać co najmniej %2$s %3$s układów."

#: pro/fields/class-acf-field-flexible-content.php:1731
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] "%1$s musi zawierać co najwyżej %2$s %3$s układ."
msgstr[1] "%1$s musi zawierać co najwyżej %2$s %3$s układy."
msgstr[2] "%1$s musi zawierać co najwyżej %2$s %3$s układów."

#: pro/fields/class-acf-field-gallery.php:25,
#: includes/admin/views/html-notice-upgrade.php:15
msgid "Gallery"
msgstr "Galeria"

#: pro/fields/class-acf-field-gallery.php:73
msgid "Add Image to Gallery"
msgstr "Dodaj obraz do galerii"

#: pro/fields/class-acf-field-gallery.php:74
msgid "Maximum selection reached"
msgstr "Maksimum ilości wyborów osiągnięte"

#: pro/fields/class-acf-field-gallery.php:320
msgid "Length"
msgstr "Długość"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr "Etykieta"

#: pro/fields/class-acf-field-gallery.php:376
msgid "Alt Text"
msgstr "Tekst alternatywny"

#: pro/fields/class-acf-field-gallery.php:500
msgid "Add to gallery"
msgstr "Dodaj do galerii"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Bulk actions"
msgstr "Działania na wielu"

#: pro/fields/class-acf-field-gallery.php:505
msgid "Sort by date uploaded"
msgstr "Sortuj po dacie przesłania"

#: pro/fields/class-acf-field-gallery.php:506
msgid "Sort by date modified"
msgstr "Sortuj po dacie modyfikacji"

#: pro/fields/class-acf-field-gallery.php:507
msgid "Sort by title"
msgstr "Sortuj po tytule"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Reverse current order"
msgstr "Odwróć aktualną kolejność"

#: pro/fields/class-acf-field-gallery.php:520
msgid "Close"
msgstr "Zamknij"

#: pro/fields/class-acf-field-gallery.php:602
msgid "Insert"
msgstr "Wstaw"

#: pro/fields/class-acf-field-gallery.php:603
msgid "Specify where new attachments are added"
msgstr "Określ gdzie są dodawane nowe załączniki"

#: pro/fields/class-acf-field-gallery.php:607
msgid "Append to the end"
msgstr "Dodaj na końcu"

#: pro/fields/class-acf-field-gallery.php:608
msgid "Prepend to the beginning"
msgstr "Dodaj do początku"

#: pro/fields/class-acf-field-gallery.php:633
msgid "Minimum Selection"
msgstr "Minimalna liczba wybranych elementów"

#: pro/fields/class-acf-field-gallery.php:644
msgid "Maximum Selection"
msgstr "Maksymalna liczba wybranych elementów"

#: pro/fields/class-acf-field-repeater.php:25,
#: includes/admin/views/html-notice-upgrade.php:9
msgid "Repeater"
msgstr "Pole powtarzalne"

#: pro/fields/class-acf-field-repeater.php:64,
#: pro/fields/class-acf-field-repeater.php:659
msgid "Minimum rows reached ({min} rows)"
msgstr "Osiągnięto minimum liczby wierszy ( {min} wierszy )"

#: pro/fields/class-acf-field-repeater.php:65
msgid "Maximum rows reached ({max} rows)"
msgstr "Osiągnięto maksimum liczby wierszy ( {max} wierszy )"

#: pro/fields/class-acf-field-repeater.php:334
msgid "Add row"
msgstr "Dodaj wiersz"

#: pro/fields/class-acf-field-repeater.php:335
msgid "Duplicate row"
msgstr "Powiel wiersz"

#: pro/fields/class-acf-field-repeater.php:336
msgid "Remove row"
msgstr "Usuń wiersz"

#: pro/fields/class-acf-field-repeater.php:414
msgid "Collapsed"
msgstr "Zwinięty"

#: pro/fields/class-acf-field-repeater.php:415
msgid "Select a sub field to show when row is collapsed"
msgstr ""
"Wybierz pole podrzędne, które mają być pokazane kiedy wiersz jest zwinięty"

#: pro/fields/class-acf-field-repeater.php:427
msgid "Minimum Rows"
msgstr "Minimalna liczba wierszy"

#: pro/fields/class-acf-field-repeater.php:439
msgid "Maximum Rows"
msgstr "Maksymalna liczba wierszy"

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr "Nie istnieją żadne typy bloków"

#: pro/locations/class-acf-location-options-page.php:22,
#: includes/admin/views/html-notice-upgrade.php:18
msgid "Options Page"
msgstr "Strona opcji"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Strona opcji nie istnieje"

#: tests/basic/test-blocks.php:456
msgid "Hero"
msgstr "Hero"

#: tests/basic/test-blocks.php:457
msgid "Display a random hero image."
msgstr "Wyświetl losowy obrazek typu hero."

#: tests/basic/test-blocks.php:630
msgid "Test JS"
msgstr "Test JS"

#: tests/basic/test-blocks.php:631
msgid "A block for testing JS."
msgstr "Blok do testowania JS."

#: includes/admin/tools/class-acf-admin-tool-export.php:35
msgid "Export Field Groups"
msgstr "Eksportuj grupy pól"

#: includes/admin/tools/class-acf-admin-tool-export.php:39,
#: includes/admin/tools/class-acf-admin-tool-export.php:335,
#: includes/admin/tools/class-acf-admin-tool-export.php:364
msgid "Generate PHP"
msgstr "Utwórz PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:96,
#: includes/admin/tools/class-acf-admin-tool-export.php:131
msgid "No field groups selected"
msgstr "Nie zaznaczono żadnej grupy pól"

#: includes/admin/tools/class-acf-admin-tool-export.php:167
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "Wyeksportowano 1 grupę pól."
msgstr[1] "Wyeksportowano %s grupy pól."
msgstr[2] "Wyeksportowano %s grup pól."

#: includes/admin/tools/class-acf-admin-tool-export.php:233,
#: includes/admin/tools/class-acf-admin-tool-export.php:262
msgid "Select Field Groups"
msgstr "Wybierz grupy pól"

#: includes/admin/tools/class-acf-admin-tool-export.php:329
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Wybierz grupy pól, które chcesz wyeksportować, a następnie wybierz metodę "
"eksportu. Użyj przycisku pobierania aby wyeksportować do pliku .json, który "
"można następnie zaimportować do innej instalacji ACF. Użyj przycisku generuj "
"do wyeksportowania ustawień do kodu PHP, który można umieścić w motywie."

#: includes/admin/tools/class-acf-admin-tool-export.php:334
msgid "Export File"
msgstr "Plik eksportu"

#: includes/admin/tools/class-acf-admin-tool-export.php:405
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Poniższy kod może być użyty do rejestracji lokalnej wersji wybranej grupy "
"lub grup pól. Lokalna grupa pól może dostarczyć wiele korzyści takich jak "
"szybszy czas ładowania, możliwość wersjonowania i dynamiczne pola/"
"ustawienia. Wystarczy skopiować i wkleić poniższy kod do pliku functions.php "
"Twojego motywu lub dołączyć go do zewnętrznego pliku."

#: includes/admin/tools/class-acf-admin-tool-export.php:435
msgid "Copy to clipboard"
msgstr "Skopiuj do schowka"

#: includes/admin/tools/class-acf-admin-tool-export.php:472
msgid "Copied"
msgstr "Skopiowano"

#: includes/admin/tools/class-acf-admin-tool-import.php:28
msgid "Import Field Groups"
msgstr "Importuj grupy pól"

#: includes/admin/tools/class-acf-admin-tool-import.php:49
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Wybierz plik JSON Advanced Custom Fields, który chcesz zaimportować. Gdy "
"klikniesz przycisk importu poniżej, ACF zaimportuje grupy pól."

#: includes/admin/tools/class-acf-admin-tool-import.php:66
msgid "Import File"
msgstr "Plik importu"

#: includes/admin/tools/class-acf-admin-tool-import.php:97
msgid "Error uploading file. Please try again"
msgstr "Błąd przesyłania pliku. Proszę spróbować ponownie"

#: includes/admin/tools/class-acf-admin-tool-import.php:102
msgid "Incorrect file type"
msgstr "Błędny typ pliku"

#: includes/admin/tools/class-acf-admin-tool-import.php:111
msgid "Import file empty"
msgstr "Importowany plik jest pusty"

#: includes/admin/tools/class-acf-admin-tool-import.php:142
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Zaimportowano 1 grupę pól"
msgstr[1] "Zaimportowano %s grupy pól"
msgstr[2] "Zaimportowano %s grup pól"

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "Wyświetlaj pola warunkowo"

#: includes/admin/views/field-group-field-conditional-logic.php:60
msgid "Show this field if"
msgstr "Pokaż to pole jeśli"

#: includes/admin/views/field-group-field-conditional-logic.php:172,
#: includes/admin/views/field-group-locations.php:38
msgid "Add rule group"
msgstr "Dodaj grupę warunków"

#: includes/admin/views/field-group-field.php:53,
#: includes/admin/views/field-group-field.php:56
msgid "Edit field"
msgstr "Edytuj pole"

#: includes/admin/views/field-group-field.php:57
msgid "Duplicate field"
msgstr "Duplikuj to pole"

#: includes/admin/views/field-group-field.php:58
msgid "Move field to another group"
msgstr "Przenieś pole do innej grupy"

#: includes/admin/views/field-group-field.php:58
msgid "Move"
msgstr "Przenieś"

#: includes/admin/views/field-group-field.php:59
msgid "Delete field"
msgstr "Usuń pole"

#: includes/admin/views/field-group-field.php:78
msgid "Field Label"
msgstr "Etykieta pola"

#: includes/admin/views/field-group-field.php:79
msgid "This is the name which will appear on the EDIT page"
msgstr "Ta nazwa będzie widoczna na stronie edycji"

#: includes/admin/views/field-group-field.php:92
msgid "Field Name"
msgstr "Nazwa pola"

#: includes/admin/views/field-group-field.php:93
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Pojedyncze słowo, bez spacji. Dozwolone są myślniki i podkreślniki"

#: includes/admin/views/field-group-field.php:106
msgid "Field Type"
msgstr "Typ pola"

#: includes/admin/views/field-group-field.php:121
msgid "Instructions"
msgstr "Instrukcje"

#: includes/admin/views/field-group-field.php:122
msgid "Instructions for authors. Shown when submitting data"
msgstr "Instrukcje dla autorów. Będą widoczne w trakcie wprowadzania danych"

#: includes/admin/views/field-group-field.php:135
msgid "Required?"
msgstr "Wymagane?"

#: includes/admin/views/field-group-field.php:161
msgid "Wrapper Attributes"
msgstr "Atrybuty kontenera"

#: includes/admin/views/field-group-field.php:167
msgid "width"
msgstr "szerokość"

#: includes/admin/views/field-group-field.php:185
msgid "class"
msgstr "class"

#: includes/admin/views/field-group-field.php:201
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:215,
#: includes/admin/views/field-group-field.php:215
msgid "Close Field"
msgstr "Zamknij to pole"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Kolejność"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Typ"

#: includes/admin/views/field-group-fields.php:19
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Brak pól. Kliknij przycisk <strong>+ Dodaj pole</strong> aby utworzyć "
"pierwsze pole."

#: includes/admin/views/field-group-fields.php:44
msgid "+ Add Field"
msgstr "+ Dodaj pole"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Warunki"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Utwórz zestaw warunków, które określą w których miejscach będą wykorzystane "
"zdefiniowane tutaj własne pola"

#: includes/admin/views/field-group-options.php:10
msgid "Active"
msgstr "Aktywne"

#: includes/admin/views/field-group-options.php:27
msgid "Show in REST API"
msgstr "Pokaż w API REST"

#: includes/admin/views/field-group-options.php:44
msgid "Style"
msgstr "Styl"

#: includes/admin/views/field-group-options.php:51
msgid "Standard (WP metabox)"
msgstr "Standardowy (WP metabox)"

#: includes/admin/views/field-group-options.php:52
msgid "Seamless (no metabox)"
msgstr "Bezpodziałowy (brak metaboxa)"

#: includes/admin/views/field-group-options.php:61
msgid "Position"
msgstr "Pozycja"

#: includes/admin/views/field-group-options.php:68
msgid "High (after title)"
msgstr "Wysoka (pod tytułem)"

#: includes/admin/views/field-group-options.php:69
msgid "Normal (after content)"
msgstr "Normalna (pod edytorem)"

#: includes/admin/views/field-group-options.php:70
msgid "Side"
msgstr "Boczna"

#: includes/admin/views/field-group-options.php:80
msgid "Label placement"
msgstr "Umieszczenie etykiet"

#: includes/admin/views/field-group-options.php:97
msgid "Instruction placement"
msgstr "Umieszczenie instrukcji"

#: includes/admin/views/field-group-options.php:104
msgid "Below labels"
msgstr "Pod etykietami"

#: includes/admin/views/field-group-options.php:105
msgid "Below fields"
msgstr "Pod polami"

#: includes/admin/views/field-group-options.php:114
msgid "Order No."
msgstr "Nr w kolejności."

#: includes/admin/views/field-group-options.php:115
msgid "Field groups with a lower order will appear first"
msgstr "Grupy pól z niższym numerem pojawią się pierwsze"

#: includes/admin/views/field-group-options.php:128
msgid "Shown in field group list"
msgstr "Wyświetlany na liście grupy pól"

#: includes/admin/views/field-group-options.php:139
msgid "Permalink"
msgstr "Odnośnik bezpośredni"

#: includes/admin/views/field-group-options.php:140
msgid "Content Editor"
msgstr "Edytor treści"

#: includes/admin/views/field-group-options.php:141
msgid "Excerpt"
msgstr "Wypis"

#: includes/admin/views/field-group-options.php:143
msgid "Discussion"
msgstr "Dyskusja"

#: includes/admin/views/field-group-options.php:145
msgid "Revisions"
msgstr "Wersje"

#: includes/admin/views/field-group-options.php:146
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/field-group-options.php:147
msgid "Author"
msgstr "Autor"

#: includes/admin/views/field-group-options.php:148
msgid "Format"
msgstr "Format"

#: includes/admin/views/field-group-options.php:149
msgid "Page Attributes"
msgstr "Atrybuty strony"

#: includes/admin/views/field-group-options.php:151
msgid "Categories"
msgstr "Kategorie"

#: includes/admin/views/field-group-options.php:152
msgid "Tags"
msgstr "Tagi"

#: includes/admin/views/field-group-options.php:153
msgid "Send Trackbacks"
msgstr "Wyślij trackbacki"

#: includes/admin/views/field-group-options.php:161
msgid "Hide on screen"
msgstr "Ukryj na stronie edycji"

#: includes/admin/views/field-group-options.php:162
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Wybierz</b> elementy, które chcesz <b>ukryć</b> na stronie edycji."

#: includes/admin/views/field-group-options.php:162
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Jeśli na stronie edycji znajduje się kilka grup pól, zostaną zastosowane "
"ustawienia pierwszej z nich. (pierwsza grupa pól to ta, która ma najniższy "
"numer w kolejności)"

#: includes/admin/views/html-admin-navigation.php:89
msgid "Upgrade to Pro"
msgstr "Przejdź na wersję Pro"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Następujące witryny wymagają aktualizacji bazy danych. Zaznacz te, które "
"chcesz zaktualizować i kliknij %s."

#: includes/admin/views/html-admin-page-upgrade-network.php:26,
#: includes/admin/views/html-admin-page-upgrade-network.php:27,
#: includes/admin/views/html-admin-page-upgrade-network.php:96
msgid "Upgrade Sites"
msgstr "Aktualizacja witryn"

#: includes/admin/views/html-admin-page-upgrade-network.php:36,
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Witryna"

#: includes/admin/views/html-admin-page-upgrade-network.php:80
msgid "Site is up to date"
msgstr "Ta witryna jest aktualna"

#: includes/admin/views/html-admin-page-upgrade-network.php:78
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "Strona wymaga aktualizacji bazy danych z %1$s do %2$s"

#: includes/admin/views/html-admin-page-upgrade-network.php:97
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Aktualizacja bazy danych zakończona. <a href=\"%s\">Wróć do kokpitu sieci</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:117
msgid "Please select at least one site to upgrade."
msgstr "Proszę wybrać co najmniej jedną witrynę do uaktualnienia."

#: includes/admin/views/html-admin-page-upgrade-network.php:121,
#: includes/admin/views/html-notice-upgrade.php:45
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Zdecydowanie zaleca się wykonanie kopii zapasowej bazy danych przed "
"kontynuowaniem. Czy na pewno chcesz uruchomić aktualizacje teraz?"

#: includes/admin/views/html-admin-page-upgrade-network.php:148,
#: includes/admin/views/html-admin-page-upgrade.php:31
msgid "Upgrading data to version %s"
msgstr "Aktualizowanie danych do wersji %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:162
msgid "Upgrade complete."
msgstr "Aktualizacja zakończona."

#: includes/admin/views/html-admin-page-upgrade-network.php:165,
#: includes/admin/views/html-admin-page-upgrade.php:65
msgid "Upgrade failed."
msgstr "Aktualizacja nie powiodła się."

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Czytam zadania aktualizacji..."

#: includes/admin/views/html-admin-page-upgrade.php:33
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Aktualizacja bazy danych zakończona. <a href=\"%s\">Zobacz co nowego</a>"

#: includes/admin/views/html-admin-tools.php:24
msgid "Back to all tools"
msgstr "Wróć do wszystkich narzędzi"

#: includes/admin/views/html-notice-upgrade.php:28
msgid "Database Upgrade Required"
msgstr "Wymagana jest aktualizacja bazy danych"

#: includes/admin/views/html-notice-upgrade.php:29
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Dziękujemy za aktualizację do %1$s v%2$s!"

#: includes/admin/views/html-notice-upgrade.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr "Ta wersja zawiera ulepszenia bazy danych i wymaga uaktualnienia."

#: includes/admin/views/html-notice-upgrade.php:31
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Proszę również sprawdzić, czy wszystkie dodatki premium (%s) są "
"zaktualizowane do najnowszej wersji."

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Deaktywuj licencję"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Aktywuj licencję"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Informacje o licencji"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Żeby odblokować aktualizacje proszę podać swój klucz licencyjny poniżej. "
"Jeśli nie posiadasz klucza prosimy zapoznać się ze <a href=\"%s\" target="
"\"_blank\">szczegółami i cennikiem</a>."

#: pro/admin/views/html-settings-updates.php:41
msgid "License Key"
msgstr "Klucz licencyjny"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr "Twój klucz licencyjny jest zdefiniowany w pliku wp-config.php."

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr "Ponów próbę aktywacji"

#: pro/admin/views/html-settings-updates.php:76
msgid "Update Information"
msgstr "Informacje o aktualizacji"

#: pro/admin/views/html-settings-updates.php:83
msgid "Current Version"
msgstr "Zainstalowana wersja"

#: pro/admin/views/html-settings-updates.php:91
msgid "Latest Version"
msgstr "Najnowsza wersja"

#: pro/admin/views/html-settings-updates.php:99
msgid "Update Available"
msgstr "Dostępna aktualizacja"

#: pro/admin/views/html-settings-updates.php:111
msgid "Please enter your license key above to unlock updates"
msgstr ""
"Proszę wpisać swój klucz licencyjny powyżej aby odblokować aktualizacje"

#: pro/admin/views/html-settings-updates.php:109
msgid "Update Plugin"
msgstr "Aktualizuj wtyczkę"

#: pro/admin/views/html-settings-updates.php:107
msgid "Please reactivate your license to unlock updates"
msgstr ""
"Proszę wpisać swój klucz licencyjny powyżej aby odblokować aktualizacje"

#: pro/admin/views/html-settings-updates.php:124
msgid "Changelog"
msgstr "Dziennik zmian"

#: pro/admin/views/html-settings-updates.php:134
msgid "Upgrade Notice"
msgstr "Informacje o aktualizacji"

#~ msgid "Inactive"
#~ msgstr "Nieaktywne"

#~ msgid "Elliot Condon"
#~ msgstr "Elliot Condon"

#, php-format
#~ msgid "Inactive <span class=\"count\">(%s)</span>"
#~ msgid_plural "Inactive <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Nieaktywne <span class=\"count\">(%s)</span>"
#~ msgstr[1] "Nieaktywne <span class=\"count\">(%s)</span>"
#~ msgstr[2] "Nieaktywnych <span class=\"count\">(%s)</span>"

#, php-format
#~ msgid "%s field group synchronised."
#~ msgid_plural "%s field groups synchronised."
#~ msgstr[0] "%s grupa pól została zsynchronizowana."
#~ msgstr[1] "%s grupy pól zostały zsynchronizowane."
#~ msgstr[2] "%s grup pól zostało zsynchronizowanych."

#~ msgid "Status"
#~ msgstr "Status"

#, php-format
#~ msgid "See what's new in <a href=\"%s\">version %s</a>."
#~ msgstr "Zobacz co nowego w <a href=\"%s\">wersji %s</a>."

#~ msgid "Resources"
#~ msgstr "Zasoby"

#~ msgid "Website"
#~ msgstr "Witryna"

#~ msgid "Documentation"
#~ msgstr "Dokumentacja"

#~ msgid "Pro"
#~ msgstr "Pro"

#, php-format
#~ msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
#~ msgstr "Dziękujemy za tworzenie z <a href=\"%s\">ACF</a>."

#~ msgid "Synchronise field group"
#~ msgstr "Synchronizuj grupę pól"

#~ msgid "Apply"
#~ msgstr "Zastosuj"

#~ msgid "Bulk Actions"
#~ msgstr "Akcje na wielu"

#~ msgid "Add-ons"
#~ msgstr "Dodatki"

#~ msgid "<b>Error</b>. Could not load add-ons list"
#~ msgstr "<b>Błąd</b>. Nie można załadować listy dodatków"

#~ msgid "Info"
#~ msgstr "Informacja"

#~ msgid "What's New"
#~ msgstr "Co nowego"

#~ msgid "Download & Install"
#~ msgstr "Pobierz i instaluj"

#~ msgid "Installed"
#~ msgstr "Zainstalowano"

#~ msgid "Welcome to Advanced Custom Fields"
#~ msgstr "Witamy w Advanced Custom Fields"

#, php-format
#~ msgid ""
#~ "Thank you for updating! ACF %s is bigger and better than ever before. We "
#~ "hope you like it."
#~ msgstr ""
#~ "Dziękujemy za aktualizację! ACF %s jest większy i lepszy niż kiedykolwiek "
#~ "wcześniej. Mamy nadzieję, że go polubisz."

#~ msgid "A Smoother Experience"
#~ msgstr "Lepsze odczucia w użytkowaniu"

#~ msgid "Improved Usability"
#~ msgstr "Zwiększona użyteczność"

#~ msgid ""
#~ "Including the popular Select2 library has improved both usability and "
#~ "speed across a number of field types including post object, page link, "
#~ "taxonomy and select."
#~ msgstr ""
#~ "Użycie popularnej biblioteki Select2 poprawiło zarówno użyteczność jak i "
#~ "szybkość wielu typów pól wliczając obiekty wpisów, odnośniki stron, "
#~ "taksonomie i pola wyboru."

#~ msgid "Improved Design"
#~ msgstr "Ulepszony wygląd"

#~ msgid ""
#~ "Many fields have undergone a visual refresh to make ACF look better than "
#~ "ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
#~ "(new) fields!"
#~ msgstr ""
#~ "Wiele pól przeszło graficzne odświeżenie, aby ACF wyglądał lepiej niż "
#~ "kiedykolwiek! Zmiany warte uwagi są widoczne w galerii, polach relacji i "
#~ "polach oEmbed (nowość)!"

#~ msgid "Improved Data"
#~ msgstr "Ulepszona struktura danych"

#~ msgid ""
#~ "Redesigning the data architecture has allowed sub fields to live "
#~ "independently from their parents. This allows you to drag and drop fields "
#~ "in and out of parent fields!"
#~ msgstr ""
#~ "Przeprojektowanie architektury danych pozwoliła polom podrzędnym być "
#~ "niezależnymi od swoich rodziców. Pozwala to na przeciąganie i upuszczanie "
#~ "pól pomiędzy rodzicami!"

#~ msgid "Goodbye Add-ons. Hello PRO"
#~ msgstr "Do widzenia Dodatki. Dzień dobry PRO"

#~ msgid "Introducing ACF PRO"
#~ msgstr "Przedstawiamy ACF PRO"

#~ msgid ""
#~ "We're changing the way premium functionality is delivered in an exciting "
#~ "way!"
#~ msgstr ""
#~ "Zmieniliśmy sposób funkcjonowania wersji premium - teraz jest dostarczana "
#~ "w ekscytujący sposób!"

#, php-format
#~ msgid ""
#~ "All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
#~ "version of ACF</a>. With both personal and developer licenses available, "
#~ "premium functionality is more affordable and accessible than ever before!"
#~ msgstr ""
#~ "Wszystkie 4 dodatki premium zostały połączone w nową <a href=\"%s"
#~ "\">wersję Pro ACF</a>. W obu licencjach, osobistej i deweloperskiej, "
#~ "funkcjonalność premium jest bardziej przystępna niż kiedykolwiek "
#~ "wcześniej!"

#~ msgid "Powerful Features"
#~ msgstr "Potężne funkcje"

#~ msgid ""
#~ "ACF PRO contains powerful features such as repeatable data, flexible "
#~ "content layouts, a beautiful gallery field and the ability to create "
#~ "extra admin options pages!"
#~ msgstr ""
#~ "ACF PRO zawiera zaawansowane funkcje, takie jak powtarzalne dane, "
#~ "elastyczne układy treści, piękne galerie i możliwość tworzenia "
#~ "dodatkowych stron opcji administracyjnych!"

#, php-format
#~ msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
#~ msgstr "Przeczytaj więcej o <a href=\"%s\">możliwościach ACF PRO</a>."

#~ msgid "Easy Upgrading"
#~ msgstr "Łatwa aktualizacja"

#~ msgid ""
#~ "Upgrading to ACF PRO is easy. Simply purchase a license online and "
#~ "download the plugin!"
#~ msgstr ""
#~ "Ulepszenie wersji do ACF PRO jest łatwe. Wystarczy zakupić licencję "
#~ "online i pobrać wtyczkę!"

#, php-format
#~ msgid ""
#~ "We also wrote an <a href=\"%s\">upgrade guide</a> to answer any "
#~ "questions, but if you do have one, please contact our support team via "
#~ "the <a href=\"%s\">help desk</a>."
#~ msgstr ""
#~ "Napisaliśmy również <a href=\"%s\">przewodnik aktualizacji</a> "
#~ "wyjaśniający wiele zagadnień, jednak jeśli masz jakieś pytanie skontaktuj "
#~ "się z nami na stronie <a href=\"%s\">wsparcia technicznego</a>."

#~ msgid "New Features"
#~ msgstr "Nowe funkcje"

#~ msgid "Link Field"
#~ msgstr "Pole linku"

#~ msgid ""
#~ "The Link field provides a simple way to select or define a link (url, "
#~ "title, target)."
#~ msgstr ""
#~ "Pole linku zapewnia prosty sposób wybrać lub określić łącze (adres URL, "
#~ "atrybut 'title', atrybut 'target')."

#~ msgid "Group Field"
#~ msgstr "Pole grupy"

#~ msgid "The Group field provides a simple way to create a group of fields."
#~ msgstr "Pole grupy zapewnia prosty sposób tworzenia grupy pól."

#~ msgid "oEmbed Field"
#~ msgstr "Pole oEmbed"

#~ msgid ""
#~ "The oEmbed field allows an easy way to embed videos, images, tweets, "
#~ "audio, and other content."
#~ msgstr ""
#~ "Pole oEmbed pozwala w łatwy sposób osadzać filmy, obrazy, tweety, audio i "
#~ "inne treści."

#~ msgid "Clone Field"
#~ msgstr "Pole klonowania"

#~ msgid "The clone field allows you to select and display existing fields."
#~ msgstr ""
#~ "Pole klonowania umożliwia zaznaczanie i wyświetlanie istniejących pól."

#~ msgid "More AJAX"
#~ msgstr "Więcej technologii AJAX"

#~ msgid "More fields use AJAX powered search to speed up page loading."
#~ msgstr "Więcej pól korzysta z AJAX, aby przyspieszyć ładowanie stron."

#~ msgid ""
#~ "New auto export to JSON feature improves speed and allows for "
#~ "syncronisation."
#~ msgstr ""
#~ "Nowy zautomatyzowany eksport do JSON ma poprawioną szybkość i pozwala na "
#~ "synchronizację."

#~ msgid "Easy Import / Export"
#~ msgstr "Łatwy Import / Eksport"

#~ msgid "Both import and export can easily be done through a new tools page."
#~ msgstr ""
#~ "Zarówno import, jak i eksport można łatwo wykonać za pomocą nowej strony "
#~ "narzędzi."

#~ msgid "New Form Locations"
#~ msgstr "Nowe lokalizacje formularzy"

#~ msgid ""
#~ "Fields can now be mapped to menus, menu items, comments, widgets and all "
#~ "user forms!"
#~ msgstr ""
#~ "Pola można teraz mapować na menu, pozycji menu, komentarzy, widżetów i "
#~ "wszystkich formularzy użytkowników!"

#~ msgid "More Customization"
#~ msgstr "Więcej dostosowywania"

#~ msgid ""
#~ "New PHP (and JS) actions and filters have been added to allow for more "
#~ "customization."
#~ msgstr ""
#~ "Dodano nowe akcje i filtry PHP (i JS), aby poszerzyć zakres "
#~ "personalizacji."

#~ msgid "Fresh UI"
#~ msgstr "Fresh UI"

#~ msgid ""
#~ "The entire plugin has had a design refresh including new field types, "
#~ "settings and design!"
#~ msgstr ""
#~ "Cała wtyczka została odświeżone, dodano nowe typy pól, ustawienia i "
#~ "wygląd!"

#~ msgid "New Settings"
#~ msgstr "Nowe ustawienia"

#~ msgid ""
#~ "Field group settings have been added for Active, Label Placement, "
#~ "Instructions Placement and Description."
#~ msgstr ""
#~ "Zostały dodane ustawienia grup pól dotyczące, Aktywności, Pozycji etykiet "
#~ "oraz Pozycji instrukcji i Opisu."

#~ msgid "Better Front End Forms"
#~ msgstr "Lepszy wygląd formularzy (Front End Forms)"

#~ msgid ""
#~ "acf_form() can now create a new post on submission with lots of new "
#~ "settings."
#~ msgstr ""
#~ "acf_form() może teraz utworzyć nowy wpis po przesłaniu i zawiera wiele "
#~ "nowych ustawień."

#~ msgid "Better Validation"
#~ msgstr "Lepsza walidacja"

#~ msgid "Form validation is now done via PHP + AJAX in favour of only JS."
#~ msgstr "Walidacja pól jest wykonana w PHP + AJAX a nie tylko w JS."

#~ msgid "Moving Fields"
#~ msgstr "Przenoszenie pól"

#~ msgid ""
#~ "New field group functionality allows you to move a field between groups & "
#~ "parents."
#~ msgstr ""
#~ "Nowa funkcjonalność pozwala na przenoszenie pól pomiędzy grupami i "
#~ "rodzicami."

#, php-format
#~ msgid "We think you'll love the changes in %s."
#~ msgstr "Uważamy, że pokochasz zmiany wprowadzone w wersji %s."

#~ msgid "Current Color"
#~ msgstr "Bieżący Kolor"

#~ msgid "Shown when entering data"
#~ msgstr "Widoczny podczas wprowadzania danych"

#~ msgid "http://www.elliotcondon.com/"
#~ msgstr "http://www.elliotcondon.com/"

#~ msgid "Error validating request"
#~ msgstr "Błąd podczas walidacji żądania"

#~ msgid "Advanced Custom Fields Database Upgrade"
#~ msgstr "Aktualizacja bazy danych Advanced Custom Fields"

#~ msgid ""
#~ "Before you start using the new awesome features, please update your "
#~ "database to the newest version."
#~ msgstr ""
#~ "Zanim zaczniesz korzystać z niesamowitych funkcji prosimy o "
#~ "zaktualizowanie bazy danych do najnowszej wersji."

#~ msgid ""
#~ "To help make upgrading easy, <a href=\"%s\">login to your store account</"
#~ "a> and claim a free copy of ACF PRO!"
#~ msgstr ""
#~ "Aby aktualizacja była łatwa, <a href=\"%s\">zaloguj się do swojego konta</"
#~ "a> i pobierz darmową kopię ACF PRO!"

#~ msgid "Under the Hood"
#~ msgstr "Pod maską"

#~ msgid "Smarter field settings"
#~ msgstr "Sprytniejsze ustawienia pól"

#~ msgid "ACF now saves its field settings as individual post objects"
#~ msgstr "ACF teraz zapisuje ustawienia pól jako osobny obiekt wpisu"

#~ msgid "Better version control"
#~ msgstr "Lepsza kontrola wersji"

#~ msgid ""
#~ "New auto export to JSON feature allows field settings to be version "
#~ "controlled"
#~ msgstr ""
#~ "Nowy zautomatyzowany eksport do JSON pozwala na wersjonowanie ustawień pól"

#~ msgid "Swapped XML for JSON"
#~ msgstr "Zmiana XML na JSON"

#~ msgid "Import / Export now uses JSON in favour of XML"
#~ msgstr "Import / Eksport teraz korzysta z JSON zamiast XML"

#~ msgid "New Forms"
#~ msgstr "Nowe formularze"

#~ msgid "A new field for embedding content has been added"
#~ msgstr "Dodano nowe pole do osadzania zawartości"

#~ msgid "New Gallery"
#~ msgstr "Nowa galeria"

#~ msgid "The gallery field has undergone a much needed facelift"
#~ msgstr "Pola galerii przeszły niezbędny facelifting"

#~ msgid "Relationship Field"
#~ msgstr "Pole relacji"

#~ msgid ""
#~ "New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
#~ msgstr ""
#~ "Nowe ustawienia pola relacji dla \"Filtrów\" (Wyszukiwarka, Typ Wpisu, "
#~ "Taksonomia)"

#~ msgid "New archives group in page_link field selection"
#~ msgstr "Nowe grupy archiwów do wyboru dla pola page_link"

#~ msgid "Better Options Pages"
#~ msgstr "Lepsze strony opcji"

#~ msgid ""
#~ "New functions for options page allow creation of both parent and child "
#~ "menu pages"
#~ msgstr ""
#~ "Nowe funkcje dla strony opcji pozwalają tworzyć strony w menu będące "
#~ "rodzicami oraz potomnymi."

#~ msgid "Parent fields"
#~ msgstr "Pola nadrzędne"

#~ msgid "Sibling fields"
#~ msgstr "Pola tego samego poziomu"

#~ msgid "Export Field Groups to PHP"
#~ msgstr "Eksportuj grupy pól do PHP"

#~ msgid "Download export file"
#~ msgstr "Pobierz plik eksportu"

#~ msgid "Generate export code"
#~ msgstr "Generuj kod eksportu"

#~ msgid "Locating"
#~ msgstr "Lokalizacja"

#~ msgid "Error."
#~ msgstr "Błąd."

#~ msgid "No embed found for the given URL."
#~ msgstr "Nie znaleziono osadzenia dla podanego URLa."

#~ msgid "Minimum values reached ( {min} values )"
#~ msgstr "Minimalna wartość została przekroczona ( {min} )"

#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "Pole zakładki będzie wyświetlane nieprawidłowo jeśli zostanie dodano do "
#~ "pola powtarzalnego wyświetlanego jako tabela lub do elastycznego pola"

#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr "Użyj \"Pola zakładki\" aby uporządkować ekran edycji grupując pola."

#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "Wszystkie pola po tym \"polu zakładki\" (lub przed następnym \"polem "
#~ "zakładki\") zostaną zgrupowane razem używając etykiety tego pola jako "
#~ "nagłówka."

#~ msgid "None"
#~ msgstr "Brak"

#~ msgid "Taxonomy Term"
#~ msgstr "Termin taksonomii"

#~ msgid "remove {layout}?"
#~ msgstr "usunąć {layout}?"

#~ msgid "This field requires at least {min} {identifier}"
#~ msgstr "To pole wymaga przynamniej {min} {identifier}"

#~ msgid "Maximum {label} limit reached ({max} {identifier})"
#~ msgstr "Maksimum {label} limit osiągnięty ({max} {identifier})"

#~ msgid "Getting Started"
#~ msgstr "Pierwsze kroki"

#~ msgid "Field Types"
#~ msgstr "Rodzaje pól"

#~ msgid "Functions"
#~ msgstr "Funkcje"

#~ msgid "Actions"
#~ msgstr "Akcje"

#~ msgid "'How to' guides"
#~ msgstr "Wskazówki 'how-to'"

#~ msgid "Tutorials"
#~ msgstr "Poradniki"

#~ msgid "FAQ"
#~ msgstr "Najczęściej zadawane pytania (FAQ)"

#~ msgid "Created by"
#~ msgstr "Stworzone przez"

#~ msgid "Error"
#~ msgstr "Błąd"

#~ msgid "See what's new"
#~ msgstr "Zobacz co nowego"

#~ msgid "eg. Show extra content"
#~ msgstr "np. Wyświetl dodatkową treść"

#~ msgid "1 field requires attention."
#~ msgid_plural "%d fields require attention."
#~ msgstr[0] "1 pole wymaga uwagi."
#~ msgstr[1] "%d pola wymagają uwagi."
#~ msgstr[2] "%d pól wymaga uwagi."

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr "<b>Sukces</b>. Narzędzie importu dodało %s grup pól: %s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Ostrzeżenie</b>. Narzędzie importu wykryło %s już istniejących grup "
#~ "pól i je pominęło: %s"

#~ msgid "Upgrade ACF"
#~ msgstr "Aktualizuj ACF"

#~ msgid "Upgrade"
#~ msgstr "Aktualizacja"

#~ msgid ""
#~ "The following sites require a DB upgrade. Check the ones you want to "
#~ "update and then click “Upgrade Database”."
#~ msgstr ""
#~ "Następujące strony wymagają aktualizacji bazy danych. Zaznacz te które "
#~ "chcesz aktualizować i kliknij 'Aktualizuj bazę danych\"."

#~ msgid "Select"
#~ msgstr "Wybór (select)"

#~ msgid "Done"
#~ msgstr "Gotowe"

#~ msgid "Today"
#~ msgstr "Dzisiaj"

#~ msgid "Show a different month"
#~ msgstr "Pokaż inny miesiąc"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Błąd połączenia</b>. Przepraszamy, spróbuj ponownie"

#~ msgid "See what's new in"
#~ msgstr "Zobacz co słychać nowego w"

#~ msgid "Drag and drop to reorder"
#~ msgstr "Przeciągnij i zmień kolejność"

#~ msgid "Upgrading data to"
#~ msgstr "Aktualizacja danych do"

#~ msgid "Return format"
#~ msgstr "Zwracany format"

#~ msgid "uploaded to this post"
#~ msgstr "przesłane do tego wpisu"

#~ msgid "File Name"
#~ msgstr "Nazwa pliku"

#~ msgid "File Size"
#~ msgstr "Rozmiar pliku"

#~ msgid "No File selected"
#~ msgstr "Nie wybrano pliku"

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr ""
#~ "Proszę pamiętać, że wszystkie teksty najpierw przepuszczane są przez "
#~ "funkcje WP"

#~ msgid "Warning"
#~ msgstr "Ostrzeżenie"

#~ msgid "Add new %s "
#~ msgstr "Dodaj nowe %s"

#~ msgid "Save Options"
#~ msgstr "Zapisz opcje"

#~ msgid "License"
#~ msgstr "Licencja"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "W celu odblokowania aktualizacji proszę wpisać swój numer licencji "
#~ "poniżej. Jeśli nie masz klucza proszę zobacz"

#~ msgid "details & pricing"
#~ msgstr "szczegóły i ceny"

#~ msgid ""
#~ "To enable updates, please enter your license key on the <a href=\"%s"
#~ "\">Updates</a> page. If you don't have a licence key, please see <a href="
#~ "\"%s\">details & pricing</a>"
#~ msgstr ""
#~ "Aby włączyć aktualizację proszę wpisać swój klucz licencji na stronie <a "
#~ "href=\"%s\">Aktualizacje</a>. Jeśli nie posiadasz klucza proszę zobaczyć "
#~ "<a href=\"%s\">szczegóły i ceny</a>"

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "Advanced Custom Fields Pro"

#~ msgid "http://www.advancedcustomfields.com/"
#~ msgstr "http://www.advancedcustomfields.com/"

#~ msgid "elliot condon"
#~ msgstr "elliot condon"

#, fuzzy
#~ msgid "Field groups are created in order from lowest to highest"
#~ msgstr ""
#~ "Grupy pól są tworzone w kolejności <br />od najniższej do najwyższej."

#, fuzzy
#~ msgid "ACF PRO Required"
#~ msgstr "Wymagane?"

#, fuzzy
#~ msgid "Update Database"
#~ msgstr "Aktualizuj bazę danych"

#, fuzzy
#~ msgid "Data Upgrade"
#~ msgstr "Aktualizacja"

#, fuzzy
#~ msgid "image"
#~ msgstr "Obrazek"

#, fuzzy
#~ msgid "relationship"
#~ msgstr "Relacja"

#, fuzzy
#~ msgid "title_is_required"
#~ msgstr "Grupa pól została opublikowana."

#, fuzzy
#~ msgid "move_field"
#~ msgstr "Zapisz pole"

#, fuzzy
#~ msgid "flexible_content"
#~ msgstr "Elastyczna treść"

#, fuzzy
#~ msgid "gallery"
#~ msgstr "Galeria"

#, fuzzy
#~ msgid "repeater"
#~ msgstr "Pole powtarzalne"

#~ msgid "Custom field updated."
#~ msgstr "Włąsne pole zostało zaktualizowane."

#~ msgid "Custom field deleted."
#~ msgstr "Własne pole zostało usunięte."

#, fuzzy
#~ msgid "Import/Export"
#~ msgstr "Import / Eksport"

#, fuzzy
#~ msgid "Attachment Details"
#~ msgstr "ID załącznika"

#~ msgid "Validation Failed. One or more fields below are required."
#~ msgstr "Walidacja nie powiodła się. Jedno lub więcej pól jest wymaganych."

#~ msgid "Field group restored to revision from %s"
#~ msgstr "Grupa pól została przywróćona z wersji %s"

#~ msgid "No ACF groups selected"
#~ msgstr "Nie zaznaczono żadnej grupy pól"

#~ msgid "Add Fields to Edit Screens"
#~ msgstr "Dodaj pola do stron edycji"

#~ msgid ""
#~ "Read documentation, learn the functions and find some tips &amp; tricks "
#~ "for your next web project."
#~ msgstr ""
#~ "Przeczytaj dokumentację, naucz się funkcji i poznaj parę tricków, które "
#~ "mogą przydać Ci się w Twoim kolejnym projekcie."

#~ msgid "Vote"
#~ msgstr "Głosuj"

#~ msgid "Follow"
#~ msgstr "Śledź"

#~ msgid "Add File to Field"
#~ msgstr "Dodaj plik do pola"

#~ msgid "Add Image to Field"
#~ msgstr "Dodaj zdjęcie do pola"

#~ msgid "Repeater field deactivated"
#~ msgstr "Pole powtarzalne zostało deaktywowane"

#~ msgid "Gallery field deactivated"
#~ msgstr "Galeria została deaktywowana"

#~ msgid "Repeater field activated"
#~ msgstr "Pole powtarzalne zostało aktywowane"

#~ msgid "Options page activated"
#~ msgstr "Strona opcji została aktywowana"

#~ msgid "Flexible Content field activated"
#~ msgstr "Pole z elastyczną zawartością zostało aktywowane"

#~ msgid "Gallery field activated"
#~ msgstr "Galeria została aktywowana"

#~ msgid "License key unrecognised"
#~ msgstr "Klucz licencji nie został rozpoznany"

#~ msgid "Advanced Custom Fields Settings"
#~ msgstr "Ustawienia zaawansowanych własnych pól"

#~ msgid "Flexible Content Field"
#~ msgstr "Pole z elastyczną zawartością"

#~ msgid "Gallery Field"
#~ msgstr "Galeria"

#~ msgid ""
#~ "Add-ons can be unlocked by purchasing a license key. Each key can be used "
#~ "on multiple sites."
#~ msgstr ""
#~ "Dodatki można odblokować kupując kod aktywacyjny. Każdy kod aktywacyjny "
#~ "może być wykorzystywany na dowolnej liczbie stron."

#~ msgid "Export Field Groups to XML"
#~ msgstr "Eksportuj Grupy pól do XML"

#~ msgid ""
#~ "ACF will create a .xml export file which is compatible with the native WP "
#~ "import plugin."
#~ msgstr ""
#~ "Wtyczka utworzy plik eksportu .xml, który jest kompatybilny z domyślną "
#~ "wtyczką importu plików."

#~ msgid "Export XML"
#~ msgstr "Eksportuj XML"

#~ msgid "Navigate to the"
#~ msgstr "Przejdź do"

#~ msgid "and select WordPress"
#~ msgstr "i wybierz Wordpress"

#~ msgid "Install WP import plugin if prompted"
#~ msgstr "Zainstaluj wtyczkę importu WP, jeśli zostaniesz o to poproszony"

#~ msgid "Upload and import your exported .xml file"
#~ msgstr "Wgraj i zaimportuj wyeksportowany wcześniej plik .xml"

#~ msgid "Select your user and ignore Import Attachments"
#~ msgstr "Wybierz użytkownika i ignoruj Importowanie załączników"

#~ msgid "That's it! Happy WordPressing"
#~ msgstr "Gotowe!"

#~ msgid "ACF will create the PHP code to include in your theme"
#~ msgstr "ACF wygeneruje kod PHP, który możesz wkleić do swego szablonu"

#~ msgid "Register Field Groups with PHP"
#~ msgstr "Utwórz grupę pól z PHP"

#~ msgid "Copy the PHP code generated"
#~ msgstr "Skopij wygenerowany kod PHP"

#~ msgid "Paste into your functions.php file"
#~ msgstr "Wklej do pliku functions.php"

#~ msgid ""
#~ "To activate any Add-ons, edit and use the code in the first few lines."
#~ msgstr ""
#~ "Aby aktywować dodatki, edytuj i użyj kodu w pierwszych kilku liniach."

#~ msgid ""
#~ "/**\n"
#~ " * Activate Add-ons\n"
#~ " * Here you can enter your activation codes to unlock Add-ons to use in "
#~ "your theme. \n"
#~ " * Since all activation codes are multi-site licenses, you are allowed to "
#~ "include your key in premium themes. \n"
#~ " * Use the commented out code to update the database with your activation "
#~ "code. \n"
#~ " * You may place this code inside an IF statement that only runs on theme "
#~ "activation.\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " * Aktywuj dodatki\n"
#~ " * Możesz tu wpisać kody aktywacyjne uruchamiające dodatkowe funkcje. \n"
#~ " * W związku z tym, że kody są na dowolną ilość licencji, możesz je "
#~ "stosować także w płatnych szablonach. \n"
#~ " * Użyj kodu aby zaktualizować bazę danych. \n"
#~ " * Możesz umieścić ten kod w funkcjach if, które uruchamiają się np. przy "
#~ "aktywacji szablonu.\n"
#~ " */"

#~ msgid ""
#~ "/**\n"
#~ " * Register field groups\n"
#~ " * The register_field_group function accepts 1 array which holds the "
#~ "relevant data to register a field group\n"
#~ " * You may edit the array as you see fit. However, this may result in "
#~ "errors if the array is not compatible with ACF\n"
#~ " * This code must run every time the functions.php file is read\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " * Zarejestruj grupy pól\n"
#~ " * Funkcja register_field_group akceptuje 1 ciąg zmiennych, która zawiera "
#~ "wszystkie dane służące rejestracji grupy\n"
#~ " * Możesz edytować tę zmienną i dopasowywać ją do swoich potrzeb. Ale "
#~ "może to też powodować błąd jeśli ta zmienna nie jest kompatybilna z ACF\n"
#~ " * Kod musi być uruchamiany każdorazowo w pliku functions.php\n"
#~ " */"

#~ msgid "requires a database upgrade"
#~ msgstr "wymagana jest aktualizacja bazy danych"

#~ msgid "why?"
#~ msgstr "dlaczego?"

#~ msgid "Please"
#~ msgstr "Proszę"

#~ msgid "backup your database"
#~ msgstr "zrobić kopię zapasową bazy danych"

#~ msgid "then click"
#~ msgstr "a następnie kliknąć"

#~ msgid "Modifying field group options 'show on page'"
#~ msgstr "Modyfikacje opcji grupy pól 'pokaż na stronie'"

#~ msgid "No choices to choose from"
#~ msgstr "Brak możliwościi wyboru"

#~ msgid "Red"
#~ msgstr "Czerwony"

#~ msgid "Blue"
#~ msgstr "Niebieski"

#~ msgid "blue : Blue"
#~ msgstr "niebieski : Niebieski"

#~ msgid "File Updated."
#~ msgstr "Plik został zaktualizowany."

#~ msgid "Media attachment updated."
#~ msgstr "Załącznik został zaktualizowany."

#~ msgid "Add Selected Files"
#~ msgstr "Dodaj zaznaczone pliki"

#~ msgid "+ Add Row"
#~ msgstr "+ Dodaj rząd"

#~ msgid "Field Order"
#~ msgstr "Kolejność pola"

#~ msgid ""
#~ "No fields. Click the \"+ Add Sub Field button\" to create your first "
#~ "field."
#~ msgstr ""
#~ "Brak pól. Kliknij przycisk \"+ Dodaj pole podrzędne\" aby utworzyć "
#~ "pierwsze własne pole."

#~ msgid "Docs"
#~ msgstr "Dokumentacja"

#~ msgid "Close Sub Field"
#~ msgstr "Zamknij pole"

#~ msgid "+ Add Sub Field"
#~ msgstr "+ Dodaj pole podrzędne"

#~ msgid "Alternate Text"
#~ msgstr "Tekst alternatywny"

#~ msgid "Thumbnail is advised"
#~ msgstr "Zalecana jest miniatura."

#~ msgid "Image Updated"
#~ msgstr "Zdjęcie zostało zaktualizowane."

#~ msgid "Grid"
#~ msgstr "Siatka"

#~ msgid "List"
#~ msgstr "Lista"

#~ msgid "Image already exists in gallery"
#~ msgstr "To zdjęcie już jest w galerii."

#~ msgid "Image Updated."
#~ msgstr "Zdjęcie zostało zaktualizowane."

#~ msgid "No images selected"
#~ msgstr "Nie wybrano obrazków"

#~ msgid "Add selected Images"
#~ msgstr "Dodaj zaznaczone obrazki"

#~ msgid ""
#~ "Filter posts by selecting a post type<br />\n"
#~ "\t\t\t\tTip: deselect all post types to show all post type's posts"
#~ msgstr ""
#~ "Filtruj wpisy wybierając typ wpisu<br />\n"
#~ "\t\t\t\tPodpowiedź: nie zaznaczenie żadnego typu wpisów spowoduje "
#~ "wyświetlenie wszystkich"

#~ msgid "Set to -1 for infinite"
#~ msgstr "Wpisanie -1 oznacza nieskończoność"

#~ msgid "Repeater Fields"
#~ msgstr "Pola powtarzalne"

#~ msgid "Table (default)"
#~ msgstr "Tabela (domyślne)"

#~ msgid "Define how to render html tags"
#~ msgstr "Określ jak traktować znaczniki HTML"

#~ msgid "HTML"
#~ msgstr "HTML"

#~ msgid "Define how to render html tags / new lines"
#~ msgstr "Określ jak traktować znaczniki HTML / nowe wiersze"

#~ msgid "eg. dd/mm/yy. read more about"
#~ msgstr "np. dd/mm/rr. czytaj więcej"

#~ msgid "Page Specific"
#~ msgstr "Związane ze stronami"

#~ msgid "Post Specific"
#~ msgstr "Związane z typem wpisu"

#~ msgid "Taxonomy (Add / Edit)"
#~ msgstr "Taksonomia (Dodaj / Edytuj)"

#~ msgid "Media (Edit)"
#~ msgstr "Medium (Edytuj)"

#~ msgid "match"
#~ msgstr "pasuje"

#~ msgid "all"
#~ msgstr "wszystkie"

#~ msgid "of the above"
#~ msgstr "do pozostałych"

#~ msgid "Unlock options add-on with an activation code"
#~ msgstr "Odblokuj dodatkowe opcje z kodem aktywacyjnym"

#~ msgid "Normal"
#~ msgstr "Normalna"

#~ msgid "No Metabox"
#~ msgstr "Bez metabox"

#~ msgid "Everything Fields deactivated"
#~ msgstr "Pola do wszystkiego zostały deaktywowane"

#~ msgid "Everything Fields activated"
#~ msgstr "Pola do wszystkiego zostały aktywowane"

#~ msgid "Row Limit"
#~ msgstr "Limit rzędów"

#~ msgid "required"
#~ msgstr "wymagane"

#~ msgid "Show on page"
#~ msgstr "Wyświetl na stronie"

#~ msgid ""
#~ "Watch tutorials, read documentation, learn the API code and find some "
#~ "tips &amp; tricks for your next web project."
#~ msgstr ""
#~ "Obejrzyj tutorial, przeczytaj dokumentację, naucz się API i poznaj parę "
#~ "tricków do przydatnych w Twoim kolejnym projekcie."

#~ msgid "View the plugins website"
#~ msgstr "Odwiedź witrynę wtyczki"

#~ msgid ""
#~ "Join the growing community over at the support forum to share ideas, "
#~ "report bugs and keep up to date with ACF"
#~ msgstr ""
#~ "Dołącz do rosnącej społeczności użytkowników i forum pomocy, aby dzielić "
#~ "się pomysłami, zgłąszać błedy i być na bierząco z tą wtyczką."

#~ msgid "View the Support Forum"
#~ msgstr "Zobacz forum pomocy"

#~ msgid "Developed by"
#~ msgstr "Opracowana przez"

#~ msgid "Vote for ACF"
#~ msgstr "Głosuj na tę wtyczkę"

#~ msgid "Twitter"
#~ msgstr "Twitter"

#~ msgid "Blog"
#~ msgstr "Blog"

#~ msgid "Unlock Special Fields."
#~ msgstr "Odblokuj pola specjalne"

#~ msgid ""
#~ "Special Fields can be unlocked by purchasing an activation code. Each "
#~ "activation code can be used on multiple sites."
#~ msgstr ""
#~ "Pola specjalne można odblokować kupując kod aktywacyjny. Każdy kod "
#~ "aktywacyjny może być wykorzystywany wielokrotnie."

#~ msgid "Visit the Plugin Store"
#~ msgstr "Odwiedź sklep wtyczki"

#~ msgid "Unlock Fields"
#~ msgstr "Odblokuj pola"

#~ msgid "Have an ACF export file? Import it here."
#~ msgstr "Wyeksportowałeś plik z polami? Możesz go zaimportować tutaj."

#~ msgid ""
#~ "Want to create an ACF export file? Just select the desired ACF's and hit "
#~ "Export"
#~ msgstr ""
#~ "Chcesz stworzyć i wyeksportować plik z polami? Wybierz pola i kliknij "
#~ "Eksport"

#~ msgid ""
#~ "No fields. Click the \"+ Add Field button\" to create your first field."
#~ msgstr ""
#~ "Brak pól. Kliknij przycisk \"+ Dodaj pole\" aby utworzyć pierwsze własne "
#~ "pole."

#~ msgid ""
#~ "Special Fields can be unlocked by purchasing a license key. Each key can "
#~ "be used on multiple sites."
#~ msgstr ""
#~ "Pola specjalne można odblokować kupując kod aktywacyjny. Każdy kod "
#~ "aktywacyjny może być wykorzystywany wielokrotnie."

#~ msgid "Select which ACF groups to export"
#~ msgstr "Wybierz, które grupy chcesz wyeksportować"

#~ msgid ""
#~ "Have an ACF export file? Import it here. Please note that v2 and v3 .xml "
#~ "files are not compatible."
#~ msgstr ""
#~ "Wyeksportowałeś plik z polami? Zaimportuj go tutaj. Zwróć uwagę, że "
#~ "wersje 2 i 3 plików .xml nie są ze sobą kompatybilne."

#~ msgid "Import your .xml file"
#~ msgstr "Zaimportuj plik .xml"

#~ msgid "Display your field group with or without a box"
#~ msgstr "Wyświetl grupę pól w ramce lub bez niej"

#~ msgid "No Options"
#~ msgstr "Brak opcji"

#~ msgid "Sorry, it seems there are no fields for this options page."
#~ msgstr "Przykro mi, ale ta strona opcji nie zawiera pól."

#~ msgid ""
#~ "Enter your choices one per line<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tRed<br />\n"
#~ "\t\t\t\tBlue<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tor<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tred : Red<br />\n"
#~ "\t\t\t\tblue : Blue"
#~ msgstr ""
#~ "Wpisz dostęne opcje, każdy w odrębnym rzędzie<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tCzerwony<br />\n"
#~ "\t\t\t\tNiebieski<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tor<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tczerwony : Czerwony<br />\n"
#~ "\t\t\t\tniebieski : Niebieski"

#~ msgid "continue editing ACF"
#~ msgstr "kontynuuj edycję"

#~ msgid "Adv Upgrade"
#~ msgstr "Zaawansowana aktualizacja"
