# Copyright (C) 2022 Advanced Custom Fields PRO
# This file is distributed under the same license as the Advanced Custom Fields PRO package.
msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields PRO\n"
"Report-Msgid-Bugs-To: https://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2022-09-29 12:33+0000\n"
"PO-Revision-Date: \n"
"Last-Translator: Delicious Brains <<EMAIL>>\n"
"Language-Team: WP Engine <<EMAIL>>\n"
"Language: en_GB\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_ex:1,2c;_n:1,2;_n_noop:1,2;_nx:1,2,4c;"
"_nx_noop:1,2,3c;_x:1,2c;esc_attr__;esc_attr_e;esc_attr_x:1,2c;esc_html__;"
"esc_html_e;esc_html_x:1,2c\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Generator: Poedit 3.1.1\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr ""

#: pro/blocks.php:169
msgid "Block type name is required."
msgstr ""

#. translators: The name of the block type
#: pro/blocks.php:177
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:709
msgid "Switch to Edit"
msgstr ""

#: pro/blocks.php:710
msgid "Switch to Preview"
msgstr ""

#: pro/blocks.php:711
msgid "Change content alignment"
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:714
msgid "%s settings"
msgstr ""

#: pro/blocks.php:919
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:925
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:47
msgid "Options"
msgstr ""

#: pro/options-page.php:77, pro/fields/class-acf-field-gallery.php:523
msgid "Update"
msgstr ""

#: pro/options-page.php:78
msgid "Options Updated"
msgstr ""

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a href=\"%1$s"
"\">Updates</a> page. If you don't have a licence key, please see <a href="
"\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""
"<b>ACF Activation Error</b>. Your defined licence key has changed, but an "
"error occurred when deactivating your old licence"

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""
"<b>ACF Activation Error</b>. Your defined licence key has changed, but an "
"error occurred when connecting to activation server"

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""

#: pro/updates.php:279
msgid "Check Again"
msgstr ""

#: pro/updates.php:593
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr ""

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""

#: pro/admin/admin-options-page.php:309
msgid "Edit field group"
msgstr ""

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr ""

#: pro/admin/admin-updates.php:122,
#: pro/admin/views/html-settings-updates.php:12
msgid "Updates"
msgstr ""

#: pro/admin/admin-updates.php:209
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""

#: pro/admin/admin-updates.php:196
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""
"<b>Error</b>. Your licence for this site has expired or been deactivated. "
"Please reactivate your ACF PRO licence."

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:814
msgid "Fields"
msgstr ""

#: pro/fields/class-acf-field-clone.php:815
msgid "Select one or more fields you wish to clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:834
msgid "Display"
msgstr ""

#: pro/fields/class-acf-field-clone.php:835
msgid "Specify the style used to render the clone field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:840
msgid "Group (displays selected fields in a group within this field)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:841
msgid "Seamless (replaces this field with selected fields)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:850,
#: pro/fields/class-acf-field-flexible-content.php:549,
#: pro/fields/class-acf-field-flexible-content.php:604,
#: pro/fields/class-acf-field-repeater.php:178
msgid "Layout"
msgstr ""

#: pro/fields/class-acf-field-clone.php:851
msgid "Specify the style used to render the selected fields"
msgstr ""

#: pro/fields/class-acf-field-clone.php:856,
#: pro/fields/class-acf-field-flexible-content.php:617,
#: pro/fields/class-acf-field-repeater.php:186,
#: pro/locations/class-acf-location-block.php:22
msgid "Block"
msgstr ""

#: pro/fields/class-acf-field-clone.php:857,
#: pro/fields/class-acf-field-flexible-content.php:616,
#: pro/fields/class-acf-field-repeater.php:185
msgid "Table"
msgstr ""

#: pro/fields/class-acf-field-clone.php:858,
#: pro/fields/class-acf-field-flexible-content.php:618,
#: pro/fields/class-acf-field-repeater.php:187
msgid "Row"
msgstr ""

#: pro/fields/class-acf-field-clone.php:864
msgid "Labels will be displayed as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:869
msgid "Prefix Field Labels"
msgstr ""

#: pro/fields/class-acf-field-clone.php:879
msgid "Values will be saved as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:884
msgid "Prefix Field Names"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1001
msgid "Unknown field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1005
msgid "(no title)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1038
msgid "Unknown field group"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1042
msgid "All fields from %s field group"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:31,
#: pro/fields/class-acf-field-repeater.php:105,
#: pro/fields/class-acf-field-repeater.php:298
msgid "Add Row"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:71,
#: pro/fields/class-acf-field-flexible-content.php:931,
#: pro/fields/class-acf-field-flexible-content.php:1010
msgid "layout"
msgid_plural "layouts"
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:72
msgid "layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:75,
#: pro/fields/class-acf-field-flexible-content.php:930,
#: pro/fields/class-acf-field-flexible-content.php:1009
msgid "This field requires at least {min} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:76
msgid "This field has a limit of {max} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:79
msgid "{available} {label} {identifier} available (max {max})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:80
msgid "{required} {label} {identifier} required (min {min})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:83
msgid "Flexible Content requires at least 1 layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:276
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:410,
#: pro/fields/class-acf-repeater-table.php:366
msgid "Drag to reorder"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:413
msgid "Add layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:414
msgid "Duplicate layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:415
msgid "Remove layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:416,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:552
msgid "Delete Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:552
msgid "Delete"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:553
msgid "Duplicate Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:553
msgid "Duplicate"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Add New Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Add New"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:581
msgid "Label"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:597
msgid "Name"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:635
msgid "Min"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:650
msgid "Max"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:693
msgid "Minimum Layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:704
msgid "Maximum Layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:715,
#: pro/fields/class-acf-field-repeater.php:294
msgid "Button Label"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1698,
#: pro/fields/class-acf-field-repeater.php:919
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1709
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:1725
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:73
msgid "Add Image to Gallery"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:74
msgid "Maximum selection reached"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:320
msgid "Length"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:335
msgid "Edit"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:336,
#: pro/fields/class-acf-field-gallery.php:491
msgid "Remove"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:352
msgid "Title"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:376
msgid "Alt Text"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:388
msgid "Description"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:500
msgid "Add to gallery"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:504
msgid "Bulk actions"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:505
msgid "Sort by date uploaded"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:506
msgid "Sort by date modified"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:507
msgid "Sort by title"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:508
msgid "Reverse current order"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:520
msgid "Close"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:552
msgid "Return Format"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:558
msgid "Image Array"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:559
msgid "Image URL"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:560
msgid "Image ID"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:568
msgid "Library"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:569
msgid "Limit the media library choice"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:574,
#: pro/locations/class-acf-location-block.php:66
msgid "All"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:575
msgid "Uploaded to post"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:611
msgid "Minimum Selection"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:621
msgid "Maximum Selection"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:631
msgid "Minimum"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:632,
#: pro/fields/class-acf-field-gallery.php:668
msgid "Restrict which images can be uploaded"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:635,
#: pro/fields/class-acf-field-gallery.php:671
msgid "Width"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:646,
#: pro/fields/class-acf-field-gallery.php:682
msgid "Height"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:658,
#: pro/fields/class-acf-field-gallery.php:694
msgid "File size"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:667
msgid "Maximum"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:703
msgid "Allowed file types"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:704
msgid "Comma separated list. Leave blank for all types"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:723
msgid "Insert"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:724
msgid "Specify where new attachments are added"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:728
msgid "Append to the end"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:729
msgid "Prepend to the beginning"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:737
msgid "Preview Size"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:840
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-repeater.php:36
msgid "Repeater"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:68,
#: pro/fields/class-acf-field-repeater.php:463
msgid "Minimum rows reached ({min} rows)"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:69
msgid "Maximum rows reached ({max} rows)"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:70
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:71
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:163
msgid "Sub Fields"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:196
msgid "Pagination"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:197
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:208
msgid "Rows Per Page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:209
msgid "Set the number of rows to be displayed on a page."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:241
msgid "Minimum Rows"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:252
msgid "Maximum Rows"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:282
msgid "Collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:283
msgid "Select a sub field to show when row is collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1003
msgid "Invalid nonce."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1018
msgid "Invalid field key or name."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1027
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:369
msgid "Click to reorder"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:403
msgid "Duplicate row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
msgid "Current Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
msgid "First Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
msgid "Previous Page"
msgstr ""

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
msgid "Next Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
msgid "Last Page"
msgstr ""

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr ""

#: pro/locations/class-acf-location-options-page.php:22
msgid "Options Page"
msgstr ""

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr ""

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Deactivate Licence"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Activate Licence"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Licence Information"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"To unlock updates, please enter your licence key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "Licence Key"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr "Your licence key is defined in wp-config.php."

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr ""

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr ""

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr ""

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr ""

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr ""

#: pro/admin/views/html-settings-updates.php:91
msgid "No"
msgstr ""

#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr ""

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr ""

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
msgid "Enter your license key to unlock updates"
msgstr "Enter your licence key to unlock updates"

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr ""

#: pro/admin/views/html-settings-updates.php:117
msgid "Please reactivate your license to unlock updates"
msgstr "Please reactivate your licence to unlock updates"

#~ msgid "Please enter your license key above to unlock updates"
#~ msgstr "Please enter your licence key above to unlock updates"
