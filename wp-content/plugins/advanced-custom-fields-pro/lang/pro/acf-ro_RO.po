msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.2.9\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2018-04-16 17:11+1000\n"
"PO-Revision-Date: 2019-11-12 08:00+1000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"Language: ro_RO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?"
"2:1));\n"
"X-Generator: Poedit 1.8.1\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:81
msgid "Advanced Custom Fields"
msgstr "Câmpuri Personalizate Avansate"

#: acf.php:388 includes/admin/admin.php:117
msgid "Field Groups"
msgstr "Grupuri de câmpuri"

#: acf.php:389
msgid "Field Group"
msgstr "Grup de câmp"

#: acf.php:390 acf.php:422 includes/admin/admin.php:118
#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Add New"
msgstr "Adaugă"

#: acf.php:391
msgid "Add New Field Group"
msgstr "Adaugă un nou grup de câmpuri"

#: acf.php:392
msgid "Edit Field Group"
msgstr "Editează grupul"

#: acf.php:393
msgid "New Field Group"
msgstr "Grup de câmp nou"

#: acf.php:394
msgid "View Field Group"
msgstr "Vizulaizează grupul de câmp"

#: acf.php:395
msgid "Search Field Groups"
msgstr "Caută în grupurile de câmp"

#: acf.php:396
msgid "No Field Groups found"
msgstr "Nu s-a găsit nici un câmp de grupuri"

#: acf.php:397
msgid "No Field Groups found in Trash"
msgstr "Nu s-a găsit nici un câmp de grupuri în coșul de gunoi"

#: acf.php:420 includes/admin/admin-field-group.php:196
#: includes/admin/admin-field-groups.php:510
#: pro/fields/class-acf-field-clone.php:811
msgid "Fields"
msgstr "Câmpuri"

#: acf.php:421
msgid "Field"
msgstr "Câmp"

#: acf.php:423
msgid "Add New Field"
msgstr "Adaugă un nou câmp"

#: acf.php:424
msgid "Edit Field"
msgstr "Editează câmpul"

#: acf.php:425 includes/admin/views/field-group-fields.php:41
#: includes/admin/views/settings-info.php:105
msgid "New Field"
msgstr "Câmp nou"

#: acf.php:426
msgid "View Field"
msgstr "Vizualizează câmpul"

#: acf.php:427
msgid "Search Fields"
msgstr "Caută câmpuri"

#: acf.php:428
msgid "No Fields found"
msgstr "Nu s-au găsit câmpuri"

#: acf.php:429
msgid "No Fields found in Trash"
msgstr "Nu s-a găsit nici un câmp în coșul de gunoi"

#: acf.php:468 includes/admin/admin-field-group.php:377
#: includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "Inactiv"

#: acf.php:473
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inactiv <span class=\"count\">(%s)</span>"
msgstr[1] "Inactive <span class=\"count\">(%s)</span>"
msgstr[2] "Inactivs <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-group.php:68
#: includes/admin/admin-field-group.php:69
#: includes/admin/admin-field-group.php:71
msgid "Field group updated."
msgstr "Grup actualizat."

#: includes/admin/admin-field-group.php:70
msgid "Field group deleted."
msgstr "Grup șters."

#: includes/admin/admin-field-group.php:73
msgid "Field group published."
msgstr "Grup publicat."

#: includes/admin/admin-field-group.php:74
msgid "Field group saved."
msgstr "Grup salvat."

#: includes/admin/admin-field-group.php:75
msgid "Field group submitted."
msgstr "Grup trimis."

#: includes/admin/admin-field-group.php:76
msgid "Field group scheduled for."
msgstr "Grup programat pentru."

#: includes/admin/admin-field-group.php:77
msgid "Field group draft updated."
msgstr "Ciorna grup actualizat."

#: includes/admin/admin-field-group.php:154
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Textul \"field_\" nu poate fi folosit la începutul denumirii unui câmp"

#: includes/admin/admin-field-group.php:155
msgid "This field cannot be moved until its changes have been saved"
msgstr "Acest câmp nu poate fi mutat decât după salvarea modificărilor"

#: includes/admin/admin-field-group.php:156
msgid "Field group title is required"
msgstr "Titlul grupului este obligatoriu"

#: includes/admin/admin-field-group.php:157
msgid "Move to trash. Are you sure?"
msgstr "Mută în coșul de gunoi. Ești sigur?"

#: includes/admin/admin-field-group.php:158
msgid "Move Custom Field"
msgstr "Mută câmpul personalizat"

#: includes/admin/admin-field-group.php:159
msgid "checked"
msgstr "marcat"

#: includes/admin/admin-field-group.php:160
msgid "(no label)"
msgstr "(fără etichetă)"

#: includes/admin/admin-field-group.php:161
#: includes/api/api-field-group.php:751
msgid "copy"
msgstr "copie"

#: includes/admin/admin-field-group.php:162
#: includes/admin/views/field-group-field-conditional-logic.php:51
#: includes/admin/views/field-group-field-conditional-logic.php:139
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:4158
msgid "or"
msgstr "sau"

#: includes/admin/admin-field-group.php:163
msgid "Null"
msgstr "Gol"

#: includes/admin/admin-field-group.php:197
msgid "Location"
msgstr "Locația"

#: includes/admin/admin-field-group.php:198
#: includes/admin/tools/class-acf-admin-tool-export.php:295
msgid "Settings"
msgstr "Setări"

#: includes/admin/admin-field-group.php:347
msgid "Field Keys"
msgstr "Cheile câmpulurilor"

#: includes/admin/admin-field-group.php:377
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Activ"

#: includes/admin/admin-field-group.php:753
msgid "Move Complete."
msgstr "Mutare Completă."

#: includes/admin/admin-field-group.php:754
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "Acest %s câmp acum poate fi găsit în %s grupul de câmpuri"

#: includes/admin/admin-field-group.php:755
msgid "Close Window"
msgstr "Închide Fereastra"

#: includes/admin/admin-field-group.php:796
msgid "Please select the destination for this field"
msgstr "Selectează destinația pentru acest câmp"

#: includes/admin/admin-field-group.php:803
msgid "Move Field"
msgstr "Mută Câmpul"

#: includes/admin/admin-field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "<span class=\"count\">(%s)</span> Activ"
msgstr[1] "<span class=\"count\">(%s)</span> Active"
msgstr[2] "<span class=\"count\">(%s)</span> Active"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "Grupul de câmpuri a fost duplicat. %s"

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s grupul de câmpuri a fost duplicat."
msgstr[1] "%s grupurile de câmpuri au fost duplicate."
msgstr[2] "%s grupurile de câmpuri au fost duplicate."

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr "Grupul de câmpuri a fost sincronizat. %s"

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s grupul de câmpuri a fost sincronizat."
msgstr[1] "%s grupurile de câmpuri au fost sincronizate."
msgstr[2] "%s grupurile de câmpuri au fost sincronizate."

#: includes/admin/admin-field-groups.php:394
#: includes/admin/admin-field-groups.php:557
msgid "Sync available"
msgstr "Sincronizare disponibilă"

#: includes/admin/admin-field-groups.php:507 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:355
msgid "Title"
msgstr "Titlu"

#: includes/admin/admin-field-groups.php:508
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/install-network.php:21
#: includes/admin/views/install-network.php:29
#: pro/fields/class-acf-field-gallery.php:382
msgid "Description"
msgstr "Descriere"

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "Stare"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr "Adaugă câmpuri puternice și intuitive pentru a personaliza WordPress."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Catalog schimbări"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Vezi ce este nou în <a href=\"%s\">versiunea %s</a>."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "Resurse"

#: includes/admin/admin-field-groups.php:619
msgid "Website"
msgstr ""

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "Documentație"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "Suport Tehnic"

#: includes/admin/admin-field-groups.php:623
msgid "Pro"
msgstr "Pro"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr ""

#: includes/admin/admin-field-groups.php:667
msgid "Duplicate this item"
msgstr "Copiază acest item"

#: includes/admin/admin-field-groups.php:667
#: includes/admin/admin-field-groups.php:683
#: includes/admin/views/field-group-field.php:46
#: pro/fields/class-acf-field-flexible-content.php:550
msgid "Duplicate"
msgstr "Copiază"

#: includes/admin/admin-field-groups.php:700
#: includes/fields/class-acf-field-google-map.php:113
#: includes/fields/class-acf-field-relationship.php:657
msgid "Search"
msgstr "Caută"

#: includes/admin/admin-field-groups.php:759
#, php-format
msgid "Select %s"
msgstr "Selectează %s"

#: includes/admin/admin-field-groups.php:767
msgid "Synchronise field group"
msgstr "Sincronizare grup de câmpuri"

#: includes/admin/admin-field-groups.php:767
#: includes/admin/admin-field-groups.php:797
msgid "Sync"
msgstr "Sincronizare"

#: includes/admin/admin-field-groups.php:779
msgid "Apply"
msgstr "Salvează"

#: includes/admin/admin-field-groups.php:797
msgid "Bulk Actions"
msgstr "Acțiuni în masă"

#: includes/admin/admin-tools.php:116
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Unelte"

#: includes/admin/admin.php:113
#: includes/admin/views/field-group-options.php:118
msgid "Custom Fields"
msgstr "Câmpuri Personalizate"

#: includes/admin/install-network.php:88 includes/admin/install.php:70
#: includes/admin/install.php:121
msgid "Upgrade Database"
msgstr "Actualizează baza de date"

#: includes/admin/install-network.php:140
msgid "Review sites & upgrade"
msgstr ""

#: includes/admin/install.php:187
msgid "Error validating request"
msgstr ""

#: includes/admin/install.php:210 includes/admin/views/install.php:104
msgid "No updates available."
msgstr ""

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Suplimente"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>Eroare</b>. Lista de suplimente nu poate fi încărcată"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Informații"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Ce este nou"

#: includes/admin/tools/class-acf-admin-tool-export.php:33
msgid "Export Field Groups"
msgstr "Exportați Grupurile de Câmputri"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:342
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "Generează PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:97
#: includes/admin/tools/class-acf-admin-tool-export.php:135
msgid "No field groups selected"
msgstr "Nu a fost selectat nici un grup de câmpuri"

#: includes/admin/tools/class-acf-admin-tool-export.php:174
#, php-format
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "Un grup exportat."
msgstr[1] "%s grupuri exportate."
msgstr[2] "%s de grupuri exportate."

#: includes/admin/tools/class-acf-admin-tool-export.php:241
#: includes/admin/tools/class-acf-admin-tool-export.php:269
msgid "Select Field Groups"
msgstr "Selectați Grupurile de Câmpuri"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Selectați grupurile de câmpuri pe care doriți să le exportați și apoi "
"selectați metoda de export. Folosiți butonul de descărcare pentru a exporta "
"într-un fișier .json pe care apoi îl puteți folosi pentru a importa într-o "
"altă instalare a ACF. Folosiți butonul Generare pentru a exporta totul în "
"cod PHP, pe care îl puteți pune apoi in tema voastră."

#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr "Exportă fișierul"

#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Următorul bloc de cod poate fi folosit pentru a înregistra o copie locală a "
"grupului(lor) de câmpuri selectat(e). Un grup de câmpuri local poate "
"facilita multe beneficii cum ar fi un timp de încărcare mai mic, control al "
"versiunii și câmpuri / setări dinamice. Pentru a beneficia de toate acestea "
"nu trebuie decât să copiați și să inserați următorul bloc de cod în fișierul "
"functions.php al temei sau să-l includeți într-un fișier extern."

#: includes/admin/tools/class-acf-admin-tool-export.php:446
msgid "Copy to clipboard"
msgstr "Copiază în clipboar"

#: includes/admin/tools/class-acf-admin-tool-export.php:483
msgid "Copied"
msgstr "Copiat"

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Importă Grupurile de câmpuri"

#: includes/admin/tools/class-acf-admin-tool-import.php:61
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Alege fișierul JSON ACF pe care dorești să-l imporți. Când vei apăsa butonul "
"import de mai jos, ACF v-a importa toate grupurile de câmpuri."

#: includes/admin/tools/class-acf-admin-tool-import.php:66
#: includes/fields/class-acf-field-file.php:37
msgid "Select File"
msgstr "Selectează fișierul"

#: includes/admin/tools/class-acf-admin-tool-import.php:76
msgid "Import File"
msgstr "Importă fișier"

#: includes/admin/tools/class-acf-admin-tool-import.php:100
#: includes/fields/class-acf-field-file.php:154
msgid "No file selected"
msgstr "Nu a fost selectat nici un fișier"

#: includes/admin/tools/class-acf-admin-tool-import.php:113
msgid "Error uploading file. Please try again"
msgstr "Eroare la încărcarea fișierului. Încearcă din nou"

#: includes/admin/tools/class-acf-admin-tool-import.php:122
msgid "Incorrect file type"
msgstr "Tipul fișierului este incorect"

#: includes/admin/tools/class-acf-admin-tool-import.php:139
msgid "Import file empty"
msgstr "Fișierul import este gol"

#: includes/admin/tools/class-acf-admin-tool-import.php:247
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Un grup importat"
msgstr[1] "%s grupuri importate"
msgstr[2] "%s de grupuri importate"

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "Condiție Logică"

#: includes/admin/views/field-group-field-conditional-logic.php:51
msgid "Show this field if"
msgstr "Arată acest câmp dacă"

#: includes/admin/views/field-group-field-conditional-logic.php:126
#: includes/admin/views/html-location-rule.php:80
msgid "and"
msgstr "și"

#: includes/admin/views/field-group-field-conditional-logic.php:141
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Adaugă grup de reguli"

#: includes/admin/views/field-group-field.php:38
#: pro/fields/class-acf-field-flexible-content.php:403
#: pro/fields/class-acf-field-repeater.php:296
msgid "Drag to reorder"
msgstr "Trage pentru a reordona"

#: includes/admin/views/field-group-field.php:42
#: includes/admin/views/field-group-field.php:45
msgid "Edit field"
msgstr "Editează câmp"

#: includes/admin/views/field-group-field.php:45
#: includes/fields/class-acf-field-file.php:136
#: includes/fields/class-acf-field-image.php:122
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:342
msgid "Edit"
msgstr "Editeză"

#: includes/admin/views/field-group-field.php:46
msgid "Duplicate field"
msgstr "Copiază câmp"

#: includes/admin/views/field-group-field.php:47
msgid "Move field to another group"
msgstr "Mută acest câmp în alt grup"

#: includes/admin/views/field-group-field.php:47
msgid "Move"
msgstr "Mută"

#: includes/admin/views/field-group-field.php:48
msgid "Delete field"
msgstr "Șterge câmp"

#: includes/admin/views/field-group-field.php:48
#: pro/fields/class-acf-field-flexible-content.php:549
msgid "Delete"
msgstr "Șterge"

#: includes/admin/views/field-group-field.php:65
msgid "Field Label"
msgstr "Etichetă Câmp"

#: includes/admin/views/field-group-field.php:66
msgid "This is the name which will appear on the EDIT page"
msgstr "Acesta este numele care va apărea în pagina de editare"

#: includes/admin/views/field-group-field.php:75
msgid "Field Name"
msgstr "Nume Câmp"

#: includes/admin/views/field-group-field.php:76
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr ""
"Un singur cuvânt, fără spații. Caracterele _ (underscore) și - (minus) sunt "
"permise"

#: includes/admin/views/field-group-field.php:85
msgid "Field Type"
msgstr "Tipul Câmpului"

#: includes/admin/views/field-group-field.php:96
msgid "Instructions"
msgstr "Instrucțiuni"

#: includes/admin/views/field-group-field.php:97
msgid "Instructions for authors. Shown when submitting data"
msgstr "Instrucțiuni pentru autor. Sunt afișate când se adaugă valori"

#: includes/admin/views/field-group-field.php:106
msgid "Required?"
msgstr "Obligatoriu?"

#: includes/admin/views/field-group-field.php:129
msgid "Wrapper Attributes"
msgstr "Atributele Wrapper-ului"

#: includes/admin/views/field-group-field.php:135
msgid "width"
msgstr "lățime"

#: includes/admin/views/field-group-field.php:150
msgid "class"
msgstr "clasă"

#: includes/admin/views/field-group-field.php:163
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:175
msgid "Close Field"
msgstr "Închide Câmpul"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Ordine"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:420
#: includes/fields/class-acf-field-radio.php:311
#: includes/fields/class-acf-field-select.php:418
#: pro/fields/class-acf-field-flexible-content.php:576
msgid "Label"
msgstr "Etichetă"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:964
#: pro/fields/class-acf-field-flexible-content.php:589
msgid "Name"
msgstr "Nume"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr ""

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Tip"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Nici un câmp. Click pe butonul <strong>+ Adaugă Câmp</strong> pentru a crea "
"primul câmp."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Adaugă Câmp"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Reguli"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Crează un set de reguli pentru a determina unde vor fi afișate aceste "
"câmpuri avansate personalizate"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Stil"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Standard (asemănător WP, folosește metabox-uri)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Seamless (fără metabox-uri)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Poziție"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Mare (după titlul aricolului / paginii)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Normal (dupa conținutul articolului / paginii)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Lateral"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Poziționarea etichetei"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:106
msgid "Top aligned"
msgstr "Aliniere Sus"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:107
msgid "Left aligned"
msgstr "Aliniere Stanga"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Plasamentul instrucțiunilor"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Sub etichete"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Sub câmpuri"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Nr. crt."

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr ""

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr ""

#: includes/admin/views/field-group-options.php:107
msgid "Hide on screen"
msgstr "Ascunde pe ecran"

#: includes/admin/views/field-group-options.php:108
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Selectează</b> ce opțiuni să fie <b>ascune</b> din ecranul de editare al "
"articolului sau al paginii."

#: includes/admin/views/field-group-options.php:108
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Daca în ecranul de editare al articolului / paginii apar mai multiple "
"grupuri de câmpuri, atunci opțiunile primul grup de câmpuri vor fi folosite "
"(cel cu numărul de ordine cel mai mic)"

#: includes/admin/views/field-group-options.php:115
msgid "Permalink"
msgstr "Legătură permanentă"

#: includes/admin/views/field-group-options.php:116
msgid "Content Editor"
msgstr "Editorul de conținut"

#: includes/admin/views/field-group-options.php:117
msgid "Excerpt"
msgstr "Descriere scurtă"

#: includes/admin/views/field-group-options.php:119
msgid "Discussion"
msgstr "Discuții"

#: includes/admin/views/field-group-options.php:120
msgid "Comments"
msgstr "Comentarii"

#: includes/admin/views/field-group-options.php:121
msgid "Revisions"
msgstr "Revizii"

#: includes/admin/views/field-group-options.php:122
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/field-group-options.php:123
msgid "Author"
msgstr "Autor"

#: includes/admin/views/field-group-options.php:124
msgid "Format"
msgstr "Format"

#: includes/admin/views/field-group-options.php:125
msgid "Page Attributes"
msgstr "Atributele Paginii"

#: includes/admin/views/field-group-options.php:126
#: includes/fields/class-acf-field-relationship.php:671
msgid "Featured Image"
msgstr "Imagine Reprezentativă"

#: includes/admin/views/field-group-options.php:127
msgid "Categories"
msgstr "Categorii"

#: includes/admin/views/field-group-options.php:128
msgid "Tags"
msgstr "Etichete"

#: includes/admin/views/field-group-options.php:129
msgid "Send Trackbacks"
msgstr "Trackback-uri"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Arată acest grup de câmpuri dacă"

#: includes/admin/views/install-network.php:4
msgid "Upgrade Sites"
msgstr ""

#: includes/admin/views/install-network.php:9
#: includes/admin/views/install.php:3
msgid "Advanced Custom Fields Database Upgrade"
msgstr ""

#: includes/admin/views/install-network.php:11
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""

#: includes/admin/views/install-network.php:20
#: includes/admin/views/install-network.php:28
msgid "Site"
msgstr ""

#: includes/admin/views/install-network.php:48
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr ""

#: includes/admin/views/install-network.php:50
msgid "Site is up to date"
msgstr ""

#: includes/admin/views/install-network.php:63
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""

#: includes/admin/views/install-network.php:102
#: includes/admin/views/install-notice.php:42
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Este puternic recomandat să faceți o copie de siguranța a bazei de date "
"înainte de a începe procesul de actualizare. Ești sigur că vrei să începi "
"actualizarea acum?"

#: includes/admin/views/install-network.php:158
msgid "Upgrade complete"
msgstr ""

#: includes/admin/views/install-network.php:162
#: includes/admin/views/install.php:9
#, php-format
msgid "Upgrading data to version %s"
msgstr "Actualizarea datelor la versiunea %s"

#: includes/admin/views/install-notice.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "Repeater"

#: includes/admin/views/install-notice.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Conținut Flexibil"

#: includes/admin/views/install-notice.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galerie"

#: includes/admin/views/install-notice.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "Pagina de Opțiuni"

#: includes/admin/views/install-notice.php:26
msgid "Database Upgrade Required"
msgstr "Actualizare bazei de date este necesară"

#: includes/admin/views/install-notice.php:28
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Îți mulțumim pentru actualizarea făcută la %s v%s!"

#: includes/admin/views/install-notice.php:28
msgid ""
"Before you start using the new awesome features, please update your database "
"to the newest version."
msgstr ""
"Înainte de a începe să folosești uimitoarele funcții noi, te rungăm să "
"actualizezi baza de date la o versiune mai recentă."

#: includes/admin/views/install-notice.php:31
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""

#: includes/admin/views/install.php:7
msgid "Reading upgrade tasks..."
msgstr "Citirea sarcinilor necesare pentru actualizare..."

#: includes/admin/views/install.php:11
#, php-format
msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Descarcă & Instalează"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Instalat"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Bine ai venit la Câmpuri Personalizate Avansate"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Iți mulțumim pentru actualizare! ACF %s  a devenit mai mare și mai bun. "
"Sperăm să-ți placă."

#: includes/admin/views/settings-info.php:17
msgid "A smoother custom field experience"
msgstr "O folosire mai ușoara a câmpurilor personalizate"

#: includes/admin/views/settings-info.php:22
msgid "Improved Usability"
msgstr "Folosire Facilă"

#: includes/admin/views/settings-info.php:23
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Includerea popularei librării Select2 a îmbunătățit folosirea dar și viteaza "
"pentru un număr ridicat de tipuri de câmpuri care includ, obiectele articol, "
"legătura paginii, taxonomia și selecția."

#: includes/admin/views/settings-info.php:27
msgid "Improved Design"
msgstr "Design îmbunătățit"

#: includes/admin/views/settings-info.php:28
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Multe câmpuri au dobândit un nou design vizual pentru a face ACF un produs "
"mai ușor de folosit! Schimbările pot fi văzute în special, la câmpurile "
"Galerie, Relații și oEmbed(nou)!"

#: includes/admin/views/settings-info.php:32
msgid "Improved Data"
msgstr "Tipuri de Date imbunătățite"

#: includes/admin/views/settings-info.php:33
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"Refacerea arhitecturii tipurilor de date a permis ca sub câmpurile să fie "
"independente de câmpurile părinte. Acest lucru vă permite să trageți și să "
"eliberați câmpurile în și în afara câmpurilor părinte!"

#: includes/admin/views/settings-info.php:39
msgid "Goodbye Add-ons. Hello PRO"
msgstr "La revedere Add-onuri. Salut PRO"

#: includes/admin/views/settings-info.php:44
msgid "Introducing ACF PRO"
msgstr "Introducere în ACF PRO"

#: includes/admin/views/settings-info.php:45
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr "Am schimbat modul în care funcționalitatea premium este transmisă!"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Toate cele 4 add-onuri premium au fost combinate într-o nouă <a href=\"%s"
"\">Versiune PRO a ACF</a>. Putând alege licența personală sau licența de "
"developer, funcționalitatea premium este acum mai accesibilă ca niciodată!"

#: includes/admin/views/settings-info.php:50
msgid "Powerful Features"
msgstr "Caracteristici puternice"

#: includes/admin/views/settings-info.php:51
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF PRO conține caracteristici puternice cum ar fi date repetabile, machete "
"de conținut flexibil, un frumos câmp pentru galerie și puterea de a crea "
"pagini administrative de opțiuni!"

#: includes/admin/views/settings-info.php:52
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Citește mai mult despre <a href=\"%s\">Caracteristicile ACF PRO</a>."

#: includes/admin/views/settings-info.php:56
msgid "Easy Upgrading"
msgstr "Actualizare ușoară"

#: includes/admin/views/settings-info.php:57
#, php-format
msgid ""
"To help make upgrading easy, <a href=\"%s\">login to your store account</a> "
"and claim a free copy of ACF PRO!"
msgstr ""
"Pentru a facilita actualizarea într-un mod ușor, <a href=\"%s\">intră în "
"contul tău</a> și obține o copie gratis a ACF PRO!"

#: includes/admin/views/settings-info.php:58
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>"
msgstr ""
"De asemenea am pus la dispoziția ta <a href=\"%s\">un ghid de actualizare</"
"a> pentru a răspunde tuturor întrebărilor, dar dacă totuși ai o întrebare, "
"te rog sa contactezi echipa noastră de suport, folosind <a href=\"%s\">help "
"desk</a>"

#: includes/admin/views/settings-info.php:66
msgid "Under the Hood"
msgstr "Sub capată"

#: includes/admin/views/settings-info.php:71
msgid "Smarter field settings"
msgstr "Setări deștepte ale câmpurilor"

#: includes/admin/views/settings-info.php:72
msgid "ACF now saves its field settings as individual post objects"
msgstr ""
"ACF salvează acum setările câmpurilor ca fiind obiecte de tip articol "
"individuale"

#: includes/admin/views/settings-info.php:76
msgid "More AJAX"
msgstr "Mai mult AJAX"

#: includes/admin/views/settings-info.php:77
msgid "More fields use AJAX powered search to speed up page loading"
msgstr ""
"Mai multe câmpuri folosesc puterea de căutare AJAX pentru a micșora timpul "
"de încărcare al paginii"

#: includes/admin/views/settings-info.php:81
msgid "Local JSON"
msgstr "JSON local"

#: includes/admin/views/settings-info.php:82
msgid "New auto export to JSON feature improves speed"
msgstr "Noua funcționalitate de auto import în JSON îmbunătățește viteza"

#: includes/admin/views/settings-info.php:88
msgid "Better version control"
msgstr "Un control mai bun al versiunii"

#: includes/admin/views/settings-info.php:89
msgid ""
"New auto export to JSON feature allows field settings to be version "
"controlled"
msgstr ""
"Noua funcționalitate de auto export în JSON permite ca setările câmpurilor "
"să fie versionabile"

#: includes/admin/views/settings-info.php:93
msgid "Swapped XML for JSON"
msgstr "Am schimbat XML în favoarea JSON"

#: includes/admin/views/settings-info.php:94
msgid "Import / Export now uses JSON in favour of XML"
msgstr "Importul / Exportul folosește acum JSON în defavoarea XML"

#: includes/admin/views/settings-info.php:98
msgid "New Forms"
msgstr "Noi formulare"

#: includes/admin/views/settings-info.php:99
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr ""
"Câmpurile pot fi acum mapate la comentarii, widget-uri sau orice alt "
"formular creat de user!"

#: includes/admin/views/settings-info.php:106
msgid "A new field for embedding content has been added"
msgstr "Un nou câmp pentru încorporarea conținutului a fost adaugat"

#: includes/admin/views/settings-info.php:110
msgid "New Gallery"
msgstr "Galerie Nouă"

#: includes/admin/views/settings-info.php:111
msgid "The gallery field has undergone a much needed facelift"
msgstr "Câmpul Galierie a suferit un facelift bine meritat"

#: includes/admin/views/settings-info.php:115
msgid "New Settings"
msgstr "Configurări noi"

#: includes/admin/views/settings-info.php:116
msgid ""
"Field group settings have been added for label placement and instruction "
"placement"
msgstr ""
"Setările grupului de câmpuri a fost adăugat pentru poziționarea etichitelor "
"și a instrucțiunilor"

#: includes/admin/views/settings-info.php:122
msgid "Better Front End Forms"
msgstr "Formulare Front End mai bune"

#: includes/admin/views/settings-info.php:123
msgid "acf_form() can now create a new post on submission"
msgstr ""
"acf_form() poate crea acum un nou articol odată ce cererea a fost trimisă"

#: includes/admin/views/settings-info.php:127
msgid "Better Validation"
msgstr "O validare mai bună"

#: includes/admin/views/settings-info.php:128
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr ""
"Validarea formularelor se face acum via PHP + AJAX în defavoarea numai JS"

#: includes/admin/views/settings-info.php:132
msgid "Relationship Field"
msgstr "Câmp de realționare"

#: includes/admin/views/settings-info.php:133
msgid ""
"New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr ""
"Setările noului câmp de relaționare pentru Filtre (Caută, Tipul Articolului, "
"Taxonomie)"

#: includes/admin/views/settings-info.php:139
msgid "Moving Fields"
msgstr "Câmpuri care pot fi mutate"

#: includes/admin/views/settings-info.php:140
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents"
msgstr ""
"Noua funcționalitate a grupului de câmpuri îți permite acum să muți "
"câmpurile între grupuri"

#: includes/admin/views/settings-info.php:144
#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Legătura Paginii"

#: includes/admin/views/settings-info.php:145
msgid "New archives group in page_link field selection"
msgstr "Noua arhivă de grup în selecția page_link"

#: includes/admin/views/settings-info.php:149
msgid "Better Options Pages"
msgstr "Opțiuni mai bune pentru Pagini"

#: includes/admin/views/settings-info.php:150
msgid ""
"New functions for options page allow creation of both parent and child menu "
"pages"
msgstr ""
"Noile funcții pentru opțiunile pagini îți permite acum create de pagini "
"meniu și submeniuri"

#: includes/admin/views/settings-info.php:159
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Credem că vei îndrăgi shimbările în %s."

#: includes/api/api-helpers.php:1039
msgid "Thumbnail"
msgstr "Miniatură"

#: includes/api/api-helpers.php:1040
msgid "Medium"
msgstr "Mediu"

#: includes/api/api-helpers.php:1041
msgid "Large"
msgstr "Mare"

#: includes/api/api-helpers.php:1090
msgid "Full Size"
msgstr "Marime completă"

#: includes/api/api-helpers.php:1431 includes/api/api-helpers.php:2004
#: pro/fields/class-acf-field-clone.php:996
msgid "(no title)"
msgstr "(fără titlu)"

#: includes/api/api-helpers.php:4079
#, php-format
msgid "Image width must be at least %dpx."
msgstr "Lățimea imaginii trebuie să fie cel puțin %dpx."

#: includes/api/api-helpers.php:4084
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "Lățimea imaginii nu trebuie să depășească %dpx."

#: includes/api/api-helpers.php:4100
#, php-format
msgid "Image height must be at least %dpx."
msgstr "Înălțimea imaginii trebuie să fie cel puțin %dpx."

#: includes/api/api-helpers.php:4105
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "Înălțimea imaginii nu trebuie să depășească %dpx."

#: includes/api/api-helpers.php:4123
#, php-format
msgid "File size must be at least %s."
msgstr "Mărimea fișierului trebuie să fie cel puțin %s."

#: includes/api/api-helpers.php:4128
#, php-format
msgid "File size must must not exceed %s."
msgstr "Mărimea fișierului nu trebuie să depășească %s."

#: includes/api/api-helpers.php:4162
#, php-format
msgid "File type must be %s."
msgstr "Tipul fișierului trebuie să fie %s."

#: includes/assets.php:164
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Modificările făcute vor fi pierdute dacă nu salvați"

#: includes/assets.php:167 includes/fields/class-acf-field-select.php:257
msgctxt "verb"
msgid "Select"
msgstr "Selectează"

#: includes/assets.php:168
msgctxt "verb"
msgid "Edit"
msgstr "Editeză"

#: includes/assets.php:169
msgctxt "verb"
msgid "Update"
msgstr "Actualizează"

#: includes/assets.php:170 pro/fields/class-acf-field-gallery.php:44
msgid "Uploaded to this post"
msgstr "Încărcate pentru acest articol"

#: includes/assets.php:171
msgid "Expand Details"
msgstr "Extinde Detaliile"

#: includes/assets.php:172
msgid "Collapse Details"
msgstr "Închide Detaliile"

#: includes/assets.php:173
msgid "Restricted"
msgstr ""

#: includes/assets.php:174
msgid "All images"
msgstr "Toate imaginiile"

#: includes/assets.php:177
msgid "Validation successful"
msgstr "Validare a fost făcută cu succes"

#: includes/assets.php:178 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Validarea a eșuat"

#: includes/assets.php:179
msgid "1 field requires attention"
msgstr ""

#: includes/assets.php:180
#, php-format
msgid "%d fields require attention"
msgstr ""

#: includes/assets.php:183
msgid "Are you sure?"
msgstr ""

#: includes/assets.php:184 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Da"

#: includes/assets.php:185 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:174
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "Nu"

#: includes/assets.php:186 includes/fields/class-acf-field-file.php:138
#: includes/fields/class-acf-field-image.php:124
#: includes/fields/class-acf-field-link.php:140
#: pro/fields/class-acf-field-gallery.php:343
#: pro/fields/class-acf-field-gallery.php:531
msgid "Remove"
msgstr "Înlătură"

#: includes/assets.php:187
msgid "Cancel"
msgstr ""

#: includes/assets.php:190
msgid "Has any value"
msgstr ""

#: includes/assets.php:191
msgid "Has no value"
msgstr ""

#: includes/assets.php:192
msgid "Value is equal to"
msgstr ""

#: includes/assets.php:193
msgid "Value is not equal to"
msgstr ""

#: includes/assets.php:194
msgid "Value matches pattern"
msgstr ""

#: includes/assets.php:195
msgid "Value contains"
msgstr ""

#: includes/assets.php:196
msgid "Value is greater than"
msgstr ""

#: includes/assets.php:197
msgid "Value is less than"
msgstr ""

#: includes/assets.php:198
msgid "Selection is greater than"
msgstr ""

#: includes/assets.php:199
msgid "Selection is less than"
msgstr ""

#: includes/fields.php:144
msgid "Basic"
msgstr "De bază"

#: includes/fields.php:145 includes/forms/form-front.php:47
msgid "Content"
msgstr "Conținut"

#: includes/fields.php:146
msgid "Choice"
msgstr "Alegere"

#: includes/fields.php:147
msgid "Relational"
msgstr "Relațional"

#: includes/fields.php:148
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:149
#: includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:389
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:290
#: pro/fields/class-acf-field-clone.php:843
#: pro/fields/class-acf-field-flexible-content.php:546
#: pro/fields/class-acf-field-flexible-content.php:595
#: pro/fields/class-acf-field-repeater.php:442
msgid "Layout"
msgstr "Schemă"

#: includes/fields.php:326
msgid "Field type does not exist"
msgstr "Tipul câmpului nu există"

#: includes/fields.php:326
msgid "Unknown"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:99
msgid "Open"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:100
msgid "Display this accordion as open on page load."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:110
msgid "Allow this accordion to open without closing others."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:120
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:349
msgid "Choices"
msgstr "Alegere"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:350
msgid "Enter each choice on a new line."
msgstr "Pune fiecare alegere pe o linie nouă."

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:350
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Pentru un mai bun control, poți specifica o valoare și o etichetă ca de "
"exemplu:"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:350
msgid "red : Red"
msgstr "roșu : Roșu"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:412
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:367
#: includes/fields/class-acf-field-taxonomy.php:793
#: includes/fields/class-acf-field-user.php:409
msgid "Allow Null?"
msgstr "Permite valori nule?"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:380
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:281
#: includes/fields/class-acf-field-range.php:146
#: includes/fields/class-acf-field-select.php:358
#: includes/fields/class-acf-field-text.php:119
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:410
msgid "Default Value"
msgstr "Valoare implicită"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:282
#: includes/fields/class-acf-field-range.php:147
#: includes/fields/class-acf-field-text.php:120
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:411
msgid "Appears when creating a new post"
msgstr "Apare cănd creați un articol nou"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:396
#: includes/fields/class-acf-field-radio.php:297
msgid "Horizontal"
msgstr "Orizontal"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:395
#: includes/fields/class-acf-field-radio.php:296
msgid "Vertical"
msgstr "Vertical"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:413
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-image.php:188
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:304
#: includes/fields/class-acf-field-taxonomy.php:833
msgid "Return Value"
msgstr "Valoarea returnată"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-image.php:189
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:305
msgid "Specify the returned value on front end"
msgstr "Specificați valoarea returnată în front end"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:419
#: includes/fields/class-acf-field-radio.php:310
#: includes/fields/class-acf-field-select.php:417
msgid "Value"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:421
#: includes/fields/class-acf-field-radio.php:312
#: includes/fields/class-acf-field-select.php:419
msgid "Both (Array)"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:780
msgid "Checkbox"
msgstr "Checkbox"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "Comută tot"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:381
#: includes/fields/class-acf-field-select.php:359
msgid "Enter each default value on a new line"
msgstr "Introdu fiecare valoare implicită pe o linie nouă"

#: includes/fields/class-acf-field-checkbox.php:403
msgid "Toggle"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:404
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Alege Culoarea"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "Curăță"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "Implicit"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "Alege Culoarea"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Alege data calendaristică"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Astăzi"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Următor"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Anterior"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Săpt"

#: includes/fields/class-acf-field-date_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "Formatul de Afișare"

#: includes/fields/class-acf-field-date_picker.php:181
#: includes/fields/class-acf-field-date_time_picker.php:184
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "Formatul afișat în momentul editării unui articol"

#: includes/fields/class-acf-field-date_picker.php:189
#: includes/fields/class-acf-field-date_picker.php:220
#: includes/fields/class-acf-field-date_time_picker.php:193
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:199
msgid "Save Format"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:200
msgid "The format used when saving a value"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-relationship.php:698
#: includes/fields/class-acf-field-select.php:412
#: includes/fields/class-acf-field-time_picker.php:124
#: includes/fields/class-acf-field-user.php:428
msgid "Return Format"
msgstr "Formatul Returnat"

#: includes/fields/class-acf-field-date_picker.php:211
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "Formatul rezultat via funcțiilor șablon"

#: includes/fields/class-acf-field-date_picker.php:229
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "Săptămâna începe în ziua de"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Alege ora"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Ora"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Oră"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minut"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Secundă"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milisecundă"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microsecundă"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Fus Orar"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Acum"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Gata"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Selectează"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr ""

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Adresă de email"

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:128
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "Textul afișat ca placeholder"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:129
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "Apare în intrare"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:185
#: includes/fields/class-acf-field-text.php:137
msgid "Prepend"
msgstr "Prefixează"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:186
#: includes/fields/class-acf-field-text.php:138
msgid "Appears before the input"
msgstr "Apare înainte de intrare"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:194
#: includes/fields/class-acf-field-text.php:146
msgid "Append"
msgstr "Adaugă"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:195
#: includes/fields/class-acf-field-text.php:147
msgid "Appears after the input"
msgstr "Apare după intrare"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Fișier"

#: includes/fields/class-acf-field-file.php:38
msgid "Edit File"
msgstr "Editează fișierul"

#: includes/fields/class-acf-field-file.php:39
msgid "Update File"
msgstr "Actualizează fișierul"

#: includes/fields/class-acf-field-file.php:125
msgid "File name"
msgstr ""

#: includes/fields/class-acf-field-file.php:129
#: includes/fields/class-acf-field-file.php:232
#: includes/fields/class-acf-field-file.php:243
#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:277
#: pro/fields/class-acf-field-gallery.php:690
#: pro/fields/class-acf-field-gallery.php:719
msgid "File size"
msgstr "Mărime fișier"

#: includes/fields/class-acf-field-file.php:154
msgid "Add File"
msgstr "Adaugă fișier"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "Mulțime de fișier"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "Cale Fișier"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "ID Fișier"

#: includes/fields/class-acf-field-file.php:214
#: includes/fields/class-acf-field-image.php:213
#: pro/fields/class-acf-field-gallery.php:655
msgid "Library"
msgstr "Librărie"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:214
#: pro/fields/class-acf-field-gallery.php:656
msgid "Limit the media library choice"
msgstr "Limitați alegerea librăriei media"

#: includes/fields/class-acf-field-file.php:220
#: includes/fields/class-acf-field-image.php:219
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:87
#: includes/locations/class-acf-location-user-role.php:111
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:661
msgid "All"
msgstr "Toate"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:220
#: pro/fields/class-acf-field-gallery.php:662
msgid "Uploaded to post"
msgstr "Încărcate pentru acest articol"

#: includes/fields/class-acf-field-file.php:228
#: includes/fields/class-acf-field-image.php:227
#: pro/fields/class-acf-field-gallery.php:669
msgid "Minimum"
msgstr "Minim"

#: includes/fields/class-acf-field-file.php:229
#: includes/fields/class-acf-field-file.php:240
msgid "Restrict which files can be uploaded"
msgstr "Restricționați ce tipuri de fișiere pot fi încărcate"

#: includes/fields/class-acf-field-file.php:239
#: includes/fields/class-acf-field-image.php:256
#: pro/fields/class-acf-field-gallery.php:698
msgid "Maximum"
msgstr "Maxim"

#: includes/fields/class-acf-field-file.php:250
#: includes/fields/class-acf-field-image.php:285
#: pro/fields/class-acf-field-gallery.php:727
msgid "Allowed file types"
msgstr "Tipuri de fișiere permise"

#: includes/fields/class-acf-field-file.php:251
#: includes/fields/class-acf-field-image.php:286
#: pro/fields/class-acf-field-gallery.php:728
msgid "Comma separated list. Leave blank for all types"
msgstr "Listă separată prin virgulă. Lăsați liber pentru toate tipurile"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Hartă Google"

#: includes/fields/class-acf-field-google-map.php:43
msgid "Sorry, this browser does not support geolocation"
msgstr "Ne pare rău, acest broswer nu suportă geo locația"

#: includes/fields/class-acf-field-google-map.php:114
msgid "Clear location"
msgstr "Sterge Locația"

#: includes/fields/class-acf-field-google-map.php:115
msgid "Find current location"
msgstr "Găsește locația curentă"

#: includes/fields/class-acf-field-google-map.php:118
msgid "Search for address..."
msgstr "Caută adresa..."

#: includes/fields/class-acf-field-google-map.php:148
#: includes/fields/class-acf-field-google-map.php:159
msgid "Center"
msgstr "Centru"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-google-map.php:160
msgid "Center the initial map"
msgstr "Centrează harta inițială"

#: includes/fields/class-acf-field-google-map.php:171
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:172
msgid "Set the initial zoom level"
msgstr "Setează nivelul de zoom inițial"

#: includes/fields/class-acf-field-google-map.php:181
#: includes/fields/class-acf-field-image.php:239
#: includes/fields/class-acf-field-image.php:268
#: includes/fields/class-acf-field-oembed.php:268
#: pro/fields/class-acf-field-gallery.php:681
#: pro/fields/class-acf-field-gallery.php:710
msgid "Height"
msgstr "Înălțime"

#: includes/fields/class-acf-field-google-map.php:182
msgid "Customise the map height"
msgstr "Personalizați înălțimea hărții"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr ""

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:381
msgid "Sub Fields"
msgstr "Sub câmpuri"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:844
msgid "Specify the style used to render the selected fields"
msgstr ""

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:849
#: pro/fields/class-acf-field-flexible-content.php:606
#: pro/fields/class-acf-field-repeater.php:450
msgid "Block"
msgstr "Bloc"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:850
#: pro/fields/class-acf-field-flexible-content.php:605
#: pro/fields/class-acf-field-repeater.php:449
msgid "Table"
msgstr "Tabel"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:851
#: pro/fields/class-acf-field-flexible-content.php:607
#: pro/fields/class-acf-field-repeater.php:451
msgid "Row"
msgstr "Linie"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Imagine"

#: includes/fields/class-acf-field-image.php:42
msgid "Select Image"
msgstr "Alege imaginea"

#: includes/fields/class-acf-field-image.php:43
#: pro/fields/class-acf-field-gallery.php:42
msgid "Edit Image"
msgstr "Editează imaginea"

#: includes/fields/class-acf-field-image.php:44
#: pro/fields/class-acf-field-gallery.php:43
msgid "Update Image"
msgstr "Actualizează imaginea"

#: includes/fields/class-acf-field-image.php:140
msgid "No image selected"
msgstr "Nu ai selectat nici o imagine"

#: includes/fields/class-acf-field-image.php:140
msgid "Add Image"
msgstr "Adaugă o imagine"

#: includes/fields/class-acf-field-image.php:194
msgid "Image Array"
msgstr "Mulțime de imagini"

#: includes/fields/class-acf-field-image.php:195
msgid "Image URL"
msgstr "URL-ul imaginii"

#: includes/fields/class-acf-field-image.php:196
msgid "Image ID"
msgstr "ID-ul imaginii"

#: includes/fields/class-acf-field-image.php:203
msgid "Preview Size"
msgstr "Dimensiunea previzualizării"

#: includes/fields/class-acf-field-image.php:204
msgid "Shown when entering data"
msgstr "Afișat la introducerea datelor"

#: includes/fields/class-acf-field-image.php:228
#: includes/fields/class-acf-field-image.php:257
#: pro/fields/class-acf-field-gallery.php:670
#: pro/fields/class-acf-field-gallery.php:699
msgid "Restrict which images can be uploaded"
msgstr "Restricționează care imagini pot fi încărcate"

#: includes/fields/class-acf-field-image.php:231
#: includes/fields/class-acf-field-image.php:260
#: includes/fields/class-acf-field-oembed.php:257
#: pro/fields/class-acf-field-gallery.php:673
#: pro/fields/class-acf-field-gallery.php:702
msgid "Width"
msgstr "Lățime"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr ""

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr ""

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr ""

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr ""

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr ""

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "Mesaj"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "Linii Noi"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "Controlează cum sunt redate noile linii"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "Adaugă automat paragrafe"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "Adaugă automat &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "Nici o Formater"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "Scăpare HTML"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Permite markup-ului HTML să fie afișat că text vizibil în loc să fie "
"interpretat"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Număr"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:155
msgid "Minimum Value"
msgstr "Valoare minimă"

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:165
msgid "Maximum Value"
msgstr "Valoare maximă"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:175
msgid "Step Size"
msgstr "Mărime pas"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "Valoarea trebuie să fie un număr"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "Valoarea trebuie să fie egală sau mai mare decât %d"

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "Valoarea trebuie să fie egală sau mai mică decât %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:216
msgid "Enter URL"
msgstr "Introduceți URL"

#: includes/fields/class-acf-field-oembed.php:254
#: includes/fields/class-acf-field-oembed.php:265
msgid "Embed Size"
msgstr "Marimea Embed"

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "Arhive"

#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:268
#: includes/fields/class-acf-field-taxonomy.php:986
msgid "Parent"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:384
#: includes/fields/class-acf-field-relationship.php:624
msgid "Filter by Post Type"
msgstr "Filtur dupa Tipul Articolului"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:392
#: includes/fields/class-acf-field-relationship.php:632
msgid "All post types"
msgstr "Toate Tipurile Articolului"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:398
#: includes/fields/class-acf-field-relationship.php:638
msgid "Filter by Taxonomy"
msgstr "Filtru după Taxonomie"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:406
#: includes/fields/class-acf-field-relationship.php:646
msgid "All taxonomies"
msgstr "Toate Taxonomiile"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:422
#: includes/fields/class-acf-field-select.php:377
#: includes/fields/class-acf-field-user.php:419
msgid "Select multiple values?"
msgstr "Permite selecția de valori multiple?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Parolă"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:703
msgid "Post Object"
msgstr "Obiect Articol"

#: includes/fields/class-acf-field-post_object.php:438
#: includes/fields/class-acf-field-relationship.php:704
msgid "Post ID"
msgstr "ID-ul Articolului"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Buton Radio"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Altceva"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr "Adaugă 'Altceva' pentru a permite o valoare personalizată"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "Salvează Altceva"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "Salvează valoarea 'Altceva' la opțiunile câmpului"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Relație"

#: includes/fields/class-acf-field-relationship.php:40
msgid "Maximum values reached ( {max} values )"
msgstr "Valorile maxime atinse  ( {max} valori )"

#: includes/fields/class-acf-field-relationship.php:41
msgid "Loading"
msgstr "Se încarcă"

#: includes/fields/class-acf-field-relationship.php:42
msgid "No matches found"
msgstr "Nici un rezultat"

#: includes/fields/class-acf-field-relationship.php:424
msgid "Select post type"
msgstr "Alegeți tipul articolului"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Select taxonomy"
msgstr "Alegeți taxonomia"

#: includes/fields/class-acf-field-relationship.php:540
msgid "Search..."
msgstr "Caută..."

#: includes/fields/class-acf-field-relationship.php:652
msgid "Filters"
msgstr "Filtre"

#: includes/fields/class-acf-field-relationship.php:658
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Tipul Articolului"

#: includes/fields/class-acf-field-relationship.php:659
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:763
#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy"
msgstr "Taxonomie"

#: includes/fields/class-acf-field-relationship.php:666
msgid "Elements"
msgstr "Elemente"

#: includes/fields/class-acf-field-relationship.php:667
msgid "Selected elements will be displayed in each result"
msgstr "Elementele selectate vor apărea în fiecare rezultat"

#: includes/fields/class-acf-field-relationship.php:678
msgid "Minimum posts"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:687
msgid "Maximum posts"
msgstr "Numărul maxim de articole"

#: includes/fields/class-acf-field-relationship.php:791
#: pro/fields/class-acf-field-gallery.php:800
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s necesită cel puțin %s selectie"
msgstr[1] "%s necesită cel puțin %s selecții"
msgstr[2] "%s necesită cel puțin %s selecții"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:785
msgctxt "noun"
msgid "Select"
msgstr "Selectează"

#: includes/fields/class-acf-field-select.php:40
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Un rezultat disponibil, apasă enter pentru a-l selecta."

#: includes/fields/class-acf-field-select.php:41
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d rezultate disponibile, apasă tastele sus/jos pentru a naviga."

#: includes/fields/class-acf-field-select.php:42
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Nici un rezultat"

#: includes/fields/class-acf-field-select.php:43
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Te rog să introduci cel puțin un caracter"

#: includes/fields/class-acf-field-select.php:44
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Te rog să introduci %d sau mai multe caractere"

#: includes/fields/class-acf-field-select.php:45
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Te rog să ștergi un caracter"

#: includes/fields/class-acf-field-select.php:46
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Te rog să ștergi %d caractere"

#: includes/fields/class-acf-field-select.php:47
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Poți selecta un singur element"

#: includes/fields/class-acf-field-select.php:48
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Poți selecta %d elemente"

#: includes/fields/class-acf-field-select.php:49
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Se încarcă mai multe rezultate&hellip;"

#: includes/fields/class-acf-field-select.php:50
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Se caută&hellip;"

#: includes/fields/class-acf-field-select.php:51
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Încărcarea a eșuat"

#: includes/fields/class-acf-field-select.php:387
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "UI stilizat"

#: includes/fields/class-acf-field-select.php:397
msgid "Use AJAX to lazy load choices?"
msgstr "Folosiți AJAX pentru a încărca alegerile în modul ”Lazy Load”?"

#: includes/fields/class-acf-field-select.php:413
msgid "Specify the value returned"
msgstr ""

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr ""

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "Tab"

#: includes/fields/class-acf-field-tab.php:102
msgid "Placement"
msgstr "Plasament"

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:713
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "Fără %s"

#: includes/fields/class-acf-field-taxonomy.php:732
msgid "None"
msgstr "Nici unul"

#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Select the taxonomy to be displayed"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:773
msgid "Appearance"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Select the appearance of this field"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:779
msgid "Multiple Values"
msgstr "Valori multiple"

#: includes/fields/class-acf-field-taxonomy.php:781
msgid "Multi Select"
msgstr "Selectie multiplă"

#: includes/fields/class-acf-field-taxonomy.php:783
msgid "Single Value"
msgstr "O singură valoare"

#: includes/fields/class-acf-field-taxonomy.php:784
msgid "Radio Buttons"
msgstr "Butoane radio"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "Create Terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:804
msgid "Allow new terms to be created whilst editing"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:813
msgid "Save Terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:814
msgid "Connect selected terms to the post"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:823
msgid "Load Terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:824
msgid "Load value from posts terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:838
msgid "Term Object"
msgstr "Obiectul Termen"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "Term ID"
msgstr "ID-ul Termenului"

#: includes/fields/class-acf-field-taxonomy.php:898
msgid "Error."
msgstr "Eroare."

#: includes/fields/class-acf-field-taxonomy.php:898
#, php-format
msgid "User unable to add new %s"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:911
#, php-format
msgid "%s already exists"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:952
#, php-format
msgid "%s added"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:998
msgid "Add"
msgstr ""

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-text.php:155
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Limită de caractere"

#: includes/fields/class-acf-field-text.php:156
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Lasă gol pentru a nu a avea o limită"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Zonă de Text"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "Linii"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "Setează înălțimea zonei de text"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Adevărat / False"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:170
msgid "Off Text"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when inactive"
msgstr ""

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Url"

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "Valoarea trebuie să fie un URL valid"

#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr "Utilizatorul"

#: includes/fields/class-acf-field-user.php:394
msgid "Filter by role"
msgstr "Filtrează după rol"

#: includes/fields/class-acf-field-user.php:402
msgid "All user roles"
msgstr "Toate rolurile de utilizator"

#: includes/fields/class-acf-field-user.php:433
msgid "User Array"
msgstr ""

#: includes/fields/class-acf-field-user.php:434
msgid "User Object"
msgstr ""

#: includes/fields/class-acf-field-user.php:435
msgid "User ID"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Editor Vizual"

#: includes/fields/class-acf-field-wysiwyg.php:359
msgid "Visual"
msgstr "Visual"

#: includes/fields/class-acf-field-wysiwyg.php:360
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Click to initialize TinyMCE"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Tabs"
msgstr "Taburi"

#: includes/fields/class-acf-field-wysiwyg.php:424
msgid "Visual & Text"
msgstr "Vizual & Text"

#: includes/fields/class-acf-field-wysiwyg.php:425
msgid "Visual Only"
msgstr "Doar Vizual"

#: includes/fields/class-acf-field-wysiwyg.php:426
msgid "Text Only"
msgstr "Doar Text"

#: includes/fields/class-acf-field-wysiwyg.php:433
msgid "Toolbar"
msgstr "Bară de instrumente"

#: includes/fields/class-acf-field-wysiwyg.php:443
msgid "Show Media Upload Buttons?"
msgstr "Arată Butoanele de Încărcare a fișierelor Media?"

#: includes/fields/class-acf-field-wysiwyg.php:453
msgid "Delay initialization?"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:454
msgid "TinyMCE will not be initalized until field is clicked"
msgstr ""

#: includes/forms/form-comment.php:166 includes/forms/form-post.php:303
#: pro/admin/admin-options-page.php:308
msgid "Edit field group"
msgstr "Editează Grupul de Câmpuri"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr ""

#: includes/forms/form-front.php:103
#: pro/fields/class-acf-field-gallery.php:573 pro/options-page.php:81
msgid "Update"
msgstr "Actualizează"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Articol Actualizat"

#: includes/forms/form-front.php:230
msgid "Spam Detected"
msgstr ""

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Articol"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Pagina"

#: includes/locations.php:96
msgid "Forms"
msgstr "Formulare"

#: includes/locations.php:247
msgid "is equal to"
msgstr "este egal cu"

#: includes/locations.php:248
msgid "is not equal to"
msgstr "nu este egal cu"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Atașament"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr ""

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Comentariu"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Rolul Utilizatorului Curent"

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "Super Admin"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Utilizatorul Curent"

#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr "Autentifiat"

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr "Vezi front-end"

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr "Vezi back-end"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr ""

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr ""

#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr ""

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr ""

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Pagina Părinte"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Macheta Pagini"

#: includes/locations/class-acf-location-page-template.php:98
#: includes/locations/class-acf-location-post-template.php:151
msgid "Default Template"
msgstr "Format Implicit"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Tipul Pagini"

#: includes/locations/class-acf-location-page-type.php:146
msgid "Front Page"
msgstr "Pagina principală"

#: includes/locations/class-acf-location-page-type.php:147
msgid "Posts Page"
msgstr "Pagina Articolelor"

#: includes/locations/class-acf-location-page-type.php:148
msgid "Top Level Page (no parent)"
msgstr "Pagina primului nivel (fără părinte)"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Parent Page (has children)"
msgstr "Pagina părinte (are succesori)"

#: includes/locations/class-acf-location-page-type.php:150
msgid "Child Page (has parent)"
msgstr "Pagina Succesor (are părinte)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Categoria Articolului"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Formatul Articolului"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Starea Articolui"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Taxonomia Articolului"

#: includes/locations/class-acf-location-post-template.php:27
msgid "Post Template"
msgstr ""

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Formularul Utilizatorului"

#: includes/locations/class-acf-location-user-form.php:88
msgid "Add / Edit"
msgstr "Adaugă / Editează"

#: includes/locations/class-acf-location-user-form.php:89
msgid "Register"
msgstr "Înregistrează"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Rolul Utilizatorului"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Piesă"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "%s valoarea este obligatorie"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Câmpuri Avansate Personalizate PRO"

#: pro/admin/admin-options-page.php:200
msgid "Publish"
msgstr "Publică"

#: pro/admin/admin-options-page.php:206
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Nu a fost găsit nici un grup de câmpuri personalizate. <a href=\"%s"
"\">Creează un Grup de Câmpuri Personalizat</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Eroare</b>. Conexiunea cu servărul a fost pierdută"

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Actualizări"

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Dezactivează Licența"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Activează Licența"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr ""

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Cod de activare"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Actualizează infromațiile"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Versiunea curentă"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Ultima versiune"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Sunt disponibile actualizări"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Actualizează Modulul"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr ""
"Te rog sa introduci codul de activare în câmpul de mai sus pentru a permite "
"actualizări"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Verifică din nou"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "Anunț Actualizări"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Clonează"

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr "Arată"

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:857
#, php-format
msgid "Labels will be displayed as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:860
msgid "Prefix Field Labels"
msgstr ""

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Names"
msgstr ""

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr "Câmp necunoscut"

#: pro/fields/class-acf-field-clone.php:1031
msgid "Unknown field group"
msgstr "Grup de câmpuri necunoscut"

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:174
#: pro/fields/class-acf-field-repeater.php:462
msgid "Add Row"
msgstr "Adaugă o linie nouă"

#: pro/fields/class-acf-field-flexible-content.php:34
msgid "layout"
msgstr "schemă"

#: pro/fields/class-acf-field-flexible-content.php:35
msgid "layouts"
msgstr "scheme"

#: pro/fields/class-acf-field-flexible-content.php:36
msgid "remove {layout}?"
msgstr "înlătură {layout}?"

#: pro/fields/class-acf-field-flexible-content.php:37
msgid "This field requires at least {min} {identifier}"
msgstr "Acest câmp necesită cel puțin {min} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:38
msgid "This field has a limit of {max} {identifier}"
msgstr "Acest câmp are o limită de {max} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:39
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Acest câmp necesită cel puțin {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:40
msgid "Maximum {label} limit reached ({max} {identifier})"
msgstr "Numărul maxim de {label} a fost atins ({max} {identifier})"

#: pro/fields/class-acf-field-flexible-content.php:41
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} disponibile (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:42
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} obligatoriu (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:43
msgid "Flexible Content requires at least 1 layout"
msgstr "Conținutul Flexibil necesită cel puțin 1 schemă"

#: pro/fields/class-acf-field-flexible-content.php:273
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Apasă butonul  \"%s\" de mai jos pentru a începe să îți creezi schema"

#: pro/fields/class-acf-field-flexible-content.php:406
msgid "Add layout"
msgstr "Adaugă Schema"

#: pro/fields/class-acf-field-flexible-content.php:407
msgid "Remove layout"
msgstr "Înlătură Schema"

#: pro/fields/class-acf-field-flexible-content.php:408
#: pro/fields/class-acf-field-repeater.php:298
msgid "Click to toggle"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:548
msgid "Reorder Layout"
msgstr "Reordonează Schema"

#: pro/fields/class-acf-field-flexible-content.php:548
msgid "Reorder"
msgstr "Reordonează"

#: pro/fields/class-acf-field-flexible-content.php:549
msgid "Delete Layout"
msgstr "Șterge Schema"

#: pro/fields/class-acf-field-flexible-content.php:550
msgid "Duplicate Layout"
msgstr "Copiază Schema"

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Add New Layout"
msgstr "Adaugă o Nouă Schemă"

#: pro/fields/class-acf-field-flexible-content.php:622
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:635
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:662
#: pro/fields/class-acf-field-repeater.php:458
msgid "Button Label"
msgstr "Buton Etichetă"

#: pro/fields/class-acf-field-flexible-content.php:671
msgid "Minimum Layouts"
msgstr "Scheme Minime"

#: pro/fields/class-acf-field-flexible-content.php:680
msgid "Maximum Layouts"
msgstr "Scheme Maxime"

#: pro/fields/class-acf-field-gallery.php:41
msgid "Add Image to Gallery"
msgstr "Adaugă imagini în Galerie"

#: pro/fields/class-acf-field-gallery.php:45
msgid "Maximum selection reached"
msgstr "Selecția maximă atinsă"

#: pro/fields/class-acf-field-gallery.php:321
msgid "Length"
msgstr "Lungime"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:373
msgid "Alt Text"
msgstr "Text alternativ"

#: pro/fields/class-acf-field-gallery.php:544
msgid "Add to gallery"
msgstr "Adaugă în galerie"

#: pro/fields/class-acf-field-gallery.php:548
msgid "Bulk actions"
msgstr "Acțiuni în masă"

#: pro/fields/class-acf-field-gallery.php:549
msgid "Sort by date uploaded"
msgstr "Sortează după data încărcării"

#: pro/fields/class-acf-field-gallery.php:550
msgid "Sort by date modified"
msgstr "Sortează după data modficării"

#: pro/fields/class-acf-field-gallery.php:551
msgid "Sort by title"
msgstr "Sortează după titlu"

#: pro/fields/class-acf-field-gallery.php:552
msgid "Reverse current order"
msgstr "Inversează ordinea curentă"

#: pro/fields/class-acf-field-gallery.php:570
msgid "Close"
msgstr "Închide"

#: pro/fields/class-acf-field-gallery.php:624
msgid "Minimum Selection"
msgstr "Selecție minimă"

#: pro/fields/class-acf-field-gallery.php:633
msgid "Maximum Selection"
msgstr "Selecție maximă"

#: pro/fields/class-acf-field-gallery.php:642
msgid "Insert"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:643
msgid "Specify where new attachments are added"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:647
msgid "Append to the end"
msgstr "Adaugă la sfârșit"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Prepend to the beginning"
msgstr "Adaugă la început"

#: pro/fields/class-acf-field-repeater.php:36
msgid "Minimum rows reached ({min} rows)"
msgstr "Numărul minim de linii a fost atins ({min} rows)"

#: pro/fields/class-acf-field-repeater.php:37
msgid "Maximum rows reached ({max} rows)"
msgstr "Numărul maxim de linii a fost atins ({max} rows)"

#: pro/fields/class-acf-field-repeater.php:335
msgid "Add row"
msgstr "Adaugă linie"

#: pro/fields/class-acf-field-repeater.php:336
msgid "Remove row"
msgstr "Înlătură linie"

#: pro/fields/class-acf-field-repeater.php:411
msgid "Collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:412
msgid "Select a sub field to show when row is collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:422
msgid "Minimum Rows"
msgstr "Numărul minim de Linii"

#: pro/fields/class-acf-field-repeater.php:432
msgid "Maximum Rows"
msgstr "Numărul maxim de Linii"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "Nu există nicio pagină de opțiuni"

#: pro/options-page.php:51
msgid "Options"
msgstr "Opțiuni"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Opțiunile au fost actualizate"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Pentru a activa actualizările, este nevoie să introduci licența în pagina <a "
"href=\"%s\">de actualizări</a>. Dacă nu ai o licență, verifică aici <a href="
"\"%s\">detaliile și prețul</a>."

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr ""

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr ""

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr ""

#~ msgid "No toggle fields available"
#~ msgstr "Nu sunt câmpuri de comutare disponibile"

#~ msgid "Parent fields"
#~ msgstr "Câpuri parinte"

#~ msgid "Sibling fields"
#~ msgstr "Câmpuri copil"

#~ msgid "Export Field Groups to PHP"
#~ msgstr "Exportă Grupurile de Câmpuri în PHP"

#~ msgid "Download export file"
#~ msgstr "Descarcă fișierul de export"

#~ msgid "Generate export code"
#~ msgstr "Generează codul de export"

#~ msgid "Import"
#~ msgstr "Importă"

#~ msgid "Locating"
#~ msgstr "Localizare"

#~ msgid "No embed found for the given URL."
#~ msgstr "Nu a fost găsit nici un oembed pentru URL introdus."

#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "Câmpul Tab nu va fi afișat corect când vei adauga un Câmp de tipul Tabel "
#~ "de stiluri repetitiv sau un Câmp de tipul Schemă de Conținut Flexibil"

#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr ""
#~ "Folosește  \"Tab Fields\" pentru o mai ușoară organizare și grupare a "
#~ "câmpurilor."

#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "Toate câmpurile care urmează după acest  \"tab field\" (sau până când un "
#~ "alt  \"tab field\" este definit) vor fi grupate împreună folosind "
#~ "eticheta acestui câmp ca fiind Titlul Tabului."

#~ msgid "Taxonomy Term"
#~ msgstr "Termenul Taxonomiei"

#~ msgid "See what's new in"
#~ msgstr "Vezi ce este nou în"

#~ msgid "version"
#~ msgstr "versiunea"

#~ msgid "Getting Started"
#~ msgstr "Pentru început"

#~ msgid "Field Types"
#~ msgstr "Tiurile Câmpului"

#~ msgid "Functions"
#~ msgstr "Funcții"

#~ msgid "Actions"
#~ msgstr "Acțiuni"

#~ msgid "'How to' guides"
#~ msgstr "Ghiduri 'Cum să...'"

#~ msgid "Tutorials"
#~ msgstr "Tutoriale"

#~ msgid "Created by"
#~ msgstr "Creat de"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr "<b>Suces</b>. Unealta import a adaugat %s grupuri de câmpuri: %s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Atenție</b>. Unealta import a detectat %s grupuri de câmpuri care "
#~ "exista deja și a ignorat: %s"

#~ msgid "Upgrade"
#~ msgstr "Îmbunătățire"

#~ msgid "Error"
#~ msgstr "Eroare"

#~ msgid "Drag and drop to reorder"
#~ msgstr "Trage și eliberează pentru a ordona"

#~ msgid "See what's new"
#~ msgstr "Află ce este nou"

#~ msgid "Done"
#~ msgstr "Terminare"

#~ msgid "Today"
#~ msgstr "Azi"

#~ msgid "Show a different month"
#~ msgstr "Arată o altă lună"

#~ msgid "Return format"
#~ msgstr "Fromatul rezultat"

#~ msgid "uploaded to this post"
#~ msgstr "încărcate la acest articol"

#~ msgid "File Name"
#~ msgstr "Numele fișierului"

#~ msgid "File Size"
#~ msgstr "Mărimea fișierului"

#~ msgid "No File selected"
#~ msgstr "Nu a fost selectat nici un fișier"

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr ""
#~ "Vă rugăm să rețineți că toate textele vor fi mai întâi trecute prin "
#~ "funcția wp"

#~ msgid "Select"
#~ msgstr "Selectează"

#~ msgid "Warning"
#~ msgstr "Atenție"

#~ msgid "eg. Show extra content"
#~ msgstr "ex. Arată extra conținut"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Eroare de conexiune</b>. Îmi pare rău, încearcă mai târziu"

#~ msgid "Save Options"
#~ msgstr "Salvează Opțiuni"

#~ msgid "License"
#~ msgstr "Licență"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Pentru a permite actualizări, te rog să introduci codul de activare în "
#~ "câmpul de mai jos. Dacă nu deții un cod de activare, te rog vizitează"

#~ msgid "details & pricing"
#~ msgstr "detalii & prețuri"

#~ msgid ""
#~ "To enable updates, please enter your license key on the <a href=\"%s"
#~ "\">Updates</a> page. If you don't have a licence key, please see <a href="
#~ "\"%s\">details & pricing</a>"
#~ msgstr ""
#~ "Pentru a activa  actualizările, te rog să introduci codul de activare pe "
#~ "pagina <a href=\"%s\">Actualizări</a>. Dacă nu ai un cod de activare, te "
#~ "rog sa vizitezi pagina <a href=\"%s\">detalii & prețuri</a>"

#~ msgid "Hide / Show All"
#~ msgstr "Selectează / Deselectează tot"

#~ msgid "Show Field Keys"
#~ msgstr "Arată Cheile Câmpului"

#~ msgid "Pending Review"
#~ msgstr "Așteaptă Revizuirea"

#~ msgid "Draft"
#~ msgstr "Ciornă"

#~ msgid "Future"
#~ msgstr "Viitor"

#~ msgid "Private"
#~ msgstr "Privat"

#~ msgid "Revision"
#~ msgstr "Revizie"

#~ msgid "Trash"
#~ msgstr "Coșul de gunoi"

#~ msgid "Import / Export"
#~ msgstr "Importă / Exportă"

#~ msgid "Field groups are created in order from lowest to highest"
#~ msgstr "Grupurile de câmpuri sunt create în ordine crescătoare"

#~ msgid "ACF PRO Required"
#~ msgstr "Este necesară versiunea ACF RPO"

#~ msgid ""
#~ "We have detected an issue which requires your attention: This website "
#~ "makes use of premium add-ons (%s) which are no longer compatible with ACF."
#~ msgstr ""
#~ "Am detectat o problemă care necesită atenția ta: Acest website folosește "
#~ "add-onuri premium (%s) care nu mai sunt compatibile cu ACF."

#~ msgid ""
#~ "Don't panic, you can simply roll back the plugin and continue using ACF "
#~ "as you know it!"
#~ msgstr ""
#~ "Nu te panica, poți reveni oricând la o versiune anterioară și poți folosi "
#~ "în continuare ACF așa cum știi!"

#~ msgid "Roll back to ACF v%s"
#~ msgstr "Revenire la versiunea %s a ACF"

#~ msgid "Learn why ACF PRO is required for my site"
#~ msgstr "Află de ce ACF PRO este cerut pentru site-ul tău"

#~ msgid "Update Database"
#~ msgstr "Actualizarea Bazei de Date"

#~ msgid "Data Upgrade"
#~ msgstr "Actualizare Date"

#~ msgid "Data upgraded successfully."
#~ msgstr "Actualizarea datelor a fost făcută cu succes."

#~ msgid "Data is at the latest version."
#~ msgstr "Datele sunt actualizate."

#~ msgid "1 required field below is empty"
#~ msgid_plural "%s required fields below are empty"
#~ msgstr[0] "1 câmp obligatoriu este gol"
#~ msgstr[1] "%s câmpuri obligatorii sunt goale"
#~ msgstr[2] ""

#~ msgid "Load & Save Terms to Post"
#~ msgstr "Încarcă și Salvează Termenii la Articol"

#~ msgid ""
#~ "Load value based on the post's terms and update the post's terms on save"
#~ msgstr ""
#~ "Încarcă valoarea pe baza termenilor articolului și actualizează termenii "
#~ "în momentul salvării"
