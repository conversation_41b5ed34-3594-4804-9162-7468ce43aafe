msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2019-11-01 18:50+0100\n"
"PO-Revision-Date: 2019-11-12 15:35+0100\n"
"Last-Translator: \n"
"Language-Team: <PERSON><PERSON> (<EMAIL>)\n"
"Language: ca\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.2.4\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:68
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:341 includes/admin/admin.php:58
msgid "Field Groups"
msgstr "Grups de camps"

#: acf.php:342
msgid "Field Group"
msgstr "Grup de camps"

#: acf.php:343 acf.php:375 includes/admin/admin.php:59
#: pro/fields/class-acf-field-flexible-content.php:558
msgid "Add New"
msgstr "Afegeix-ne un"

#: acf.php:344
msgid "Add New Field Group"
msgstr "Afegeix un nou grup de camps"

#: acf.php:345
msgid "Edit Field Group"
msgstr "Edita el grup de camps"

#: acf.php:346
msgid "New Field Group"
msgstr "Nou grup de camps"

#: acf.php:347
msgid "View Field Group"
msgstr "Mostra el grup de camps"

#: acf.php:348
msgid "Search Field Groups"
msgstr "Cerca grups de camps"

#: acf.php:349
msgid "No Field Groups found"
msgstr "No s’ha trobat cap grup de camps"

#: acf.php:350
msgid "No Field Groups found in Trash"
msgstr "No s’ha trobat cap grup de camps a la paperera"

#: acf.php:373 includes/admin/admin-field-group.php:220
#: includes/admin/admin-field-groups.php:530
#: pro/fields/class-acf-field-clone.php:811
msgid "Fields"
msgstr "Camps"

#: acf.php:374
msgid "Field"
msgstr "Camp"

#: acf.php:376
msgid "Add New Field"
msgstr "Afegeix un nou camp"

#: acf.php:377
msgid "Edit Field"
msgstr "Edita el camp"

#: acf.php:378 includes/admin/views/field-group-fields.php:41
msgid "New Field"
msgstr "Nou camp"

#: acf.php:379
msgid "View Field"
msgstr "Mostra el camp"

#: acf.php:380
msgid "Search Fields"
msgstr "Cerca camps"

#: acf.php:381
msgid "No Fields found"
msgstr "No s’han trobat camps"

#: acf.php:382
msgid "No Fields found in Trash"
msgstr "No s’han trobat camps a la paperera"

#: acf.php:417 includes/admin/admin-field-group.php:402
#: includes/admin/admin-field-groups.php:587
msgid "Inactive"
msgstr "Inactiu"

#: acf.php:422
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inactiu <span class=“count”>(%s)</span>"
msgstr[1] "Inactius <span class=“count”>(%s)</span>"

#: includes/acf-field-functions.php:831
#: includes/admin/admin-field-group.php:178
msgid "(no label)"
msgstr "(sense etiqueta)"

#: includes/acf-field-group-functions.php:819
#: includes/admin/admin-field-group.php:180
msgid "copy"
msgstr "copia"

#: includes/admin/admin-field-group.php:86
#: includes/admin/admin-field-group.php:87
#: includes/admin/admin-field-group.php:89
msgid "Field group updated."
msgstr "S’ha actualitzat el grup de camps."

#: includes/admin/admin-field-group.php:88
msgid "Field group deleted."
msgstr "S’ha esborrat el grup de camps."

#: includes/admin/admin-field-group.php:91
msgid "Field group published."
msgstr "S’ha publicat el grup de camps."

#: includes/admin/admin-field-group.php:92
msgid "Field group saved."
msgstr "S’ha desat el grup de camps."

#: includes/admin/admin-field-group.php:93
msgid "Field group submitted."
msgstr "S’ha tramès el grup de camps."

#: includes/admin/admin-field-group.php:94
msgid "Field group scheduled for."
msgstr "S’ha programat el grup de camps."

#: includes/admin/admin-field-group.php:95
msgid "Field group draft updated."
msgstr "S’ha desat l’esborrany del grup de camps."

#: includes/admin/admin-field-group.php:171
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "La cadena “field_” no pot ser usada al principi del nom d’un camp"

#: includes/admin/admin-field-group.php:172
msgid "This field cannot be moved until its changes have been saved"
msgstr "Aquest camp no es pot moure fins que no se n’hagin desat els canvis"

#: includes/admin/admin-field-group.php:173
msgid "Field group title is required"
msgstr "Cal un nom pel grup de cmaps"

#: includes/admin/admin-field-group.php:174
msgid "Move to trash. Are you sure?"
msgstr "Segur que ho voleu moure a la paperera?"

#: includes/admin/admin-field-group.php:175
msgid "No toggle fields available"
msgstr "No hi ha camps commutables disponibles"

#: includes/admin/admin-field-group.php:176
msgid "Move Custom Field"
msgstr "Mou el grup de camps"

#: includes/admin/admin-field-group.php:177
msgid "Checked"
msgstr "Activat"

#: includes/admin/admin-field-group.php:179
msgid "(this field)"
msgstr "(aquest camp)"

#: includes/admin/admin-field-group.php:181
#: includes/admin/views/field-group-field-conditional-logic.php:51
#: includes/admin/views/field-group-field-conditional-logic.php:151
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3649
msgid "or"
msgstr "o"

#: includes/admin/admin-field-group.php:182
msgid "Null"
msgstr "Nul"

#: includes/admin/admin-field-group.php:221
msgid "Location"
msgstr "Ubicació"

#: includes/admin/admin-field-group.php:222
#: includes/admin/tools/class-acf-admin-tool-export.php:295
msgid "Settings"
msgstr "Paràmetres"

#: includes/admin/admin-field-group.php:372
msgid "Field Keys"
msgstr "Claus dels camps"

#: includes/admin/admin-field-group.php:402
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Actiu"

#: includes/admin/admin-field-group.php:767
msgid "Move Complete."
msgstr "S’ha completat el moviment."

#: includes/admin/admin-field-group.php:768
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "El camp %s es pot trobar ara al grup de camps %s"

#: includes/admin/admin-field-group.php:769
msgid "Close Window"
msgstr "Tanca la finestra"

#: includes/admin/admin-field-group.php:810
msgid "Please select the destination for this field"
msgstr "Escolliu el destí d’aquest camp"

#: includes/admin/admin-field-group.php:817
msgid "Move Field"
msgstr "Mou el camp"

#: includes/admin/admin-field-groups.php:89
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Actiu <span class=“count”>(%s)</span>"
msgstr[1] "Actius <span class=“count”>(%s)</span>"

#: includes/admin/admin-field-groups.php:156
#, php-format
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "S’ha duplicat el grup de camps."
msgstr[1] "S’han duplicat %s grups de camps."

#: includes/admin/admin-field-groups.php:243
#, php-format
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "S’ha sincronitzat el grup de camps."
msgstr[1] "S’han sincronitzat %s grups de camps."

#: includes/admin/admin-field-groups.php:414
#: includes/admin/admin-field-groups.php:577
msgid "Sync available"
msgstr "Sincronització disponible"

#: includes/admin/admin-field-groups.php:527 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:353
msgid "Title"
msgstr "Títol"

#: includes/admin/admin-field-groups.php:528
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
#: pro/fields/class-acf-field-gallery.php:380
msgid "Description"
msgstr "Descripció"

#: includes/admin/admin-field-groups.php:529
msgid "Status"
msgstr "Estat"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:626
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Personalitza el WordPress amb camps potents, professionals i intuïtius."

#: includes/admin/admin-field-groups.php:628
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Registre de canvis"

#: includes/admin/admin-field-groups.php:633
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Mira què hi ha de nou a la <a href=“%s”>versió %s</a>."

#: includes/admin/admin-field-groups.php:636
msgid "Resources"
msgstr "Recursos"

#: includes/admin/admin-field-groups.php:638
msgid "Website"
msgstr "Lloc web"

#: includes/admin/admin-field-groups.php:639
msgid "Documentation"
msgstr "Documentació"

#: includes/admin/admin-field-groups.php:640
msgid "Support"
msgstr "Suport"

#: includes/admin/admin-field-groups.php:642
#: includes/admin/views/settings-info.php:81
msgid "Pro"
msgstr "Pro"

#: includes/admin/admin-field-groups.php:647
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Gràcies per crear amb  <a href=“%s”>ACF</a>."

#: includes/admin/admin-field-groups.php:686
msgid "Duplicate this item"
msgstr "Duplica aquest element"

#: includes/admin/admin-field-groups.php:686
#: includes/admin/admin-field-groups.php:702
#: includes/admin/views/field-group-field.php:46
#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Duplicate"
msgstr "Duplica"

#: includes/admin/admin-field-groups.php:719
#: includes/fields/class-acf-field-google-map.php:146
#: includes/fields/class-acf-field-relationship.php:593
msgid "Search"
msgstr "Cerca"

#: includes/admin/admin-field-groups.php:778
#, php-format
msgid "Select %s"
msgstr "Selecciona %s"

#: includes/admin/admin-field-groups.php:786
msgid "Synchronise field group"
msgstr "Sincronitza el grup de camps"

#: includes/admin/admin-field-groups.php:786
#: includes/admin/admin-field-groups.php:816
msgid "Sync"
msgstr "Sincronitza"

#: includes/admin/admin-field-groups.php:798
msgid "Apply"
msgstr "Aplica"

#: includes/admin/admin-field-groups.php:816
msgid "Bulk Actions"
msgstr "Accions massives"

#: includes/admin/admin-tools.php:116
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Eines"

#: includes/admin/admin-upgrade.php:47 includes/admin/admin-upgrade.php:109
#: includes/admin/admin-upgrade.php:110 includes/admin/admin-upgrade.php:173
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Actualitza la base de dades"

#: includes/admin/admin-upgrade.php:197
msgid "Review sites & upgrade"
msgstr "Revisa els llocs i actualitza"

#: includes/admin/admin.php:54 includes/admin/views/field-group-options.php:110
msgid "Custom Fields"
msgstr "Camps personalitzats"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Informació"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Novetats"

#: includes/admin/tools/class-acf-admin-tool-export.php:33
msgid "Export Field Groups"
msgstr "Exporta els grups de camps"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:342
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "Genera PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:97
#: includes/admin/tools/class-acf-admin-tool-export.php:135
msgid "No field groups selected"
msgstr "No s’han escollit grups de camps"

#: includes/admin/tools/class-acf-admin-tool-export.php:174
#, php-format
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "S’ha exportat el grup de camps."
msgstr[1] "S’ha exportat %s grups de camps."

#: includes/admin/tools/class-acf-admin-tool-export.php:241
#: includes/admin/tools/class-acf-admin-tool-export.php:269
msgid "Select Field Groups"
msgstr "Escull els grups de camps"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Escolliu els grups de camps que voleu exportar i després escolliu el mètode "
"d’exportació. Useu el botó de descàrrega per a exportar-ho a un fitxer .json "
"que després podreu importar a una altra instal·lació d’ACF. Useu el botó de "
"generació per a exportar codi PHP que podreu usar al vostre tema."

#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr "Exporta el fitxer"

#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"El següent codi es pot usar per a registrar una versió local del(s) grup(s) "
"de camps escollit(s). Un grup de camps local pot aportar diversos avantatges "
"com ara temps de càrrega més ràpids, control de versions, i opcions i camps "
"dinàmics. Simplement copieu i enganxeu el següent codi al fitxer functions."
"php del vostre tema, o incloeu-lo en un fitxer extern."

#: includes/admin/tools/class-acf-admin-tool-export.php:446
msgid "Copy to clipboard"
msgstr "Copia-ho al porta-retalls"

#: includes/admin/tools/class-acf-admin-tool-export.php:483
msgid "Copied"
msgstr "S’ha copiat"

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Importa grups de camps"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Escolliu el fitxer JSON de l’Advanced Custom Fields que voleu importar. En "
"fer clic al botó d’importació, l’ACF importarà els grups de camps."

#: includes/admin/tools/class-acf-admin-tool-import.php:52
#: includes/fields/class-acf-field-file.php:57
msgid "Select File"
msgstr "Escull el fitxer"

#: includes/admin/tools/class-acf-admin-tool-import.php:62
msgid "Import File"
msgstr "Importa el fitxer"

#: includes/admin/tools/class-acf-admin-tool-import.php:85
#: includes/fields/class-acf-field-file.php:170
msgid "No file selected"
msgstr "No s’ha escollit cap fitxer"

#: includes/admin/tools/class-acf-admin-tool-import.php:93
msgid "Error uploading file. Please try again"
msgstr "S’ha produït un error. Torneu-ho a provar"

#: includes/admin/tools/class-acf-admin-tool-import.php:98
msgid "Incorrect file type"
msgstr "Tipus de fitxer incorrecte"

#: includes/admin/tools/class-acf-admin-tool-import.php:107
msgid "Import file empty"
msgstr "El fitxer d’importació és buit"

#: includes/admin/tools/class-acf-admin-tool-import.php:138
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "S’ha importat el grup de camps"
msgstr[1] "S’han importat %s grups de camps"

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "Lògica condicional"

#: includes/admin/views/field-group-field-conditional-logic.php:51
msgid "Show this field if"
msgstr "Mostra aquest camp si"

#: includes/admin/views/field-group-field-conditional-logic.php:138
#: includes/admin/views/html-location-rule.php:86
msgid "and"
msgstr "i"

#: includes/admin/views/field-group-field-conditional-logic.php:153
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Afegeix un grup de regles"

#: includes/admin/views/field-group-field.php:38
#: pro/fields/class-acf-field-flexible-content.php:410
#: pro/fields/class-acf-field-repeater.php:299
msgid "Drag to reorder"
msgstr "Arrossegueu per a reordenar"

#: includes/admin/views/field-group-field.php:42
#: includes/admin/views/field-group-field.php:45
msgid "Edit field"
msgstr "Edita el camp"

#: includes/admin/views/field-group-field.php:45
#: includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:138
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:337
msgid "Edit"
msgstr "Edita"

#: includes/admin/views/field-group-field.php:46
msgid "Duplicate field"
msgstr "Duplica el camp"

#: includes/admin/views/field-group-field.php:47
msgid "Move field to another group"
msgstr "Mou el camp a un altre grup"

#: includes/admin/views/field-group-field.php:47
msgid "Move"
msgstr "Mou"

#: includes/admin/views/field-group-field.php:48
msgid "Delete field"
msgstr "Esborra el camp"

#: includes/admin/views/field-group-field.php:48
#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Delete"
msgstr "Esborra"

#: includes/admin/views/field-group-field.php:65
msgid "Field Label"
msgstr "Etiqueta del camp"

#: includes/admin/views/field-group-field.php:66
msgid "This is the name which will appear on the EDIT page"
msgstr "Aquest és el nom que apareixerà a la pàgina d’edició"

#: includes/admin/views/field-group-field.php:75
msgid "Field Name"
msgstr "Nom del camp"

#: includes/admin/views/field-group-field.php:76
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Una sola paraula, sense espais. S’admeten barres baixes i guions"

#: includes/admin/views/field-group-field.php:85
msgid "Field Type"
msgstr "Tipus de camp"

#: includes/admin/views/field-group-field.php:96
msgid "Instructions"
msgstr "Instruccions"

#: includes/admin/views/field-group-field.php:97
msgid "Instructions for authors. Shown when submitting data"
msgstr "Instruccions per als autors. Es mostren en omplir els formularis"

#: includes/admin/views/field-group-field.php:106
msgid "Required?"
msgstr "Obligatori?"

#: includes/admin/views/field-group-field.php:129
msgid "Wrapper Attributes"
msgstr "Atributs del contenidor"

#: includes/admin/views/field-group-field.php:135
msgid "width"
msgstr "amplada"

#: includes/admin/views/field-group-field.php:150
msgid "class"
msgstr "classe"

#: includes/admin/views/field-group-field.php:163
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:175
msgid "Close Field"
msgstr "Tanca el camp"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Ordre"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:420
#: includes/fields/class-acf-field-radio.php:311
#: includes/fields/class-acf-field-select.php:433
#: pro/fields/class-acf-field-flexible-content.php:582
msgid "Label"
msgstr "Etiqueta"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:939
#: pro/fields/class-acf-field-flexible-content.php:596
msgid "Name"
msgstr "Nom"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Clau"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Tipus"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"No hi ha camps. Feu clic al botó <strong>+ Afegeix un camp</strong> per a "
"crear el vostre primer camp."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Afegeix un camp"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Regles"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Crea un grup de regles que determinaran quines pantalles d’edició mostraran "
"aquests camps personalitzats"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Estil"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Estàndard (en una caixa meta de WP)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Fluid (sense la caixa meta)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Posició"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Alta (damunt del títol)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Normal (després del contingut)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Lateral"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Posició de les etiquetes"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:106
msgid "Top aligned"
msgstr "Al damunt"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:107
msgid "Left aligned"
msgstr "Al costat"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Posició de les instruccions"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Sota les etiquetes"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Sota els camps"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Núm. d’ordre"

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "Els grups de camps amb un valor més baix apareixeran primer"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Es mostra a la llista de grups de camps"

#: includes/admin/views/field-group-options.php:107
msgid "Permalink"
msgstr "Enllaç permanent"

#: includes/admin/views/field-group-options.php:108
msgid "Content Editor"
msgstr "Editor de contingut"

#: includes/admin/views/field-group-options.php:109
msgid "Excerpt"
msgstr "Extracte"

#: includes/admin/views/field-group-options.php:111
msgid "Discussion"
msgstr "Discussió"

#: includes/admin/views/field-group-options.php:112
msgid "Comments"
msgstr "Comentaris"

#: includes/admin/views/field-group-options.php:113
msgid "Revisions"
msgstr "Revisions"

#: includes/admin/views/field-group-options.php:114
msgid "Slug"
msgstr "Àlies"

#: includes/admin/views/field-group-options.php:115
msgid "Author"
msgstr "Autor"

#: includes/admin/views/field-group-options.php:116
msgid "Format"
msgstr "Format"

#: includes/admin/views/field-group-options.php:117
msgid "Page Attributes"
msgstr "Atributs de la pàgina"

#: includes/admin/views/field-group-options.php:118
#: includes/fields/class-acf-field-relationship.php:607
msgid "Featured Image"
msgstr "Imatge destacada"

#: includes/admin/views/field-group-options.php:119
msgid "Categories"
msgstr "Categories"

#: includes/admin/views/field-group-options.php:120
msgid "Tags"
msgstr "Etiquetes"

#: includes/admin/views/field-group-options.php:121
msgid "Send Trackbacks"
msgstr "Envia retroenllaços"

#: includes/admin/views/field-group-options.php:128
msgid "Hide on screen"
msgstr "Amaga en pantalla"

#: includes/admin/views/field-group-options.php:129
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Escolliu</b> elements a <b>amagar</b>de la pantalla d’edició."

#: includes/admin/views/field-group-options.php:129
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Si hi ha múltiples grups de camps a la pantalla d’edició, s’usaran les "
"opcions del primer grup de camps (el que tingui el menor número d’ordre)"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Els següents llocs necessiten una actualització de la base de dades. "
"Escolliu els que vulgueu actualitzar i feu clic a %s."

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:92
msgid "Upgrade Sites"
msgstr "Actualitza els llocs"

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Lloc"

#: includes/admin/views/html-admin-page-upgrade-network.php:74
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Cal actualitzar la base de dades del lloc de %s a %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:76
msgid "Site is up to date"
msgstr "El lloc està actualitzat"

#: includes/admin/views/html-admin-page-upgrade-network.php:93
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"S’ha completat l’actualització de la base de dades. <a href=\"%s\">Torna a "
"l’administració de la xarxa</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:113
msgid "Please select at least one site to upgrade."
msgstr "Escolliu almenys un lloc per a actualitzar."

#: includes/admin/views/html-admin-page-upgrade-network.php:117
#: includes/admin/views/html-notice-upgrade.php:38
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Es recomana que feu una còpia de seguretat de la base de dades abans de "
"continuar. Segur que voleu executar l’actualitzador ara?"

#: includes/admin/views/html-admin-page-upgrade-network.php:144
#: includes/admin/views/html-admin-page-upgrade.php:31
#, php-format
msgid "Upgrading data to version %s"
msgstr "S’estan actualitzant les dades a la versió %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:167
msgid "Upgrade complete."
msgstr "S’ha completat l’actualització."

#: includes/admin/views/html-admin-page-upgrade-network.php:176
#: includes/admin/views/html-admin-page-upgrade-network.php:185
#: includes/admin/views/html-admin-page-upgrade.php:78
#: includes/admin/views/html-admin-page-upgrade.php:87
msgid "Upgrade failed."
msgstr "L’actualització ha fallat."

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "S’estan llegint les tasques d’actualització…"

#: includes/admin/views/html-admin-page-upgrade.php:33
#, php-format
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"S’ha completat l’actualització de la base de dades. <a href=\"%s\">Mira què "
"hi ha de nou</a>"

#: includes/admin/views/html-admin-page-upgrade.php:116
#: includes/ajax/class-acf-ajax-upgrade.php:32
msgid "No updates available."
msgstr "No hi ha actualitzacions disponibles."

#: includes/admin/views/html-admin-tools.php:21
msgid "Back to all tools"
msgstr "Torna a totes les eines"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Mostra aquest grup de camps si"

#: includes/admin/views/html-notice-upgrade.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "Repetible"

#: includes/admin/views/html-notice-upgrade.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Contingut flexible"

#: includes/admin/views/html-notice-upgrade.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galeria"

#: includes/admin/views/html-notice-upgrade.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "Pàgina d’opcions"

#: includes/admin/views/html-notice-upgrade.php:21
msgid "Database Upgrade Required"
msgstr "Cal actualitzar la base de dades"

#: includes/admin/views/html-notice-upgrade.php:22
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Gràcies per actualitzar a %s v%s!"

#: includes/admin/views/html-notice-upgrade.php:22
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Aquesta versió inclou millores a la base de dades i necessita una "
"actualització."

#: includes/admin/views/html-notice-upgrade.php:24
#, php-format
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Comproveu que tots els complements prèmium (%s) estan actualitzats a la "
"darrera versió."

#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Complements"

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Descarrega i instal·la"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Instal·lats"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Benvingut/da a Advanced Custom Fields"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Gràcies per actualitzar! L’ACF %s és més gran i millor que mai. Esperem que "
"us agradi."

#: includes/admin/views/settings-info.php:15
msgid "A Smoother Experience"
msgstr "Una millor experiència"

#: includes/admin/views/settings-info.php:18
msgid "Improved Usability"
msgstr "Usabilitat millorada"

#: includes/admin/views/settings-info.php:19
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"En incloure la popular llibreria Select2 hem millorat tant la usabilitat com "
"la velocitat en un munt de tipus de camps, incloent objecte post, enllaç de "
"pàgina, taxonomia i selecció."

#: includes/admin/views/settings-info.php:22
msgid "Improved Design"
msgstr "Disseny millorat"

#: includes/admin/views/settings-info.php:23
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Hem actualitzat l’aspecte de molts camps perquè l’ACF llueixi més que mai! "
"Es poden veure canvis a les galeries, relacions, i al nou camp d’oEmbed!"

#: includes/admin/views/settings-info.php:26
msgid "Improved Data"
msgstr "Dades millorades"

#: includes/admin/views/settings-info.php:27
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"El redisseny de l’arquitectura de dades ha permès que els subcamps siguin "
"independents dels seus pares. Això permet arrossegar camps des de i cap a "
"camps pares!"

#: includes/admin/views/settings-info.php:35
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Adeu, complements. Hola, PRO"

#: includes/admin/views/settings-info.php:38
msgid "Introducing ACF PRO"
msgstr "Presentem l’ACF PRO"

#: includes/admin/views/settings-info.php:39
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr "Estem canviant la manera en què presentem les funcionalitats prèmium!"

#: includes/admin/views/settings-info.php:40
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Els quatre complements prèmium s’han combinat a la nova <a href=\"%s"
"\">versió PRO de l’ACF</a>. Amb llicències personals i per a desenvolupadors "
"disponibles, les funcionalitats prèmium són més assequibles i accessibles "
"que mai!"

#: includes/admin/views/settings-info.php:44
msgid "Powerful Features"
msgstr "Característiques potents"

#: includes/admin/views/settings-info.php:45
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"L’ACF PRO conté característiques potents com ara camps repetibles, "
"disposicions amb contingut flexible, un bonic camp de galeria i la "
"possibilitat de crear noves pàgines d’opcions a l’administració!"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr ""
"Més informació sobre <a href=\"%s\">les característiques de l’ACF PRO</a>."

#: includes/admin/views/settings-info.php:50
msgid "Easy Upgrading"
msgstr "Fàcil actualització"

#: includes/admin/views/settings-info.php:51
msgid ""
"Upgrading to ACF PRO is easy. Simply purchase a license online and download "
"the plugin!"
msgstr ""
"L’actualització a l’ACF PRO és senzilla. Només cal que compreu una llicència "
"en línia i descarregueu l’extensió!"

#: includes/admin/views/settings-info.php:52
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>."
msgstr ""
"També hem escrit una <a href=\"%s\">guia d’actualització</a> per a respondre "
"qualsevol pregunta, però si en teniu cap, contacteu amb el nostre equip de "
"suport al <a href=\"%s\">tauler d’ajuda</a>."

#: includes/admin/views/settings-info.php:61
msgid "New Features"
msgstr "Noves característiques"

#: includes/admin/views/settings-info.php:66
msgid "Link Field"
msgstr "Camp d'enllaç"

#: includes/admin/views/settings-info.php:67
msgid ""
"The Link field provides a simple way to select or define a link (url, title, "
"target)."
msgstr ""
"El camp d’enllaç ofereix una manera senzilla d’escollir o definir un enllaç "
"(url, títol, destí)."

#: includes/admin/views/settings-info.php:71
msgid "Group Field"
msgstr "Camp de grup"

#: includes/admin/views/settings-info.php:72
msgid "The Group field provides a simple way to create a group of fields."
msgstr "El camp de grup facilita la creació d’un grup de camps."

#: includes/admin/views/settings-info.php:76
msgid "oEmbed Field"
msgstr "Camp d’oEmbed"

#: includes/admin/views/settings-info.php:77
msgid ""
"The oEmbed field allows an easy way to embed videos, images, tweets, audio, "
"and other content."
msgstr ""
"El camp d’oEmbed permet incrustar fàcilment vídeos, imatges, tuits, àudio i "
"altres continguts."

#: includes/admin/views/settings-info.php:81
msgid "Clone Field"
msgstr "Camp de clon"

#: includes/admin/views/settings-info.php:82
msgid "The clone field allows you to select and display existing fields."
msgstr "El camp de clon permet escollir i mostrar camps existents."

#: includes/admin/views/settings-info.php:86
msgid "More AJAX"
msgstr "Més AJAX"

#: includes/admin/views/settings-info.php:87
msgid "More fields use AJAX powered search to speed up page loading."
msgstr ""
"Més camps usen una cerca que funciona amb AJAX per a accelerar la càrrega de "
"la pàgina."

#: includes/admin/views/settings-info.php:91
msgid "Local JSON"
msgstr "JSON local"

#: includes/admin/views/settings-info.php:92
msgid ""
"New auto export to JSON feature improves speed and allows for syncronisation."
msgstr ""
"La nova funció d’auto exportació a JSON millora la velocitat i permet la "
"sincronització."

#: includes/admin/views/settings-info.php:96
msgid "Easy Import / Export"
msgstr "Importació i exportació senzilla"

#: includes/admin/views/settings-info.php:97
msgid "Both import and export can easily be done through a new tools page."
msgstr ""
"Tant la importació com l’exportació es poden realitzar fàcilment des de la "
"nova pàgina d’eines."

#: includes/admin/views/settings-info.php:101
msgid "New Form Locations"
msgstr "Noves ubicacions per als formularis"

#: includes/admin/views/settings-info.php:102
msgid ""
"Fields can now be mapped to menus, menu items, comments, widgets and all "
"user forms!"
msgstr ""
"Els camps es poden assignar a menús, elements del menú, comentaris, ginys i "
"formularis d’usuari!"

#: includes/admin/views/settings-info.php:106
msgid "More Customization"
msgstr "Més personalització"

#: includes/admin/views/settings-info.php:107
msgid ""
"New PHP (and JS) actions and filters have been added to allow for more "
"customization."
msgstr ""
"S’han afegit nous filtres i accions de PHP (i JS) per a permetre més "
"personalització."

#: includes/admin/views/settings-info.php:111
msgid "Fresh UI"
msgstr "Interfície estilitzada"

#: includes/admin/views/settings-info.php:112
msgid ""
"The entire plugin has had a design refresh including new field types, "
"settings and design!"
msgstr ""
"S’ha redissenyat tota l’extensió, incloent nous tipus de camps, opcions i "
"disseny!"

#: includes/admin/views/settings-info.php:116
msgid "New Settings"
msgstr "Noves opcions"

#: includes/admin/views/settings-info.php:117
msgid ""
"Field group settings have been added for Active, Label Placement, "
"Instructions Placement and Description."
msgstr ""
"S’han afegit les següents opcions als grups de camps: actiu, posició de "
"l’etiqueta, posició de les instruccions, i descripció."

#: includes/admin/views/settings-info.php:121
msgid "Better Front End Forms"
msgstr "Millors formularis a la interfície frontal"

#: includes/admin/views/settings-info.php:122
msgid ""
"acf_form() can now create a new post on submission with lots of new settings."
msgstr ""
"acf_form() ara pot crear una nova entrada en ser enviat amb un munt de noves "
"opcions."

#: includes/admin/views/settings-info.php:126
msgid "Better Validation"
msgstr "Validació millorada"

#: includes/admin/views/settings-info.php:127
msgid "Form validation is now done via PHP + AJAX in favour of only JS."
msgstr ""
"La validació del formulari ara es fa amb PHP + AJAX en lloc de només JS."

#: includes/admin/views/settings-info.php:131
msgid "Moving Fields"
msgstr "Moure els camps"

#: includes/admin/views/settings-info.php:132
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents."
msgstr ""
"Una nova funcionalitat als grups de camps permet moure un camp entre grups i "
"pares."

#: includes/admin/views/settings-info.php:143
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Creiem que us encantaran els canvis a %s."

#: includes/api/api-helpers.php:827
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/api/api-helpers.php:828
msgid "Medium"
msgstr "Mitjana"

#: includes/api/api-helpers.php:829
msgid "Large"
msgstr "Grossa"

#: includes/api/api-helpers.php:878
msgid "Full Size"
msgstr "Mida completa"

#: includes/api/api-helpers.php:1599 includes/api/api-term.php:147
#: pro/fields/class-acf-field-clone.php:996
msgid "(no title)"
msgstr "(sense títol)"

#: includes/api/api-helpers.php:3570
#, php-format
msgid "Image width must be at least %dpx."
msgstr "L’amplada de la imatge ha de ser almenys de %dpx."

#: includes/api/api-helpers.php:3575
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "L’amplada de la imatge no pot ser superior a %dpx."

#: includes/api/api-helpers.php:3591
#, php-format
msgid "Image height must be at least %dpx."
msgstr "L’alçada de la imatge ha de ser almenys de %dpx."

#: includes/api/api-helpers.php:3596
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "L’alçada de la imatge no pot ser superior a %dpx."

#: includes/api/api-helpers.php:3614
#, php-format
msgid "File size must be at least %s."
msgstr "La mida del fitxer ha de ser almenys %s."

#: includes/api/api-helpers.php:3619
#, php-format
msgid "File size must must not exceed %s."
msgstr "La mida del fitxer no pot ser superior a %s."

#: includes/api/api-helpers.php:3653
#, php-format
msgid "File type must be %s."
msgstr "El tipus de fitxer ha de ser %s."

#: includes/assets.php:168
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Perdreu els canvis que heu fet si abandoneu aquesta pàgina"

#: includes/assets.php:171 includes/fields/class-acf-field-select.php:259
msgctxt "verb"
msgid "Select"
msgstr "Selecciona"

#: includes/assets.php:172
msgctxt "verb"
msgid "Edit"
msgstr "Edita"

#: includes/assets.php:173
msgctxt "verb"
msgid "Update"
msgstr "Actualitza"

#: includes/assets.php:174
msgid "Uploaded to this post"
msgstr "Penjat a aquesta entrada"

#: includes/assets.php:175
msgid "Expand Details"
msgstr "Expandeix els detalls"

#: includes/assets.php:176
msgid "Collapse Details"
msgstr "Amaga els detalls"

#: includes/assets.php:177
msgid "Restricted"
msgstr "Restringit"

#: includes/assets.php:178 includes/fields/class-acf-field-image.php:66
msgid "All images"
msgstr "Totes les imatges"

#: includes/assets.php:181
msgid "Validation successful"
msgstr "Validació correcta"

#: includes/assets.php:182 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "La validació ha fallat"

#: includes/assets.php:183
msgid "1 field requires attention"
msgstr "Cal revisar un camp"

#: includes/assets.php:184
#, php-format
msgid "%d fields require attention"
msgstr "Cal revisar %d camps"

#: includes/assets.php:187
msgid "Are you sure?"
msgstr "N'esteu segur?"

#: includes/assets.php:188 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Sí"

#: includes/assets.php:189 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:174
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "No"

#: includes/assets.php:190 includes/fields/class-acf-field-file.php:154
#: includes/fields/class-acf-field-image.php:140
#: includes/fields/class-acf-field-link.php:140
#: pro/fields/class-acf-field-gallery.php:338
#: pro/fields/class-acf-field-gallery.php:478
msgid "Remove"
msgstr "Suprimeix"

#: includes/assets.php:191
msgid "Cancel"
msgstr "Cancel·la"

#: includes/assets.php:194
msgid "Has any value"
msgstr "Té algun valor"

#: includes/assets.php:195
msgid "Has no value"
msgstr "No té cap valor"

#: includes/assets.php:196
msgid "Value is equal to"
msgstr "El valor és igual a"

#: includes/assets.php:197
msgid "Value is not equal to"
msgstr "El valor no és igual a"

#: includes/assets.php:198
msgid "Value matches pattern"
msgstr "El valor coincideix amb el patró"

#: includes/assets.php:199
msgid "Value contains"
msgstr "El valor conté"

#: includes/assets.php:200
msgid "Value is greater than"
msgstr "El valor és superior a"

#: includes/assets.php:201
msgid "Value is less than"
msgstr "El valor és inferior a"

#: includes/assets.php:202
msgid "Selection is greater than"
msgstr "La selecció és superior a"

#: includes/assets.php:203
msgid "Selection is less than"
msgstr "La selecció és inferior a"

#: includes/assets.php:206 includes/forms/form-comment.php:166
#: pro/admin/admin-options-page.php:325
msgid "Edit field group"
msgstr "Edita el grup de camps"

#: includes/fields.php:308
msgid "Field type does not exist"
msgstr "El tipus de camp no existeix"

#: includes/fields.php:308
msgid "Unknown"
msgstr "Desconegut"

#: includes/fields.php:349
msgid "Basic"
msgstr "Bàsic"

#: includes/fields.php:350 includes/forms/form-front.php:47
msgid "Content"
msgstr "Contingut"

#: includes/fields.php:351
msgid "Choice"
msgstr "Elecció"

#: includes/fields.php:352
msgid "Relational"
msgstr "Relacional"

#: includes/fields.php:353
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:354 includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:389
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:290
#: pro/fields/class-acf-field-clone.php:843
#: pro/fields/class-acf-field-flexible-content.php:553
#: pro/fields/class-acf-field-flexible-content.php:602
#: pro/fields/class-acf-field-repeater.php:448
msgid "Layout"
msgstr "Disposició"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Acordió"

#: includes/fields/class-acf-field-accordion.php:99
msgid "Open"
msgstr "Obert"

#: includes/fields/class-acf-field-accordion.php:100
msgid "Display this accordion as open on page load."
msgstr "Mostra aquest acordió obert en carregar la pàgina."

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr "Expansió múltiple"

#: includes/fields/class-acf-field-accordion.php:110
msgid "Allow this accordion to open without closing others."
msgstr "Permet que aquest acordió s’obri sense tancar els altres."

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr "Punt final"

#: includes/fields/class-acf-field-accordion.php:120
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Definiu un punt final per a aturar l’acordió previ. Aquest acordió no serà "
"visible."

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Grup de botons"

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:364
msgid "Choices"
msgstr "Opcions"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "Enter each choice on a new line."
msgstr "Introduïu cada opció en una línia nova."

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Per a més control, podeu establir tant el valor com l’etiqueta d’aquesta "
"manera:"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "red : Red"
msgstr "vermell : Vermell"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:382
#: includes/fields/class-acf-field-taxonomy.php:784
#: includes/fields/class-acf-field-user.php:393
msgid "Allow Null?"
msgstr "Permet nul?"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:380
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:281
#: includes/fields/class-acf-field-range.php:149
#: includes/fields/class-acf-field-select.php:373
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:381
msgid "Default Value"
msgstr "Valor per defecte"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:282
#: includes/fields/class-acf-field-range.php:150
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:382
msgid "Appears when creating a new post"
msgstr "Apareix quan es crea una nova entrada"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:396
#: includes/fields/class-acf-field-radio.php:297
msgid "Horizontal"
msgstr "Horitzontal"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:395
#: includes/fields/class-acf-field-radio.php:296
msgid "Vertical"
msgstr "Vertical"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:413
#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:304
#: includes/fields/class-acf-field-taxonomy.php:829
msgid "Return Value"
msgstr "Valor de retorn"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:305
msgid "Specify the returned value on front end"
msgstr "Especifiqueu el valor a retornar a la interfície frontal"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:419
#: includes/fields/class-acf-field-radio.php:310
#: includes/fields/class-acf-field-select.php:432
msgid "Value"
msgstr "Valor"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:421
#: includes/fields/class-acf-field-radio.php:312
#: includes/fields/class-acf-field-select.php:434
msgid "Both (Array)"
msgstr "Ambdós (matriu)"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Checkbox"
msgstr "Casella de selecció"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "Commuta’ls tots"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr "Afegeix una nova opció"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "Permet personalitzats"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr "Permet afegir-hi valors personalitzats"

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "Desa personalitzats"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr "Desa els valors personalitzats a les opcions del camp"

#: includes/fields/class-acf-field-checkbox.php:381
#: includes/fields/class-acf-field-select.php:374
msgid "Enter each default value on a new line"
msgstr "Afegiu cada valor per defecte en una línia nova"

#: includes/fields/class-acf-field-checkbox.php:403
msgid "Toggle"
msgstr "Commuta"

#: includes/fields/class-acf-field-checkbox.php:404
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Afegeix una casella extra per a commutar totes les opcions"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Selector de color"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "Esborra"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "Predeterminat"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "Escolliu un color"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Color actual"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Selector de data"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Fet"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Avui"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Següent"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Anterior"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Stm"

#: includes/fields/class-acf-field-date_picker.php:178
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "Format a mostrar"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_time_picker.php:184
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "El format que es mostrarà quan editeu una entrada"

#: includes/fields/class-acf-field-date_picker.php:187
#: includes/fields/class-acf-field-date_picker.php:218
#: includes/fields/class-acf-field-date_time_picker.php:193
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr "Personalitzat:"

#: includes/fields/class-acf-field-date_picker.php:197
msgid "Save Format"
msgstr "Format de desat"

#: includes/fields/class-acf-field-date_picker.php:198
msgid "The format used when saving a value"
msgstr "El format que s’usarà en desar el valor"

#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-image.php:204
#: includes/fields/class-acf-field-post_object.php:431
#: includes/fields/class-acf-field-relationship.php:634
#: includes/fields/class-acf-field-select.php:427
#: includes/fields/class-acf-field-time_picker.php:124
#: includes/fields/class-acf-field-user.php:412
#: pro/fields/class-acf-field-gallery.php:557
msgid "Return Format"
msgstr "Format de retorn"

#: includes/fields/class-acf-field-date_picker.php:209
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "El format que es retornarà a través de les funcions del tema"

#: includes/fields/class-acf-field-date_picker.php:227
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "La setmana comença en"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Selector de data i hora"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Escolliu l’hora"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minut"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Segon"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Mil·lisegon"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microsegon"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Fus horari"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Ara"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Fet"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Selecciona"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Correu electrònic"

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:104
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "Text de mostra"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:105
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "Apareix a dins del camp"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:188
#: includes/fields/class-acf-field-text.php:113
msgid "Prepend"
msgstr "Afegeix al principi"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:189
#: includes/fields/class-acf-field-text.php:114
msgid "Appears before the input"
msgstr "Apareix abans del camp"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:197
#: includes/fields/class-acf-field-text.php:122
msgid "Append"
msgstr "Afegeix al final"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:198
#: includes/fields/class-acf-field-text.php:123
msgid "Appears after the input"
msgstr "Apareix després del camp"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Fitxer"

#: includes/fields/class-acf-field-file.php:58
msgid "Edit File"
msgstr "Edita el fitxer"

#: includes/fields/class-acf-field-file.php:59
msgid "Update File"
msgstr "Actualitza el fitxer"

#: includes/fields/class-acf-field-file.php:141
msgid "File name"
msgstr "Nom del fitxer"

#: includes/fields/class-acf-field-file.php:145
#: includes/fields/class-acf-field-file.php:248
#: includes/fields/class-acf-field-file.php:259
#: includes/fields/class-acf-field-image.php:264
#: includes/fields/class-acf-field-image.php:293
#: pro/fields/class-acf-field-gallery.php:642
#: pro/fields/class-acf-field-gallery.php:671
msgid "File size"
msgstr "Mida del fitxer"

#: includes/fields/class-acf-field-file.php:170
msgid "Add File"
msgstr "Afegeix un fitxer"

#: includes/fields/class-acf-field-file.php:221
msgid "File Array"
msgstr "Matriu de fitxer"

#: includes/fields/class-acf-field-file.php:222
msgid "File URL"
msgstr "URL del fitxer"

#: includes/fields/class-acf-field-file.php:223
msgid "File ID"
msgstr "ID del fitxer"

#: includes/fields/class-acf-field-file.php:230
#: includes/fields/class-acf-field-image.php:229
#: pro/fields/class-acf-field-gallery.php:592
msgid "Library"
msgstr "Mediateca"

#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:230
#: pro/fields/class-acf-field-gallery.php:593
msgid "Limit the media library choice"
msgstr "Limita l’elecció d’elements de la mediateca"

#: includes/fields/class-acf-field-file.php:236
#: includes/fields/class-acf-field-image.php:235
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:72
#: includes/locations/class-acf-location-user-role.php:88
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:598
#: pro/locations/class-acf-location-block.php:79
msgid "All"
msgstr "Tots"

#: includes/fields/class-acf-field-file.php:237
#: includes/fields/class-acf-field-image.php:236
#: pro/fields/class-acf-field-gallery.php:599
msgid "Uploaded to post"
msgstr "Carregats a l’entrada"

#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-image.php:243
#: pro/fields/class-acf-field-gallery.php:621
msgid "Minimum"
msgstr "Mínim"

#: includes/fields/class-acf-field-file.php:245
#: includes/fields/class-acf-field-file.php:256
msgid "Restrict which files can be uploaded"
msgstr "Limita quins fitxers poden ser carregats"

#: includes/fields/class-acf-field-file.php:255
#: includes/fields/class-acf-field-image.php:272
#: pro/fields/class-acf-field-gallery.php:650
msgid "Maximum"
msgstr "Màxim"

#: includes/fields/class-acf-field-file.php:266
#: includes/fields/class-acf-field-image.php:301
#: pro/fields/class-acf-field-gallery.php:678
msgid "Allowed file types"
msgstr "Tipus de fitxers permesos"

#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-image.php:302
#: pro/fields/class-acf-field-gallery.php:679
msgid "Comma separated list. Leave blank for all types"
msgstr "Llista separada amb comes. Deixeu-la en blanc per a tots els tipus"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Mapa de Google"

#: includes/fields/class-acf-field-google-map.php:59
msgid "Sorry, this browser does not support geolocation"
msgstr "Aquest navegador no suporta geolocalització"

#: includes/fields/class-acf-field-google-map.php:147
msgid "Clear location"
msgstr "Neteja la ubicació"

#: includes/fields/class-acf-field-google-map.php:148
msgid "Find current location"
msgstr "Cerca la ubicació actual"

#: includes/fields/class-acf-field-google-map.php:151
msgid "Search for address..."
msgstr "Cerca l’adreça…"

#: includes/fields/class-acf-field-google-map.php:181
#: includes/fields/class-acf-field-google-map.php:192
msgid "Center"
msgstr "Centra"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:193
msgid "Center the initial map"
msgstr "Centra el mapa inicial"

#: includes/fields/class-acf-field-google-map.php:204
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:205
msgid "Set the initial zoom level"
msgstr "Estableix el valor inicial de zoom"

#: includes/fields/class-acf-field-google-map.php:214
#: includes/fields/class-acf-field-image.php:255
#: includes/fields/class-acf-field-image.php:284
#: includes/fields/class-acf-field-oembed.php:268
#: pro/fields/class-acf-field-gallery.php:633
#: pro/fields/class-acf-field-gallery.php:662
msgid "Height"
msgstr "Alçada"

#: includes/fields/class-acf-field-google-map.php:215
msgid "Customize the map height"
msgstr "Personalitzeu l’alçada del mapa"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Grup"

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:384
msgid "Sub Fields"
msgstr "Sub camps"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:844
msgid "Specify the style used to render the selected fields"
msgstr "Especifiqueu l’estil usat per a mostrar els camps escollits"

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:849
#: pro/fields/class-acf-field-flexible-content.php:613
#: pro/fields/class-acf-field-repeater.php:456
#: pro/locations/class-acf-location-block.php:27
msgid "Block"
msgstr "Bloc"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:850
#: pro/fields/class-acf-field-flexible-content.php:612
#: pro/fields/class-acf-field-repeater.php:455
msgid "Table"
msgstr "Taula"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:851
#: pro/fields/class-acf-field-flexible-content.php:614
#: pro/fields/class-acf-field-repeater.php:457
msgid "Row"
msgstr "Fila"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Imatge"

#: includes/fields/class-acf-field-image.php:63
msgid "Select Image"
msgstr "Escolliu una imatge"

#: includes/fields/class-acf-field-image.php:64
msgid "Edit Image"
msgstr "Edita imatge"

#: includes/fields/class-acf-field-image.php:65
msgid "Update Image"
msgstr "Penja imatge"

#: includes/fields/class-acf-field-image.php:156
msgid "No image selected"
msgstr "No s’ha escollit cap imatge"

#: includes/fields/class-acf-field-image.php:156
msgid "Add Image"
msgstr "Afegeix imatge"

#: includes/fields/class-acf-field-image.php:210
#: pro/fields/class-acf-field-gallery.php:563
msgid "Image Array"
msgstr "Matriu d'imatge"

#: includes/fields/class-acf-field-image.php:211
#: pro/fields/class-acf-field-gallery.php:564
msgid "Image URL"
msgstr "URL de la imatge"

#: includes/fields/class-acf-field-image.php:212
#: pro/fields/class-acf-field-gallery.php:565
msgid "Image ID"
msgstr "ID de la imatge"

#: includes/fields/class-acf-field-image.php:219
#: pro/fields/class-acf-field-gallery.php:571
msgid "Preview Size"
msgstr "Mida de la vista prèvia"

#: includes/fields/class-acf-field-image.php:244
#: includes/fields/class-acf-field-image.php:273
#: pro/fields/class-acf-field-gallery.php:622
#: pro/fields/class-acf-field-gallery.php:651
msgid "Restrict which images can be uploaded"
msgstr "Limita quines imatges es poden penjar"

#: includes/fields/class-acf-field-image.php:247
#: includes/fields/class-acf-field-image.php:276
#: includes/fields/class-acf-field-oembed.php:257
#: pro/fields/class-acf-field-gallery.php:625
#: pro/fields/class-acf-field-gallery.php:654
msgid "Width"
msgstr "Amplada"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Enllaç"

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "Escolliu l’enllaç"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr "S’obre en una finestra/pestanya nova"

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "Matriu d’enllaç"

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr "URL de l’enllaç"

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "Missatge"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "Noves línies"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "Controla com es mostren les noves línies"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "Afegeix paràgrafs automàticament"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "Afegeix &lt;br&gt; automàticament"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "Sense formatejar"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "Escapa l’HTML"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Permet que el marcat HTML es mostri com a text visible en comptes de "
"renderitzat"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Número"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:158
msgid "Minimum Value"
msgstr "Valor mínim"

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:168
msgid "Maximum Value"
msgstr "Valor màxim"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:178
msgid "Step Size"
msgstr "Mida del pas"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "El valor ha de ser un número"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "El valor ha de ser igual o superior a %d"

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "El valor ha de ser igual o inferior a %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:216
msgid "Enter URL"
msgstr "Introduïu la URL"

#: includes/fields/class-acf-field-oembed.php:254
#: includes/fields/class-acf-field-oembed.php:265
msgid "Embed Size"
msgstr "Mida de la incrustació"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Enllaç de pàgina"

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "Arxius"

#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:267
#: includes/fields/class-acf-field-taxonomy.php:961
msgid "Parent"
msgstr "Pare"

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:383
#: includes/fields/class-acf-field-relationship.php:560
msgid "Filter by Post Type"
msgstr "Filtra per tipus de contingut"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:391
#: includes/fields/class-acf-field-relationship.php:568
msgid "All post types"
msgstr "Tots els tipus de contingut"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:397
#: includes/fields/class-acf-field-relationship.php:574
msgid "Filter by Taxonomy"
msgstr "Filtra per taxonomia"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:405
#: includes/fields/class-acf-field-relationship.php:582
msgid "All taxonomies"
msgstr "Totes les taxonomies"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr "Permet les URLs dels arxius"

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-select.php:392
#: includes/fields/class-acf-field-user.php:403
msgid "Select multiple values?"
msgstr "Escollir múltiples valors?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Contrasenya"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:436
#: includes/fields/class-acf-field-relationship.php:639
msgid "Post Object"
msgstr "Objecte de l’entrada"

#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:640
msgid "Post ID"
msgstr "ID de l’entrada"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Botó d’opció"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Altres"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr "Afegeix l’opció ‘Altres’ per a permetre valors personalitzats"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "Desa Altres"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "Desa els valors d’’Altres’ a les opcions del camp"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Rang"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Relació"

#: includes/fields/class-acf-field-relationship.php:62
msgid "Maximum values reached ( {max} values )"
msgstr "S’ha arribat al màxim de valors ({max} valors)"

#: includes/fields/class-acf-field-relationship.php:63
msgid "Loading"
msgstr "S'està carregant"

#: includes/fields/class-acf-field-relationship.php:64
msgid "No matches found"
msgstr "No hi ha coincidències"

#: includes/fields/class-acf-field-relationship.php:411
msgid "Select post type"
msgstr "Escolliu el tipus de contingut"

#: includes/fields/class-acf-field-relationship.php:420
msgid "Select taxonomy"
msgstr "Escolliu la taxonomia"

#: includes/fields/class-acf-field-relationship.php:477
msgid "Search..."
msgstr "Cerca…"

#: includes/fields/class-acf-field-relationship.php:588
msgid "Filters"
msgstr "Filtres"

#: includes/fields/class-acf-field-relationship.php:594
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Tipus de contingut"

#: includes/fields/class-acf-field-relationship.php:595
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:754
#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy"
msgstr "Taxonomia"

#: includes/fields/class-acf-field-relationship.php:602
msgid "Elements"
msgstr "Elements"

#: includes/fields/class-acf-field-relationship.php:603
msgid "Selected elements will be displayed in each result"
msgstr "Els elements escollits es mostraran a cada resultat"

#: includes/fields/class-acf-field-relationship.php:614
msgid "Minimum posts"
msgstr "Mínim d'entrades"

#: includes/fields/class-acf-field-relationship.php:623
msgid "Maximum posts"
msgstr "Màxim d’entrades"

#: includes/fields/class-acf-field-relationship.php:727
#: pro/fields/class-acf-field-gallery.php:779
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s necessita almenys %s selecció"
msgstr[1] "%s necessita almenys %s seleccions"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:776
msgctxt "noun"
msgid "Select"
msgstr "Selecció"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Hi ha disponible un resultat, premeu retorn per a escollir-lo."

#: includes/fields/class-acf-field-select.php:112
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"Hi ha disponibles %d resultats, useu les fletxes amunt i avall per a navegar-"
"hi."

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "No hi ha coincidències"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Introduïu un o més caràcters"

#: includes/fields/class-acf-field-select.php:115
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Introduïu %d o més caràcters"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Esborreu un caràcter"

#: includes/fields/class-acf-field-select.php:117
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Esborreu %d caràcters"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Només podeu escollir un element"

#: includes/fields/class-acf-field-select.php:119
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Només podeu escollir %d elements"

#: includes/fields/class-acf-field-select.php:120
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "S'estan carregant més resultats&hellip;"

#: includes/fields/class-acf-field-select.php:121
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "S'està cercant&hellip;"

#: includes/fields/class-acf-field-select.php:122
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "No s'ha pogut carregar"

#: includes/fields/class-acf-field-select.php:402
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "Interfície estilitzada"

#: includes/fields/class-acf-field-select.php:412
msgid "Use AJAX to lazy load choices?"
msgstr "Usa AJAX per a carregar opcions de manera relaxada?"

#: includes/fields/class-acf-field-select.php:428
msgid "Specify the value returned"
msgstr "Especifiqueu el valor a retornar"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Separador"

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "Pestanya"

#: includes/fields/class-acf-field-tab.php:102
msgid "Placement"
msgstr "Ubicació"

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""
"Definiu un punt de final per a aturar les pestanyes anteriors. Això generarà "
"un nou grup de pestanyes."

#: includes/fields/class-acf-field-taxonomy.php:714
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "No hi ha %s"

#: includes/fields/class-acf-field-taxonomy.php:755
msgid "Select the taxonomy to be displayed"
msgstr "Escolliu la taxonomia a mostrar"

#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Appearance"
msgstr "Aparença"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Select the appearance of this field"
msgstr "Escolliu l’aparença d’aquest camp"

#: includes/fields/class-acf-field-taxonomy.php:770
msgid "Multiple Values"
msgstr "Múltiples valors"

#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Multi Select"
msgstr "Selecció múltiple"

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Single Value"
msgstr "Un sol valor"

#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Radio Buttons"
msgstr "Botons d’opció"

#: includes/fields/class-acf-field-taxonomy.php:799
msgid "Create Terms"
msgstr "Crea els termes"

#: includes/fields/class-acf-field-taxonomy.php:800
msgid "Allow new terms to be created whilst editing"
msgstr "Permet crear nous termes mentre s’està editant"

#: includes/fields/class-acf-field-taxonomy.php:809
msgid "Save Terms"
msgstr "Desa els termes"

#: includes/fields/class-acf-field-taxonomy.php:810
msgid "Connect selected terms to the post"
msgstr "Connecta els termes escollits a l’entrada"

#: includes/fields/class-acf-field-taxonomy.php:819
msgid "Load Terms"
msgstr "Carrega els termes"

#: includes/fields/class-acf-field-taxonomy.php:820
msgid "Load value from posts terms"
msgstr "Carrega el valor dels termes de l’entrada"

#: includes/fields/class-acf-field-taxonomy.php:834
msgid "Term Object"
msgstr "Objecte de terme"

#: includes/fields/class-acf-field-taxonomy.php:835
msgid "Term ID"
msgstr "ID de terme"

#: includes/fields/class-acf-field-taxonomy.php:885
#, php-format
msgid "User unable to add new %s"
msgstr "L’usuari no pot crear nous %s"

#: includes/fields/class-acf-field-taxonomy.php:895
#, php-format
msgid "%s already exists"
msgstr "%s ja existeix"

#: includes/fields/class-acf-field-taxonomy.php:927
#, php-format
msgid "%s added"
msgstr "%s afegit"

#: includes/fields/class-acf-field-taxonomy.php:973
#: includes/locations/class-acf-location-user-form.php:73
msgid "Add"
msgstr "Afegeix"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-text.php:131
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Límit de caràcters"

#: includes/fields/class-acf-field-text.php:132
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Deixeu-lo en blanc per no establir cap límit"

#: includes/fields/class-acf-field-text.php:157
#: includes/fields/class-acf-field-textarea.php:215
#, php-format
msgid "Value must not exceed %d characters"
msgstr "El valor no pot superar els %d caràcters"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Àrea de text"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "Files"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "Estableix l’alçada de l’àrea de text"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Selector d'hora"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Cert / Fals"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr "Mostra el text al costat de la casella de selecció"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Text d’actiu"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "El text que es mostrarà quan està actiu"

#: includes/fields/class-acf-field-true_false.php:170
msgid "Off Text"
msgstr "Text d’inactiu"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when inactive"
msgstr "El text que es mostrarà quan està inactiu"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "URL"

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "El valor ha de ser una URL vàlida"

#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr "Usuari"

#: includes/fields/class-acf-field-user.php:378
msgid "Filter by role"
msgstr "Filtra per rol"

#: includes/fields/class-acf-field-user.php:386
msgid "All user roles"
msgstr "Tots els rols d'usuari"

#: includes/fields/class-acf-field-user.php:417
msgid "User Array"
msgstr "Matriu d’usuari"

#: includes/fields/class-acf-field-user.php:418
msgid "User Object"
msgstr "Objecte d'usuari"

#: includes/fields/class-acf-field-user.php:419
msgid "User ID"
msgstr "ID d'usuari"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Editor Wysiwyg"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual"
msgstr "Visual"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-wysiwyg.php:337
msgid "Click to initialize TinyMCE"
msgstr "Feu clic per a inicialitzar el TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:390
msgid "Tabs"
msgstr "Pestanyes"

#: includes/fields/class-acf-field-wysiwyg.php:395
msgid "Visual & Text"
msgstr "Visual i Text"

#: includes/fields/class-acf-field-wysiwyg.php:396
msgid "Visual Only"
msgstr "Només Visual"

#: includes/fields/class-acf-field-wysiwyg.php:397
msgid "Text Only"
msgstr "Només Text"

#: includes/fields/class-acf-field-wysiwyg.php:404
msgid "Toolbar"
msgstr "Barra d'eines"

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Show Media Upload Buttons?"
msgstr "Mostra els botons de penjar mèdia?"

#: includes/fields/class-acf-field-wysiwyg.php:429
msgid "Delay initialization?"
msgstr "Endarrereix la inicialització?"

#: includes/fields/class-acf-field-wysiwyg.php:430
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "El TinyMCE no s’inicialitzarà fins que no es faci clic al camp"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Valida el correu"

#: includes/forms/form-front.php:104 pro/fields/class-acf-field-gallery.php:510
#: pro/options-page.php:81
msgid "Update"
msgstr "Actualitza"

#: includes/forms/form-front.php:105
msgid "Post updated"
msgstr "S'ha actualitzat l'entrada"

#: includes/forms/form-front.php:231
msgid "Spam Detected"
msgstr "S’ha detectat brossa"

#: includes/forms/form-user.php:336
#, php-format
msgid "<strong>ERROR</strong>: %s"
msgstr "<strong>ERROR</strong>: %s"

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Entrada"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Pàgina"

#: includes/locations.php:96
msgid "Forms"
msgstr "Formularis"

#: includes/locations.php:243
msgid "is equal to"
msgstr "és igual a"

#: includes/locations.php:244
msgid "is not equal to"
msgstr "no és igual a"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Adjunt"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr "Tots els formats de %s"

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Comentari"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Rol de l’usuari actual"

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "Superadministrador"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Usuari actual"

#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr "Amb la sessió iniciada"

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr "Veient la part frontal"

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr "Veient l’administració"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "Element del menú"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "Menú"

#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr "Ubicacions dels menús"

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr "Menús"

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Pàgina mare"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Plantilla de la pàgina"

#: includes/locations/class-acf-location-page-template.php:87
#: includes/locations/class-acf-location-post-template.php:134
msgid "Default Template"
msgstr "Plantilla per defecte"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Tipus de pàgina"

#: includes/locations/class-acf-location-page-type.php:146
msgid "Front Page"
msgstr "Portada"

#: includes/locations/class-acf-location-page-type.php:147
msgid "Posts Page"
msgstr "Pàgina de les entrades"

#: includes/locations/class-acf-location-page-type.php:148
msgid "Top Level Page (no parent)"
msgstr "Pàgina de primer nivell (no té mare)"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Parent Page (has children)"
msgstr "Pàgina mare (té filles)"

#: includes/locations/class-acf-location-page-type.php:150
msgid "Child Page (has parent)"
msgstr "Pàgina filla (té mare)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Categoria de l'entrada"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Format de l’entrada"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Estat de l'entrada"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Taxonomia de l’entrada"

#: includes/locations/class-acf-location-post-template.php:27
msgid "Post Template"
msgstr "Plantilla de l’entrada"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Formulari d’usuari"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Add / Edit"
msgstr "Afegeix / Edita"

#: includes/locations/class-acf-location-user-form.php:75
msgid "Register"
msgstr "Registra"

#: includes/locations/class-acf-location-user-role.php:22
msgid "User Role"
msgstr "Rol de l'usuari"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Giny"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "Cal introduir un valor a %s"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:198
msgid "Publish"
msgstr "Publica"

#: pro/admin/admin-options-page.php:204
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"No s’han trobat grups de camps personalitzats per a aquesta pàgina "
"d’opcions. <a href=\"%s\">Creeu un grup de camps nou</a>"

#: pro/admin/admin-updates.php:49
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Error</b>. No s’ha pogut connectar al servidor d’actualitzacions"

#: pro/admin/admin-updates.php:118 pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Actualitzacions"

#: pro/admin/admin-updates.php:191
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Error</b>. No s’ha pogut verificar el paquet d’actualització. Torneu-ho a "
"intentar o desactiveu i torneu a activar la vostra llicència de l’ACF PRO."

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Desactiva la llicència"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Activa la llicència"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Informació de la llicència"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Per a desbloquejar les actualitzacions, introduïu la clau de llicència a "
"continuació. Si no teniu cap clau de llicència, vegeu els <a href=\"%s"
"\">detalls i preu</a>."

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Clau de llicència"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Informació de l'actualització"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Versió actual"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Darrera versió"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Actualització disponible"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Actualitza l’extensió"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr ""
"Introduïu la clau de llicència al damunt per a desbloquejar les "
"actualitzacions"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Torneu-ho a comprovar"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "Avís d’actualització"

#: pro/blocks.php:371
msgid "Switch to Edit"
msgstr "Canvia a edició"

#: pro/blocks.php:372
msgid "Switch to Preview"
msgstr "Canvia a previsualització"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Clon"

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr "Escolliu un o més camps a clonar"

#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr "Mostra"

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr "Indiqueu l’estil que s’usarà per a mostrar el camp clonat"

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr "Grup (mostra els camps escollits en un grup dins d’aquest camp)"

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr "Fluid (reemplaça aquest camp amb els camps escollits)"

#: pro/fields/class-acf-field-clone.php:857
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Les etiquetes es mostraran com a %s"

#: pro/fields/class-acf-field-clone.php:860
msgid "Prefix Field Labels"
msgstr "Prefixa les etiquetes dels camps"

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr "Els valors es desaran com a %s"

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Names"
msgstr "Prefixa els noms dels camps"

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr "Camp desconegut"

#: pro/fields/class-acf-field-clone.php:1031
msgid "Unknown field group"
msgstr "Grup de camps desconegut"

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr "Tots els camps del grup de camps %s"

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:193
#: pro/fields/class-acf-field-repeater.php:468
msgid "Add Row"
msgstr "Afegeix una fila"

#: pro/fields/class-acf-field-flexible-content.php:73
#: pro/fields/class-acf-field-flexible-content.php:924
#: pro/fields/class-acf-field-flexible-content.php:1006
msgid "layout"
msgid_plural "layouts"
msgstr[0] "disposició"
msgstr[1] "disposicions"

#: pro/fields/class-acf-field-flexible-content.php:74
msgid "layouts"
msgstr "disposicions"

#: pro/fields/class-acf-field-flexible-content.php:77
#: pro/fields/class-acf-field-flexible-content.php:923
#: pro/fields/class-acf-field-flexible-content.php:1005
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Aquest camp requereix almenys {min} {label} de {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:78
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Aquest camp té un límit de {max} {label} de {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:81
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} de {identifier} disponible (màx {max})"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} de {identifier} necessari (mín {min})"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "Flexible Content requires at least 1 layout"
msgstr "El contingut flexible necessita almenys una disposició"

#: pro/fields/class-acf-field-flexible-content.php:287
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Feu clic al botó “%s” de sota per a començar a crear el vostre disseny"

#: pro/fields/class-acf-field-flexible-content.php:413
msgid "Add layout"
msgstr "Afegeix una disposició"

#: pro/fields/class-acf-field-flexible-content.php:414
msgid "Remove layout"
msgstr "Esborra la disposició"

#: pro/fields/class-acf-field-flexible-content.php:415
#: pro/fields/class-acf-field-repeater.php:301
msgid "Click to toggle"
msgstr "Feu clic per alternar"

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Reorder Layout"
msgstr "Reordena la disposició"

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Reorder"
msgstr "Reordena"

#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Delete Layout"
msgstr "Esborra la disposició"

#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Duplicate Layout"
msgstr "Duplica la disposició"

#: pro/fields/class-acf-field-flexible-content.php:558
msgid "Add New Layout"
msgstr "Afegeix una disposició"

#: pro/fields/class-acf-field-flexible-content.php:629
msgid "Min"
msgstr "Mín"

#: pro/fields/class-acf-field-flexible-content.php:642
msgid "Max"
msgstr "Màx"

#: pro/fields/class-acf-field-flexible-content.php:669
#: pro/fields/class-acf-field-repeater.php:464
msgid "Button Label"
msgstr "Etiqueta del botó"

#: pro/fields/class-acf-field-flexible-content.php:678
msgid "Minimum Layouts"
msgstr "Mínim de disposicions"

#: pro/fields/class-acf-field-flexible-content.php:687
msgid "Maximum Layouts"
msgstr "Màxim de disposicions"

#: pro/fields/class-acf-field-gallery.php:73
msgid "Add Image to Gallery"
msgstr "Afegeix una imatge a la galeria"

#: pro/fields/class-acf-field-gallery.php:74
msgid "Maximum selection reached"
msgstr "S’ha arribat al màxim d’elements seleccionats"

#: pro/fields/class-acf-field-gallery.php:322
msgid "Length"
msgstr "Llargada"

#: pro/fields/class-acf-field-gallery.php:362
msgid "Caption"
msgstr "Llegenda"

#: pro/fields/class-acf-field-gallery.php:371
msgid "Alt Text"
msgstr "Text alternatiu"

#: pro/fields/class-acf-field-gallery.php:487
msgid "Add to gallery"
msgstr "Afegeix a la galeria"

#: pro/fields/class-acf-field-gallery.php:491
msgid "Bulk actions"
msgstr "Accions massives"

#: pro/fields/class-acf-field-gallery.php:492
msgid "Sort by date uploaded"
msgstr "Ordena per la data de càrrega"

#: pro/fields/class-acf-field-gallery.php:493
msgid "Sort by date modified"
msgstr "Ordena per la data de modificació"

#: pro/fields/class-acf-field-gallery.php:494
msgid "Sort by title"
msgstr "Ordena pel títol"

#: pro/fields/class-acf-field-gallery.php:495
msgid "Reverse current order"
msgstr "Inverteix l’ordre actual"

#: pro/fields/class-acf-field-gallery.php:507
msgid "Close"
msgstr "Tanca"

#: pro/fields/class-acf-field-gallery.php:580
msgid "Insert"
msgstr "Insereix"

#: pro/fields/class-acf-field-gallery.php:581
msgid "Specify where new attachments are added"
msgstr "Especifiqueu on s’afegeixen els nous fitxers adjunts"

#: pro/fields/class-acf-field-gallery.php:585
msgid "Append to the end"
msgstr "Afegeix-los al final"

#: pro/fields/class-acf-field-gallery.php:586
msgid "Prepend to the beginning"
msgstr "Afegeix-los al principi"

#: pro/fields/class-acf-field-gallery.php:605
msgid "Minimum Selection"
msgstr "Selecció mínima"

#: pro/fields/class-acf-field-gallery.php:613
msgid "Maximum Selection"
msgstr "Selecció màxima"

#: pro/fields/class-acf-field-repeater.php:65
#: pro/fields/class-acf-field-repeater.php:661
msgid "Minimum rows reached ({min} rows)"
msgstr "No s’ha arribat al mínim de files ({min} files)"

#: pro/fields/class-acf-field-repeater.php:66
msgid "Maximum rows reached ({max} rows)"
msgstr "S’ha superat el màxim de files ({max} files)"

#: pro/fields/class-acf-field-repeater.php:338
msgid "Add row"
msgstr "Afegeix una fila"

#: pro/fields/class-acf-field-repeater.php:339
msgid "Remove row"
msgstr "Esborra la fila"

#: pro/fields/class-acf-field-repeater.php:417
msgid "Collapsed"
msgstr "Replegat"

#: pro/fields/class-acf-field-repeater.php:418
msgid "Select a sub field to show when row is collapsed"
msgstr "Escull un subcamp per a mostrar quan la fila estigui replegada"

#: pro/fields/class-acf-field-repeater.php:428
msgid "Minimum Rows"
msgstr "Mínim de files"

#: pro/fields/class-acf-field-repeater.php:438
msgid "Maximum Rows"
msgstr "Màxim de files"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "No hi ha pàgines d’opcions"

#: pro/options-page.php:51
msgid "Options"
msgstr "Opcions"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "S’han actualitzat les opcions"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Per a activar les actualitzacions, introduïu la clau de llicència a la "
"pàgina d’<a href=\"%s\">Actualitzacions</a>. Si no teniu cap clau de "
"llicència, vegeu-ne els<a href=\"%s\">detalls i preu</a>."

#: tests/basic/test-blocks.php:30
msgid "Normal"
msgstr "Normal"

#: tests/basic/test-blocks.php:31
msgid "Fancy"
msgstr "Sofisticat"

#. Plugin URI of the plugin/theme
#. Author URI of the plugin/theme
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"
