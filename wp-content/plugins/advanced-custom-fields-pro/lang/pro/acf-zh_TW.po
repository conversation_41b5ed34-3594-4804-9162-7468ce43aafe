msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.8.7\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2020-02-13 17:09+0800\n"
"PO-Revision-Date: 2020-03-10 13:40+0800\n"
"Last-Translator: <PERSON> Lu <<EMAIL>>\n"
"Language-Team: Audi Lu <<EMAIL>>\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.1.1\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:68
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:340 includes/admin/admin.php:52
msgid "Field Groups"
msgstr "欄位群組"

#: acf.php:341
msgid "Field Group"
msgstr "欄位群組"

#: acf.php:342 acf.php:374 includes/admin/admin.php:53
#: pro/fields/class-acf-field-flexible-content.php:558
msgid "Add New"
msgstr "新建"

#: acf.php:343
msgid "Add New Field Group"
msgstr "新增欄位群組"

#: acf.php:344
msgid "Edit Field Group"
msgstr "編輯欄位群組"

#: acf.php:345
msgid "New Field Group"
msgstr "新增欄位群組"

#: acf.php:346
msgid "View Field Group"
msgstr "檢視欄位群組"

#: acf.php:347
msgid "Search Field Groups"
msgstr "搜尋欄位群組"

#: acf.php:348
msgid "No Field Groups found"
msgstr "沒有找到欄位群組"

#: acf.php:349
msgid "No Field Groups found in Trash"
msgstr "回收桶裡沒有找到欄位群組"

#: acf.php:372 includes/admin/admin-field-group.php:220
#: includes/admin/admin-field-groups.php:530
#: pro/fields/class-acf-field-clone.php:811
msgid "Fields"
msgstr "欄位"

#: acf.php:373
msgid "Field"
msgstr "欄位"

#: acf.php:375
msgid "Add New Field"
msgstr "新增欄位"

#: acf.php:376
msgid "Edit Field"
msgstr "編輯欄位"

#: acf.php:377 includes/admin/views/field-group-fields.php:41
msgid "New Field"
msgstr "新欄位"

#: acf.php:378
msgid "View Field"
msgstr "檢視欄位"

#: acf.php:379
msgid "Search Fields"
msgstr "搜尋欄位"

#: acf.php:380
msgid "No Fields found"
msgstr "沒有找到欄位"

#: acf.php:381
msgid "No Fields found in Trash"
msgstr "回收桶中沒有找到欄位群組"

#: acf.php:416 includes/admin/admin-field-group.php:402
#: includes/admin/admin-field-groups.php:587
msgid "Inactive"
msgstr "未啟用"

#: acf.php:421
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "未啟用 <span class=\"count\">(%s)</span>"

#: includes/acf-field-functions.php:831
#: includes/admin/admin-field-group.php:178
msgid "(no label)"
msgstr "(無標籤)"

#: includes/acf-field-group-functions.php:819
#: includes/admin/admin-field-group.php:180
msgid "copy"
msgstr "複製"

#: includes/admin/admin-field-group.php:86
#: includes/admin/admin-field-group.php:87
#: includes/admin/admin-field-group.php:89
msgid "Field group updated."
msgstr "欄位群組已更新。"

#: includes/admin/admin-field-group.php:88
msgid "Field group deleted."
msgstr "欄位群組已刪除。"

#: includes/admin/admin-field-group.php:91
msgid "Field group published."
msgstr "欄位群組已發佈。"

#: includes/admin/admin-field-group.php:92
msgid "Field group saved."
msgstr "設定已儲存。"

#: includes/admin/admin-field-group.php:93
msgid "Field group submitted."
msgstr "欄位群組已提交。"

#: includes/admin/admin-field-group.php:94
msgid "Field group scheduled for."
msgstr "欄位群組已排程。"

#: includes/admin/admin-field-group.php:95
msgid "Field group draft updated."
msgstr "欄位群組草稿已更新。"

#: includes/admin/admin-field-group.php:171
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "\"field_\" 這個字串不能用在欄位名稱的開頭"

#: includes/admin/admin-field-group.php:172
msgid "This field cannot be moved until its changes have been saved"
msgstr "在儲存變更之前，欄位無法搬移"

#: includes/admin/admin-field-group.php:173
msgid "Field group title is required"
msgstr "欄位群組的標題為必填"

#: includes/admin/admin-field-group.php:174
msgid "Move to trash. Are you sure?"
msgstr "選取項目會移至回收桶，確定要繼續進行嗎？"

#: includes/admin/admin-field-group.php:175
msgid "No toggle fields available"
msgstr "沒有可用的條件欄位"

#: includes/admin/admin-field-group.php:176
msgid "Move Custom Field"
msgstr "移動自訂欄位"

#: includes/admin/admin-field-group.php:177
msgid "Checked"
msgstr "已選"

#: includes/admin/admin-field-group.php:179
msgid "(this field)"
msgstr "(此欄位)"

#: includes/admin/admin-field-group.php:181
#: includes/admin/views/field-group-field-conditional-logic.php:51
#: includes/admin/views/field-group-field-conditional-logic.php:151
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3649
msgid "or"
msgstr "或"

#: includes/admin/admin-field-group.php:182
msgid "Null"
msgstr "空"

#: includes/admin/admin-field-group.php:221
msgid "Location"
msgstr "位置"

#: includes/admin/admin-field-group.php:222
#: includes/admin/tools/class-acf-admin-tool-export.php:295
msgid "Settings"
msgstr "設定"

#: includes/admin/admin-field-group.php:372
msgid "Field Keys"
msgstr "欄位鍵值"

#: includes/admin/admin-field-group.php:402
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "啟用"

#: includes/admin/admin-field-group.php:767
msgid "Move Complete."
msgstr "完成搬移。"

#: includes/admin/admin-field-group.php:768
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "%s 欄位現在可以在 %s 欄位群組中找到"

#: includes/admin/admin-field-group.php:769
msgid "Close Window"
msgstr "關閉視窗"

#: includes/admin/admin-field-group.php:810
msgid "Please select the destination for this field"
msgstr "請選取這個欄位的目標欄位群組"

#: includes/admin/admin-field-group.php:817
msgid "Move Field"
msgstr "移動欄位"

#: includes/admin/admin-field-groups.php:89
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "啟用 <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:156
#, php-format
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s 欄位群組重複。"

#: includes/admin/admin-field-groups.php:243
#, php-format
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s 欄位群組已同步。"

#: includes/admin/admin-field-groups.php:414
#: includes/admin/admin-field-groups.php:577
msgid "Sync available"
msgstr "可同步"

#: includes/admin/admin-field-groups.php:527 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:353
msgid "Title"
msgstr "標題"

#: includes/admin/admin-field-groups.php:528
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
#: pro/fields/class-acf-field-gallery.php:380
msgid "Description"
msgstr "描述"

#: includes/admin/admin-field-groups.php:529
msgid "Status"
msgstr "狀態"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:626
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "使用專業直覺且功能強大的欄位來客製 WordPress。"

#: includes/admin/admin-field-groups.php:628 includes/admin/admin.php:123
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "更新日誌"

#: includes/admin/admin-field-groups.php:633
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "了解 <a href=\"%s\"> %s 版本</a>新增的功能。"

#: includes/admin/admin-field-groups.php:636
msgid "Resources"
msgstr "資源"

#: includes/admin/admin-field-groups.php:638
msgid "Website"
msgstr "網站"

#: includes/admin/admin-field-groups.php:639
msgid "Documentation"
msgstr "文件"

#: includes/admin/admin-field-groups.php:640
msgid "Support"
msgstr "支援"

#: includes/admin/admin-field-groups.php:642
#: includes/admin/views/settings-info.php:81
msgid "Pro"
msgstr "Pro"

#: includes/admin/admin-field-groups.php:647
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "感謝您使用 <a href=\"%s\">ACF</a>。"

#: includes/admin/admin-field-groups.php:686
msgid "Duplicate this item"
msgstr "複製此項目"

#: includes/admin/admin-field-groups.php:686
#: includes/admin/admin-field-groups.php:702
#: includes/admin/views/field-group-field.php:46
#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Duplicate"
msgstr "複製"

#: includes/admin/admin-field-groups.php:719
#: includes/fields/class-acf-field-google-map.php:146
#: includes/fields/class-acf-field-relationship.php:593
msgid "Search"
msgstr "搜尋"

#: includes/admin/admin-field-groups.php:778
#, php-format
msgid "Select %s"
msgstr "選擇 %s"

#: includes/admin/admin-field-groups.php:786
msgid "Synchronise field group"
msgstr "同步欄位群組"

#: includes/admin/admin-field-groups.php:786
#: includes/admin/admin-field-groups.php:816
msgid "Sync"
msgstr "同步"

#: includes/admin/admin-field-groups.php:798
msgid "Apply"
msgstr "套用"

#: includes/admin/admin-field-groups.php:816
msgid "Bulk Actions"
msgstr "批次動作"

#: includes/admin/admin-tools.php:116
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "工具"

#: includes/admin/admin-upgrade.php:47 includes/admin/admin-upgrade.php:109
#: includes/admin/admin-upgrade.php:110 includes/admin/admin-upgrade.php:173
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "升級資料庫"

#: includes/admin/admin-upgrade.php:197
msgid "Review sites & upgrade"
msgstr "檢查網站和升級"

#: includes/admin/admin.php:51 includes/admin/views/field-group-options.php:110
msgid "Custom Fields"
msgstr "自訂欄位"

#: includes/admin/admin.php:57
msgid "Info"
msgstr "資訊"

#: includes/admin/admin.php:122
msgid "What's New"
msgstr "最新消息"

#: includes/admin/tools/class-acf-admin-tool-export.php:33
msgid "Export Field Groups"
msgstr "匯出欄位群組"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:342
#: includes/admin/tools/class-acf-admin-tool-export.php:371
#| msgid "Create PHP"
msgid "Generate PHP"
msgstr "產出 PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:97
#: includes/admin/tools/class-acf-admin-tool-export.php:135
msgid "No field groups selected"
msgstr "尚未選擇欄位群組"

#: includes/admin/tools/class-acf-admin-tool-export.php:174
#, php-format
#| msgid "Export Field Groups"
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "已匯出 %s 個欄位群組。"

#: includes/admin/tools/class-acf-admin-tool-export.php:241
#: includes/admin/tools/class-acf-admin-tool-export.php:269
msgid "Select Field Groups"
msgstr "選取欄位群組"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"選擇你想匯出的欄位群組，再選擇匯出方式。使用匯出檔案將匯出一個 .json 檔，讓你"
"可以在其他安裝 ACF 的站台匯入設定。使用產出 PHP 按鈕將會匯出 PHP 程式碼，以便"
"置入你的佈景之中。"

#: includes/admin/tools/class-acf-admin-tool-export.php:341
#| msgid "Export"
msgid "Export File"
msgstr "匯出檔案"

#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"以下程式碼可用於註冊所選欄位群組的本機版本。本機的欄位群組可以提供許多好處，"
"例如更快的載入時間、版本控制和動態欄位/設定。 只需將以下程式碼複製並貼到佈景"
"主題的 functions.php 文件中，或將它自外部文件包含進來。"

#: includes/admin/tools/class-acf-admin-tool-export.php:446
msgid "Copy to clipboard"
msgstr "複製到剪貼簿"

#: includes/admin/tools/class-acf-admin-tool-export.php:483
msgid "Copied"
msgstr "已複製"

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "匯入欄位群組"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"選取你想匯入的 Advanced Custom Fields JSON 檔案。當你點擊下方匯入按鈕時，ACF "
"將匯入欄位群組。"

#: includes/admin/tools/class-acf-admin-tool-import.php:52
#: includes/fields/class-acf-field-file.php:57
msgid "Select File"
msgstr "選擇檔案"

#: includes/admin/tools/class-acf-admin-tool-import.php:62
msgid "Import File"
msgstr "匯入檔案"

#: includes/admin/tools/class-acf-admin-tool-import.php:85
#: includes/fields/class-acf-field-file.php:170
msgid "No file selected"
msgstr "沒有選擇檔案"

#: includes/admin/tools/class-acf-admin-tool-import.php:93
msgid "Error uploading file. Please try again"
msgstr "檔案上傳錯誤。請再試一次"

#: includes/admin/tools/class-acf-admin-tool-import.php:98
msgid "Incorrect file type"
msgstr "檔案類型不正確"

#: includes/admin/tools/class-acf-admin-tool-import.php:107
msgid "Import file empty"
msgstr "匯入的檔案是空的"

#: includes/admin/tools/class-acf-admin-tool-import.php:138
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "匯入 %s 欄位群組"

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "啟用條件邏輯"

#: includes/admin/views/field-group-field-conditional-logic.php:51
msgid "Show this field if"
msgstr "符合下列規則就顯示欄位"

#: includes/admin/views/field-group-field-conditional-logic.php:138
#: includes/admin/views/html-location-rule.php:86
msgid "and"
msgstr "且"

#: includes/admin/views/field-group-field-conditional-logic.php:153
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "新增規則組"

#: includes/admin/views/field-group-field.php:38
#: pro/fields/class-acf-field-flexible-content.php:410
#: pro/fields/class-acf-field-repeater.php:299
msgid "Drag to reorder"
msgstr "拖曳排序"

#: includes/admin/views/field-group-field.php:42
#: includes/admin/views/field-group-field.php:45
msgid "Edit field"
msgstr "編輯欄位"

#: includes/admin/views/field-group-field.php:45
#: includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:138
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:337
msgid "Edit"
msgstr "編輯"

#: includes/admin/views/field-group-field.php:46
msgid "Duplicate field"
msgstr "複製欄位"

#: includes/admin/views/field-group-field.php:47
msgid "Move field to another group"
msgstr "將欄位移到其它群组"

#: includes/admin/views/field-group-field.php:47
msgid "Move"
msgstr "移動"

#: includes/admin/views/field-group-field.php:48
msgid "Delete field"
msgstr "刪除欄位"

#: includes/admin/views/field-group-field.php:48
#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Delete"
msgstr "刪除"

#: includes/admin/views/field-group-field.php:65
msgid "Field Label"
msgstr "欄位標籤"

#: includes/admin/views/field-group-field.php:66
msgid "This is the name which will appear on the EDIT page"
msgstr "顯示於內容編輯頁面、供使用者瞭解這個欄位用途的名稱"

#: includes/admin/views/field-group-field.php:75
msgid "Field Name"
msgstr "欄位名稱"

#: includes/admin/views/field-group-field.php:76
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr ""
"請使用英文及數字字元撰寫單一字串；不可使用空格，但可使用底線 _ 及破折號 -"

#: includes/admin/views/field-group-field.php:85
msgid "Field Type"
msgstr "欄位類型"

#: includes/admin/views/field-group-field.php:96
msgid "Instructions"
msgstr "說明"

#: includes/admin/views/field-group-field.php:97
msgid "Instructions for authors. Shown when submitting data"
msgstr "顯示給作者的說明文字。會在送出資料時顯示"

#: includes/admin/views/field-group-field.php:106
msgid "Required?"
msgstr "是否為必填欄位"

#: includes/admin/views/field-group-field.php:129
msgid "Wrapper Attributes"
msgstr "包覆元素的屬性"

#: includes/admin/views/field-group-field.php:135
msgid "width"
msgstr "寬度"

#: includes/admin/views/field-group-field.php:150
msgid "class"
msgstr "class"

#: includes/admin/views/field-group-field.php:163
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:175
msgid "Close Field"
msgstr "關閉欄位"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "順序"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:420
#: includes/fields/class-acf-field-radio.php:311
#: includes/fields/class-acf-field-select.php:433
#: pro/fields/class-acf-field-flexible-content.php:582
msgid "Label"
msgstr "標籤"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:939
#: pro/fields/class-acf-field-flexible-content.php:596
msgid "Name"
msgstr "名稱"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "索引鍵值"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "類型"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"尚無欄位。請點擊 <strong>[+ 新增欄位]</strong> 按鈕以建立群組中的第一個欄位。"

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ 新增欄位"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "規則"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr "建立一組規則以確定自訂欄位在哪些編輯介面顯示"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "欄位群組樣式"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "標準 (WP 自訂區塊)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "隨選即用 (沒有自訂區塊)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "欄位群組位置"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "頂端 (標題下方)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "一般 (內容下方)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "側邊"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "標籤位置"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:106
msgid "Top aligned"
msgstr "置頂"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:107
msgid "Left aligned"
msgstr "置左"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "操作說明位置"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "欄位標籤下方"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "欄位下方"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "欄位群組順序編號"

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "順序編號較小的欄位群組會先顯示"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "顯示於欄位群組清單的說明內容"

#: includes/admin/views/field-group-options.php:107
msgid "Permalink"
msgstr "永久連結"

#: includes/admin/views/field-group-options.php:108
msgid "Content Editor"
msgstr "內容編輯器"

#: includes/admin/views/field-group-options.php:109
msgid "Excerpt"
msgstr "摘要"

#: includes/admin/views/field-group-options.php:111
msgid "Discussion"
msgstr "討論"

#: includes/admin/views/field-group-options.php:112
msgid "Comments"
msgstr "留言"

#: includes/admin/views/field-group-options.php:113
msgid "Revisions"
msgstr "內容修訂"

#: includes/admin/views/field-group-options.php:114
msgid "Slug"
msgstr "代稱"

#: includes/admin/views/field-group-options.php:115
msgid "Author"
msgstr "作者"

#: includes/admin/views/field-group-options.php:116
msgid "Format"
msgstr "格式"

#: includes/admin/views/field-group-options.php:117
msgid "Page Attributes"
msgstr "頁面屬性"

#: includes/admin/views/field-group-options.php:118
#: includes/fields/class-acf-field-relationship.php:607
msgid "Featured Image"
msgstr "特色圖片"

#: includes/admin/views/field-group-options.php:119
msgid "Categories"
msgstr "類別"

#: includes/admin/views/field-group-options.php:120
msgid "Tags"
msgstr "標籤"

#: includes/admin/views/field-group-options.php:121
msgid "Send Trackbacks"
msgstr "傳送引用通知"

#: includes/admin/views/field-group-options.php:128
msgid "Hide on screen"
msgstr "需要在編輯畫面隱藏的項目"

#: includes/admin/views/field-group-options.php:129
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>選取</b>要在編輯畫面<b>隱藏</b>的項目"

#: includes/admin/views/field-group-options.php:129
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"如果編輯畫面出現多個欄位群組，則會使用第一個欄位群組的設定，亦即 [順序編號] "
"數值最小的那個欄位群組設定"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr "以下站台需要進行資料庫更新。檢查要更新的內容，然後點擊 %s。"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:92
msgid "Upgrade Sites"
msgstr "升級網站"

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "網站"

#: includes/admin/views/html-admin-page-upgrade-network.php:74
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "網站需要從 %s 升級到 %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:76
msgid "Site is up to date"
msgstr "網站已是最新版本"

#: includes/admin/views/html-admin-page-upgrade-network.php:93
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr "資料庫更新完成<a href=\"%s\"> 返回控制台 </a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:113
msgid "Please select at least one site to upgrade."
msgstr "請至少選擇一個要升級的站點。"

#: includes/admin/views/html-admin-page-upgrade-network.php:117
#: includes/admin/views/html-notice-upgrade.php:38
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr "強烈建議在進行這項操作前，先備份網站的資料庫。確定要執行更新程式嗎？"

#: includes/admin/views/html-admin-page-upgrade-network.php:144
#: includes/admin/views/html-admin-page-upgrade.php:31
#, php-format
msgid "Upgrading data to version %s"
msgstr "將資料升級至 %s 版"

#: includes/admin/views/html-admin-page-upgrade-network.php:167
msgid "Upgrade complete."
msgstr "更新完成。"

#: includes/admin/views/html-admin-page-upgrade-network.php:176
#: includes/admin/views/html-admin-page-upgrade-network.php:185
#: includes/admin/views/html-admin-page-upgrade.php:78
#: includes/admin/views/html-admin-page-upgrade.php:87
msgid "Upgrade failed."
msgstr "更新失敗。"

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "正在讀取更新任務..."

#: includes/admin/views/html-admin-page-upgrade.php:33
#, php-format
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "資料庫更新完成<a href=\"%s\"> 查看新內容 </a>"

#: includes/admin/views/html-admin-page-upgrade.php:116
#: includes/ajax/class-acf-ajax-upgrade.php:32
msgid "No updates available."
msgstr "沒有可用的更新。"

#: includes/admin/views/html-admin-tools.php:21
msgid "Back to all tools"
msgstr "返回所有工具"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "顯示此欄位群組的條件"

#: includes/admin/views/html-notice-upgrade.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "重複器"

#: includes/admin/views/html-notice-upgrade.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "彈性內容"

#: includes/admin/views/html-notice-upgrade.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "圖庫"

#: includes/admin/views/html-notice-upgrade.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "設定頁面"

#: includes/admin/views/html-notice-upgrade.php:21
msgid "Database Upgrade Required"
msgstr "資料庫需要升級"

#: includes/admin/views/html-notice-upgrade.php:22
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "感謝您更新至 %s %s 版！"

#: includes/admin/views/html-notice-upgrade.php:22
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr "此版本包含對資料庫的改進，需要更新。"

#: includes/admin/views/html-notice-upgrade.php:24
#, php-format
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr "請檢查所有高級項目 (%s) 均更新至最新版本。"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "歡迎來到高級自訂欄位"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"感謝你完成更新！ACF %s 版比之前版本有更大更多的改進，開發團隊希望你會喜歡它。"

#: includes/admin/views/settings-info.php:15
msgid "A Smoother Experience"
msgstr "更順暢的體驗"

#: includes/admin/views/settings-info.php:18
msgid "Improved Usability"
msgstr "改進可用性"

#: includes/admin/views/settings-info.php:19
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"引入流行的 Select2 函式庫提升了多種欄位類型的可用性和速度，包括文章物件、頁面"
"連結、分類法和選擇控制項。"

#: includes/admin/views/settings-info.php:22
msgid "Improved Design"
msgstr "改進設計"

#: includes/admin/views/settings-info.php:23
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"許多欄位都經過了視覺更新，使 ACF 看起來比以前更好！在圖庫、關係和 oEmbed "
"(新) 欄位上可看到顯著的變化！"

#: includes/admin/views/settings-info.php:26
msgid "Improved Data"
msgstr "改進資料"

#: includes/admin/views/settings-info.php:27
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"重新設計資料架構使子欄位能夠獨立於父欄位而存在。這允許您在父欄位裡將欄位拖放"
"至外層或內層！"

#: includes/admin/views/settings-info.php:35
msgid "Goodbye Add-ons. Hello PRO"
msgstr "告別附加元件，擁抱 Pro 版"

#: includes/admin/views/settings-info.php:38
msgid "Introducing ACF PRO"
msgstr "ACF PRO介绍"

#: includes/admin/views/settings-info.php:39
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr "我們正在以令人興奮的方式改變提供高級功能的方式！"

#: includes/admin/views/settings-info.php:40
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"所有 4 個優質 Add-on 擴充元件已被合併成一個新的<a href=\"%s\">ACF 的專業版</"
"a>。提供個人和開發者授權，價格比以往任何時候更實惠！"

#: includes/admin/views/settings-info.php:44
msgid "Powerful Features"
msgstr "強大的功能"

#: includes/admin/views/settings-info.php:45
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF PRO包含強大的功能，例如可重複資料，彈性內容排版，漂亮的相簿欄位以及建立額"
"外管理選項頁面的功能！"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "進一步了解<a href=\\\"%s\\\">ACF PRO的功能</a>。"

#: includes/admin/views/settings-info.php:50
msgid "Easy Upgrading"
msgstr "輕鬆升級"

#: includes/admin/views/settings-info.php:51
msgid ""
"Upgrading to ACF PRO is easy. Simply purchase a license online and download "
"the plugin!"
msgstr "升級到 ACF PRO 很容易。 只需在線購買許可授權並下載外掛即可！"

#: includes/admin/views/settings-info.php:52
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>."
msgstr ""
"我們編寫了<a href=\"%s\"> 升級指南 </a>來回答任何問題，如您有任何問題，請通過"
"<a href=\"%s\"> 服務台 </a>與支援小組聯絡。"

#: includes/admin/views/settings-info.php:61
msgid "New Features"
msgstr "新功能"

#: includes/admin/views/settings-info.php:66
#| msgid "Edit Field"
msgid "Link Field"
msgstr "連結欄位"

#: includes/admin/views/settings-info.php:67
msgid ""
"The Link field provides a simple way to select or define a link (url, title, "
"target)."
msgstr "連結欄位能簡單的選擇或定義連結 (網址、標題、開啟目標)。"

#: includes/admin/views/settings-info.php:71
#| msgid "Move Field"
msgid "Group Field"
msgstr "群組欄位"

#: includes/admin/views/settings-info.php:72
msgid "The Group field provides a simple way to create a group of fields."
msgstr "群組欄位能簡單的建立欄位的群組。"

#: includes/admin/views/settings-info.php:76
#| msgid "Edit Field"
msgid "oEmbed Field"
msgstr "oEmbed 欄位"

#: includes/admin/views/settings-info.php:77
msgid ""
"The oEmbed field allows an easy way to embed videos, images, tweets, audio, "
"and other content."
msgstr "oEmbed 欄位能簡單的嵌入影片、圖片、推文、音檔和其他內容。"

#: includes/admin/views/settings-info.php:81
#| msgid "Close Field"
msgid "Clone Field"
msgstr "分身欄位"

#: includes/admin/views/settings-info.php:82
msgid "The clone field allows you to select and display existing fields."
msgstr "分身欄位能讓你選擇並顯示現有的欄位。"

#: includes/admin/views/settings-info.php:86
msgid "More AJAX"
msgstr "更多 AJAX"

#: includes/admin/views/settings-info.php:87
msgid "More fields use AJAX powered search to speed up page loading."
msgstr "更多欄位使用 AJAX 搜尋來加快頁面載入速度。"

#: includes/admin/views/settings-info.php:91
msgid "Local JSON"
msgstr "本機 JSON"

#: includes/admin/views/settings-info.php:92
msgid ""
"New auto export to JSON feature improves speed and allows for syncronisation."
msgstr "新的自動匯出 JSON 功能改善了速度並允許同步。"

#: includes/admin/views/settings-info.php:96
msgid "Easy Import / Export"
msgstr "輕鬆 匯入 / 匯出"

#: includes/admin/views/settings-info.php:97
msgid "Both import and export can easily be done through a new tools page."
msgstr "匯入 / 匯出可通過新工具頁面輕鬆完成。"

#: includes/admin/views/settings-info.php:101
msgid "New Form Locations"
msgstr "新表單位置"

#: includes/admin/views/settings-info.php:102
msgid ""
"Fields can now be mapped to menus, menu items, comments, widgets and all "
"user forms!"
msgstr "欄位現在可以被對應到選單、選單項目、留言、小工具及所有使用者表單！"

#: includes/admin/views/settings-info.php:106
#| msgid "Move Custom Field"
msgid "More Customization"
msgstr "更多自訂"

#: includes/admin/views/settings-info.php:107
msgid ""
"New PHP (and JS) actions and filters have been added to allow for more "
"customization."
msgstr "加入了新的 PHP ( 和 JS ) 的 actions 和 filters，方便進行更多客製。"

#: includes/admin/views/settings-info.php:111
msgid "Fresh UI"
msgstr "全新 UI"

#: includes/admin/views/settings-info.php:112
msgid ""
"The entire plugin has had a design refresh including new field types, "
"settings and design!"
msgstr "整體外掛翻新了介面，包括新的欄位類型，設定和設計！"

#: includes/admin/views/settings-info.php:116
msgid "New Settings"
msgstr "新設定"

#: includes/admin/views/settings-info.php:117
msgid ""
"Field group settings have been added for Active, Label Placement, "
"Instructions Placement and Description."
msgstr "欄位群組設定加入了啟用、標籤位置、說明位置及描述。"

#: includes/admin/views/settings-info.php:121
msgid "Better Front End Forms"
msgstr "更好的前端表單"

#: includes/admin/views/settings-info.php:122
msgid ""
"acf_form() can now create a new post on submission with lots of new settings."
msgstr "acf_form() 現在可以在提交時創建一篇新文章，並附帶大量新設定。"

#: includes/admin/views/settings-info.php:126
msgid "Better Validation"
msgstr "更好的驗證"

#: includes/admin/views/settings-info.php:127
msgid "Form validation is now done via PHP + AJAX in favour of only JS."
msgstr "表單驗證現在通過 PHP + AJAX 完成。"

#: includes/admin/views/settings-info.php:131
msgid "Moving Fields"
msgstr "移動欄位"

#: includes/admin/views/settings-info.php:132
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents."
msgstr "新的欄位群組功能，允許您在群組和上層群組之間移動欄位。"

#: includes/admin/views/settings-info.php:143
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "開發團隊希望您會喜愛 %s 版的變更。"

#: includes/api/api-helpers.php:827
msgid "Thumbnail"
msgstr "縮略圖"

#: includes/api/api-helpers.php:828
msgid "Medium"
msgstr "中"

#: includes/api/api-helpers.php:829
msgid "Large"
msgstr "大"

#: includes/api/api-helpers.php:878
msgid "Full Size"
msgstr "完整尺寸"

#: includes/api/api-helpers.php:1599 includes/api/api-term.php:147
#: pro/fields/class-acf-field-clone.php:996
msgid "(no title)"
msgstr "(無標題)"

#: includes/api/api-helpers.php:3570
#, php-format
msgid "Image width must be at least %dpx."
msgstr "圖片寬度必須至少為 %d px。"

#: includes/api/api-helpers.php:3575
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "圖片寬度不得超過%dpx。"

#: includes/api/api-helpers.php:3591
#, php-format
msgid "Image height must be at least %dpx."
msgstr "圖片高度必須至少 %dpx."

#: includes/api/api-helpers.php:3596
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "圖片高度不得超過%dpx。"

#: includes/api/api-helpers.php:3614
#, php-format
msgid "File size must be at least %s."
msgstr "檔案大小至少是 %s。"

#: includes/api/api-helpers.php:3619
#, php-format
msgid "File size must must not exceed %s."
msgstr "檔案大小最大不能超過 %s。"

#: includes/api/api-helpers.php:3653
#, php-format
msgid "File type must be %s."
msgstr "檔案類型必須是%s。"

#: includes/assets.php:184
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "如果您離開這個頁面，您所做的變更將遺失"

#: includes/assets.php:187 includes/fields/class-acf-field-select.php:259
#| msgid "Select"
msgctxt "verb"
msgid "Select"
msgstr "選擇"

#: includes/assets.php:188
#| msgid "Edit"
msgctxt "verb"
msgid "Edit"
msgstr "編輯"

#: includes/assets.php:189
#| msgid "Update"
msgctxt "verb"
msgid "Update"
msgstr "更新"

#: includes/assets.php:190
msgid "Uploaded to this post"
msgstr "已上傳到這篇文章"

#: includes/assets.php:191
msgid "Expand Details"
msgstr "展開詳細資料"

#: includes/assets.php:192
msgid "Collapse Details"
msgstr "收合詳細資料"

#: includes/assets.php:193
msgid "Restricted"
msgstr "受限"

#: includes/assets.php:194 includes/fields/class-acf-field-image.php:66
msgid "All images"
msgstr "所有圖片"

#: includes/assets.php:197
msgid "Validation successful"
msgstr "驗證成功"

#: includes/assets.php:198 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "驗證失敗"

#: includes/assets.php:199
msgid "1 field requires attention"
msgstr "1 個欄位需要注意"

#: includes/assets.php:200
#, php-format
msgid "%d fields require attention"
msgstr "%d 個欄位需要注意"

#: includes/assets.php:203
#| msgid "Move to trash. Are you sure?"
msgid "Are you sure?"
msgstr "確定要繼續操作嗎？"

#: includes/assets.php:204 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "是"

#: includes/assets.php:205 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:174
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "否"

#: includes/assets.php:206 includes/fields/class-acf-field-file.php:154
#: includes/fields/class-acf-field-image.php:140
#: includes/fields/class-acf-field-link.php:140
#: pro/fields/class-acf-field-gallery.php:338
#: pro/fields/class-acf-field-gallery.php:478
msgid "Remove"
msgstr "刪除"

#: includes/assets.php:207
msgid "Cancel"
msgstr "取消"

#: includes/assets.php:210
msgid "Has any value"
msgstr "含有任何設定值"

#: includes/assets.php:211
msgid "Has no value"
msgstr "不含設定值"

#: includes/assets.php:212
#| msgid "is equal to"
msgid "Value is equal to"
msgstr "設定值等於"

#: includes/assets.php:213
#| msgid "is not equal to"
msgid "Value is not equal to"
msgstr "設定值不等於"

#: includes/assets.php:214
msgid "Value matches pattern"
msgstr "設定值符合模式"

#: includes/assets.php:215
msgid "Value contains"
msgstr "設定值包含"

#: includes/assets.php:216
msgid "Value is greater than"
msgstr "設定值大於"

#: includes/assets.php:217
msgid "Value is less than"
msgstr "設定值小於"

#: includes/assets.php:218
msgid "Selection is greater than"
msgstr "選擇大於"

#: includes/assets.php:219
#| msgid "Select File"
msgid "Selection is less than"
msgstr "選擇少於"

#: includes/assets.php:222 includes/forms/form-comment.php:166
#: pro/admin/admin-options-page.php:325
msgid "Edit field group"
msgstr "編輯欄位群組"

#: includes/fields.php:308
msgid "Field type does not exist"
msgstr "欄位類型不存在"

#: includes/fields.php:308
msgid "Unknown"
msgstr "未知"

#: includes/fields.php:349
msgid "Basic"
msgstr "基本"

#: includes/fields.php:350 includes/forms/form-front.php:47
msgid "Content"
msgstr "內容"

#: includes/fields.php:351
msgid "Choice"
msgstr "選項"

#: includes/fields.php:352
msgid "Relational"
msgstr "關係"

#: includes/fields.php:353
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:354 includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:389
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:290
#: pro/fields/class-acf-field-clone.php:843
#: pro/fields/class-acf-field-flexible-content.php:553
#: pro/fields/class-acf-field-flexible-content.php:602
#: pro/fields/class-acf-field-repeater.php:448
msgid "Layout"
msgstr "版面配置"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "收合容器"

#: includes/fields/class-acf-field-accordion.php:99
msgid "Open"
msgstr "開啟"

#: includes/fields/class-acf-field-accordion.php:100
msgid "Display this accordion as open on page load."
msgstr "將此收合容器顯示為在頁面載入時打開。"

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr "多擴展"

#: includes/fields/class-acf-field-accordion.php:110
msgid "Allow this accordion to open without closing others."
msgstr "允許此收合容器打開而不關閉其他。"

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr "端點"

#: includes/fields/class-acf-field-accordion.php:120
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr "定義一個前收合容器停止的端點。此收合容器將不可見。"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "按鈕群組"

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:364
msgid "Choices"
msgstr "選項"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "Enter each choice on a new line."
msgstr "每行輸入一個選項"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"為了能對資料有的掌控，可以同時指定如下所示的選項值與選項標籤，格式為「選項"
"值 : 選項標籤」(請使用半形冒號，並在冒號前後加上半形空格區隔選項值及選項標"
"籤):"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "red : Red"
msgstr "red : 紅"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:382
#: includes/fields/class-acf-field-taxonomy.php:784
#: includes/fields/class-acf-field-user.php:393
msgid "Allow Null?"
msgstr "是否接受空值"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:380
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:281
#: includes/fields/class-acf-field-range.php:149
#: includes/fields/class-acf-field-select.php:373
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:381
msgid "Default Value"
msgstr "預設值"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:282
#: includes/fields/class-acf-field-range.php:150
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:382
msgid "Appears when creating a new post"
msgstr "建立新文章時出現"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:396
#: includes/fields/class-acf-field-radio.php:297
msgid "Horizontal"
msgstr "水平"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:395
#: includes/fields/class-acf-field-radio.php:296
msgid "Vertical"
msgstr "垂直"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:413
#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:304
#: includes/fields/class-acf-field-taxonomy.php:829
msgid "Return Value"
msgstr "返回值"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:305
msgid "Specify the returned value on front end"
msgstr "指定在網站前端傳回的值"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:419
#: includes/fields/class-acf-field-radio.php:310
#: includes/fields/class-acf-field-select.php:432
msgid "Value"
msgstr "數值"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:421
#: includes/fields/class-acf-field-radio.php:312
#: includes/fields/class-acf-field-select.php:434
msgid "Both (Array)"
msgstr "兩者（陣列）"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Checkbox"
msgstr "複選框"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "切換全部"

#: includes/fields/class-acf-field-checkbox.php:221
#| msgid "Add New Field"
msgid "Add new choice"
msgstr "新增選項"

#: includes/fields/class-acf-field-checkbox.php:353
#| msgid "Allow Null?"
msgid "Allow Custom"
msgstr "允許自訂"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr "允許加入[自訂]值"

#: includes/fields/class-acf-field-checkbox.php:364
#| msgid "Move Custom Field"
msgid "Save Custom"
msgstr "儲存自訂"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr "儲存 [自訂] 值到欄位的選項"

#: includes/fields/class-acf-field-checkbox.php:381
#: includes/fields/class-acf-field-select.php:374
msgid "Enter each default value on a new line"
msgstr "每行輸入一個預設值"

#: includes/fields/class-acf-field-checkbox.php:403
msgid "Toggle"
msgstr "切換"

#: includes/fields/class-acf-field-checkbox.php:404
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "前置一個額外的核選框以切換所有選擇"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "顏色選擇器"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "清除"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "預設值"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "選擇顏色"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "目前顏色"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "日期選擇器"

#: includes/fields/class-acf-field-date_picker.php:59
#| msgid "Done"
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "完成"

#: includes/fields/class-acf-field-date_picker.php:60
#| msgid "Today"
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "今天"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "下一個"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "上一個"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "星期"

#: includes/fields/class-acf-field-date_picker.php:178
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "顯示格式"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_time_picker.php:184
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "編輯文章時顯示的時間格式"

#: includes/fields/class-acf-field-date_picker.php:187
#: includes/fields/class-acf-field-date_picker.php:218
#: includes/fields/class-acf-field-date_time_picker.php:193
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
#| msgid "Custom Fields"
msgid "Custom:"
msgstr "自訂："

#: includes/fields/class-acf-field-date_picker.php:197
#| msgid "Format"
msgid "Save Format"
msgstr "儲存格式"

#: includes/fields/class-acf-field-date_picker.php:198
#| msgid "This format will be seen by the user when entering a value"
msgid "The format used when saving a value"
msgstr "儲存數值時使用的格式"

#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-image.php:204
#: includes/fields/class-acf-field-post_object.php:431
#: includes/fields/class-acf-field-relationship.php:634
#: includes/fields/class-acf-field-select.php:427
#: includes/fields/class-acf-field-time_picker.php:124
#: includes/fields/class-acf-field-user.php:412
#: pro/fields/class-acf-field-gallery.php:557
msgid "Return Format"
msgstr "回傳格式"

#: includes/fields/class-acf-field-date_picker.php:209
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "範本函式回傳的格式"

#: includes/fields/class-acf-field-date_picker.php:227
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "每週開始於"

#: includes/fields/class-acf-field-date_time_picker.php:25
#| msgid "Date & Time Picker"
msgid "Date Time Picker"
msgstr "日期時間選擇器"

#: includes/fields/class-acf-field-date_time_picker.php:68
#| msgid "Close Field"
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "選擇時間"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "時間"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "時"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "分"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "秒"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "毫秒"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "微秒"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "時區"

#: includes/fields/class-acf-field-date_time_picker.php:76
#| msgid "No"
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "目前"

#: includes/fields/class-acf-field-date_time_picker.php:77
#| msgid "Done"
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "完成"

#: includes/fields/class-acf-field-date_time_picker.php:78
#| msgid "Select"
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "選擇"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "上午"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "下午"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "電子郵件"

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:104
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "預留位置文字"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:105
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "出現在輸入欄位中"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:188
#: includes/fields/class-acf-field-text.php:113
msgid "Prepend"
msgstr "欄位前置文字"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:189
#: includes/fields/class-acf-field-text.php:114
msgid "Appears before the input"
msgstr "出現在輸入欄位之前"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:197
#: includes/fields/class-acf-field-text.php:122
msgid "Append"
msgstr "欄位後綴文字"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:198
#: includes/fields/class-acf-field-text.php:123
msgid "Appears after the input"
msgstr "出現在輸入欄位後面"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "檔案"

#: includes/fields/class-acf-field-file.php:58
msgid "Edit File"
msgstr "編輯檔案"

#: includes/fields/class-acf-field-file.php:59
msgid "Update File"
msgstr "更新檔案"

#: includes/fields/class-acf-field-file.php:141
#| msgid "File Name"
msgid "File name"
msgstr "檔名"

#: includes/fields/class-acf-field-file.php:145
#: includes/fields/class-acf-field-file.php:248
#: includes/fields/class-acf-field-file.php:259
#: includes/fields/class-acf-field-image.php:264
#: includes/fields/class-acf-field-image.php:293
#: pro/fields/class-acf-field-gallery.php:642
#: pro/fields/class-acf-field-gallery.php:671
msgid "File size"
msgstr "檔案容量"

#: includes/fields/class-acf-field-file.php:170
msgid "Add File"
msgstr "新增檔案"

#: includes/fields/class-acf-field-file.php:221
msgid "File Array"
msgstr "檔案陣列"

#: includes/fields/class-acf-field-file.php:222
msgid "File URL"
msgstr "檔案URL"

#: includes/fields/class-acf-field-file.php:223
msgid "File ID"
msgstr "檔案ID"

#: includes/fields/class-acf-field-file.php:230
#: includes/fields/class-acf-field-image.php:229
#: pro/fields/class-acf-field-gallery.php:592
msgid "Library"
msgstr "媒體庫"

#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:230
#: pro/fields/class-acf-field-gallery.php:593
msgid "Limit the media library choice"
msgstr "限制媒體庫選擇"

#: includes/fields/class-acf-field-file.php:236
#: includes/fields/class-acf-field-image.php:235
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:72
#: includes/locations/class-acf-location-user-role.php:88
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:598
#: pro/locations/class-acf-location-block.php:79
msgid "All"
msgstr "所有"

#: includes/fields/class-acf-field-file.php:237
#: includes/fields/class-acf-field-image.php:236
#: pro/fields/class-acf-field-gallery.php:599
msgid "Uploaded to post"
msgstr "已上傳至文章"

#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-image.php:243
#: pro/fields/class-acf-field-gallery.php:621
msgid "Minimum"
msgstr "最小"

#: includes/fields/class-acf-field-file.php:245
#: includes/fields/class-acf-field-file.php:256
msgid "Restrict which files can be uploaded"
msgstr "限制檔案上傳類型"

#: includes/fields/class-acf-field-file.php:255
#: includes/fields/class-acf-field-image.php:272
#: pro/fields/class-acf-field-gallery.php:650
msgid "Maximum"
msgstr "最大"

#: includes/fields/class-acf-field-file.php:266
#: includes/fields/class-acf-field-image.php:301
#: pro/fields/class-acf-field-gallery.php:678
msgid "Allowed file types"
msgstr "允許的檔案類型"

#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-image.php:302
#: pro/fields/class-acf-field-gallery.php:679
msgid "Comma separated list. Leave blank for all types"
msgstr "請以逗號分隔列出。留白表示允許所有類型"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google 地圖"

#: includes/fields/class-acf-field-google-map.php:59
msgid "Sorry, this browser does not support geolocation"
msgstr "很抱歉，使用中的瀏覽器不支援地理位置"

#: includes/fields/class-acf-field-google-map.php:147
msgid "Clear location"
msgstr "清除位置"

#: includes/fields/class-acf-field-google-map.php:148
msgid "Find current location"
msgstr "搜尋目前位置"

#: includes/fields/class-acf-field-google-map.php:151
msgid "Search for address..."
msgstr "搜尋地址..."

#: includes/fields/class-acf-field-google-map.php:181
#: includes/fields/class-acf-field-google-map.php:192
msgid "Center"
msgstr "中間"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:193
msgid "Center the initial map"
msgstr "載入地圖後的初始中心位置，請輸入緯度 (lat) 及經度 (lng)"

#: includes/fields/class-acf-field-google-map.php:204
msgid "Zoom"
msgstr "縮放"

#: includes/fields/class-acf-field-google-map.php:205
msgid "Set the initial zoom level"
msgstr "載入地圖後的初始縮放層級"

#: includes/fields/class-acf-field-google-map.php:214
#: includes/fields/class-acf-field-image.php:255
#: includes/fields/class-acf-field-image.php:284
#: includes/fields/class-acf-field-oembed.php:268
#: pro/fields/class-acf-field-gallery.php:633
#: pro/fields/class-acf-field-gallery.php:662
msgid "Height"
msgstr "高"

#: includes/fields/class-acf-field-google-map.php:215
msgid "Customize the map height"
msgstr "自訂地圖高度"

#: includes/fields/class-acf-field-group.php:25
#| msgid "Field Group"
msgid "Group"
msgstr "群組"

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:384
msgid "Sub Fields"
msgstr "子欄位"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:844
msgid "Specify the style used to render the selected fields"
msgstr "指定用於呈現選定欄位的樣式"

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:849
#: pro/fields/class-acf-field-flexible-content.php:613
#: pro/fields/class-acf-field-repeater.php:456
#: pro/locations/class-acf-location-block.php:27
msgid "Block"
msgstr "區塊"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:850
#: pro/fields/class-acf-field-flexible-content.php:612
#: pro/fields/class-acf-field-repeater.php:455
msgid "Table"
msgstr "表格"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:851
#: pro/fields/class-acf-field-flexible-content.php:614
#: pro/fields/class-acf-field-repeater.php:457
msgid "Row"
msgstr "行"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "圖片"

#: includes/fields/class-acf-field-image.php:63
msgid "Select Image"
msgstr "選擇圖片"

#: includes/fields/class-acf-field-image.php:64
msgid "Edit Image"
msgstr "編輯圖片"

#: includes/fields/class-acf-field-image.php:65
msgid "Update Image"
msgstr "更新圖片"

#: includes/fields/class-acf-field-image.php:156
msgid "No image selected"
msgstr "尚未選取圖片"

#: includes/fields/class-acf-field-image.php:156
msgid "Add Image"
msgstr "新增圖片"

#: includes/fields/class-acf-field-image.php:210
#: pro/fields/class-acf-field-gallery.php:563
msgid "Image Array"
msgstr "圖片陣列"

#: includes/fields/class-acf-field-image.php:211
#: pro/fields/class-acf-field-gallery.php:564
msgid "Image URL"
msgstr "圖片網址"

#: includes/fields/class-acf-field-image.php:212
#: pro/fields/class-acf-field-gallery.php:565
msgid "Image ID"
msgstr "圖片ID"

#: includes/fields/class-acf-field-image.php:219
#: pro/fields/class-acf-field-gallery.php:571
msgid "Preview Size"
msgstr "預覽圖大小"

#: includes/fields/class-acf-field-image.php:244
#: includes/fields/class-acf-field-image.php:273
#: pro/fields/class-acf-field-gallery.php:622
#: pro/fields/class-acf-field-gallery.php:651
msgid "Restrict which images can be uploaded"
msgstr "限制哪些圖片可以上傳"

#: includes/fields/class-acf-field-image.php:247
#: includes/fields/class-acf-field-image.php:276
#: includes/fields/class-acf-field-oembed.php:257
#: pro/fields/class-acf-field-gallery.php:625
#: pro/fields/class-acf-field-gallery.php:654
msgid "Width"
msgstr "寬"

#: includes/fields/class-acf-field-link.php:25
#| msgid "Page Link"
msgid "Link"
msgstr "連結"

#: includes/fields/class-acf-field-link.php:133
#| msgid "Select File"
msgid "Select Link"
msgstr "選取連結"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr "於新視窗/分頁開啟"

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "連結陣列"

#: includes/fields/class-acf-field-link.php:173
#| msgid "File URL"
msgid "Link URL"
msgstr "連結網址"

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "訊息"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "新行"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "控制如何呈現新行"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "自動增加段落"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "自動加入 &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "無格式"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "跳脫 HTML"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "允許 HTML 標記顯示為可見文字而不是顯示繪製結果"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "數字"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:158
msgid "Minimum Value"
msgstr "最小值"

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:168
msgid "Maximum Value"
msgstr "最大值"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:178
msgid "Step Size"
msgstr "數值增減幅度"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "值必須是一個數字"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "值必須等於或高於%d"

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "值必須等於或低於%d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:216
msgid "Enter URL"
msgstr "輸入網址"

#: includes/fields/class-acf-field-oembed.php:254
#: includes/fields/class-acf-field-oembed.php:265
msgid "Embed Size"
msgstr "嵌入大小"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "頁面連結"

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "彙整"

#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:267
#: includes/fields/class-acf-field-taxonomy.php:961
#| msgid "Page Parent"
msgid "Parent"
msgstr "上層"

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:383
#: includes/fields/class-acf-field-relationship.php:560
msgid "Filter by Post Type"
msgstr "以文章型別篩選"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:391
#: includes/fields/class-acf-field-relationship.php:568
msgid "All post types"
msgstr "所有文章類型"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:397
#: includes/fields/class-acf-field-relationship.php:574
msgid "Filter by Taxonomy"
msgstr "以分類法篩選"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:405
#: includes/fields/class-acf-field-relationship.php:582
msgid "All taxonomies"
msgstr "所有分類法"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr "允許文章彙整網址"

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-select.php:392
#: includes/fields/class-acf-field-user.php:403
msgid "Select multiple values?"
msgstr "是否可複選"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "密碼"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:436
#: includes/fields/class-acf-field-relationship.php:639
msgid "Post Object"
msgstr "文章物件"

#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:640
msgid "Post ID"
msgstr "文章 ID"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "單選按鈕"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "其他"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr "加入 [其他] 這個選項，讓使用者可輸入自訂值"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "儲存其它"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "儲存填入 [其他] 選項中的值，作為這個欄位的選項"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "範圍"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "關係"

#: includes/fields/class-acf-field-relationship.php:62
msgid "Maximum values reached ( {max} values )"
msgstr "最大值為 {max} 篇，目前已達最大值"

#: includes/fields/class-acf-field-relationship.php:63
msgid "Loading"
msgstr "載入中"

#: includes/fields/class-acf-field-relationship.php:64
msgid "No matches found"
msgstr "找不到符合的"

#: includes/fields/class-acf-field-relationship.php:411
msgid "Select post type"
msgstr "選取內容類型"

#: includes/fields/class-acf-field-relationship.php:420
msgid "Select taxonomy"
msgstr "選取分類法"

#: includes/fields/class-acf-field-relationship.php:477
msgid "Search..."
msgstr "搜尋..."

#: includes/fields/class-acf-field-relationship.php:588
msgid "Filters"
msgstr "外掛內建的篩選條件"

#: includes/fields/class-acf-field-relationship.php:594
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "文章類型"

#: includes/fields/class-acf-field-relationship.php:595
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:754
#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy"
msgstr "分類法"

#: includes/fields/class-acf-field-relationship.php:602
msgid "Elements"
msgstr "元素"

#: includes/fields/class-acf-field-relationship.php:603
msgid "Selected elements will be displayed in each result"
msgstr "選擇的元素將在每個結果中顯示"

#: includes/fields/class-acf-field-relationship.php:614
msgid "Minimum posts"
msgstr "最少的文章"

#: includes/fields/class-acf-field-relationship.php:623
msgid "Maximum posts"
msgstr "最大文章數"

#: includes/fields/class-acf-field-relationship.php:727
#: pro/fields/class-acf-field-gallery.php:779
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s 需要至少 %s 選擇"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:776
#| msgid "Select"
msgctxt "noun"
msgid "Select"
msgstr "選擇"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "有一個結果可用。請按 enter 選擇它。"

#: includes/fields/class-acf-field-select.php:112
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d 個可用結果，請使用上下鍵進行導覽。"

#: includes/fields/class-acf-field-select.php:113
#| msgid "No Fields found"
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "找不到符合的"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "請輸入 1 個或更多字元"

#: includes/fields/class-acf-field-select.php:115
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "請輸入 %d 個或更多字元"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "請刪除 1 個字元"

#: includes/fields/class-acf-field-select.php:117
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "請刪除 %d 個字元"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "你只能夠選 1 個項目"

#: includes/fields/class-acf-field-select.php:119
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "你只能選 %d 個項目"

#: includes/fields/class-acf-field-select.php:120
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "載入更多結果&hellip;"

#: includes/fields/class-acf-field-select.php:121
#| msgid "Search Fields"
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "搜尋中&hellip;"

#: includes/fields/class-acf-field-select.php:122
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "載入失敗"

#: includes/fields/class-acf-field-select.php:402
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "程式化 UI"

#: includes/fields/class-acf-field-select.php:412
msgid "Use AJAX to lazy load choices?"
msgstr "使用 AJAX 去 lazy load 選擇"

#: includes/fields/class-acf-field-select.php:428
msgid "Specify the value returned"
msgstr "指定回傳的值"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "分隔"

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "頁籤"

#: includes/fields/class-acf-field-tab.php:102
msgid "Placement"
msgstr "位置"

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr "定義上一個頁籤要停止的端點。這將開始一組新的頁籤群組。"

#: includes/fields/class-acf-field-taxonomy.php:714
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "沒有 %s"

#: includes/fields/class-acf-field-taxonomy.php:755
msgid "Select the taxonomy to be displayed"
msgstr "選擇要顯示的分類法"

#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Appearance"
msgstr "外觀"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Select the appearance of this field"
msgstr "選擇此欄位的外觀"

#: includes/fields/class-acf-field-taxonomy.php:770
msgid "Multiple Values"
msgstr "多選"

#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Multi Select"
msgstr "多選"

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Single Value"
msgstr "單個值"

#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Radio Buttons"
msgstr "單選框"

#: includes/fields/class-acf-field-taxonomy.php:799
msgid "Create Terms"
msgstr "建立字詞"

#: includes/fields/class-acf-field-taxonomy.php:800
msgid "Allow new terms to be created whilst editing"
msgstr "允許在編輯時建立新的字詞"

#: includes/fields/class-acf-field-taxonomy.php:809
msgid "Save Terms"
msgstr "儲存字詞"

#: includes/fields/class-acf-field-taxonomy.php:810
msgid "Connect selected terms to the post"
msgstr "連結選擇的字詞到文章"

#: includes/fields/class-acf-field-taxonomy.php:819
msgid "Load Terms"
msgstr "載入字詞"

#: includes/fields/class-acf-field-taxonomy.php:820
msgid "Load value from posts terms"
msgstr "從文章字詞載入數值"

#: includes/fields/class-acf-field-taxonomy.php:834
msgid "Term Object"
msgstr "對象緩存"

#: includes/fields/class-acf-field-taxonomy.php:835
msgid "Term ID"
msgstr "內容ID"

#: includes/fields/class-acf-field-taxonomy.php:885
#, php-format
msgid "User unable to add new %s"
msgstr "使用者無法加入新的 %s"

#: includes/fields/class-acf-field-taxonomy.php:895
#, php-format
msgid "%s already exists"
msgstr "%s 已經存在"

#: includes/fields/class-acf-field-taxonomy.php:927
#, php-format
msgid "%s added"
msgstr "%s 已新增"

#: includes/fields/class-acf-field-taxonomy.php:973
#: includes/locations/class-acf-location-user-form.php:73
msgid "Add"
msgstr "加入"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "文字"

#: includes/fields/class-acf-field-text.php:131
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "字元限制"

#: includes/fields/class-acf-field-text.php:132
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "留白為無限制"

#: includes/fields/class-acf-field-text.php:157
#: includes/fields/class-acf-field-textarea.php:215
#, php-format
msgid "Value must not exceed %d characters"
msgstr "值不得超過 %d 字元"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "文字區域"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "行"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "設定文字區域高度"

#: includes/fields/class-acf-field-time_picker.php:25
#| msgid "Date & Time Picker"
msgid "Time Picker"
msgstr "時間選擇器"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "真/假"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr "在複選框旁邊顯示文字"

#: includes/fields/class-acf-field-true_false.php:155
#| msgid "Text"
msgid "On Text"
msgstr "啟動用字"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "啟用時顯示文字"

#: includes/fields/class-acf-field-true_false.php:170
#| msgid "Text"
msgid "Off Text"
msgstr "關閉用字"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when inactive"
msgstr "停用時顯示文字"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "網址"

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "填入值必須是合法的網址"

#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr "使用者"

#: includes/fields/class-acf-field-user.php:378
msgid "Filter by role"
msgstr "根據角色篩選"

#: includes/fields/class-acf-field-user.php:386
msgid "All user roles"
msgstr "所有使用者角色"

#: includes/fields/class-acf-field-user.php:417
msgid "User Array"
msgstr "使用者陣列"

#: includes/fields/class-acf-field-user.php:418
#| msgid "Term Object"
msgid "User Object"
msgstr "使用者物件"

#: includes/fields/class-acf-field-user.php:419
#| msgid "User"
msgid "User ID"
msgstr "使用者 ID"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "可視化編輯器"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual"
msgstr "視覺"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "文字"

#: includes/fields/class-acf-field-wysiwyg.php:337
msgid "Click to initialize TinyMCE"
msgstr "點擊初始化 TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:390
msgid "Tabs"
msgstr "分頁"

#: includes/fields/class-acf-field-wysiwyg.php:395
msgid "Visual & Text"
msgstr "視覺 & 文字"

#: includes/fields/class-acf-field-wysiwyg.php:396
msgid "Visual Only"
msgstr "僅視覺"

#: includes/fields/class-acf-field-wysiwyg.php:397
msgid "Text Only"
msgstr "文字"

#: includes/fields/class-acf-field-wysiwyg.php:404
msgid "Toolbar"
msgstr "工具條"

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Show Media Upload Buttons?"
msgstr "是否顯示媒體上傳按鈕"

#: includes/fields/class-acf-field-wysiwyg.php:429
msgid "Delay initialization?"
msgstr "延遲初始化？"

#: includes/fields/class-acf-field-wysiwyg.php:430
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "在按一下欄位之前，不會初始化 TinyMCE"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "驗證 Email"

#: includes/forms/form-front.php:104 pro/fields/class-acf-field-gallery.php:510
#: pro/options-page.php:81
msgid "Update"
msgstr "更新"

#: includes/forms/form-front.php:105
msgid "Post updated"
msgstr "文章已更新"

#: includes/forms/form-front.php:231
msgid "Spam Detected"
msgstr "已檢測到垃圾郵件"

#: includes/forms/form-user.php:336
#, php-format
msgid "<strong>ERROR</strong>: %s"
msgstr "<strong>錯誤</strong>： %s"

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "文章"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "頁面"

#: includes/locations.php:96
msgid "Forms"
msgstr "表單"

#: includes/locations.php:243
msgid "is equal to"
msgstr "等於"

#: includes/locations.php:244
msgid "is not equal to"
msgstr "不等於"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "附件"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr "所有 %s 格式"

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "留言"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "目前使用者角色"

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "多站網路管理員"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "目前使用者"

#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr "已登入"

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr "查看前端"

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr "查看後端"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "選單項目"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "選單"

#: includes/locations/class-acf-location-nav-menu.php:109
#| msgid "Location"
msgid "Menu Locations"
msgstr "選單位置"

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr "選單"

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "最上層頁面"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "頁面範本"

#: includes/locations/class-acf-location-page-template.php:87
#: includes/locations/class-acf-location-post-template.php:134
msgid "Default Template"
msgstr "預設範本"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "頁面類型"

#: includes/locations/class-acf-location-page-type.php:146
msgid "Front Page"
msgstr "網站首頁"

#: includes/locations/class-acf-location-page-type.php:147
msgid "Posts Page"
msgstr "文章頁面"

#: includes/locations/class-acf-location-page-type.php:148
msgid "Top Level Page (no parent)"
msgstr "最上層頁面 (再無上層頁面的頁面)"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Parent Page (has children)"
msgstr "上層頁面 (含有子頁面)"

#: includes/locations/class-acf-location-page-type.php:150
msgid "Child Page (has parent)"
msgstr "子頁面 (具有上層頁面)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "文章類別"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "文章格式"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "文章狀態"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "文章分類法"

#: includes/locations/class-acf-location-post-template.php:27
#| msgid "Page Template"
msgid "Post Template"
msgstr "文章範本"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "使用者表單"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Add / Edit"
msgstr "新增/編輯"

#: includes/locations/class-acf-location-user-form.php:75
msgid "Register"
msgstr "註冊"

#: includes/locations/class-acf-location-user-role.php:22
msgid "User Role"
msgstr "使用者角色"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "小工具"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "%s 值為必填"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:198
msgid "Publish"
msgstr "發佈"

#: pro/admin/admin-options-page.php:204
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr "此設定頁沒有自訂欄位群組。<a href=\"%s\">建立一個自訂欄位群組</a>"

#: pro/admin/admin-updates.php:49
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>錯誤</b>。 無法連接到更新伺服器"

#: pro/admin/admin-updates.php:118 pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "更新"

#: pro/admin/admin-updates.php:191
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>錯誤</b>。無法對更新包進行驗證。請再次檢查或停用並重新啟動您的 ACF PRO 授"
"權。"

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "停用授權"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "啟用授權"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "授權資訊"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"要解鎖更新服務，請於下方輸入您的授權金鑰。若你沒有授權金鑰，請查閱 <a href="
"\"%s\" target=\"_blank\">詳情與價目</a>。"

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "授權金鑰"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "更新資訊"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "目前版本"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "最新版本"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "可用更新"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "更新外掛"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr "請於上方輸入你的授權金鑰以解鎖更新"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "再檢查一次"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "升級提醒"

#: pro/blocks.php:373
msgid "Switch to Edit"
msgstr "切換至編輯"

#: pro/blocks.php:374
msgid "Switch to Preview"
msgstr "切換至預覽"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "分身"

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr "選取一或多個你希望複製的欄位"

#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr "顯示"

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr "指定繪製分身欄位的樣式"

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr "群組（顯示該欄位內群組中被選定的欄位）"

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr "無縫（用選定欄位取代此欄位）"

#: pro/fields/class-acf-field-clone.php:857
#, php-format
#| msgid "Selected elements will be displayed in each result"
msgid "Labels will be displayed as %s"
msgstr "標籤將顯示為%s"

#: pro/fields/class-acf-field-clone.php:860
#| msgid "Field Label"
msgid "Prefix Field Labels"
msgstr "前置欄位標籤"

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr "值將被儲存為 %s"

#: pro/fields/class-acf-field-clone.php:874
#| msgid "Field Name"
msgid "Prefix Field Names"
msgstr "前置欄位名稱"

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr "未知的欄位"

#: pro/fields/class-acf-field-clone.php:1031
#| msgid "Synchronise field group"
msgid "Unknown field group"
msgstr "未知的欄位群組"

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr "所有欄位來自 %s 欄位群組"

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:193
#: pro/fields/class-acf-field-repeater.php:468
msgid "Add Row"
msgstr "新增列"

#: pro/fields/class-acf-field-flexible-content.php:73
#: pro/fields/class-acf-field-flexible-content.php:924
#: pro/fields/class-acf-field-flexible-content.php:1006
msgid "layout"
msgid_plural "layouts"
msgstr[0] "版面配置"

#: pro/fields/class-acf-field-flexible-content.php:74
msgid "layouts"
msgstr "版面"

#: pro/fields/class-acf-field-flexible-content.php:77
#: pro/fields/class-acf-field-flexible-content.php:923
#: pro/fields/class-acf-field-flexible-content.php:1005
msgid "This field requires at least {min} {label} {identifier}"
msgstr "這個欄位至少需要 {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:78
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "此欄位的限制為 {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:81
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} 可用 (最大 {max})"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} 需要 (最小 {min})"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "Flexible Content requires at least 1 layout"
msgstr "彈性內容需要至少 1 個版面配置"

#: pro/fields/class-acf-field-flexible-content.php:287
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "點擊下方的 \"%s\" 按鈕以新增設定"

#: pro/fields/class-acf-field-flexible-content.php:413
msgid "Add layout"
msgstr "新增版面"

#: pro/fields/class-acf-field-flexible-content.php:414
msgid "Remove layout"
msgstr "移除版面"

#: pro/fields/class-acf-field-flexible-content.php:415
#: pro/fields/class-acf-field-repeater.php:301
msgid "Click to toggle"
msgstr "點擊切換"

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Reorder Layout"
msgstr "重排版面"

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Reorder"
msgstr "重排"

#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Delete Layout"
msgstr "刪除版面"

#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Duplicate Layout"
msgstr "複製版面"

#: pro/fields/class-acf-field-flexible-content.php:558
msgid "Add New Layout"
msgstr "新增版面"

#: pro/fields/class-acf-field-flexible-content.php:629
msgid "Min"
msgstr "最小"

#: pro/fields/class-acf-field-flexible-content.php:642
msgid "Max"
msgstr "最大"

#: pro/fields/class-acf-field-flexible-content.php:669
#: pro/fields/class-acf-field-repeater.php:464
msgid "Button Label"
msgstr "按鈕標籤"

#: pro/fields/class-acf-field-flexible-content.php:678
msgid "Minimum Layouts"
msgstr "最少可使用版面數量"

#: pro/fields/class-acf-field-flexible-content.php:687
msgid "Maximum Layouts"
msgstr "最多可使用版面數量"

#: pro/fields/class-acf-field-gallery.php:73
msgid "Add Image to Gallery"
msgstr "新增圖片到圖庫"

#: pro/fields/class-acf-field-gallery.php:74
msgid "Maximum selection reached"
msgstr "已達到最大選擇"

#: pro/fields/class-acf-field-gallery.php:322
msgid "Length"
msgstr "長度"

#: pro/fields/class-acf-field-gallery.php:362
msgid "Caption"
msgstr "標題"

#: pro/fields/class-acf-field-gallery.php:371
#| msgid "Alternate Text"
msgid "Alt Text"
msgstr "替代文字"

#: pro/fields/class-acf-field-gallery.php:487
msgid "Add to gallery"
msgstr "加入圖庫"

#: pro/fields/class-acf-field-gallery.php:491
msgid "Bulk actions"
msgstr "批次操作"

#: pro/fields/class-acf-field-gallery.php:492
msgid "Sort by date uploaded"
msgstr "依上傳日期排序"

#: pro/fields/class-acf-field-gallery.php:493
msgid "Sort by date modified"
msgstr "依修改日期排序"

#: pro/fields/class-acf-field-gallery.php:494
msgid "Sort by title"
msgstr "依標題排序"

#: pro/fields/class-acf-field-gallery.php:495
msgid "Reverse current order"
msgstr "反向目前順序"

#: pro/fields/class-acf-field-gallery.php:507
msgid "Close"
msgstr "關閉"

#: pro/fields/class-acf-field-gallery.php:580
msgid "Insert"
msgstr "插入"

#: pro/fields/class-acf-field-gallery.php:581
msgid "Specify where new attachments are added"
msgstr "指定新附件加入的位置"

#: pro/fields/class-acf-field-gallery.php:585
msgid "Append to the end"
msgstr "附加在後"

#: pro/fields/class-acf-field-gallery.php:586
msgid "Prepend to the beginning"
msgstr "插入至最前"

#: pro/fields/class-acf-field-gallery.php:605
msgid "Minimum Selection"
msgstr "最小選擇"

#: pro/fields/class-acf-field-gallery.php:613
msgid "Maximum Selection"
msgstr "最大選擇"

#: pro/fields/class-acf-field-repeater.php:65
#: pro/fields/class-acf-field-repeater.php:661
msgid "Minimum rows reached ({min} rows)"
msgstr "已達最小行數 ( {min} 行 )"

#: pro/fields/class-acf-field-repeater.php:66
msgid "Maximum rows reached ({max} rows)"
msgstr "已達最大行數 ( {max} 行 )"

#: pro/fields/class-acf-field-repeater.php:338
msgid "Add row"
msgstr "新增列"

#: pro/fields/class-acf-field-repeater.php:339
msgid "Remove row"
msgstr "移除列"

#: pro/fields/class-acf-field-repeater.php:417
msgid "Collapsed"
msgstr "收合"

#: pro/fields/class-acf-field-repeater.php:418
msgid "Select a sub field to show when row is collapsed"
msgstr "選取一個子欄位，讓它在行列收合時顯示"

#: pro/fields/class-acf-field-repeater.php:428
msgid "Minimum Rows"
msgstr "最小行數"

#: pro/fields/class-acf-field-repeater.php:438
msgid "Maximum Rows"
msgstr "最大行數"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "設定頁面不存在"

#: pro/options-page.php:51
msgid "Options"
msgstr "選項"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "選項已更新"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"要啟用更新，請在<a href=\"%s\">更新</a>頁面上輸入您的授權金鑰。 如果您沒有授"
"權金鑰，請參閱<a href=\"%s\">詳情和定價</a>。"

#. Plugin URI of the plugin/theme
#. Author URI of the plugin/theme
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"

#~ msgid "Disabled"
#~ msgstr "已停用"

#~ msgid "Parent fields"
#~ msgstr "父欄位"

#~ msgid "Sibling fields"
#~ msgstr "分支欄位"

#~ msgid "See what's new in"
#~ msgstr "檢視更新內容于"

#~ msgid "version"
#~ msgstr "版本"

#~ msgid "Getting Started"
#~ msgstr "開始"

#~ msgid "Field Types"
#~ msgstr "欄位類型"

#~ msgid "Functions"
#~ msgstr "功能"

#~ msgid "Actions"
#~ msgstr "操作"

#~ msgid "'How to' guides"
#~ msgstr "新手引導"

#~ msgid "Tutorials"
#~ msgstr "教學"

#~ msgid "Created by"
#~ msgstr "建立者"

#~ msgid "Add-ons"
#~ msgstr "附加功能"

#~ msgid "Upgrade"
#~ msgstr "升級"

#~ msgid "Error"
#~ msgstr "錯誤"

#~ msgid "Error."
#~ msgstr "錯誤."

#~ msgid "Drag and drop to reorder"
#~ msgstr "托拽排序"

#, fuzzy
#~ msgid "Taxonomy Term"
#~ msgstr "分類法"

#, fuzzy
#~ msgid "Download & Install"
#~ msgstr "下載附加功能"

#~ msgid "Installed"
#~ msgstr "已安裝"

#, fuzzy
#~ msgid "New Gallery"
#~ msgstr "相簿"

#, fuzzy
#~ msgid "Relationship Field"
#~ msgstr "關係"

#~ msgid "Better Options Pages"
#~ msgstr "更好的設定頁面"

#~ msgid "Export Field Groups to PHP"
#~ msgstr "匯出欄位群組到PHP"

#, fuzzy
#~ msgid "See what's new"
#~ msgstr "檢視更新內容于"

#~ msgid "Show a different month"
#~ msgstr "顯示其他月份"

#~ msgid "Return format"
#~ msgstr "回傳格式"

#~ msgid "File Size"
#~ msgstr "檔案大小"

#~ msgid "No File selected"
#~ msgstr "尚未選擇檔案"

#, fuzzy
#~ msgid "Locating"
#~ msgstr "位置"

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr "請注意，所有文字將通過WP過濾功能"

#, fuzzy
#~ msgid "No embed found for the given URL."
#~ msgstr "沒有為選項頁找到自訂欄位群組。."

#~ msgid "None"
#~ msgstr "None"

#~ msgid "eg. Show extra content"
#~ msgstr "例如：顯示附加內容"

#~ msgid "Save Options"
#~ msgstr "儲存"

#, fuzzy
#~ msgid "remove {layout}?"
#~ msgstr "重排序排版"

#, fuzzy
#~ msgid "Maximum {label} limit reached ({max} {identifier})"
#~ msgstr "達到了最大值 ( {max} 值 ) "

#, fuzzy
#~ msgid "Show Field Keys"
#~ msgstr "顯示欄位密鑰："

#, fuzzy
#~ msgid "Private"
#~ msgstr "啟用"

#, fuzzy
#~ msgid "Revision"
#~ msgstr "版本控製"

#, fuzzy
#~ msgid "Field groups are created in order from lowest to highest"
#~ msgstr "欄位群組排序<br />從低到高。"

#, fuzzy
#~ msgid "ACF PRO Required"
#~ msgstr "(必填項)"

#, fuzzy
#~ msgid "Update Database"
#~ msgstr "升級資料庫"

#, fuzzy
#~ msgid "Data Upgrade"
#~ msgstr "升級"

#, fuzzy
#~ msgid "Data is at the latest version."
#~ msgstr "非常感謝你升級外掛到最新版本！"

#~ msgid "Load & Save Terms to Post"
#~ msgstr "加載&儲存條目到文章。"

#~ msgid ""
#~ "Load value based on the post's terms and update the post's terms on save"
#~ msgstr "在文章上加載值，儲存時更新文章條目。"

#, fuzzy
#~ msgid "image"
#~ msgstr "圖像"

#, fuzzy
#~ msgid "relationship"
#~ msgstr "關係"

#, fuzzy
#~ msgid "unload"
#~ msgstr "下載"

#, fuzzy
#~ msgid "title_is_required"
#~ msgstr "欄位群組已發佈。"

#, fuzzy
#~ msgid "move_field"
#~ msgstr "儲存欄位"

#, fuzzy
#~ msgid "flexible_content"
#~ msgstr "大段內容"

#, fuzzy
#~ msgid "gallery"
#~ msgstr "相簿"

#, fuzzy
#~ msgid "repeater"
#~ msgstr "複製"

#~ msgid "Custom field updated."
#~ msgstr "自訂欄位已更新。"

#~ msgid "Custom field deleted."
#~ msgstr "自訂欄位已刪除。"

#~ msgid "Column Width"
#~ msgstr "分欄寬度"

#, fuzzy
#~ msgid "Attachment Details"
#~ msgstr "附件已更新"

#~ msgid "Validation Failed. One or more fields below are required."
#~ msgstr "驗證失敗，下面一個或多個欄位是必需的。"

#~ msgid "Field group restored to revision from %s"
#~ msgstr "欄位群組已恢複到版本%s"

#~ msgid "No ACF groups selected"
#~ msgstr "沒有選擇 ACF 群組"

#~ msgid "Repeater Field"
#~ msgstr "複製欄位"

#~ msgid ""
#~ "Create infinite rows of repeatable data with this versatile interface!"
#~ msgstr "使用此多功能介面為可重覆資料建立無限行列。 "

#~ msgid "Gallery Field"
#~ msgstr "相簿欄位"

#~ msgid "Create image galleries in a simple and intuitive interface!"
#~ msgstr "使用簡單直覺的介面建立相簿！"

#~ msgid "Create global data to use throughout your website!"
#~ msgstr "建立全站可用的資料。"

#~ msgid "Flexible Content Field"
#~ msgstr "多樣內容欄位"

#~ msgid "Create unique designs with a flexible content layout manager!"
#~ msgstr "透過內容排版管理器建立獨特的設計。"

#~ msgid "Gravity Forms Field"
#~ msgstr "Gravity 表單欄位"

#~ msgid "Creates a select field populated with Gravity Forms!"
#~ msgstr "建立一個由Gravity表單處理的選擇欄位。"

#~ msgid "jQuery date & time picker"
#~ msgstr "jQuery 日期 & 時間選擇器"

#~ msgid "Find addresses and coordinates of a desired location"
#~ msgstr "查找需要的位置的地址和坐標。"

#~ msgid "Contact Form 7 Field"
#~ msgstr "Contact Form 7 欄位"

#~ msgid "Assign one or more contact form 7 forms to a post"
#~ msgstr "分配一個或多個contact form 7表單到文章"

#~ msgid "Advanced Custom Fields Add-Ons"
#~ msgstr "自訂欄位附加功能"

#~ msgid ""
#~ "The following Add-ons are available to increase the functionality of the "
#~ "Advanced Custom Fields plugin."
#~ msgstr "下面的附加項可以提高外掛功能。"

#~ msgid ""
#~ "Each Add-on can be installed as a separate plugin (receives updates) or "
#~ "included in your theme (does not receive updates)."
#~ msgstr ""
#~ "每個附件都可以作為一個單獨的外掛安裝（可以獲取更新）或包含在你的主題中（不"
#~ "能獲取更新）"

#~ msgid "Purchase & Install"
#~ msgstr "購買和安裝"

#~ msgid "Select the field groups to be exported"
#~ msgstr "選擇需要匯出的欄位群組。"

#~ msgid "Export to XML"
#~ msgstr "匯出到XML"

#~ msgid "Export to PHP"
#~ msgstr "匯出到PHP"

#~ msgid ""
#~ "ACF will create a .xml export file which is compatible with the native WP "
#~ "import plugin."
#~ msgstr "ACF將建立一個相容於原生 WP 匯入外掛的.xml檔案。"

#~ msgid ""
#~ "Imported field groups <b>will</b> appear in the list of editable field "
#~ "groups. This is useful for migrating fields groups between Wp websites."
#~ msgstr ""
#~ "匯入欄位群組<b>將</b>出現在可編輯欄位群組後面，在幾個 WP 站點之間遷移欄位"
#~ "群組時，這將非常有用。"

#~ msgid "Select field group(s) from the list and click \"Export XML\""
#~ msgstr "從列表中選擇欄位群組，然後點擊 \"匯出XML\" "

#~ msgid "Save the .xml file when prompted"
#~ msgstr "匯出後儲存.xml檔案"

#~ msgid "Navigate to Tools &raquo; Import and select WordPress"
#~ msgstr "進入工具 &raquo; 匯入，然後選擇 WordPress "

#~ msgid "Install WP import plugin if prompted"
#~ msgstr "安裝 WP 匯入外掛後開始"

#~ msgid "Upload and import your exported .xml file"
#~ msgstr "上傳並匯入.xml檔案"

#~ msgid "Select your user and ignore Import Attachments"
#~ msgstr "選擇使用者並忽略匯入附件"

#~ msgid "That's it! Happy WordPressing"
#~ msgstr "成功了，使用愉快！"

#~ msgid "ACF will create the PHP code to include in your theme."
#~ msgstr "ACP將匯出可以包含到主題中的PHP程式碼"

#~ msgid ""
#~ "Registered field groups <b>will not</b> appear in the list of editable "
#~ "field groups. This is useful for including fields in themes."
#~ msgstr ""
#~ "已註冊欄位<b>不會</b>出現在可編輯分組中，這對主題中包含的欄位非常有用。"

#~ msgid ""
#~ "Please note that if you export and register field groups within the same "
#~ "WP, you will see duplicate fields on your edit screens. To fix this, "
#~ "please move the original field group to the trash or remove the code from "
#~ "your functions.php file."
#~ msgstr ""
#~ "請注意，如果在同一個網站匯出並註冊欄位群組，您會在您的編輯屏幕上看到重複的"
#~ "字段，為瞭解決這個問題，請將原欄位群組移動到回收桶或刪除您的functions.php"
#~ "檔案中的程式碼。"

#~ msgid "Select field group(s) from the list and click \"Create PHP\""
#~ msgstr "參加列表中選擇表單組，然後點擊 \"生成PHP\""

#~ msgid "Copy the PHP code generated"
#~ msgstr "複製生成的PHP程式碼。"

#~ msgid "Paste into your functions.php file"
#~ msgstr "請插入您的function.php檔案"

#~ msgid ""
#~ "To activate any Add-ons, edit and use the code in the first few lines."
#~ msgstr "要啟用附加組件，編輯和應用程式碼中的前幾行。"

#~ msgid "Notes"
#~ msgstr "注意"

#~ msgid "Include in theme"
#~ msgstr "包含在主題中"

#~ msgid ""
#~ "The Advanced Custom Fields plugin can be included within a theme. To do "
#~ "so, move the ACF plugin inside your theme and add the following code to "
#~ "your functions.php file:"
#~ msgstr ""
#~ "欄位外掛可以包含到主題中，如果需要進行此操作，請移動欄位外掛到themes檔案夾"
#~ "並新增以下程式碼到functions.php檔案："

#~ msgid ""
#~ "To remove all visual interfaces from the ACF plugin, you can use a "
#~ "constant to enable lite mode. Add the following code to you functions.php "
#~ "file <b>before</b> the include_once code:"
#~ msgstr ""
#~ "要刪除所有ACF外掛的可視化介面，你可以用一個常數，使精簡版模式，將下面的代"
#~ "碼新增到functions.php檔案中include_once程式碼<b>之前</b>。"

#~ msgid "Back to export"
#~ msgstr "返回到匯出器"

#~ msgid ""
#~ "/**\n"
#~ " *  Install Add-ons\n"
#~ " *  \n"
#~ " *  The following code will include all 4 premium Add-Ons in your theme.\n"
#~ " *  Please do not attempt to include a file which does not exist. This "
#~ "will produce an error.\n"
#~ " *  \n"
#~ " *  All fields must be included during the 'acf/register_fields' action.\n"
#~ " *  Other types of Add-ons (like the options page) can be included "
#~ "outside of this action.\n"
#~ " *  \n"
#~ " *  The following code assumes you have a folder 'add-ons' inside your "
#~ "theme.\n"
#~ " *\n"
#~ " *  IMPORTANT\n"
#~ " *  Add-ons may be included in a premium theme as outlined in the terms "
#~ "and conditions.\n"
#~ " *  However, they are NOT to be included in a premium / free plugin.\n"
#~ " *  For more information, please read http://www.advancedcustomfields.com/"
#~ "terms-conditions/\n"
#~ " */"
#~ msgstr ""
#~ "/ **\n"
#~ " *安裝附加組件\n"
#~ " *\n"
#~ " *下面的程式碼將包括所有4個高級附加組件到您的主題\n"
#~ " *請不要試圖包含一個不存在的檔案，這將產生一個錯誤。\n"
#~ " *\n"
#~ " *所有欄位都必須在'acf/register_fields'動作執行時包含。\n"
#~ " *其他類型的加載項（如選項頁）可以包含在這個動作之外。\n"
#~ " *\n"
#~ " *下面的程式碼假定你在你的主題裡面有一個“add-ons”檔案夾。\n"
#~ " *\n"
#~ " *重要\n"
#~ " *附加組件可能在一個高級主題中包含下面的條款及條件。\n"
#~ " *但是，他們都沒有被列入高級或免費外掛。\n"
#~ " *欲瞭解更多信息，請讀取http://www.advancedcustomfields.com/terms-"
#~ "conditions/\n"
#~ " */"

#~ msgid ""
#~ "/**\n"
#~ " *  Register Field Groups\n"
#~ " *\n"
#~ " *  The register_field_group function accepts 1 array which holds the "
#~ "relevant data to register a field group\n"
#~ " *  You may edit the array as you see fit. However, this may result in "
#~ "errors if the array is not compatible with ACF\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " * 註冊欄位群組\n"
#~ " *\n"
#~ " * register_field_group函數接受一個包含註冊欄位群組有關數據的數組\n"
#~ " *您可以編輯您認為合適的數組，然而，如果數組不相容ACF，這可能會導致錯誤\n"
#~ " */"

#~ msgid "Vote"
#~ msgstr "投票"

#~ msgid "Follow"
#~ msgstr "關注"

#~ msgid "Activation codes have grown into plugins!"
#~ msgstr "啟用碼成為了外掛！"

#~ msgid ""
#~ "Add-ons are now activated by downloading and installing individual "
#~ "plugins. Although these plugins will not be hosted on the wordpress.org "
#~ "repository, each Add-on will continue to receive updates in the usual way."
#~ msgstr ""
#~ "附加組件現在通過下載和安裝單獨的外掛啟用，雖然這些外掛不在wordpress.org庫"
#~ "託管，每個附加組件將通過合適的方式得到更新。"

#~ msgid "All previous Add-ons have been successfully installed"
#~ msgstr "所有附加功能已安裝！"

#~ msgid "This website uses premium Add-ons which need to be downloaded"
#~ msgstr "此站點使用的高級功能需要下載。"

#~ msgid "Download your activated Add-ons"
#~ msgstr "下載已啟用的附加功能"

#~ msgid ""
#~ "This website does not use premium Add-ons and will not be affected by "
#~ "this change."
#~ msgstr "此站點未使用高級功能，這個改變沒有影響。"

#~ msgid "Easier Development"
#~ msgstr "快速開發"

#~ msgid "New Field Types"
#~ msgstr "新欄位類型"

#~ msgid "Email Field"
#~ msgstr "電子郵件欄位"

#~ msgid "Password Field"
#~ msgstr "密碼欄位"

#~ msgid "Custom Field Types"
#~ msgstr "自訂欄位類型"

#~ msgid ""
#~ "Creating your own field type has never been easier! Unfortunately, "
#~ "version 3 field types are not compatible with version 4."
#~ msgstr ""
#~ "建立您自己的欄位類型從未如此簡單！不幸的是，版本3的欄位類型不相容版本4。"

#~ msgid "Migrating your field types is easy, please"
#~ msgstr "數據遷移非常簡單，請"

#~ msgid "follow this tutorial"
#~ msgstr "跟隨這個嚮導"

#~ msgid "to learn more."
#~ msgstr "瞭解更多。"

#~ msgid "Actions &amp; Filters"
#~ msgstr "動作&amp;過濾器"

#~ msgid ""
#~ "All actions & filters have recieved a major facelift to make customizing "
#~ "ACF even easier! Please"
#~ msgstr "所有動作和過濾器得到了一次重大改版一遍更方便的定製ACF！請"

#~ msgid "read this guide"
#~ msgstr "閱讀此嚮導"

#~ msgid "to find the updated naming convention."
#~ msgstr "找到更新命名約定。"

#~ msgid "Preview draft is now working!"
#~ msgstr "預覽功能已經可用！"

#~ msgid "This bug has been squashed along with many other little critters!"
#~ msgstr "這個錯誤已經與許多其他小動物一起被壓扁了！"

#~ msgid "See the full changelog"
#~ msgstr "檢視全部更新日誌"

#~ msgid "Database Changes"
#~ msgstr "資料庫改變"

#~ msgid ""
#~ "Absolutely <strong>no</strong> changes have been made to the database "
#~ "between versions 3 and 4. This means you can roll back to version 3 "
#~ "without any issues."
#~ msgstr ""
#~ "資料庫在版本3和4之間<strong>沒有</strong>任何修改，這意味你可以安全回滾到"
#~ "版本3而不會遇到任何問題。"

#~ msgid "Potential Issues"
#~ msgstr "潛在問題"

#~ msgid ""
#~ "Do to the sizable changes surounding Add-ons, field types and action/"
#~ "filters, your website may not operate correctly. It is important that you "
#~ "read the full"
#~ msgstr ""
#~ "需要在附加組件，欄位類型和動作/過濾之間做重大修改時，你可的網站可能會出現"
#~ "一些問題，所有強烈建議閱讀全部"

#~ msgid "Migrating from v3 to v4"
#~ msgstr "從V3遷移到V4"

#~ msgid "guide to view the full list of changes."
#~ msgstr "檢視所有更新列表。"

#~ msgid "Really Important!"
#~ msgstr "非常重要！"

#~ msgid ""
#~ "If you updated the ACF plugin without prior knowledge of such changes, "
#~ "Please roll back to the latest"
#~ msgstr "如果你沒有收到更新通知而升級到了ACF外掛，請回滾到最近的一個版本。"

#~ msgid "version 3"
#~ msgstr "版本 3"

#~ msgid "of this plugin."
#~ msgstr "這個外掛"

#~ msgid "Thank You"
#~ msgstr "謝謝！"

#~ msgid ""
#~ "A <strong>BIG</strong> thank you to everyone who has helped test the "
#~ "version 4 beta and for all the support I have received."
#~ msgstr "非常感謝幫助我測試版本4的所有人。"

#~ msgid "Without you all, this release would not have been possible!"
#~ msgstr "沒有你們，此版本可能還沒有發佈。"

#~ msgid "Changelog for"
#~ msgstr "更新日誌："

#~ msgid "Learn more"
#~ msgstr "瞭解更多"

#~ msgid "Overview"
#~ msgstr "預覽"

#~ msgid ""
#~ "Previously, all Add-ons were unlocked via an activation code (purchased "
#~ "from the ACF Add-ons store). New to v4, all Add-ons act as separate "
#~ "plugins which need to be individually downloaded, installed and updated."
#~ msgstr ""
#~ "在此之前，所有附加組件通過一個啟用碼（從ACF附加組件的商店購買）解鎖，到了"
#~ "版本V4，所有附加組件作為單獨的外掛下載，安裝和更新。"

#~ msgid ""
#~ "This page will assist you in downloading and installing each available "
#~ "Add-on."
#~ msgstr "此頁將幫助您下載和安裝每個可用的附加組件。"

#~ msgid "Available Add-ons"
#~ msgstr "可用附加功能"

#~ msgid ""
#~ "The following Add-ons have been detected as activated on this website."
#~ msgstr "在此網站上檢測到以下附加已啟用。"

#~ msgid "Activation Code"
#~ msgstr "啟用碼"

#~ msgid "Installation"
#~ msgstr "安裝"

#~ msgid "For each Add-on available, please perform the following:"
#~ msgstr "對於每個可以用附加組件，請執行以下操作："

#~ msgid "Download the Add-on plugin (.zip file) to your desktop"
#~ msgstr "下載附加功能（.zip檔案）到電腦。"

#~ msgid "Navigate to"
#~ msgstr "連結到"

#~ msgid "Plugins > Add New > Upload"
#~ msgstr "外掛>新增>上傳"

#~ msgid ""
#~ "Use the uploader to browse, select and install your Add-on (.zip file)"
#~ msgstr "使用檔案上載器，瀏覽，選擇並安裝附加組件（zip檔案）"

#~ msgid ""
#~ "Once the plugin has been uploaded and installed, click the 'Activate "
#~ "Plugin' link"
#~ msgstr "外掛上傳並安裝後，點擊'啟用外掛'連結。"

#~ msgid "The Add-on is now installed and activated!"
#~ msgstr "附加功能已安裝並啟用。"

#~ msgid "Awesome. Let's get to work"
#~ msgstr "太棒了！我們開始吧。"

#~ msgid "Modifying field group options 'show on page'"
#~ msgstr "修改欄位群組選項'在頁面上顯示'"

#~ msgid "Modifying field option 'taxonomy'"
#~ msgstr "修改欄位選項'分類法'"

#~ msgid "Moving user custom fields from wp_options to wp_usermeta'"
#~ msgstr "從wp_options移動會員自訂欄位到wp_usermeta"

#~ msgid "blue : Blue"
#~ msgstr " blue : Blue "

#~ msgid "eg: #ffffff"
#~ msgstr "如: #ffffff "

#~ msgid "Dummy"
#~ msgstr "二進製"

#~ msgid "File Object"
#~ msgstr "檔案對象"

#~ msgid "File Updated."
#~ msgstr "檔案已更新"

#~ msgid "Media attachment updated."
#~ msgstr "媒體附件已更新。"

#~ msgid "Add Selected Files"
#~ msgstr "新增已選擇檔案"

#~ msgid "Image Object"
#~ msgstr "對象圖像"

#~ msgid "Image Updated."
#~ msgstr "圖片已更新"

#~ msgid "No images selected"
#~ msgstr "沒有選擇圖片"

#~ msgid "Add Selected Images"
#~ msgstr "新增所選圖片"

#~ msgid "Text &amp; HTML entered here will appear inline with the fields"
#~ msgstr "在這裡輸入的文本和HTML將和此欄位一起出現。"

#~ msgid "Enter your choices one per line"
#~ msgstr "輸入選項，每行一個"

#~ msgid "Red"
#~ msgstr "紅"

#~ msgid "Blue"
#~ msgstr "藍"

#~ msgid "Post Type Select"
#~ msgstr "文章類型選擇"

#~ msgid "You can use multiple tabs to break up your fields into sections."
#~ msgstr "你可以使用選項卡分割欄位到多個區域。"

#~ msgid "Define how to render html tags"
#~ msgstr "定義怎麼生成html標簽"

#~ msgid "HTML"
#~ msgstr "HTML"

#~ msgid "Define how to render html tags / new lines"
#~ msgstr "定義怎麼處理html標簽和換行"

#~ msgid ""
#~ "This format will determin the value saved to the database and returned "
#~ "via the API"
#~ msgstr "此格式將決定存儲在資料庫中的值，並通過API返回。"

#~ msgid "\"yymmdd\" is the most versatile save format. Read more about"
#~ msgstr "\"yymmdd\" 是最常用的格式，如需瞭解更多，請參考"

#~ msgid "jQuery date formats"
#~ msgstr "jQuery日期格式"

#~ msgid ""
#~ "\"dd/mm/yy\" or \"mm/dd/yy\" are the most used Display Formats. Read more "
#~ "about"
#~ msgstr "\"dd/mm/yy\" 或 \"mm/dd/yy\" 為最常用的顯示格式，瞭解更多"

#~ msgid "Field Order"
#~ msgstr "欄位順序"

#~ msgid "Edit this Field"
#~ msgstr "編輯欄位"

#~ msgid "Docs"
#~ msgstr "文檔"

#~ msgid "Field Instructions"
#~ msgstr "欄位說明"

#~ msgid "Show this field when"
#~ msgstr "符合這些規則中的"

#~ msgid "all"
#~ msgstr "所有"

#~ msgid "any"
#~ msgstr "任一個"

#~ msgid "these rules are met"
#~ msgstr "項時，顯示此欄位"

#~ msgid "Taxonomy Term (Add / Edit)"
#~ msgstr "分類法條目（新增/編輯）"

#~ msgid "Media Attachment (Edit)"
#~ msgstr "媒體附件（編輯）"

#~ msgid "Unlock options add-on with an activation code"
#~ msgstr "使用啟用碼解鎖附加功能"

#~ msgid "Normal"
#~ msgstr "普通"

#~ msgid "No Metabox"
#~ msgstr "無Metabox"

#~ msgid "Add-Ons"
#~ msgstr "附加"

#~ msgid "Just updated to version 4?"
#~ msgstr "剛更新到版本4？"

#~ msgid ""
#~ "Activation codes have changed to plugins! Download your purchased add-ons"
#~ msgstr "啟用碼已改變了外掛，請下載已購買的附加功能。"

#~ msgid "here"
#~ msgstr "這裡"

#~ msgid "match"
#~ msgstr "符合"

#~ msgid "of the above"
#~ msgstr "  "

#~ msgid ""
#~ "Read documentation, learn the functions and find some tips &amp; tricks "
#~ "for your next web project."
#~ msgstr "閱讀文檔，學習功能和發現一些小提示，然後應用到你下一個網站項目中。"

#~ msgid "Visit the ACF website"
#~ msgstr "訪問ACF網站"

#~ msgid "Add File to Field"
#~ msgstr "新增檔案"

#~ msgid "Add Image to Field"
#~ msgstr "新增圖片"

#~ msgid "Repeater field deactivated"
#~ msgstr "檢測到複製欄位"

#~ msgid "Gallery field deactivated"
#~ msgstr "檢測到相簿欄位"

#~ msgid "Repeater field activated"
#~ msgstr "複製外掛已啟用。"

#~ msgid "Options page activated"
#~ msgstr "選項頁面已啟用"

#~ msgid "Flexible Content field activated"
#~ msgstr "多樣內容欄位已啟用"

#~ msgid "Gallery field activated"
#~ msgstr "外掛啟用成功。"

#~ msgid "License key unrecognised"
#~ msgstr "許可密鑰未註冊"

#~ msgid ""
#~ "Add-ons can be unlocked by purchasing a license key. Each key can be used "
#~ "on multiple sites."
#~ msgstr "可以購買一個許可證來啟用附加功能，每個許可證可用於許多站點。"

#~ msgid "Register Field Groups"
#~ msgstr "註冊欄位群組"

#~ msgid "Advanced Custom Fields Settings"
#~ msgstr "高級自動設定"

#~ msgid "requires a database upgrade"
#~ msgstr "資料庫需要升級"

#~ msgid "why?"
#~ msgstr "為什麼？"

#~ msgid "Please"
#~ msgstr "請"

#~ msgid "backup your database"
#~ msgstr "備份資料庫"

#~ msgid "then click"
#~ msgstr "然後點擊"

#~ msgid "No choices to choose from"
#~ msgstr "選擇表單沒有選"

#~ msgid "+ Add Row"
#~ msgstr "新增行"

#~ msgid ""
#~ "No fields. Click the \"+ Add Sub Field button\" to create your first "
#~ "field."
#~ msgstr "沒有欄位，點擊<strong>新增</strong>按鈕建立第一個欄位。"

#~ msgid "Close Sub Field"
#~ msgstr "選擇子欄位"

#~ msgid "+ Add Sub Field"
#~ msgstr "新增子欄位"

#~ msgid "Thumbnail is advised"
#~ msgstr "建設使用縮略圖"

#~ msgid "Image Updated"
#~ msgstr "圖片已更新"

#~ msgid "Grid"
#~ msgstr "柵格"

#~ msgid "List"
#~ msgstr "列表"

#~ msgid "1 image selected"
#~ msgstr "已選擇1張圖片"

#~ msgid "{count} images selected"
#~ msgstr "選擇了 {count}張圖片"

#~ msgid "Added"
#~ msgstr "已新增"

#~ msgid "Image already exists in gallery"
#~ msgstr "圖片已在相簿中"

#~ msgid "Repeater Fields"
#~ msgstr "複製欄位"

#~ msgid "Table (default)"
#~ msgstr "表格（預設）"

#~ msgid "Run filter \"the_content\"?"
#~ msgstr "是否運行過濾器 \"the_content\"?"

#~ msgid "Media (Edit)"
#~ msgstr "媒體（編輯）"
