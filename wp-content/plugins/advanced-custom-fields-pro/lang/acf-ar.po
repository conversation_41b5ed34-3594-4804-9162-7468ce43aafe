# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2022-12-13T15:07:38+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: acf.php:68
msgid "Advanced Custom Fields"
msgstr "الحقول المخصصة المتقدمة"

#: acf.php:342 includes/admin/admin.php:52
msgid "Field Groups"
msgstr "مجموعات الحقول"

#: acf.php:343
msgid "Field Group"
msgstr "مجموعة الحقول"

#: acf.php:344 acf.php:376 includes/admin/admin.php:53
#: pro/fields/class-acf-field-flexible-content.php:558
msgid "Add New"
msgstr "إضافة جديد"

#: acf.php:345
msgid "Add New Field Group"
msgstr "إضافة مجموعة حقول جديدة"

#: acf.php:346
msgid "Edit Field Group"
msgstr "تحرير مجموعة الحقول"

#: acf.php:347
msgid "New Field Group"
msgstr "مجموعة حقول جديدة"

#: acf.php:348
msgid "View Field Group"
msgstr "عرض مجموعة الحقول"

#: acf.php:349
msgid "Search Field Groups"
msgstr "بحث في مجموعات الحقول"

#: acf.php:350
msgid "No Field Groups found"
msgstr "لم يتم العثور على نتائج"

#: acf.php:351
msgid "No Field Groups found in Trash"
msgstr "لا توجد مجموعات حقول في سلة المهملات"

#: acf.php:374 includes/admin/admin-field-group.php:220
#: includes/admin/admin-field-groups.php:530
#: pro/fields/class-acf-field-clone.php:811
msgid "Fields"
msgstr "حقول"

#: acf.php:375
msgid "Field"
msgstr "حقل"

#: acf.php:377
msgid "Add New Field"
msgstr "إضافة حقل جديد"

#: acf.php:378
msgid "Edit Field"
msgstr "تحرير الحقل"

#: acf.php:379 includes/admin/views/field-group-fields.php:41
msgid "New Field"
msgstr "حقل جديد"

#: acf.php:380
msgid "View Field"
msgstr "عرض الحقل"

#: acf.php:381
msgid "Search Fields"
msgstr "بحث في الحقول"

#: acf.php:382
msgid "No Fields found"
msgstr "لم يتم العثور على أية حقول"

#: acf.php:383
msgid "No Fields found in Trash"
msgstr "لم يتم العثور على أية حقول في سلة المهملات"

#: acf.php:418 includes/admin/admin-field-group.php:402
#: includes/admin/admin-field-groups.php:587
msgid "Inactive"
msgstr "غير نشط"

#: acf.php:423
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "غير نشطة <span class=\"count\">(%s)</span>"
msgstr[1] "غير نشط <span class=\"count\">(%s)</span>"
msgstr[2] "غير نشطة <span class=\"count\">(%s)</span>"
msgstr[3] "غير نشطة <span class=\"count\">(%s)</span>"
msgstr[4] "غير نشطة <span class=\"count\">(%s)</span>"
msgstr[5] "غير نشطة <span class=\"count\">(%s)</span>"

#: includes/acf-field-functions.php:831
#: includes/admin/admin-field-group.php:178
msgid "(no label)"
msgstr "(بدون عنوان)"

#: includes/acf-field-group-functions.php:819
#: includes/admin/admin-field-group.php:180
msgid "copy"
msgstr "نسخ"

#: includes/admin/admin-field-group.php:86
#: includes/admin/admin-field-group.php:87
#: includes/admin/admin-field-group.php:89
msgid "Field group updated."
msgstr "تم تحديث مجموعة الحقول."

#: includes/admin/admin-field-group.php:88
msgid "Field group deleted."
msgstr "تم حذف مجموعة الحقول."

#: includes/admin/admin-field-group.php:91
msgid "Field group published."
msgstr "تم نشر مجموعة الحقول."

#: includes/admin/admin-field-group.php:92
msgid "Field group saved."
msgstr "تم حفظ مجموعة الحقول."

#: includes/admin/admin-field-group.php:93
msgid "Field group submitted."
msgstr "تم تقديم مجموعة الحقول."

#: includes/admin/admin-field-group.php:94
msgid "Field group scheduled for."
msgstr "تم جدولة مجموعة الحقول لـ."

#: includes/admin/admin-field-group.php:95
msgid "Field group draft updated."
msgstr "تم تحديث مسودة مجموعة الحقول."

#: includes/admin/admin-field-group.php:171
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "لا يجوز استخدام المقطع \"field_\" في بداية اسم الحقل"

#: includes/admin/admin-field-group.php:172
msgid "This field cannot be moved until its changes have been saved"
msgstr "لا يمكن نقل هذا الحقل حتى يتم حفظ تغييراته"

#: includes/admin/admin-field-group.php:173
msgid "Field group title is required"
msgstr "عنوان مجموعة الحقول مطلوب"

#: includes/admin/admin-field-group.php:174
msgid "Move to trash. Are you sure?"
msgstr "ارسال إلى سلة المهملات. هل أنت متأكد؟"

#: includes/admin/admin-field-group.php:175
msgid "No toggle fields available"
msgstr "تبديل الحقول غير متوفر"

#: includes/admin/admin-field-group.php:176
msgid "Move Custom Field"
msgstr "نقل الحقل المخصص"

#: includes/admin/admin-field-group.php:177
msgid "Checked"
msgstr "مفحوص"

#: includes/admin/admin-field-group.php:179
msgid "(this field)"
msgstr "(هذا الحقل)"

#: includes/admin/admin-field-group.php:181
#: includes/admin/views/field-group-field-conditional-logic.php:51
#: includes/admin/views/field-group-field-conditional-logic.php:151
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3665
msgid "or"
msgstr "او"

#: includes/admin/admin-field-group.php:182
msgid "Null"
msgstr "لا شيء"

#: includes/admin/admin-field-group.php:221
msgid "Location"
msgstr "الموقع"

#: includes/admin/admin-field-group.php:222
#: includes/admin/tools/class-acf-admin-tool-export.php:295
msgid "Settings"
msgstr "الإعدادات"

#: includes/admin/admin-field-group.php:372
msgid "Field Keys"
msgstr "مفاتيح الحقل"

#: includes/admin/admin-field-group.php:402
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "نشط"

#: includes/admin/admin-field-group.php:763
msgid "Move Complete."
msgstr "تم النقل."

#: includes/admin/admin-field-group.php:764
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "الحقل %s يمكن الآن إيجاده في مجموعة الحقول %s"

#: includes/admin/admin-field-group.php:765
msgid "Close Window"
msgstr "إغلاق النافذة"

#: includes/admin/admin-field-group.php:806
msgid "Please select the destination for this field"
msgstr "الرجاء تحديد الوجهة لهذا الحقل"

#: includes/admin/admin-field-group.php:813
msgid "Move Field"
msgstr "نقل الحقل"

#: includes/admin/admin-field-groups.php:89
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "نشط <span class=\"count\">(%s)</span>"
msgstr[1] "نشط <span class=\"count\">(%s)</span>"
msgstr[2] "نشطة <span class=\"count\">(%s)</span>"
msgstr[3] "نشطة <span class=\"count\">(%s)</span>"
msgstr[4] "نشطة <span class=\"count\">(%s)</span>"
msgstr[5] "نشطة <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:156
#, php-format
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "تم تكرار مجموعة الحقول %s."
msgstr[1] "تم تكرار مجموعة الحقول %s."
msgstr[2] "تم تكرار مجموعة الحقول %s."
msgstr[3] "تم تكرار مجموعة الحقول %s."
msgstr[4] "تم تكرار مجموعة الحقول %s."
msgstr[5] "تم تكرار مجموعة الحقول %s."

#: includes/admin/admin-field-groups.php:243
#, php-format
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "تمت مزامنة مجموعة الحقول %s."
msgstr[1] "تمت مزامنة مجموعة الحقول %s."
msgstr[2] "تمت مزامنة مجموعة الحقول %s."
msgstr[3] "تمت مزامنة مجموعة الحقول %s."
msgstr[4] "تمت مزامنة مجموعة الحقول %s."
msgstr[5] "تمت مزامنة مجموعة الحقول %s."

#: includes/admin/admin-field-groups.php:414
#: includes/admin/admin-field-groups.php:577
msgid "Sync available"
msgstr "المزامنة متوفرة"

#: includes/admin/admin-field-groups.php:527 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:353
msgid "Title"
msgstr "العنوان"

#: includes/admin/admin-field-groups.php:528
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
#: pro/fields/class-acf-field-gallery.php:380
msgid "Description"
msgstr "الوصف"

#: includes/admin/admin-field-groups.php:529
msgid "Status"
msgstr "الحالة"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:626
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "خصص ووردبرس بحقول قوية، مهنية، وبديهية‪."

#: includes/admin/admin-field-groups.php:628 includes/admin/admin.php:126
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "سجل التغييرات"

#: includes/admin/admin-field-groups.php:633
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "اطلع على الجديد في <a href=\"%s\">النسخة %s</a>."

#: includes/admin/admin-field-groups.php:636
msgid "Resources"
msgstr "الموارد"

#: includes/admin/admin-field-groups.php:638
msgid "Website"
msgstr "الموقع الإليكتروني"

#: includes/admin/admin-field-groups.php:639
msgid "Documentation"
msgstr "التوثيق"

#: includes/admin/admin-field-groups.php:640
msgid "Support"
msgstr "الدعم"

#: includes/admin/admin-field-groups.php:642
#: includes/admin/views/settings-info.php:81
msgid "Pro"
msgstr "احترافي"

#: includes/admin/admin-field-groups.php:647
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "شكرا لك لاستخدامك <a href=\"%s\">ACF</a>."

#: includes/admin/admin-field-groups.php:686
msgid "Duplicate this item"
msgstr "تكرار هذا العنصر"

#: includes/admin/admin-field-groups.php:686
#: includes/admin/admin-field-groups.php:702
#: includes/admin/views/field-group-field.php:46
#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Duplicate"
msgstr "تكرار"

#: includes/admin/admin-field-groups.php:719
#: includes/fields/class-acf-field-google-map.php:146
#: includes/fields/class-acf-field-relationship.php:587
msgid "Search"
msgstr "بحث"

#: includes/admin/admin-field-groups.php:778
#, php-format
msgid "Select %s"
msgstr "اختيار %s"

#: includes/admin/admin-field-groups.php:786
msgid "Synchronise field group"
msgstr "مزامنة مجموعة الحقول"

#: includes/admin/admin-field-groups.php:786
#: includes/admin/admin-field-groups.php:816
msgid "Sync"
msgstr "مزامنة"

#: includes/admin/admin-field-groups.php:798
msgid "Apply"
msgstr "تطبيق"

#: includes/admin/admin-field-groups.php:816
msgid "Bulk Actions"
msgstr "اجراءات جماعية"

#: includes/admin/admin-tools.php:116
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "أدوات"

#: includes/admin/admin-upgrade.php:49 includes/admin/admin-upgrade.php:111
#: includes/admin/admin-upgrade.php:112 includes/admin/admin-upgrade.php:175
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "ترقية قاعدة البيانات"

#: includes/admin/admin-upgrade.php:199
msgid "Review sites & upgrade"
msgstr "استعراض المواقع والترقية"

#: includes/admin/admin.php:51 includes/admin/views/field-group-options.php:110
msgid "Custom Fields"
msgstr "الحقول المخصصة"

#: includes/admin/admin.php:57
msgid "Info"
msgstr "معلومات"

#: includes/admin/admin.php:125
msgid "What's New"
msgstr "ما الجديد"

#: includes/admin/tools/class-acf-admin-tool-export.php:33
msgid "Export Field Groups"
msgstr "تصدير مجموعات الحقول"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:342
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "توليد PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:97
#: includes/admin/tools/class-acf-admin-tool-export.php:135
msgid "No field groups selected"
msgstr "لم يتم تحديد مجموعات الحقول"

#: includes/admin/tools/class-acf-admin-tool-export.php:174
#, php-format
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "تم تصدير مجموعة حقول واحدة."
msgstr[1] "تم تصدير مجموعة حقول واحدة"
msgstr[2] "تم تصدير مجموعتي حقول"
msgstr[3] "تم تصدير %s مجموعات حقول"
msgstr[4] "تم تصدير %s مجموعات حقول"
msgstr[5] "تم تصدير %s مجموعات حقول"

#: includes/admin/tools/class-acf-admin-tool-export.php:241
#: includes/admin/tools/class-acf-admin-tool-export.php:269
msgid "Select Field Groups"
msgstr "حدد مجموعات الحقول"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"حدد مجموعات الحقول التي ترغب في تصديرها ومن ثم حدد طريقة التصدير. استخدام زر "
"التحميل للتصدير إلى ملف .json الذي يمكنك من ثم استيراده إلى تثبيت ACF آخر. "
"استخدم زر التوليد للتصدير بصيغة PHP الذي يمكنك ادراجه في القالب الخاص بك."

#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr "تصدير الملف"

#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"يمكن استخدام الكود التالي لتسجيل نسخة محلية من مجموعة الحقول المحددة. مجموعة "
"الحقول المحلية يمكن أن توفر العديد من المزايا مثل التحميل بشكل أسرع، والتحكم "
"في الإصدار والإعدادات والحقول الديناميكية. ببساطة أنسخ وألصق الكود التالي "
"إلى ملف functions.php بالقالب الخاص بك أو إدراجه ضمن ملف خارجي."

#: includes/admin/tools/class-acf-admin-tool-export.php:446
msgid "Copy to clipboard"
msgstr "نسخ إلى الحافظة"

#: includes/admin/tools/class-acf-admin-tool-export.php:483
msgid "Copied"
msgstr "تم النسخ"

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "استيراد مجموعات الحقول"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"حدد ملف JSON الذي ترغب في استيراده. عند النقر على زر استيراد أدناه، ACF "
"ستقوم باستيراد مجموعات الحقول."

#: includes/admin/tools/class-acf-admin-tool-import.php:52
#: includes/fields/class-acf-field-file.php:57
msgid "Select File"
msgstr "إختر ملف"

#: includes/admin/tools/class-acf-admin-tool-import.php:62
msgid "Import File"
msgstr "استيراد ملف"

#: includes/admin/tools/class-acf-admin-tool-import.php:85
#: includes/fields/class-acf-field-file.php:169
msgid "No file selected"
msgstr "لم يتم إختيار ملف"

#: includes/admin/tools/class-acf-admin-tool-import.php:93
msgid "Error uploading file. Please try again"
msgstr "خطأ في تحميل الملف . حاول مرة أخرى"

#: includes/admin/tools/class-acf-admin-tool-import.php:98
msgid "Incorrect file type"
msgstr "نوع الملف غير صحيح"

#: includes/admin/tools/class-acf-admin-tool-import.php:107
msgid "Import file empty"
msgstr "الملف المستورد فارغ"

#: includes/admin/tools/class-acf-admin-tool-import.php:138
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "تم استيراد مجموعة حقول واحدة"
msgstr[1] "تم استيراد مجموعة حقول واحدة"
msgstr[2] "تم استيراد مجموعتي حقول"
msgstr[3] "تم استيراد %s مجموعات حقول"
msgstr[4] "تم استيراد %s مجموعات حقول"
msgstr[5] "تم استيراد %s مجموعات حقول"

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "المنطق الشرطي"

#: includes/admin/views/field-group-field-conditional-logic.php:51
msgid "Show this field if"
msgstr "إظهار هذا الحقل إذا"

#: includes/admin/views/field-group-field-conditional-logic.php:138
#: includes/admin/views/html-location-rule.php:86
msgid "and"
msgstr "و"

#: includes/admin/views/field-group-field-conditional-logic.php:153
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "إضافة مجموعة قاعدة"

#: includes/admin/views/field-group-field.php:38
#: pro/fields/class-acf-field-flexible-content.php:410
#: pro/fields/class-acf-field-repeater.php:299
msgid "Drag to reorder"
msgstr "اسحب لإعادة الترتيب"

#: includes/admin/views/field-group-field.php:42
#: includes/admin/views/field-group-field.php:45
msgid "Edit field"
msgstr "تحرير الحقل"

#: includes/admin/views/field-group-field.php:45
#: includes/fields/class-acf-field-file.php:151
#: includes/fields/class-acf-field-image.php:132
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:337
msgid "Edit"
msgstr "تحرير"

#: includes/admin/views/field-group-field.php:46
msgid "Duplicate field"
msgstr "تكرار الحقل"

#: includes/admin/views/field-group-field.php:47
msgid "Move field to another group"
msgstr "نقل الحقل إلى مجموعة أخرى"

#: includes/admin/views/field-group-field.php:47
msgid "Move"
msgstr "نقل"

#: includes/admin/views/field-group-field.php:48
msgid "Delete field"
msgstr "حذف الحقل"

#: includes/admin/views/field-group-field.php:48
#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Delete"
msgstr "حذف"

#: includes/admin/views/field-group-field.php:65
msgid "Field Label"
msgstr "تسمية الحقل"

#: includes/admin/views/field-group-field.php:66
msgid "This is the name which will appear on the EDIT page"
msgstr "هذا هو الاسم الذي سيظهر في صفحة التحرير"

#: includes/admin/views/field-group-field.php:75
msgid "Field Name"
msgstr "اسم الحقل"

#: includes/admin/views/field-group-field.php:76
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "كلمة واحدة، بدون مسافات. مسموح بالشرطات والشرطات السفلية"

#: includes/admin/views/field-group-field.php:85
msgid "Field Type"
msgstr "نوع الحقل"

#: includes/admin/views/field-group-field.php:96
msgid "Instructions"
msgstr "التعليمات"

#: includes/admin/views/field-group-field.php:97
msgid "Instructions for authors. Shown when submitting data"
msgstr "تعليمات للمؤلفين. سيظهر عند إرسال البيانات"

#: includes/admin/views/field-group-field.php:106
msgid "Required?"
msgstr "مطلوب؟"

#: includes/admin/views/field-group-field.php:129
msgid "Wrapper Attributes"
msgstr "سمات المجمع"

#: includes/admin/views/field-group-field.php:135
msgid "width"
msgstr "العرض"

#: includes/admin/views/field-group-field.php:150
msgid "class"
msgstr "class (الفئة)"

#: includes/admin/views/field-group-field.php:163
msgid "id"
msgstr "id (المعرف)"

#: includes/admin/views/field-group-field.php:175
msgid "Close Field"
msgstr "أغلق الحقل"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "ترتيب"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:420
#: includes/fields/class-acf-field-radio.php:311
#: includes/fields/class-acf-field-select.php:433
#: pro/fields/class-acf-field-flexible-content.php:582
msgid "Label"
msgstr "تسمية"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:936
#: pro/fields/class-acf-field-flexible-content.php:596
msgid "Name"
msgstr "الاسم"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "المفتاح"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "النوع"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr "لا توجد حقول. انقر على زر <strong>+ إضافة حقل</strong> لإنشاء أول حقل."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ اضف حقل"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "القواعد"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"إنشىء مجموعة من القواعد لتحديد أي شاشات التحرير ستستخدم هذه الحقول المخصصة"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "نمط"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "قياسي (WP metabox)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "سلس (بدون metabox)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "الموضع"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "عالي (بعد العنوان)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "عادي (بعد المحتوى)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "الجانب"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "تعيين مكان التسمية"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:106
msgid "Top aligned"
msgstr "محاذاة إلى الأعلى"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:107
msgid "Left aligned"
msgstr "محاذاة لليسار"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "تعيين مكان التعليمات"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "أسفل التسميات"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "بعد الحقول"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "رقم الترتيب."

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "مجموعات الحقول ذات الترتيب الأدنى ستظهر أولا"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "اظهار في قائمة مجموعة الحقول"

#: includes/admin/views/field-group-options.php:107
msgid "Permalink"
msgstr "الرابط الدائم"

#: includes/admin/views/field-group-options.php:108
msgid "Content Editor"
msgstr "محرر المحتوى"

#: includes/admin/views/field-group-options.php:109
msgid "Excerpt"
msgstr "مختصر الموضوع"

#: includes/admin/views/field-group-options.php:111
msgid "Discussion"
msgstr "النقاش"

#: includes/admin/views/field-group-options.php:112
msgid "Comments"
msgstr "التعليقات"

#: includes/admin/views/field-group-options.php:113
msgid "Revisions"
msgstr "المراجعة"

#: includes/admin/views/field-group-options.php:114
msgid "Slug"
msgstr "الاسم اللطيف"

#: includes/admin/views/field-group-options.php:115
msgid "Author"
msgstr "الكاتب"

#: includes/admin/views/field-group-options.php:116
msgid "Format"
msgstr "الشكل"

#: includes/admin/views/field-group-options.php:117
msgid "Page Attributes"
msgstr "سمات الصفحة"

#: includes/admin/views/field-group-options.php:118
#: includes/fields/class-acf-field-relationship.php:601
msgid "Featured Image"
msgstr "صورة بارزة"

#: includes/admin/views/field-group-options.php:119
msgid "Categories"
msgstr "التصنيفات"

#: includes/admin/views/field-group-options.php:120
msgid "Tags"
msgstr "الوسوم"

#: includes/admin/views/field-group-options.php:121
msgid "Send Trackbacks"
msgstr "إرسال Trackbacks"

#: includes/admin/views/field-group-options.php:128
msgid "Hide on screen"
msgstr "إخفاء على الشاشة"

#: includes/admin/views/field-group-options.php:129
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>تحديد</b> العناصر <b>لإخفائها</b> من شاشة التحرير."

#: includes/admin/views/field-group-options.php:129
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"إذا ظهرت مجموعات حقول متعددة في شاشة التحرير. سيتم استخدام خيارات المجموعة "
"الأولى (تلك التي تحتوي على أقل رقم ترتيب)"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"تتطلب المواقع التالية ترقية قاعدة البيانات. تحقق من تلك التي تحتاج إلى "
"ترقيتها ومن ثم انقر على %s."

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:92
msgid "Upgrade Sites"
msgstr "ترقية المواقع"

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "الموقع"

#: includes/admin/views/html-admin-page-upgrade-network.php:74
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "يتطلب الموقع ترقية قاعدة البيانات من %s إلى %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:76
msgid "Site is up to date"
msgstr "الموقع محدث"

#: includes/admin/views/html-admin-page-upgrade-network.php:93
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"تمت ترقية قاعدة البيانات. <a href=\"%s\">العودة إلى لوحة معلومات الشبكة</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:113
msgid "Please select at least one site to upgrade."
msgstr "يرجى تحديد موقع واحد على الأقل للترقية."

#: includes/admin/views/html-admin-page-upgrade-network.php:117
#: includes/admin/views/html-notice-upgrade.php:38
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"يوصى بشدة أن تقوم بأخذ نسخة احتياطية من قاعدة البيانات قبل المتابعة. هل أنت "
"متأكد أنك ترغب في تشغيل التحديث الآن؟"

#: includes/admin/views/html-admin-page-upgrade-network.php:144
#: includes/admin/views/html-admin-page-upgrade.php:31
#, php-format
msgid "Upgrading data to version %s"
msgstr "ترقية البيانات إلى الإصدار %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:158
msgid "Upgrade complete."
msgstr "اكتملت عملية الترقية."

#: includes/admin/views/html-admin-page-upgrade-network.php:161
#: includes/admin/views/html-admin-page-upgrade.php:65
msgid "Upgrade failed."
msgstr "فشلت الترقية."

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "قراءة مهام الترقية..."

#: includes/admin/views/html-admin-page-upgrade.php:33
#, php-format
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "تمت ترقية قاعدة البيانات. <a href=\"%s\">اطلع على الجديد</a>"

#: includes/admin/views/html-admin-page-upgrade.php:94
#: includes/ajax/class-acf-ajax-upgrade.php:32
msgid "No updates available."
msgstr "لا توجد تحديثات متوفرة."

#: includes/admin/views/html-admin-tools.php:21
msgid "Back to all tools"
msgstr "العودة إلى جميع الأدوات"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "إظهار هذه المجموعة إذا"

#: includes/admin/views/html-notice-upgrade.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "المكرر"

#: includes/admin/views/html-notice-upgrade.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "المحتوى المرن"

#: includes/admin/views/html-notice-upgrade.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "الالبوم"

#: includes/admin/views/html-notice-upgrade.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "خيارات الصفحة"

#: includes/admin/views/html-notice-upgrade.php:21
msgid "Database Upgrade Required"
msgstr "ترقية قاعدة البيانات مطلوبة"

#: includes/admin/views/html-notice-upgrade.php:22
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "شكرا لك على تحديث %s إلى الإصدار %s!"

#: includes/admin/views/html-notice-upgrade.php:22
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr "يحتوي هذا الإصدار على تحسينات لقاعدة البيانات الخاصة بك ويتطلب ترقية."

#: includes/admin/views/html-notice-upgrade.php:24
#, php-format
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr "يرجى أيضا التأكد من تحديث أي إضافات مدفوعة (%s) أولا إلى أحدث إصدار."

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "مرحبا بك في الحقول المخصصة المتقدمة"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"شكرا لك للتحديث! ACF %s أكبر وأفضل من أي وقت مضى. نأمل أن تنال إعجابكم."

#: includes/admin/views/settings-info.php:15
msgid "A Smoother Experience"
msgstr "تجربة أكثر سلاسة"

#: includes/admin/views/settings-info.php:18
msgid "Improved Usability"
msgstr "تحسين قابلية الاستخدام"

#: includes/admin/views/settings-info.php:19
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"دمج مكتبة Select2 حسن قابلية الاستخدام والسرعة عبر عدد من أنواع الحقول بما "
"في ذلك موضوع المنشور، رابط الصفحة، التصنيف والتحديد."

#: includes/admin/views/settings-info.php:22
msgid "Improved Design"
msgstr "تصميم محسّن"

#: includes/admin/views/settings-info.php:23
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"شهدت العديد من الحقول تحديث مرئي جعل ACF تبدو أفضل من أي وقت مضى! تلاحظ "
"التغييرات في المعرض، العلاقة وحقول oEmbed (جديد)!"

#: includes/admin/views/settings-info.php:26
msgid "Improved Data"
msgstr "بيانات محسّنة"

#: includes/admin/views/settings-info.php:27
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"إعادة تصميم هيكل البيانات سمحت للحقول الفرعية للعمل بشكل مستقل عن الحقول "
"الأصلية. هذا يسمح لك بسحب وافلات الحقول داخل وخارج الحقول الأصلية!"

#: includes/admin/views/settings-info.php:35
msgid "Goodbye Add-ons. Hello PRO"
msgstr "وداعا للوظائف الإضافية. مرحبا برو"

#: includes/admin/views/settings-info.php:38
msgid "Introducing ACF PRO"
msgstr "نقدم ACF برو"

#: includes/admin/views/settings-info.php:39
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr "نحن نغير الطريقة التي يتم بها تقديم الأداء المتميز بطريقة مثيرة!"

#: includes/admin/views/settings-info.php:40
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"تم دمج الإضافات المدفوعة الأربعة في <a href=\"%s\">النسخة الإحترافية من ACF</"
"a>. مع توفير رخص شخصية واخرى للمطورين، لتصبح الوظائف المميزة بأسعار معقولة "
"ويمكن الوصول إليها أكثر من أي وقت مضى!"

#: includes/admin/views/settings-info.php:44
msgid "Powerful Features"
msgstr "ميزات قوية"

#: includes/admin/views/settings-info.php:45
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"يحتوي ACF PRO على ميزات قوية مثل البيانات القابلة للتكرار، والمحتوى المرن، "
"وحقل المعرض الجميل والقدرة على إنشاء صفحات خيارات إضافية للمشرفين!"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "اقرأ المزيد حول <a href=\"%s\">ميزات ACF PRO</a>."

#: includes/admin/views/settings-info.php:50
msgid "Easy Upgrading"
msgstr "ترقية سهلة"

#: includes/admin/views/settings-info.php:51
msgid ""
"Upgrading to ACF PRO is easy. Simply purchase a license online and download "
"the plugin!"
msgstr ""
"الترقية إلى ACF PRO سهلة. ببساطة اشتري ترخيص عبر الإنترنت وحمّل الإضافة!"

#: includes/admin/views/settings-info.php:52
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>."
msgstr ""
"نحن كتبنا أيضا <a href=\"%s\">دليل للتحديث</a> للرد على أية أسئلة، ولكن إذا "
"كان إذا كان لديك اي سؤال، الرجاء الاتصال بفريق الدعم الخاص بنا عن طريق <a "
"href=\"%s\">مكتب المساعدة</a>."

#: includes/admin/views/settings-info.php:61
msgid "New Features"
msgstr "ميزات جديدة"

#: includes/admin/views/settings-info.php:66
msgid "Link Field"
msgstr "حقل الارتباط"

#: includes/admin/views/settings-info.php:67
msgid ""
"The Link field provides a simple way to select or define a link (url, title, "
"target)."
msgstr ""
"يوفر حقل الارتباط طريقة بسيطة لتحديد او تعريف رابط (عنوان url ، العنوان ، "
"الهدف)."

#: includes/admin/views/settings-info.php:71
msgid "Group Field"
msgstr "حقل المجموعة"

#: includes/admin/views/settings-info.php:72
msgid "The Group field provides a simple way to create a group of fields."
msgstr "يوفر حقل المجموعة طريقة بسيطة لإنشاء مجموعة من الحقول."

#: includes/admin/views/settings-info.php:76
msgid "oEmbed Field"
msgstr "حقل تضمين oEmbed"

#: includes/admin/views/settings-info.php:77
msgid ""
"The oEmbed field allows an easy way to embed videos, images, tweets, audio, "
"and other content."
msgstr ""
"يتيح حقل oEmbed طريقة سهلة لتضمين مقاطع الفيديو والصور والتغريدات والصوت "
"والمحتويات الأخرى."

#: includes/admin/views/settings-info.php:81
msgid "Clone Field"
msgstr "حقل التكرار"

#: includes/admin/views/settings-info.php:82
msgid "The clone field allows you to select and display existing fields."
msgstr "يسمح لك حقل التكرار تحديد الحقول الموجودة وعرضها."

#: includes/admin/views/settings-info.php:86
msgid "More AJAX"
msgstr "اجاكس أكثر"

#: includes/admin/views/settings-info.php:87
msgid "More fields use AJAX powered search to speed up page loading."
msgstr "حقول اكثر تستخدم بحث أجاكس لتسريع تحميل الصفحة."

#: includes/admin/views/settings-info.php:91
msgid "Local JSON"
msgstr "JSON محلي"

#: includes/admin/views/settings-info.php:92
msgid ""
"New auto export to JSON feature improves speed and allows for syncronisation."
msgstr ""
"يعمل التصدير التلقائي الجديد إلى JSON على تحسين السرعة ويسمح بالمزامنة."

#: includes/admin/views/settings-info.php:96
msgid "Easy Import / Export"
msgstr "سهولة الاستيراد / التصدير"

#: includes/admin/views/settings-info.php:97
msgid "Both import and export can easily be done through a new tools page."
msgstr "يمكن إجراء الاستيراد والتصدير بسهولة من خلال صفحة الأدوات الجديدة."

#: includes/admin/views/settings-info.php:101
msgid "New Form Locations"
msgstr "نموذج جديد للمواقع"

#: includes/admin/views/settings-info.php:102
msgid ""
"Fields can now be mapped to menus, menu items, comments, widgets and all "
"user forms!"
msgstr ""
"يمكن الآن تعيين الحقول للقوائم وعناصر القائمة والتعليقات والودجات وجميع "
"نماذج المستخدم!"

#: includes/admin/views/settings-info.php:106
msgid "More Customization"
msgstr "المزيد من التخصيص"

#: includes/admin/views/settings-info.php:107
msgid ""
"New PHP (and JS) actions and filters have been added to allow for more "
"customization."
msgstr "تمت إضافة إجراءات وفلاتر PHP (و JS) جديدة للسماح بمزيد من التخصيص."

#: includes/admin/views/settings-info.php:111
msgid "Fresh UI"
msgstr "واجهة مستخدم جديدة"

#: includes/admin/views/settings-info.php:112
msgid ""
"The entire plugin has had a design refresh including new field types, "
"settings and design!"
msgstr ""
"تحتوي الإضافة على تحديث كامل للتصميم بما في ذلك أنواع حقول جديدة والإعدادات "
"والتصميم!"

#: includes/admin/views/settings-info.php:116
msgid "New Settings"
msgstr "إعدادات جديدة"

#: includes/admin/views/settings-info.php:117
msgid ""
"Field group settings have been added for Active, Label Placement, "
"Instructions Placement and Description."
msgstr ""
"تمت إضافة إعدادات مجموعة الحقول للتنشيط و تعيين مكان التسمية و تعيين مكان "
"التعليمات والوصف."

#: includes/admin/views/settings-info.php:121
msgid "Better Front End Forms"
msgstr "نماذج افضل"

#: includes/admin/views/settings-info.php:122
msgid ""
"acf_form() can now create a new post on submission with lots of new settings."
msgstr ""
"يمكن الآن لـ acf_form() إنشاء مشاركة جديدة عند الإرسال مع الكثير من "
"الإعدادات الجديدة."

#: includes/admin/views/settings-info.php:126
msgid "Better Validation"
msgstr "تحقق افضل"

#: includes/admin/views/settings-info.php:127
msgid "Form validation is now done via PHP + AJAX in favour of only JS."
msgstr ""
"يتم الآن التحقق من صحة النموذج عن طريق PHP + AJAX بدلا من جافا سكريبت فقط."

#: includes/admin/views/settings-info.php:131
msgid "Moving Fields"
msgstr "نقل الحقول"

#: includes/admin/views/settings-info.php:132
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents."
msgstr "يمكن الان نقل الحقل بين المجموعات و المجموعات الأصلية."

#: includes/admin/views/settings-info.php:143
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "نعتقد أنك ستحب هذه التغييرات في %s."

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "غير صالح"

#: includes/api/api-helpers.php:827
msgid "Thumbnail"
msgstr "الصورة المصغرة"

#: includes/api/api-helpers.php:828
msgid "Medium"
msgstr "متوسط"

#: includes/api/api-helpers.php:829
msgid "Large"
msgstr "كبير"

#: includes/api/api-helpers.php:878
msgid "Full Size"
msgstr "العرض الكامل"

#: includes/api/api-helpers.php:1615 includes/api/api-term.php:147
#: pro/fields/class-acf-field-clone.php:996
msgid "(no title)"
msgstr "(بدون عنوان)"

#: includes/api/api-helpers.php:3586
#, php-format
msgid "Image width must be at least %dpx."
msgstr "يجب أن يكون عرض الصورة على الأقل %dpx."

#: includes/api/api-helpers.php:3591
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "يجب إلا يتجاوز عرض الصورة %dpx."

#: includes/api/api-helpers.php:3607
#, php-format
msgid "Image height must be at least %dpx."
msgstr "يجب أن يكون ارتفاع الصورة على الأقل %dpx."

#: includes/api/api-helpers.php:3612
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "يجب إلا يتجاوز ارتفاع الصورة %dpx."

#: includes/api/api-helpers.php:3630
#, php-format
msgid "File size must be at least %s."
msgstr "يجب إلا يقل حجم الملف عن %s."

#: includes/api/api-helpers.php:3635
#, php-format
msgid "File size must must not exceed %s."
msgstr "حجم الملف يجب يجب أن لا يتجاوز %s."

#: includes/api/api-helpers.php:3669
#, php-format
msgid "File type must be %s."
msgstr "يجب أن يكون نوع الملف %s."

#: includes/assets.php:184
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "سيتم فقدان التغييرات التي أجريتها إذا غادرت هذه الصفحة"

#: includes/assets.php:187 includes/fields/class-acf-field-select.php:259
msgctxt "verb"
msgid "Select"
msgstr "اختار"

#: includes/assets.php:188
msgctxt "verb"
msgid "Edit"
msgstr "تحرير"

#: includes/assets.php:189
msgctxt "verb"
msgid "Update"
msgstr "تحديث"

#: includes/assets.php:190
msgid "Uploaded to this post"
msgstr "مرفوع الى هذه المقالة"

#: includes/assets.php:191
msgid "Expand Details"
msgstr "توسيع التفاصيل"

#: includes/assets.php:192
msgid "Collapse Details"
msgstr "طي التفاصيل"

#: includes/assets.php:193
msgid "Restricted"
msgstr "محظور"

#: includes/assets.php:194 includes/fields/class-acf-field-image.php:66
msgid "All images"
msgstr "جميع الصور"

#: includes/assets.php:197
msgid "Validation successful"
msgstr "عملية التحقق تمت بنجاح"

#: includes/assets.php:198 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "فشل في عملية التحقق"

#: includes/assets.php:199
msgid "1 field requires attention"
msgstr "حقل واحد يتطلب الاهتمام"

#: includes/assets.php:200
#, php-format
msgid "%d fields require attention"
msgstr "%d حقول تتطلب الاهتمام"

#: includes/assets.php:203
msgid "Are you sure?"
msgstr "هل أنت متأكد؟"

#: includes/assets.php:204 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "نعم"

#: includes/assets.php:205 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:174
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "لا"

#: includes/assets.php:206 includes/fields/class-acf-field-file.php:153
#: includes/fields/class-acf-field-image.php:134
#: includes/fields/class-acf-field-link.php:140
#: pro/fields/class-acf-field-gallery.php:338
#: pro/fields/class-acf-field-gallery.php:478
msgid "Remove"
msgstr "ازالة"

#: includes/assets.php:207
msgid "Cancel"
msgstr "الغاء"

#: includes/assets.php:210
msgid "Has any value"
msgstr "له أي قيمة"

#: includes/assets.php:211
msgid "Has no value"
msgstr "ليس له قيمة"

#: includes/assets.php:212
msgid "Value is equal to"
msgstr "القيمة تساوي"

#: includes/assets.php:213
msgid "Value is not equal to"
msgstr "القيمة لا تساوي"

#: includes/assets.php:214
msgid "Value matches pattern"
msgstr "تتطابق القيمة مع النمط"

#: includes/assets.php:215
msgid "Value contains"
msgstr "تحتوي القيمة على"

#: includes/assets.php:216
msgid "Value is greater than"
msgstr "القيمة أكبر من"

#: includes/assets.php:217
msgid "Value is less than"
msgstr "القيمة أقل من"

#: includes/assets.php:218
msgid "Selection is greater than"
msgstr "التحديد أكبر من"

#: includes/assets.php:219
msgid "Selection is less than"
msgstr "التحديد أقل من"

#: includes/assets.php:222 includes/forms/form-comment.php:166
#: pro/admin/admin-options-page.php:325
msgid "Edit field group"
msgstr "تحرير مجموعة الحقول"

#: includes/fields.php:308
msgid "Field type does not exist"
msgstr "نوع الحقل غير موجود"

#: includes/fields.php:308
msgid "Unknown"
msgstr "غير معروف"

#: includes/fields.php:349
msgid "Basic"
msgstr "أساسية"

#: includes/fields.php:350 includes/forms/form-front.php:47
msgid "Content"
msgstr "المحتوى"

#: includes/fields.php:351
msgid "Choice"
msgstr "خيار"

#: includes/fields.php:352
msgid "Relational"
msgstr "ذو علاقة"

#: includes/fields.php:353
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:354 includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:389
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:290
#: pro/fields/class-acf-field-clone.php:843
#: pro/fields/class-acf-field-flexible-content.php:553
#: pro/fields/class-acf-field-flexible-content.php:602
#: pro/fields/class-acf-field-repeater.php:448
msgid "Layout"
msgstr "المخطط"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "الأكورديون"

#: includes/fields/class-acf-field-accordion.php:99
msgid "Open"
msgstr "فتح"

#: includes/fields/class-acf-field-accordion.php:100
msgid "Display this accordion as open on page load."
msgstr "اعرض هذا الأكورديون على أنه مفتوح عند تحميل الصفحة."

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr "توسع متعدد"

#: includes/fields/class-acf-field-accordion.php:110
msgid "Allow this accordion to open without closing others."
msgstr "اسمح بفتح الأكورديون دون إغلاق الآخرين."

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr "نقطة النهاية"

#: includes/fields/class-acf-field-accordion.php:120
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr "حدد نقطة نهاية لإيقاف الأكورديون السابق. هذا الأكورديون لن يكون مرئيًا."

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "مجموعة ازرار"

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:364
msgid "Choices"
msgstr "خيارات"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "Enter each choice on a new line."
msgstr "أدخل كل خيار في سطر جديد."

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "For more control, you may specify both a value and label like this:"
msgstr "لمزيد من التحكم، يمكنك تحديد كل من القيمة والتسمية كما يلي:"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "red : Red"
msgstr "أحمر : أحمر"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:382
#: includes/fields/class-acf-field-taxonomy.php:781
#: includes/fields/class-acf-field-user.php:63
msgid "Allow Null?"
msgstr "السماح بالفارغ؟"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:380
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:281
#: includes/fields/class-acf-field-range.php:155
#: includes/fields/class-acf-field-select.php:373
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:381
msgid "Default Value"
msgstr "قيمة إفتراضية"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:282
#: includes/fields/class-acf-field-range.php:156
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:382
msgid "Appears when creating a new post"
msgstr "يظهر عند إنشاء مقالة جديدة"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:396
#: includes/fields/class-acf-field-radio.php:297
msgid "Horizontal"
msgstr "أفقي"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:395
#: includes/fields/class-acf-field-radio.php:296
msgid "Vertical"
msgstr "عمودي"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:413
#: includes/fields/class-acf-field-file.php:214
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:304
#: includes/fields/class-acf-field-taxonomy.php:826
msgid "Return Value"
msgstr "القيمة المرجعة"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:305
msgid "Specify the returned value on front end"
msgstr "حدد القيمة التي سيتم إرجاعها في الواجهة الأمامية"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:419
#: includes/fields/class-acf-field-radio.php:310
#: includes/fields/class-acf-field-select.php:432
msgid "Value"
msgstr "القيمة"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:421
#: includes/fields/class-acf-field-radio.php:312
#: includes/fields/class-acf-field-select.php:434
msgid "Both (Array)"
msgstr "كلاهما (Array)"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:768
msgid "Checkbox"
msgstr "مربع اختيار"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "تبديل الكل"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr "إضافة اختيار جديد"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "اسمح بالتخصيص"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr "السماح بإضافة قيم \"مخصصة\""

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "حفظ المخصص"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr "حفظ القيم \"المخصصة\" لخيارات الحقل"

#: includes/fields/class-acf-field-checkbox.php:381
#: includes/fields/class-acf-field-select.php:374
msgid "Enter each default value on a new line"
msgstr "ادخل كل قيمة افتراضية في سطر جديد"

#: includes/fields/class-acf-field-checkbox.php:403
msgid "Toggle"
msgstr "تبديل"

#: includes/fields/class-acf-field-checkbox.php:404
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "أضف مربع اختيار إضافي في البداية لتبديل جميع الخيارات"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "محدد اللون"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "مسح"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "الافتراضي"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "اختر اللون"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "اللون الحالي"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "عنصر إختيار التاريخ"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "تم"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "اليوم"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "التالي"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "السابق"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "اسبوع"

#: includes/fields/class-acf-field-date_picker.php:178
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "تنسيق العرض"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_time_picker.php:184
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "تنسيق العرض عند تحرير المقال"

#: includes/fields/class-acf-field-date_picker.php:187
#: includes/fields/class-acf-field-date_picker.php:218
#: includes/fields/class-acf-field-date_time_picker.php:193
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr "مخصص:"

#: includes/fields/class-acf-field-date_picker.php:197
msgid "Save Format"
msgstr "حفظ التنسيق"

#: includes/fields/class-acf-field-date_picker.php:198
msgid "The format used when saving a value"
msgstr "التنسيق المستخدم عند حفظ القيمة"

#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-image.php:195
#: includes/fields/class-acf-field-post_object.php:431
#: includes/fields/class-acf-field-relationship.php:628
#: includes/fields/class-acf-field-select.php:427
#: includes/fields/class-acf-field-time_picker.php:124
#: includes/fields/class-acf-field-user.php:79
#: pro/fields/class-acf-field-gallery.php:557
msgid "Return Format"
msgstr "التنسيق المسترجع"

#: includes/fields/class-acf-field-date_picker.php:209
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "التنسيق المسترجع عن طريق وظائف القالب"

#: includes/fields/class-acf-field-date_picker.php:227
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "يبدأ الأسبوع في"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "اختيار التاريخ والوقت"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "اختر الوقت"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "الوقت"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "الساعة"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "الدقيقة"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "الثانية"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "ميلي ثانية"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "ميكرو ثانية"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "المنطقة الزمنية"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "الان"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "تم"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "اختر"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "صباحا"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "ص"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "مساء"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "م"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "البريد الإلكتروني"

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:104
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "نص الـ placeholder"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:105
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "سيظهر داخل مربع الإدخال"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:194
#: includes/fields/class-acf-field-text.php:113
msgid "Prepend"
msgstr "بادئة"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:195
#: includes/fields/class-acf-field-text.php:114
msgid "Appears before the input"
msgstr "يظهر قبل الإدخال"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:203
#: includes/fields/class-acf-field-text.php:122
msgid "Append"
msgstr "لاحقة"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:204
#: includes/fields/class-acf-field-text.php:123
msgid "Appears after the input"
msgstr "يظهر بعد الإدخال"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "ملف"

#: includes/fields/class-acf-field-file.php:58
msgid "Edit File"
msgstr "تعديل الملف"

#: includes/fields/class-acf-field-file.php:59
msgid "Update File"
msgstr "تحديث الملف"

#: includes/fields/class-acf-field-file.php:141
msgid "File name"
msgstr "إسم الملف"

#: includes/fields/class-acf-field-file.php:145
#: includes/fields/class-acf-field-file.php:247
#: includes/fields/class-acf-field-file.php:258
#: includes/fields/class-acf-field-image.php:255
#: includes/fields/class-acf-field-image.php:284
#: pro/fields/class-acf-field-gallery.php:642
#: pro/fields/class-acf-field-gallery.php:671
msgid "File size"
msgstr "حجم الملف"

#: includes/fields/class-acf-field-file.php:169
msgid "Add File"
msgstr "إضافة ملف"

#: includes/fields/class-acf-field-file.php:220
msgid "File Array"
msgstr "مصفوفة الملف"

#: includes/fields/class-acf-field-file.php:221
msgid "File URL"
msgstr "رابط الملف URL"

#: includes/fields/class-acf-field-file.php:222
msgid "File ID"
msgstr "معرف الملف"

#: includes/fields/class-acf-field-file.php:229
#: includes/fields/class-acf-field-image.php:220
#: pro/fields/class-acf-field-gallery.php:592
msgid "Library"
msgstr "المكتبة"

#: includes/fields/class-acf-field-file.php:230
#: includes/fields/class-acf-field-image.php:221
#: pro/fields/class-acf-field-gallery.php:593
msgid "Limit the media library choice"
msgstr "الحد من اختيار مكتبة الوسائط"

#: includes/fields/class-acf-field-file.php:235
#: includes/fields/class-acf-field-image.php:226
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:72
#: includes/locations/class-acf-location-user-role.php:88
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:598
#: pro/locations/class-acf-location-block.php:79
msgid "All"
msgstr "الكل"

#: includes/fields/class-acf-field-file.php:236
#: includes/fields/class-acf-field-image.php:227
#: pro/fields/class-acf-field-gallery.php:599
msgid "Uploaded to post"
msgstr "مرفوع الى المقالة"

#: includes/fields/class-acf-field-file.php:243
#: includes/fields/class-acf-field-image.php:234
#: pro/fields/class-acf-field-gallery.php:621
msgid "Minimum"
msgstr "الحد الأدنى"

#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-file.php:255
msgid "Restrict which files can be uploaded"
msgstr "تقييد الملفات التي يمكن رفعها"

#: includes/fields/class-acf-field-file.php:254
#: includes/fields/class-acf-field-image.php:263
#: pro/fields/class-acf-field-gallery.php:650
msgid "Maximum"
msgstr "الحد الأقصى"

#: includes/fields/class-acf-field-file.php:265
#: includes/fields/class-acf-field-image.php:292
#: pro/fields/class-acf-field-gallery.php:678
msgid "Allowed file types"
msgstr "أنواع الملفات المسموح بها"

#: includes/fields/class-acf-field-file.php:266
#: includes/fields/class-acf-field-image.php:293
#: pro/fields/class-acf-field-gallery.php:679
msgid "Comma separated list. Leave blank for all types"
msgstr "قائمة مفصولة بفواصل. اترك المساحة فارغة للسماح بالكل"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "خرائط جوجل"

#: includes/fields/class-acf-field-google-map.php:59
msgid "Sorry, this browser does not support geolocation"
msgstr "عذراً، هذا المتصفح لا يدعم تحديد الموقع الجغرافي"

#: includes/fields/class-acf-field-google-map.php:147
msgid "Clear location"
msgstr "مسح الموقع"

#: includes/fields/class-acf-field-google-map.php:148
msgid "Find current location"
msgstr "البحث عن الموقع الحالي"

#: includes/fields/class-acf-field-google-map.php:151
msgid "Search for address..."
msgstr "البحث عن عنوان..."

#: includes/fields/class-acf-field-google-map.php:181
#: includes/fields/class-acf-field-google-map.php:192
msgid "Center"
msgstr "منتصف"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:193
msgid "Center the initial map"
msgstr "مركز الخريطة الأولي"

#: includes/fields/class-acf-field-google-map.php:204
msgid "Zoom"
msgstr "تكبير"

#: includes/fields/class-acf-field-google-map.php:205
msgid "Set the initial zoom level"
msgstr "ضبط مستوى التكبير"

#: includes/fields/class-acf-field-google-map.php:214
#: includes/fields/class-acf-field-image.php:246
#: includes/fields/class-acf-field-image.php:275
#: includes/fields/class-acf-field-oembed.php:268
#: pro/fields/class-acf-field-gallery.php:633
#: pro/fields/class-acf-field-gallery.php:662
msgid "Height"
msgstr "الإرتفاع"

#: includes/fields/class-acf-field-google-map.php:215
msgid "Customize the map height"
msgstr "تخصيص ارتفاع الخريطة"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "مجموعة"

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:384
msgid "Sub Fields"
msgstr "الحقول الفرعية"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:844
msgid "Specify the style used to render the selected fields"
msgstr "حدد النمط المستخدم لعرض الحقول المحددة"

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:849
#: pro/fields/class-acf-field-flexible-content.php:613
#: pro/fields/class-acf-field-repeater.php:456
#: pro/locations/class-acf-location-block.php:27
msgid "Block"
msgstr "كتلة"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:850
#: pro/fields/class-acf-field-flexible-content.php:612
#: pro/fields/class-acf-field-repeater.php:455
msgid "Table"
msgstr "جدول"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:851
#: pro/fields/class-acf-field-flexible-content.php:614
#: pro/fields/class-acf-field-repeater.php:457
msgid "Row"
msgstr "صف"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "صورة"

#: includes/fields/class-acf-field-image.php:63
msgid "Select Image"
msgstr "إختر صورة"

#: includes/fields/class-acf-field-image.php:64
msgid "Edit Image"
msgstr "تحرير الصورة"

#: includes/fields/class-acf-field-image.php:65
msgid "Update Image"
msgstr "تحديث الصورة"

#: includes/fields/class-acf-field-image.php:149
msgid "No image selected"
msgstr "لم يتم اختيار صورة"

#: includes/fields/class-acf-field-image.php:149
msgid "Add Image"
msgstr "اضافة صورة"

#: includes/fields/class-acf-field-image.php:201
#: pro/fields/class-acf-field-gallery.php:563
msgid "Image Array"
msgstr "مصفوفة الصور"

#: includes/fields/class-acf-field-image.php:202
#: pro/fields/class-acf-field-gallery.php:564
msgid "Image URL"
msgstr "رابط الصورة"

#: includes/fields/class-acf-field-image.php:203
#: pro/fields/class-acf-field-gallery.php:565
msgid "Image ID"
msgstr "معرف الصورة"

#: includes/fields/class-acf-field-image.php:210
#: pro/fields/class-acf-field-gallery.php:571
msgid "Preview Size"
msgstr "حجم المعاينة"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:264
#: pro/fields/class-acf-field-gallery.php:622
#: pro/fields/class-acf-field-gallery.php:651
msgid "Restrict which images can be uploaded"
msgstr "تقييد الصور التي يمكن رفعها"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:267
#: includes/fields/class-acf-field-oembed.php:257
#: pro/fields/class-acf-field-gallery.php:625
#: pro/fields/class-acf-field-gallery.php:654
msgid "Width"
msgstr "العرض"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "الرابط"

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "إختر رابط"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr "فتح في نافذة / علامة تبويب جديدة"

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "مصفوفة الرابط"

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr "رابط URL"

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "الرسالة"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "سطور جديدة"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "تحكم في طريقة عرض السطور الجديدة"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "إضافة الفقرات تلقائيا"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "اضف  &lt;br&gt; تلقائياً."

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "بدون تنسيق"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "استبعاد كود HTML"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "السماح بعرض كود HTML كنص"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "رقم"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:164
msgid "Minimum Value"
msgstr "قيمة الحد الأدنى"

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:174
msgid "Maximum Value"
msgstr "قيمة الحد الأقصى"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:184
msgid "Step Size"
msgstr "حجم الخطوة"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "يجب أن تكون القيمة رقماً"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "يجب أن تكون القيمة مساوية أو أكبر من  %d"

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "يجب أن تكون القيمة مساوية أو أقل من  %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:216
msgid "Enter URL"
msgstr "قم بإدخال عنوان URL"

#: includes/fields/class-acf-field-oembed.php:254
#: includes/fields/class-acf-field-oembed.php:265
msgid "Embed Size"
msgstr "حجم المضمن"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "رابط الصفحة"

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "الأرشيفات"

#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:267
#: includes/fields/class-acf-field-taxonomy.php:958
msgid "Parent"
msgstr "الأب"

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:383
#: includes/fields/class-acf-field-relationship.php:554
msgid "Filter by Post Type"
msgstr "فرز حسب نوع المقالة"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:391
#: includes/fields/class-acf-field-relationship.php:562
msgid "All post types"
msgstr "كافة أنواع المقالات"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:397
#: includes/fields/class-acf-field-relationship.php:568
msgid "Filter by Taxonomy"
msgstr "تصفية حسب التصنيف"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:405
#: includes/fields/class-acf-field-relationship.php:576
msgid "All taxonomies"
msgstr "كافة التصنيفات"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr "السماح بالعناوين المؤرشفة"

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-select.php:392
#: includes/fields/class-acf-field-user.php:71
msgid "Select multiple values?"
msgstr "تحديد قيم متعددة؟"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "كلمة السر"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:436
#: includes/fields/class-acf-field-relationship.php:633
msgid "Post Object"
msgstr "Post Object"

#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:634
msgid "Post ID"
msgstr "معرف المقال"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "زر الراديو"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "أخرى"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr "إضافة خيار 'آخر' للسماح بقيم مخصصة"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "حفظ الأخرى"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "حفظ القيم الأخرى لخيارات الحقل"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "نطاق"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "علاقة"

#: includes/fields/class-acf-field-relationship.php:62
msgid "Maximum values reached ( {max} values )"
msgstr "وصلت إلى الحد الأقصى للقيم ( {max} قيمة )"

#: includes/fields/class-acf-field-relationship.php:63
msgid "Loading"
msgstr "تحميل"

#: includes/fields/class-acf-field-relationship.php:64
msgid "No matches found"
msgstr "لم يتم العثور على مطابقات"

#: includes/fields/class-acf-field-relationship.php:411
msgid "Select post type"
msgstr "اختر نوع المقال"

#: includes/fields/class-acf-field-relationship.php:420
msgid "Select taxonomy"
msgstr "اختر التصنيف"

#: includes/fields/class-acf-field-relationship.php:476
msgid "Search..."
msgstr "بحث..."

#: includes/fields/class-acf-field-relationship.php:582
msgid "Filters"
msgstr "فرز"

#: includes/fields/class-acf-field-relationship.php:588
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "نوع المقال"

#: includes/fields/class-acf-field-relationship.php:589
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:751
#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy"
msgstr "التصنيف"

#: includes/fields/class-acf-field-relationship.php:596
msgid "Elements"
msgstr "العناصر"

#: includes/fields/class-acf-field-relationship.php:597
msgid "Selected elements will be displayed in each result"
msgstr "سيتم عرض العناصر المحددة في كل نتيجة"

#: includes/fields/class-acf-field-relationship.php:608
msgid "Minimum posts"
msgstr "الحد الأدنى للمقالات"

#: includes/fields/class-acf-field-relationship.php:617
msgid "Maximum posts"
msgstr "الحد الأقصى للمقالات"

#: includes/fields/class-acf-field-relationship.php:721
#: pro/fields/class-acf-field-gallery.php:779
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s يتطلب على الأقل %s تحديد"
msgstr[1] "%s يتطلب على الأقل %s تحديد"
msgstr[2] "%s يتطلب على الأقل %s تحديدان"
msgstr[3] "%s يتطلب على الأقل %s تحديد"
msgstr[4] "%s يتطلب على الأقل %s تحديد"
msgstr[5] "%s يتطلب على الأقل %s تحديد"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:773
msgctxt "noun"
msgid "Select"
msgstr "اختار"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "نتيجة واحدة متاحة، اضغط على زر الإدخال لتحديدها."

#: includes/fields/class-acf-field-select.php:112
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d نتيجة متاحة، استخدم مفاتيح الأسهم للتنقل."

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "لم يتم العثور على مطابقات"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "الرجاء إدخال حرف واحد أو أكثر"

#: includes/fields/class-acf-field-select.php:115
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "الرجاء إدخال %d حرف أو أكثر"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "الرجاء حذف حرف واحد"

#: includes/fields/class-acf-field-select.php:117
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "الرجاء حذف %d حرف"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "يمكنك تحديد عنصر واحد فقط"

#: includes/fields/class-acf-field-select.php:119
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "يمكنك تحديد %d عنصر فقط"

#: includes/fields/class-acf-field-select.php:120
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "تحميل نتائج أكثر&hellip;"

#: includes/fields/class-acf-field-select.php:121
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "بحث &hellip;"

#: includes/fields/class-acf-field-select.php:122
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "عملية التحميل فشلت"

#: includes/fields/class-acf-field-select.php:402
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "واجهة المستخدم الأنيقة"

#: includes/fields/class-acf-field-select.php:412
msgid "Use AJAX to lazy load choices?"
msgstr "استخدام AJAX لخيارات التحميل الكسول؟"

#: includes/fields/class-acf-field-select.php:428
msgid "Specify the value returned"
msgstr "حدد القيمة التي سيتم إرجاعها"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "فاصل"

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "تبويب"

#: includes/fields/class-acf-field-tab.php:102
msgid "Placement"
msgstr "الموضع"

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""
"حدد نقطة نهاية لإيقاف علامات التبويب السابقة. سيؤدي هذا إلى بدء مجموعة جديدة "
"من علامات التبويب."

#: includes/fields/class-acf-field-taxonomy.php:711
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "لا %s"

#: includes/fields/class-acf-field-taxonomy.php:752
msgid "Select the taxonomy to be displayed"
msgstr "حدد التصنيف الذي سيتم عرضه"

#: includes/fields/class-acf-field-taxonomy.php:761
msgid "Appearance"
msgstr "المظهر"

#: includes/fields/class-acf-field-taxonomy.php:762
msgid "Select the appearance of this field"
msgstr "حدد مظهر هذا الحقل"

#: includes/fields/class-acf-field-taxonomy.php:767
msgid "Multiple Values"
msgstr "قيم متعددة"

#: includes/fields/class-acf-field-taxonomy.php:769
msgid "Multi Select"
msgstr "متعددة الاختيار"

#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Single Value"
msgstr "قيمة مفردة"

#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Radio Buttons"
msgstr "ازرار الراديو"

#: includes/fields/class-acf-field-taxonomy.php:796
msgid "Create Terms"
msgstr "إنشاء شروط"

#: includes/fields/class-acf-field-taxonomy.php:797
msgid "Allow new terms to be created whilst editing"
msgstr "السماح بإنشاء شروط جديدة أثناء التحرير"

#: includes/fields/class-acf-field-taxonomy.php:806
msgid "Save Terms"
msgstr "حفظ الشروط"

#: includes/fields/class-acf-field-taxonomy.php:807
msgid "Connect selected terms to the post"
msgstr "وصل الشروط المحددة بالمقالة"

#: includes/fields/class-acf-field-taxonomy.php:816
msgid "Load Terms"
msgstr "تحميل الشروط"

#: includes/fields/class-acf-field-taxonomy.php:817
msgid "Load value from posts terms"
msgstr "تحميل قيمة من شروط المقالة"

#: includes/fields/class-acf-field-taxonomy.php:831
msgid "Term Object"
msgstr "Term Object"

#: includes/fields/class-acf-field-taxonomy.php:832
msgid "Term ID"
msgstr "Term ID"

#: includes/fields/class-acf-field-taxonomy.php:882
#, php-format
msgid "User unable to add new %s"
msgstr "المستخدم غير قادر على إضافة %s جديد"

#: includes/fields/class-acf-field-taxonomy.php:892
#, php-format
msgid "%s already exists"
msgstr "%s موجود بالفعل"

#: includes/fields/class-acf-field-taxonomy.php:924
#, php-format
msgid "%s added"
msgstr "تمت اضافة %s"

#: includes/fields/class-acf-field-taxonomy.php:970
#: includes/locations/class-acf-location-user-form.php:73
msgid "Add"
msgstr "إضافة"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "نص"

#: includes/fields/class-acf-field-text.php:131
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "الحد الأقصى للحروف"

#: includes/fields/class-acf-field-text.php:132
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "اتركه فارغا لبدون حد"

#: includes/fields/class-acf-field-text.php:157
#: includes/fields/class-acf-field-textarea.php:213
#, php-format
msgid "Value must not exceed %d characters"
msgstr "الحد الأقصى للقيمة %d حرف"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "مربع نص"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "صفوف"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "تعيين ارتفاع مربع النص"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "عنصر إختيار الوقت"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "صح / خطأ"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr "عرض النص بجانب مربع الاختيار"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "النص اثناء التفعيل"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "النص المعروض عند التنشيط"

#: includes/fields/class-acf-field-true_false.php:170
msgid "Off Text"
msgstr "النص اثناء عدم التفعيل"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when inactive"
msgstr "النص المعروض عند عدم النشاط"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "الرابط"

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "القيمة يجب أن تكون عنوان رابط صحيح"

#: includes/fields/class-acf-field-user.php:20 includes/locations.php:95
msgid "User"
msgstr "المستخدم"

#: includes/fields/class-acf-field-user.php:51
msgid "Filter by role"
msgstr "فرز بحسب الدور"

#: includes/fields/class-acf-field-user.php:59
msgid "All user roles"
msgstr "جميع رتب المستخدم"

#: includes/fields/class-acf-field-user.php:84
msgid "User Array"
msgstr "مصفوفة المستخدم"

#: includes/fields/class-acf-field-user.php:85
msgid "User Object"
msgstr "User Object"

#: includes/fields/class-acf-field-user.php:86
msgid "User ID"
msgstr "معرف المستخدم"

#: includes/fields/class-acf-field-user.php:334
msgid "Error loading field."
msgstr "خطأ في تحميل الحقل."

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "محرر Wysiwyg"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual"
msgstr "مرئي"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "نص"

#: includes/fields/class-acf-field-wysiwyg.php:337
msgid "Click to initialize TinyMCE"
msgstr "انقر لبدء تهيئة TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:390
msgid "Tabs"
msgstr "علامات التبويب"

#: includes/fields/class-acf-field-wysiwyg.php:395
msgid "Visual & Text"
msgstr "نص و مرئي"

#: includes/fields/class-acf-field-wysiwyg.php:396
msgid "Visual Only"
msgstr "المرئي فقط"

#: includes/fields/class-acf-field-wysiwyg.php:397
msgid "Text Only"
msgstr "النص فقط"

#: includes/fields/class-acf-field-wysiwyg.php:404
msgid "Toolbar"
msgstr "شريط الأدوات"

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Show Media Upload Buttons?"
msgstr "اظهار زر إضافة ملفات الوسائط؟"

#: includes/fields/class-acf-field-wysiwyg.php:429
msgid "Delay initialization?"
msgstr "تأخير التهيئة؟"

#: includes/fields/class-acf-field-wysiwyg.php:430
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "لن يتم تهيئة TinyMCE حتى يتم النقر فوق الحقل"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "التحقق من البريد الإليكتروني"

#: includes/forms/form-front.php:104 pro/fields/class-acf-field-gallery.php:510
#: pro/options-page.php:81
msgid "Update"
msgstr "تحديث"

#: includes/forms/form-front.php:105
msgid "Post updated"
msgstr "تم تحديث المنشور "

#: includes/forms/form-front.php:231
msgid "Spam Detected"
msgstr "تم الكشف عن البريد المزعج"

#: includes/forms/form-user.php:336
#, php-format
msgid "<strong>ERROR</strong>: %s"
msgstr "<strong>خطأ</strong>: %s"

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "مقالة"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "صفحة"

#: includes/locations.php:96
msgid "Forms"
msgstr "نماذج"

#: includes/locations.php:243
msgid "is equal to"
msgstr "يساوي"

#: includes/locations.php:244
msgid "is not equal to"
msgstr "لا يساوي"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "مرفقات"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr "كل صيغ %s"

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "تعليق"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "رتبة المستخدم الحالي"

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "مدير"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "المستخدم الحالي"

#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr "مسجل الدخول"

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr "عرض الواجهة الأمامية"

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr "عرض الواجهة الخلفية"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "عنصر القائمة"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "القائمة"

#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr "مواقع القائمة"

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr "القوائم"

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "أب الصفحة"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "قالب الصفحة"

#: includes/locations/class-acf-location-page-template.php:87
#: includes/locations/class-acf-location-post-template.php:134
msgid "Default Template"
msgstr "قالب افتراضي"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "نوع الصفحة"

#: includes/locations/class-acf-location-page-type.php:146
msgid "Front Page"
msgstr "الصفحة الرئسية"

#: includes/locations/class-acf-location-page-type.php:147
msgid "Posts Page"
msgstr "صفحة المقالات"

#: includes/locations/class-acf-location-page-type.php:148
msgid "Top Level Page (no parent)"
msgstr "أعلى مستوى للصفحة (بدون أب)"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Parent Page (has children)"
msgstr "صفحة أب (لديها فروع)"

#: includes/locations/class-acf-location-page-type.php:150
msgid "Child Page (has parent)"
msgstr "صفحة فرعية (لديها أب)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "تصنيف المقالة"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "تنسيق المقالة"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "حالة المقالة"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "تصنيف المقالة"

#: includes/locations/class-acf-location-post-template.php:27
msgid "Post Template"
msgstr "قالب المقالة"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "نموذج المستخدم"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Add / Edit"
msgstr "إضافة / تعديل"

#: includes/locations/class-acf-location-user-form.php:75
msgid "Register"
msgstr "التسجيل"

#: includes/locations/class-acf-location-user-role.php:22
msgid "User Role"
msgstr "رتبة المستخدم"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "ودجت"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "قيمة %s مطلوبة"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "الحقول المخصصة المتقدمة للمحترفين"

#: pro/admin/admin-options-page.php:198
msgid "Publish"
msgstr "نشر"

#: pro/admin/admin-options-page.php:204
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"لم يتم العثور على أية \"مجموعات حقول مخصصة لصفحة الخيارات هذة. <a href=\"%s"
"\">أنشئ مجموعة حقول مخصصة</a>"

#: pro/admin/admin-updates.php:49
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>خطأ</b>. تعذر الاتصال بخادم التحديث"

#: pro/admin/admin-updates.php:118 pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "تحديثات"

#: pro/admin/admin-updates.php:191
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b> خطأ </ b>. تعذرت مصادقة حزمة التحديث. يرجى التحقق مرة أخرى أو إلغاء "
"تنشيط وإعادة تنشيط ترخيص ACF PRO الخاص بك."

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "تعطيل الترخيص"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "تفعيل الترخيص"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "معلومات الترخيص"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"لتمكين التحديثات، الرجاء إدخال مفتاح الترخيص الخاص بك أدناه. إذا لم يكن لديك "
"مفتاح ترخيص، يرجى الاطلاع على <a href=\"%s\">التفاصيل والتسعير</a>."

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "مفتاح الترخيص"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "معلومات التحديث"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "النسخة الحالية"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "آخر نسخة"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "هنالك تحديث متاح"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "تحديث الاضافة"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr "يرجى إدخال مفتاح الترخيص أعلاه لإلغاء تأمين التحديثات"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "تحقق مرة اخرى"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "إشعار الترقية"

#: pro/blocks.php:36
msgid "Block type name is required."
msgstr "اسم نوع الكتلة مطلوب."

#: pro/blocks.php:43
#, php-format
msgid "Block type \"%s\" is already registered."
msgstr "نوع الكتلة \"%s\" مسجل بالفعل."

#: pro/blocks.php:393
msgid "Switch to Edit"
msgstr "قم بالتبديل للتحرير"

#: pro/blocks.php:394
msgid "Switch to Preview"
msgstr "قم بالتبديل للمعاينة"

#: pro/blocks.php:397
#, php-format
msgid "%s settings"
msgstr "%s الإعدادات"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "تكرار"

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr "حدد حقل واحد أو أكثر ترغب في تكراره"

#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr "عرض"

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr "حدد النمط المستخدم لعرض حقل التكرار"

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr "المجموعة (تعرض الحقول المحددة في مجموعة ضمن هذا الحقل)"

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr "سلس (يستبدل هذا الحقل بالحقول المحددة)"

#: pro/fields/class-acf-field-clone.php:857
#, php-format
msgid "Labels will be displayed as %s"
msgstr "سيتم عرض التسمية كـ %s"

#: pro/fields/class-acf-field-clone.php:860
msgid "Prefix Field Labels"
msgstr "بادئة تسمية الحقول"

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr "سيتم حفظ القيم كـ %s"

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Names"
msgstr "بادئة أسماء الحقول"

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr "حقل غير معروف"

#: pro/fields/class-acf-field-clone.php:1031
msgid "Unknown field group"
msgstr "مجموعة حقول غير معروفة"

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr "جميع الحقول من مجموعة الحقول %s"

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:193
#: pro/fields/class-acf-field-repeater.php:468
msgid "Add Row"
msgstr "إضافة صف"

#: pro/fields/class-acf-field-flexible-content.php:73
#: pro/fields/class-acf-field-flexible-content.php:924
#: pro/fields/class-acf-field-flexible-content.php:1006
msgid "layout"
msgid_plural "layouts"
msgstr[0] "التخطيط"
msgstr[1] "التخطيط"
msgstr[2] "التخطيط"
msgstr[3] "التخطيط"
msgstr[4] "التخطيط"
msgstr[5] "التخطيط"

#: pro/fields/class-acf-field-flexible-content.php:74
msgid "layouts"
msgstr "التخطيطات"

#: pro/fields/class-acf-field-flexible-content.php:77
#: pro/fields/class-acf-field-flexible-content.php:923
#: pro/fields/class-acf-field-flexible-content.php:1005
msgid "This field requires at least {min} {label} {identifier}"
msgstr "يتطلب هذا الحقل على الأقل {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:78
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "يحتوي هذا الحقل حد {max} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:81
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} متاح (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} مطلوب (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "Flexible Content requires at least 1 layout"
msgstr "يتطلب المحتوى المرن تخطيط واحد على الأقل"

#: pro/fields/class-acf-field-flexible-content.php:287
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "انقر فوق الزر \"%s\" أدناه لبدء إنشاء التخطيط الخاص بك"

#: pro/fields/class-acf-field-flexible-content.php:413
msgid "Add layout"
msgstr "إضافة مخطط جديد"

#: pro/fields/class-acf-field-flexible-content.php:414
msgid "Remove layout"
msgstr "إزالة المخطط"

#: pro/fields/class-acf-field-flexible-content.php:415
#: pro/fields/class-acf-field-repeater.php:301
msgid "Click to toggle"
msgstr "انقر للتبديل"

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Reorder Layout"
msgstr "إعادة ترتيب التخطيط"

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Reorder"
msgstr "إعادة ترتيب"

#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Delete Layout"
msgstr "حذف المخطط"

#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Duplicate Layout"
msgstr "تكرار التخطيط"

#: pro/fields/class-acf-field-flexible-content.php:558
msgid "Add New Layout"
msgstr "إضافة مخطط جديد"

#: pro/fields/class-acf-field-flexible-content.php:629
msgid "Min"
msgstr "الحد الأدنى"

#: pro/fields/class-acf-field-flexible-content.php:642
msgid "Max"
msgstr "الحد أقصى"

#: pro/fields/class-acf-field-flexible-content.php:669
#: pro/fields/class-acf-field-repeater.php:464
msgid "Button Label"
msgstr "تسمية الزر"

#: pro/fields/class-acf-field-flexible-content.php:678
msgid "Minimum Layouts"
msgstr "الحد الأدنى للتخطيطات"

#: pro/fields/class-acf-field-flexible-content.php:687
msgid "Maximum Layouts"
msgstr "الحد الأقصى للتخطيطات"

#: pro/fields/class-acf-field-gallery.php:73
msgid "Add Image to Gallery"
msgstr "اضافة صورة للمعرض"

#: pro/fields/class-acf-field-gallery.php:74
msgid "Maximum selection reached"
msgstr "وصلت للحد الأقصى"

#: pro/fields/class-acf-field-gallery.php:322
msgid "Length"
msgstr "الطول"

#: pro/fields/class-acf-field-gallery.php:362
msgid "Caption"
msgstr "كلمات توضيحية"

#: pro/fields/class-acf-field-gallery.php:371
msgid "Alt Text"
msgstr "النص البديل"

#: pro/fields/class-acf-field-gallery.php:487
msgid "Add to gallery"
msgstr "اضافة الى المعرض"

#: pro/fields/class-acf-field-gallery.php:491
msgid "Bulk actions"
msgstr "اجراءات جماعية"

#: pro/fields/class-acf-field-gallery.php:492
msgid "Sort by date uploaded"
msgstr "ترتيب حسب تاريخ الرفع"

#: pro/fields/class-acf-field-gallery.php:493
msgid "Sort by date modified"
msgstr "ترتيب حسب تاريخ التعديل"

#: pro/fields/class-acf-field-gallery.php:494
msgid "Sort by title"
msgstr "ترتيب حسب العنوان"

#: pro/fields/class-acf-field-gallery.php:495
msgid "Reverse current order"
msgstr "عكس الترتيب الحالي"

#: pro/fields/class-acf-field-gallery.php:507
msgid "Close"
msgstr "إغلاق"

#: pro/fields/class-acf-field-gallery.php:580
msgid "Insert"
msgstr "إدراج"

#: pro/fields/class-acf-field-gallery.php:581
msgid "Specify where new attachments are added"
msgstr "حدد مكان إضافة المرفقات الجديدة"

#: pro/fields/class-acf-field-gallery.php:585
msgid "Append to the end"
msgstr "إلحاق بالنهاية"

#: pro/fields/class-acf-field-gallery.php:586
msgid "Prepend to the beginning"
msgstr "إلحاق بالبداية"

#: pro/fields/class-acf-field-gallery.php:605
msgid "Minimum Selection"
msgstr "الحد الأدنى للاختيار"

#: pro/fields/class-acf-field-gallery.php:613
msgid "Maximum Selection"
msgstr "الحد الأقصى للاختيار"

#: pro/fields/class-acf-field-repeater.php:65
#: pro/fields/class-acf-field-repeater.php:661
msgid "Minimum rows reached ({min} rows)"
msgstr "وصلت للحد الأدنى من الصفوف ({min} صف)"

#: pro/fields/class-acf-field-repeater.php:66
msgid "Maximum rows reached ({max} rows)"
msgstr "بلغت الحد الأقصى من الصفوف ({max} صف)"

#: pro/fields/class-acf-field-repeater.php:338
msgid "Add row"
msgstr "إضافة صف"

#: pro/fields/class-acf-field-repeater.php:339
msgid "Remove row"
msgstr "إزالة صف"

#: pro/fields/class-acf-field-repeater.php:417
msgid "Collapsed"
msgstr "طي"

#: pro/fields/class-acf-field-repeater.php:418
msgid "Select a sub field to show when row is collapsed"
msgstr "حدد حقل فرعي للإظهار عند طي الصف"

#: pro/fields/class-acf-field-repeater.php:428
msgid "Minimum Rows"
msgstr "الحد الأدنى من الصفوف"

#: pro/fields/class-acf-field-repeater.php:438
msgid "Maximum Rows"
msgstr "الحد الأقصى من الصفوف"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "لا توجد صفحة خيارات"

#: pro/options-page.php:51
msgid "Options"
msgstr "خيارات"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "تم تحديث الإعدادات"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"لتمكين التحديثات، الرجاء إدخال مفتاح الترخيص الخاص بك على صفحة <a href=\"%s"
"\">التحديثات</a> . إذا لم يكن لديك مفتاح ترخيص، يرجى الاطلاع على <a href=\"%s"
"\">التفاصيل والتسعير</a>."

#: tests/basic/test-blocks.php:114
msgid "Normal"
msgstr "طبيعي"

#: tests/basic/test-blocks.php:115
msgid "Fancy"
msgstr "فاخر"

#. Plugin URI of the plugin/theme
#. Author URI of the plugin/theme
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "إليوت كوندون"

#~ msgid "Parent fields"
#~ msgstr "الحقول الأصلية"

#~ msgid "Sibling fields"
#~ msgstr "الحقول الفرعية"

#, php-format
#~ msgid "%s field group duplicated."
#~ msgid_plural "%s field groups duplicated."
#~ msgstr[0] "تم تكرار مجموعة الحقول. %s"
#~ msgstr[1] "تم تكرار مجموعة الحقول. %s"
#~ msgstr[2] "تم تكرار مجموعة الحقول. %s"
#~ msgstr[3] "تم تكرار مجموعة الحقول. %s"
#~ msgstr[4] "تم تكرار مجموعة الحقول. %s"
#~ msgstr[5] "تم تكرار مجموعة الحقول. %s"

#, php-format
#~ msgid "%s field group synchronised."
#~ msgid_plural "%s field groups synchronised."
#~ msgstr[0] "تمت مزامنة مجموعة الحقول. %s"
#~ msgstr[1] "تمت مزامنة مجموعة الحقول. %s"
#~ msgstr[2] "تمت مزامنة مجموعة الحقول. %s"
#~ msgstr[3] "تمت مزامنة مجموعة الحقول. %s"
#~ msgstr[4] "تمت مزامنة مجموعة الحقول. %s"
#~ msgstr[5] "تمت مزامنة مجموعة الحقول. %s"

#~ msgid "Error validating request"
#~ msgstr "حدث خطأ أثناء التحقق من صحة الطلب"

#~ msgid "Add-ons"
#~ msgstr "الإضافات"

#~ msgid "<b>Error</b>. Could not load add-ons list"
#~ msgstr "<b>خطأ.</b> لا يمكن تحميل قائمة الإضافات"

#~ msgid "Advanced Custom Fields Database Upgrade"
#~ msgstr " ترقية قاعدة بيانات الحقول المخصصة المتقدمة"

#~ msgid ""
#~ "Before you start using the new awesome features, please update your "
#~ "database to the newest version."
#~ msgstr ""
#~ "قبل البدء باستخدام الميزات الجديدة، الرجاء تحديث قاعدة البيانات الخاصة بك "
#~ "إلى الإصدار الأحدث."

#~ msgid "Download & Install"
#~ msgstr "تحميل وتثبيت"

#~ msgid "Installed"
#~ msgstr "تم التثبيت"

#, php-format
#~ msgid ""
#~ "To help make upgrading easy, <a href=\"%s\">login to your store account</"
#~ "a> and claim a free copy of ACF PRO!"
#~ msgstr ""
#~ "للمساعدة في جعل الترقية سهلة، <a href=\"%s\">سجل الدخول إلى حسابك في "
#~ "المتجر</a> واحصل على نسخة مجانية من ACF PRO!"

#~ msgid "Under the Hood"
#~ msgstr "تحت الغطاء"

#~ msgid "Smarter field settings"
#~ msgstr "إعدادات حقول أكثر ذكاء"

#~ msgid "ACF now saves its field settings as individual post objects"
#~ msgstr "ACF الآن يحفظ إعدادات الحقول كـ post object منفصل"

#~ msgid "Better version control"
#~ msgstr "تحكم أفضل في الإصدارات"

#~ msgid ""
#~ "New auto export to JSON feature allows field settings to be version "
#~ "controlled"
#~ msgstr ""
#~ "يسمح التصدير الاتوماتيكي الجديدة إلى JSON لإعدادات الحقول بأن تكون قابلة "
#~ "لتحكم الإصدارات"

#~ msgid "Swapped XML for JSON"
#~ msgstr "استبدال XML بـ JSON"

#~ msgid "Import / Export now uses JSON in favour of XML"
#~ msgstr "الاستيراد والتصدير الآن يستخدم JSON عوضا عن XML"

#~ msgid "New Forms"
#~ msgstr "أشكال جديدة"

#~ msgid "A new field for embedding content has been added"
#~ msgstr "تم إضافة حقل جديد لتضمين المحتوى"

#~ msgid "New Gallery"
#~ msgstr "معرض صور جديد"

#~ msgid "The gallery field has undergone a much needed facelift"
#~ msgstr "شهد حقل المعرض عملية تغيير جذرية"

#~ msgid "Relationship Field"
#~ msgstr "حقل العلاقة"

#~ msgid ""
#~ "New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
#~ msgstr "إعداد جديد لحقل العلاقة خاص بالفلاتر (البحث، نوع المقالة، التصنيف)"

#~ msgid "New archives group in page_link field selection"
#~ msgstr "مجموعة المحفوظات الجديدة في تحديد الحقل page_link"

#~ msgid "Better Options Pages"
#~ msgstr "صفحات خيارات أفضل"

#~ msgid ""
#~ "New functions for options page allow creation of both parent and child "
#~ "menu pages"
#~ msgstr ""
#~ "مهام جديدة لصفحة الخيارات تسمح بإنشاء كل من صفحات القائمة الأصلية والفرعية"

#~ msgid "Export Field Groups to PHP"
#~ msgstr "تصدير مجموعات الحقول لـ PHP"

#~ msgid "Download export file"
#~ msgstr "تنزيل ملف التصدير"

#~ msgid "Generate export code"
#~ msgstr "توليد كود التصدير"

#~ msgid "Import"
#~ msgstr "استيراد"

#~ msgid "Locating"
#~ msgstr "تحديد الموقع"

#~ msgid "Shown when entering data"
#~ msgstr "تظهر عند إدخال البيانات"

#~ msgid "Error."
#~ msgstr "خطأ."

#~ msgid "No embed found for the given URL."
#~ msgstr "لم يتم العثور على تضمين لعنوان URL المحدد."

#~ msgid "Minimum values reached ( {min} values )"
#~ msgstr "تم الوصول الى الحد الأدنى من القيم ( {min} قيمة )"

#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "سيتم عرض حقل علامة التبويب بشكل غير صحيح عند إضافته إلى حقل مكرر بتنسيق "
#~ "جدول أو محتوى مرن"

#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr ""
#~ "استخدم \"حقل علامة التبويب\" لتنظيم أفضل لشاشة التحرير الخاصة بك عن طريق "
#~ "تجميع الحقول معا."

#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "كافة الحقول بعد \"حقل علامة التبويب\" هذة (أو حتى إضافة \"حقل علامة تبويب "
#~ "آخر\") سوف يتم تجميعها معا باستخدام تسمية هذا الحقل كعنوان للتبويب."

#~ msgid "None"
#~ msgstr "لا شيء"

#~ msgid "Taxonomy Term"
#~ msgstr "شروط التصنيف"

#~ msgid "remove {layout}?"
#~ msgstr "إزالة {layout}؟"

#~ msgid "This field requires at least {min} {identifier}"
#~ msgstr "يتطلب هذا الحقل على الأقل {min} {identifier}"

#~ msgid "Maximum {label} limit reached ({max} {identifier})"
#~ msgstr "تم الوصول إلى حد أقصى ({max} {identifier}) لـ {label}"

#~ msgid "http://www.elliotcondon.com/"
#~ msgstr "http://www.elliotcondon.com/"

#~ msgid "Disabled"
#~ msgstr "تعطيل"

#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "تعطيل <span class=\"count\">(%s)</span>"
#~ msgstr[1] "تعطيل <span class=\"count\">(%s)</span>"
#~ msgstr[2] "تعطيل <span class=\"count\">(%s)</span>"
#~ msgstr[3] "تعطيل <span class=\"count\">(%s)</span>"
#~ msgstr[4] "تعطيل <span class=\"count\">(%s)</span>"
#~ msgstr[5] "تعطيل <span class=\"count\">(%s)</span>"

#~ msgid "See what's new in"
#~ msgstr "أنظر ما هو الجديد في"

#~ msgid "version"
#~ msgstr "النسخة"

#~ msgid "Getting Started"
#~ msgstr "بدء العمل"

#~ msgid "Field Types"
#~ msgstr "أنواع بيانات الحقول"

#~ msgid "Functions"
#~ msgstr "الدالات"

#~ msgid "Actions"
#~ msgstr "الإجراءات"

#~ msgid "'How to' guides"
#~ msgstr "'كيف' أدلة"

#~ msgid "Tutorials"
#~ msgstr "الدروس التعليمية"

#~ msgid "Created by"
#~ msgstr "أنشئ بواسطة"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr "<b>تم بنجاح</b> أداة استيراد أضافت  %s جماعات الحقل %s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>تحذير.</b> الكشف عن أداة استيراد مجموعة الحقول  %s موجودة بالفعل، وتم "
#~ "تجاهل  %s"

#~ msgid "Upgrade ACF"
#~ msgstr "ترقية ACF"

#~ msgid "Upgrade"
#~ msgstr "ترقية"

#~ msgid "Error"
#~ msgstr "خطأ"

#~ msgid "Upgrading data to"
#~ msgstr "تحديث البيانات"

#~ msgid "See what's new"
#~ msgstr "أنظر ما هو الجديد في"

#~ msgid "Show a different month"
#~ msgstr "عرض شهر مختلف"

#~ msgid "Return format"
#~ msgstr "إعادة تنسيق"

#~ msgid "uploaded to this post"
#~ msgstr "اضافة للصفحة"

#~ msgid "File Size"
#~ msgstr "حجم الملف"

#~ msgid "No File selected"
#~ msgstr "لا يوجد ملف محدد."

#~ msgid "eg. Show extra content"
#~ msgstr "على سبيل المثال. إظهار محتوى إضافي"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>خطأ في الاتصال</b>. آسف، الرجاء المحاولة مرة أخرى"

#~ msgid "Save Options"
#~ msgstr "حفظ الإعدادات"

#~ msgid "License"
#~ msgstr "الترخيص"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "لللحصول على التحديثات، الرجاء إدخال مفتاح الترخيص الخاص بك أدناه. إذا لم "
#~ "يكن لديك مفتاح ترخيص، الرجاء مراجعة"

#~ msgid "details & pricing"
#~ msgstr "التفاصيل & الأسعار"

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "حقول مخصصة متقدمة برو"
