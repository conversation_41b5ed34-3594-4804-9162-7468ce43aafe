# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2022-12-13T15:07:38+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: cs_CZ\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/api/api-template.php:867
msgid "[ACF shortcode value disabled for preview]"
msgstr ""

#: includes/admin/admin-field-group.php:672
msgid "Close Modal"
msgstr ""

#: includes/admin/admin-field-group.php:162
#: assets/build/js/acf-field-group.js:1304
#: assets/build/js/acf-field-group.js:1468
msgid "Field moved to other group"
msgstr ""

#: includes/admin/admin-field-group.php:161 assets/build/js/acf.js:1397
#: assets/build/js/acf.js:1463
msgid "Close modal"
msgstr ""

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr ""

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr ""

#: includes/fields/class-acf-field-select.php:450
#: includes/fields/class-acf-field-true_false.php:197
msgid "Use a stylized checkbox using select2"
msgstr ""

#: includes/fields/class-acf-field-radio.php:257
msgid "Save Other Choice"
msgstr ""

#: includes/fields/class-acf-field-radio.php:246
msgid "Allow Other Choice"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:446
msgid "Add Toggle All"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:405
msgid "Save Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:394
msgid "Allow Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:144
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Aktualizace"

#: includes/admin/views/html-admin-navigation.php:79
msgid "Advanced Custom Fields logo"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:22
msgid "Save Changes"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:16
msgid "Field Group Title"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:3
msgid "Add title"
msgstr ""

#. translators: %s url to getting started guide
#: includes/admin/views/field-groups-empty.php:20
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""

#: includes/admin/views/field-groups-empty.php:15
msgid "Add Field Group"
msgstr ""

#. translators: %s url to creating a field group page
#: includes/admin/views/field-groups-empty.php:10
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""

#: includes/admin/views/field-groups-empty.php:5
msgid "Add Your First Field Group"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:16
msgid "Upgrade Now"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:11
msgid "Options Pages"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:10
msgid "ACF Blocks"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:4
#: includes/admin/views/html-admin-navigation.php:97
msgid "Unlock Extra Features with ACF PRO"
msgstr ""

#: includes/admin/views/field-group-options.php:242
msgid "Delete Field Group"
msgstr ""

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/field-group-options.php:236
msgid "Created on %1$s at %2$s"
msgstr ""

#: includes/admin/views/field-group-options.php:179
msgid "Group Settings"
msgstr ""

#: includes/admin/views/field-group-options.php:28
msgid "Location Rules"
msgstr ""

#. translators: %s url to field types list
#: includes/admin/views/field-group-fields.php:61
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""

#: includes/admin/views/field-group-fields.php:54
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""

#: includes/admin/views/field-group-fields.php:53
msgid "Add Your First Field"
msgstr ""

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/field-group-fields.php:32
msgid "#"
msgstr ""

#: includes/admin/views/field-group-fields.php:22
#: includes/admin/views/field-group-fields.php:56
#: includes/admin/views/field-group-fields.php:92
#: includes/admin/views/html-admin-form-top.php:21
msgid "Add Field"
msgstr ""

#: includes/admin/views/field-group-field.php:191
#: includes/admin/views/field-group-options.php:40
msgid "Presentation"
msgstr ""

#: includes/admin/views/field-group-field.php:159
msgid "Validation"
msgstr ""

#: includes/admin/views/field-group-field.php:93
msgid "General"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:70
msgid "Import JSON"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:361
msgid "Export Field Groups - Generate PHP"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid "Export As JSON"
msgstr ""

#: includes/admin/admin-field-groups.php:638
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/admin-field-groups.php:585
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/admin-field-groups.php:537
msgid "Deactivate this item"
msgstr ""

#: includes/admin/admin-field-groups.php:533
msgid "Activate this item"
msgstr ""

#: includes/admin/admin-field-group.php:158
#: assets/build/js/acf-field-group.js:2366
#: assets/build/js/acf-field-group.js:2635
msgid "Move field group to trash?"
msgstr ""

#: acf.php:448 includes/admin/admin-field-group.php:353
#: includes/admin/admin-field-groups.php:232
msgctxt "post status"
msgid "Inactive"
msgstr ""

#. Author of the plugin
msgid "WP Engine"
msgstr ""

#: acf.php:506
msgid ""
"Advanced Custom Fields and Advanced Custom Fields should not be active at "
"the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields a Advanced Custom Fields PRO by neměly být aktivní "
"současně. Automaticky jsme deaktivovali Advanced Custom Fields PRO."

#: acf.php:504
msgid ""
"Advanced Custom Fields and Advanced Custom Fields should not be active at "
"the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields a Advanced Custom Fields PRO by neměly být aktivní "
"současně. Automaticky jsme deaktivovali Advanced Custom Fields."

#: includes/acf-value-functions.php:374
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" target=\"_blank"
"\">Learn how to fix this</a>."
msgstr ""
"<strong>%1$s</strong> – Zjistili jsme jedno nebo více volání k načtení "
"hodnot polí ACF před inicializací ACF. Toto není podporováno a může mít za "
"následek chybná nebo chybějící data. <a href=\"%2$s\" target=\"_blank"
"\">Přečtěte si, jak to opravit</a>."

#: includes/fields/class-acf-field-user.php:537
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s musí mít uživatele s rolí %2$s."
msgstr[1] "%1$s musí mít uživatele s jednou z následujících rolí: %2$s"
msgstr[2] "%1$s musí mít uživatele s jednou z následujících rolí: %2$s"

#: includes/fields/class-acf-field-user.php:528
msgid "%1$s must have a valid user ID."
msgstr "%1$s musí mít platné ID uživatele."

#: includes/fields/class-acf-field-user.php:366
msgid "Invalid request."
msgstr "Neplatný požadavek."

#: includes/fields/class-acf-field-select.php:683
msgid "%1$s is not one of %2$s"
msgstr "%1$s není jedním z %2$s"

#: includes/fields/class-acf-field-post_object.php:669
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-post_object.php:653
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must have a valid post ID."
msgstr ""

#: includes/fields/class-acf-field-file.php:472
msgid "%s requires a valid attachment ID."
msgstr ""

#: includes/admin/views/field-group-options.php:205
msgid "Show in REST API"
msgstr "Zobrazit v REST API"

#: includes/fields/class-acf-field-color_picker.php:167
msgid "Enable Transparency"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:186
msgid "RGBA Array"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:96
msgid "RGBA String"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:95
#: includes/fields/class-acf-field-color_picker.php:185
msgid "Hex String"
msgstr ""

#: includes/admin/admin-field-group.php:187
#: assets/build/js/acf-field-group.js:837
#: assets/build/js/acf-field-group.js:933
msgid "Gallery (Pro only)"
msgstr "Galerie (pouze Pro)"

#: includes/admin/admin-field-group.php:186
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:923
msgid "Clone (Pro only)"
msgstr "Klonovat (pouze Pro)"

#: includes/admin/admin-field-group.php:185
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:920
msgid "Flexible Content (Pro only)"
msgstr "Flexibilní obsah (pouze Pro)"

#: includes/admin/admin-field-group.php:184
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:917
msgid "Repeater (Pro only)"
msgstr "Opakovač (pouze Pro)"

#: includes/admin/admin-field-group.php:353
msgctxt "post status"
msgid "Active"
msgstr "Aktivní"

#: includes/fields/class-acf-field-email.php:178
msgid "'%s' is not a valid email address"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:74
msgid "Color value"
msgstr "Hodnota barvy"

#: includes/fields/class-acf-field-color_picker.php:72
msgid "Select default color"
msgstr "Vyberte výchozí barvu"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Clear color"
msgstr "Zrušit barvu"

#: includes/acf-wp-functions.php:87
msgid "Blocks"
msgstr "Bloky"

#: includes/acf-wp-functions.php:83
msgid "Options"
msgstr "Konfigurace"

#: includes/acf-wp-functions.php:79
msgid "Users"
msgstr "Uživatelé"

#: includes/acf-wp-functions.php:75
msgid "Menu items"
msgstr "Položky menu"

#: includes/acf-wp-functions.php:67
msgid "Widgets"
msgstr "Widgety"

#: includes/acf-wp-functions.php:59
msgid "Attachments"
msgstr "Přílohy"

#: includes/acf-wp-functions.php:54
msgid "Taxonomies"
msgstr "Taxonomie"

#: includes/acf-wp-functions.php:41
msgid "Posts"
msgstr "Příspěvky"

#: includes/ajax/class-acf-ajax-local-json-diff.php:68
msgid "JSON field group (newer)"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:64
msgid "Original field group"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:59
msgid "Last updated: %s"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:53
msgid "Sorry, this field group is unavailable for diff comparison."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:43
msgid "Invalid field group ID."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:36
msgid "Invalid field group parameter(s)."
msgstr ""

#: includes/admin/admin-field-groups.php:506
msgid "Awaiting save"
msgstr ""

#: includes/admin/admin-field-groups.php:503
msgid "Saved"
msgstr "Uloženo"

#: includes/admin/admin-field-groups.php:499
msgid "Import"
msgstr "Import"

#: includes/admin/admin-field-groups.php:495
msgid "Review changes"
msgstr "Zkontrolovat změny"

#: includes/admin/admin-field-groups.php:471
msgid "Located in: %s"
msgstr ""

#: includes/admin/admin-field-groups.php:467
msgid "Located in plugin: %s"
msgstr "Nachází se v pluginu: %s"

#: includes/admin/admin-field-groups.php:463
msgid "Located in theme: %s"
msgstr "Nachází se v šabloně: %s"

#: includes/admin/admin-field-groups.php:441
msgid "Various"
msgstr "Různé"

#: includes/admin/admin-field-groups.php:200
#: includes/admin/admin-field-groups.php:565
msgid "Sync changes"
msgstr "Synchronizovat změny"

#: includes/admin/admin-field-groups.php:199
msgid "Loading diff"
msgstr ""

#: includes/admin/admin-field-groups.php:198
msgid "Review local JSON changes"
msgstr ""

#: includes/admin/admin.php:172
msgid "Visit website"
msgstr "Navštívit stránky"

#: includes/admin/admin.php:171
msgid "View details"
msgstr "Zobrazit podrobnosti"

#: includes/admin/admin.php:170
msgid "Version %s"
msgstr "Verze %s"

#: includes/admin/admin.php:169
msgid "Information"
msgstr "Informace"

#: includes/admin/admin.php:160
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""

#: includes/admin/admin.php:156
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the ‘how-tos’ of the ACF world."
msgstr ""

#: includes/admin/admin.php:152
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""

#: includes/admin/admin.php:149
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""

#: includes/admin/admin.php:146 includes/admin/admin.php:148
msgid "Help & Support"
msgstr ""

#: includes/admin/admin.php:137
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""

#: includes/admin/admin.php:134
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""

#: includes/admin/admin.php:132
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""

#: includes/admin/admin.php:129 includes/admin/admin.php:131
msgid "Overview"
msgstr "Přehled"

#: includes/locations.php:36
msgid "Location type \"%s\" is already registered."
msgstr ""

#: includes/locations.php:25
msgid "Class \"%s\" does not exist."
msgstr ""

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Neplatná hodnota."

#: includes/fields/class-acf-field-user.php:361
msgid "Error loading field."
msgstr ""

#: assets/build/js/acf-input.js:2742 assets/build/js/acf-input.js:2811
#: assets/build/js/acf-input.js:2913 assets/build/js/acf-input.js:2987
msgid "Location not found: %s"
msgstr ""

#: includes/forms/form-user.php:353
msgid "<strong>Error</strong>: %s"
msgstr ""

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Uživatelská úroveň"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Komentář"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Formát příspěvku"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Položka nabídky"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Stav příspěvku"

#: includes/acf-wp-functions.php:71
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Nabídky"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Umístění nabídky"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Nabídka"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Taxonomie příspěvku"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Podřazená stránka (má rodiče)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Rodičovská stránka (má potomky)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Stránka nejvyšší úrovně (žádný nadřazený)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Stránka příspěvku"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Hlavní stránka"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Typ stránky"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Prohlížíte backend"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Prohlížíte frontend"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Přihlášen"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Aktuální uživatel"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Šablona stránky"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Registrovat"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Přidat / Editovat"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Uživatelský formulář"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Rodičovská stránka"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super Admin"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Aktuální uživatelská role"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Výchozí šablona"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Šablona příspěvku"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Rubrika příspěvku"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Všechny formáty %s"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Příloha"

#: includes/validation.php:365
msgid "%s value is required"
msgstr "%s hodnota je vyžadována"

#: includes/admin/views/field-group-field-conditional-logic.php:59
msgid "Show this field if"
msgstr "Zobrazit toto pole, pokud"

#: includes/admin/views/field-group-field-conditional-logic.php:26
#: includes/admin/views/field-group-field.php:279
msgid "Conditional Logic"
msgstr "Podmíněná logika"

#: includes/admin/admin.php:207
#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/html-location-rule.php:92
msgid "and"
msgstr "a"

#: includes/admin/admin-field-groups.php:290
msgid "Local JSON"
msgstr "Lokální JSON"

#: includes/admin/views/settings-info.php:84
msgid "Clone Field"
msgstr "Klonovat pole"

#: includes/admin/views/html-notice-upgrade.php:30
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""

#: includes/admin/views/html-notice-upgrade.php:28
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr "Tato verze obsahuje vylepšení databáze a vyžaduje upgrade."

#: includes/admin/views/html-notice-upgrade.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr ""

#: includes/admin/views/html-notice-upgrade.php:27
msgid "Database Upgrade Required"
msgstr "Vyžadován upgrade databáze"

#: includes/admin/views/html-notice-upgrade.php:18
msgid "Options Page"
msgstr "Stránka konfigurace"

#: includes/admin/views/html-notice-upgrade.php:15
msgid "Gallery"
msgstr "Galerie"

#: includes/admin/views/html-notice-upgrade.php:12
msgid "Flexible Content"
msgstr "Flexibilní obsah"

#: includes/admin/views/html-notice-upgrade.php:9
msgid "Repeater"
msgstr "Opakovač"

#: includes/admin/views/html-admin-tools.php:24
msgid "Back to all tools"
msgstr ""

#: includes/admin/views/field-group-options.php:160
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Pokud se na obrazovce úprav objeví více skupin polí, použije se nastavení "
"dle první skupiny polí (té s nejnižším pořadovým číslem)"

#: includes/admin/views/field-group-options.php:160
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Zvolte</b> položky, které budou na obrazovce úprav <b>skryté</b>."

#: includes/admin/views/field-group-options.php:159
msgid "Hide on screen"
msgstr "Skrýt na obrazovce"

#: includes/admin/views/field-group-options.php:151
msgid "Send Trackbacks"
msgstr "Odesílat zpětné linkování odkazů"

#: includes/admin/views/field-group-options.php:150
msgid "Tags"
msgstr "Štítky"

#: includes/admin/views/field-group-options.php:149
msgid "Categories"
msgstr "Kategorie"

#: includes/admin/views/field-group-options.php:147
msgid "Page Attributes"
msgstr "Atributy stránky"

#: includes/admin/views/field-group-options.php:146
msgid "Format"
msgstr "Formát"

#: includes/admin/views/field-group-options.php:145
msgid "Author"
msgstr "Autor"

#: includes/admin/views/field-group-options.php:144
msgid "Slug"
msgstr "Adresa"

#: includes/admin/views/field-group-options.php:143
msgid "Revisions"
msgstr "Revize"

#: includes/acf-wp-functions.php:63
#: includes/admin/views/field-group-options.php:142
msgid "Comments"
msgstr "Komentáře"

#: includes/admin/views/field-group-options.php:141
msgid "Discussion"
msgstr "Diskuze"

#: includes/admin/views/field-group-options.php:139
msgid "Excerpt"
msgstr "Stručný výpis"

#: includes/admin/views/field-group-options.php:138
msgid "Content Editor"
msgstr "Editor obsahu"

#: includes/admin/views/field-group-options.php:137
msgid "Permalink"
msgstr "Trvalý odkaz"

#: includes/admin/views/field-group-options.php:222
msgid "Shown in field group list"
msgstr "Zobrazit v seznamu skupin polí"

#: includes/admin/views/field-group-options.php:122
msgid "Field groups with a lower order will appear first"
msgstr "Skupiny polí s nižším pořadím se zobrazí první"

#: includes/admin/views/field-group-options.php:121
msgid "Order No."
msgstr "Pořadové č."

#: includes/admin/views/field-group-options.php:112
msgid "Below fields"
msgstr "Pod poli"

#: includes/admin/views/field-group-options.php:111
msgid "Below labels"
msgstr "Pod štítky"

#: includes/admin/views/field-group-options.php:104
msgid "Instruction placement"
msgstr "Umístění instrukcí"

#: includes/admin/views/field-group-options.php:87
msgid "Label placement"
msgstr "Umístění štítků"

#: includes/admin/views/field-group-options.php:77
msgid "Side"
msgstr "Na straně"

#: includes/admin/views/field-group-options.php:76
msgid "Normal (after content)"
msgstr "Normální (po obsahu)"

#: includes/admin/views/field-group-options.php:75
msgid "High (after title)"
msgstr "Vysoko (po nadpisu)"

#: includes/admin/views/field-group-options.php:68
msgid "Position"
msgstr "Pozice"

#: includes/admin/views/field-group-options.php:59
msgid "Seamless (no metabox)"
msgstr "Bezokrajové (bez metaboxu)"

#: includes/admin/views/field-group-options.php:58
msgid "Standard (WP metabox)"
msgstr "Standardní (WP metabox)"

#: includes/admin/views/field-group-options.php:51
msgid "Style"
msgstr "Styl"

#: includes/admin/views/field-group-fields.php:44
msgid "Type"
msgstr "Typ"

#: includes/admin/admin-field-groups.php:285
#: includes/admin/views/field-group-fields.php:43
msgid "Key"
msgstr "Klíč"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/field-group-fields.php:37
msgid "Order"
msgstr "Pořadí"

#: includes/admin/views/field-group-field.php:294
msgid "Close Field"
msgstr "Zavřít pole"

#: includes/admin/views/field-group-field.php:235
msgid "id"
msgstr "ID"

#: includes/admin/views/field-group-field.php:219
msgid "class"
msgstr "třída"

#: includes/admin/views/field-group-field.php:256
msgid "width"
msgstr "šířka"

#: includes/admin/views/field-group-field.php:250
msgid "Wrapper Attributes"
msgstr "Atributy obalového pole"

#: includes/admin/views/field-group-field.php:171
msgid "Required"
msgstr "Požadováno?"

#: includes/admin/views/field-group-field.php:203
msgid "Instructions for authors. Shown when submitting data"
msgstr "Instrukce pro autory. Jsou zobrazeny při zadávání dat"

#: includes/admin/views/field-group-field.php:202
msgid "Instructions"
msgstr "Instrukce"

#: includes/admin/views/field-group-field.php:106
msgid "Field Type"
msgstr "Typ pole"

#: includes/admin/views/field-group-field.php:134
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Jedno slovo, bez mezer. Podtržítka a pomlčky jsou povoleny"

#: includes/admin/views/field-group-field.php:133
msgid "Field Name"
msgstr "Jméno pole"

#: includes/admin/views/field-group-field.php:121
msgid "This is the name which will appear on the EDIT page"
msgstr "Toto je jméno, které se zobrazí na stránce úprav"

#: includes/admin/views/field-group-field.php:120
msgid "Field Label"
msgstr "Štítek pole"

#: includes/admin/views/field-group-field.php:70
msgid "Delete"
msgstr "Smazat"

#: includes/admin/views/field-group-field.php:70
msgid "Delete field"
msgstr "Smazat pole"

#: includes/admin/views/field-group-field.php:68
msgid "Move"
msgstr "Přesunout"

#: includes/admin/views/field-group-field.php:68
msgid "Move field to another group"
msgstr "Přesunout pole do jiné skupiny"

#: includes/admin/views/field-group-field.php:66
msgid "Duplicate field"
msgstr "Duplikovat pole"

#: includes/admin/views/field-group-field.php:62
#: includes/admin/views/field-group-field.php:65
msgid "Edit field"
msgstr "Upravit pole"

#: includes/admin/views/field-group-field.php:58
msgid "Drag to reorder"
msgstr "Přetažením změníte pořadí"

#: includes/admin/admin-field-group.php:168
#: includes/admin/views/html-location-group.php:3
#: assets/build/js/acf-field-group.js:1935
#: assets/build/js/acf-field-group.js:2166
msgid "Show this field group if"
msgstr "Zobrazit tuto skupinu polí, pokud"

#: includes/admin/views/html-admin-page-upgrade.php:94
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "K dispozici nejsou žádné aktualizace."

#: includes/admin/views/html-admin-page-upgrade.php:33
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Upgrade databáze byl dokončen. <a href=\"%s\">Podívejte se, co je nového</a>"

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Čtení úkolů aktualizace..."

#: includes/admin/views/html-admin-page-upgrade-network.php:165
#: includes/admin/views/html-admin-page-upgrade.php:65
msgid "Upgrade failed."
msgstr "Upgrade se nezdařil."

#: includes/admin/views/html-admin-page-upgrade-network.php:162
msgid "Upgrade complete."
msgstr "Aktualizace dokončena."

#: includes/admin/views/html-admin-page-upgrade-network.php:148
#: includes/admin/views/html-admin-page-upgrade.php:31
msgid "Upgrading data to version %s"
msgstr "Aktualizace dat na verzi %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:121
#: includes/admin/views/html-notice-upgrade.php:44
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Důrazně doporučujeme zálohovat databázi před pokračováním. Opravdu chcete "
"aktualizaci spustit?"

#: includes/admin/views/html-admin-page-upgrade-network.php:117
msgid "Please select at least one site to upgrade."
msgstr "Vyberte alespoň jednu stránku, kterou chcete upgradovat."

#: includes/admin/views/html-admin-page-upgrade-network.php:97
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Aktualizace databáze je dokončena. <a href=\"%s\">Návrat na nástěnku sítě</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:80
msgid "Site is up to date"
msgstr "Stránky jsou aktuální"

#: includes/admin/views/html-admin-page-upgrade-network.php:78
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr ""

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Stránky"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:96
msgid "Upgrade Sites"
msgstr "Upgradovat stránky"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Následující stránky vyžadují upgrade DB. Zaškrtněte ty, které chcete "
"aktualizovat, a poté klikněte na %s."

#: includes/admin/views/field-group-field-conditional-logic.php:171
#: includes/admin/views/field-group-locations.php:38
msgid "Add rule group"
msgstr "Přidat skupinu pravidel"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Vytváří sadu pravidel pro určení, na kterých stránkách úprav budou použita "
"tato vlastní pole"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Pravidla"

#: includes/admin/tools/class-acf-admin-tool-export.php:478
msgid "Copied"
msgstr "Zkopírováno"

#: includes/admin/tools/class-acf-admin-tool-export.php:441
msgid "Copy to clipboard"
msgstr "Zkopírovat od schránky"

#: includes/admin/tools/class-acf-admin-tool-export.php:362
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Následující kód lze použít k registraci lokální verze vybrané skupiny polí. "
"Místní skupina polí může poskytnout mnoho výhod, jako jsou rychlejší doby "
"načítání, řízení verzí a dynamická pole / nastavení. Jednoduše zkopírujte a "
"vložte následující kód do souboru functions.php svého motivu nebo jej vložte "
"do externího souboru."

#: includes/admin/tools/class-acf-admin-tool-export.php:329
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Vyberte skupiny polí, které chcete exportovat, a vyberte způsob exportu. "
"Použijte tlačítko pro stažení pro exportování do souboru .json, který pak "
"můžete importovat do jiné instalace ACF. Pomocí tlačítka generovat můžete "
"exportovat do kódu PHP, který můžete umístit do vašeho tématu."

#: includes/admin/tools/class-acf-admin-tool-export.php:233
#: includes/admin/tools/class-acf-admin-tool-export.php:262
msgid "Select Field Groups"
msgstr "Zvolit skupiny polí"

#: includes/admin/tools/class-acf-admin-tool-export.php:167
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "Exportovaná 1 skupina polí."
msgstr[1] "Exportované %s skupiny polí."
msgstr[2] "Exportovaných %s skupin polí."

#: includes/admin/tools/class-acf-admin-tool-export.php:96
#: includes/admin/tools/class-acf-admin-tool-export.php:131
msgid "No field groups selected"
msgstr "Nebyly vybrány žádné skupiny polí"

#: includes/admin/tools/class-acf-admin-tool-export.php:39
#: includes/admin/tools/class-acf-admin-tool-export.php:337
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "Vytvořit PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:35
msgid "Export Field Groups"
msgstr "Exportovat skupiny polí"

#: includes/admin/tools/class-acf-admin-tool-import.php:146
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Importovaná 1 skupina polí"
msgstr[1] "Importované %s skupiny polí"
msgstr[2] "Importovaných %s skupin polí"

#: includes/admin/tools/class-acf-admin-tool-import.php:115
msgid "Import file empty"
msgstr "Importovaný soubor je prázdný"

#: includes/admin/tools/class-acf-admin-tool-import.php:106
msgid "Incorrect file type"
msgstr "Nesprávný typ souboru"

#: includes/admin/tools/class-acf-admin-tool-import.php:101
msgid "Error uploading file. Please try again"
msgstr "Chyba při nahrávání souboru. Prosím zkuste to znovu"

#: includes/admin/tools/class-acf-admin-tool-import.php:51
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups"
msgstr ""
"Vyberte Advanced Custom Fields JSON soubor, který chcete importovat. Po "
"klepnutí na tlačítko importu níže bude ACF importovat skupiny polí."

#: includes/admin/tools/class-acf-admin-tool-import.php:28
#: includes/admin/tools/class-acf-admin-tool-import.php:50
msgid "Import Field Groups"
msgstr "Importovat skupiny polí"

#: includes/admin/admin-field-groups.php:494
msgid "Sync"
msgstr "Synchronizace"

#: includes/admin/admin-field-groups.php:942
msgid "Select %s"
msgstr "Zvolit %s"

#: includes/admin/admin-field-groups.php:527
#: includes/admin/admin-field-groups.php:556
#: includes/admin/views/field-group-field.php:66
msgid "Duplicate"
msgstr "Duplikovat"

#: includes/admin/admin-field-groups.php:527
msgid "Duplicate this item"
msgstr "Duplikovat tuto položku"

#: includes/admin/admin-field-groups.php:284
#: includes/admin/views/field-group-options.php:221
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
msgid "Description"
msgstr "Popis"

#: includes/admin/admin-field-groups.php:491
#: includes/admin/admin-field-groups.php:829
msgid "Sync available"
msgstr "Synchronizace je k dispozici"

#: includes/admin/admin-field-groups.php:754
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/admin-field-groups.php:696
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/admin-field-groups.php:118
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Aktivní <span class=\"count\">(%s)</span>"
msgstr[1] "Aktivní <span class=\"count\">(%s)</span>"
msgstr[2] "Aktivních <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:237
msgid "Review sites & upgrade"
msgstr "Zkontrolujte stránky a aktualizujte"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:93
#: includes/admin/admin-upgrade.php:94 includes/admin/admin-upgrade.php:213
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Aktualizovat databázi"

#: includes/admin/admin.php:49 includes/admin/views/field-group-options.php:140
msgid "Custom Fields"
msgstr "Vlastní pole"

#: includes/admin/admin-field-group.php:718
msgid "Move Field"
msgstr "Přesunout pole"

#: includes/admin/admin-field-group.php:707
#: includes/admin/admin-field-group.php:711
msgid "Please select the destination for this field"
msgstr "Prosím zvolte umístění pro toto pole"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/admin-field-group.php:668
msgid "The %1$s field can now be found in the %2$s field group"
msgstr ""

#: includes/admin/admin-field-group.php:665
msgid "Move Complete."
msgstr "Přesun hotov."

#: includes/admin/views/field-group-field.php:273
#: includes/admin/views/field-group-options.php:189
msgid "Active"
msgstr "Aktivní"

#: includes/admin/admin-field-group.php:325
msgid "Field Keys"
msgstr "Klíče polí"

#: includes/admin/admin-field-group.php:224
#: includes/admin/tools/class-acf-admin-tool-export.php:286
msgid "Settings"
msgstr "Nastavení"

#: includes/admin/admin-field-groups.php:286
msgid "Location"
msgstr "Umístění"

#: includes/admin/admin-field-group.php:169 assets/build/js/acf-input.js:983
#: assets/build/js/acf-input.js:1075
msgid "Null"
msgstr "Nula"

#: includes/acf-field-group-functions.php:846
#: includes/admin/admin-field-group.php:166
#: assets/build/js/acf-field-group.js:1158
#: assets/build/js/acf-field-group.js:1310
msgid "copy"
msgstr "kopírovat"

#: includes/admin/admin-field-group.php:165
#: assets/build/js/acf-field-group.js:344
#: assets/build/js/acf-field-group.js:389
msgid "(this field)"
msgstr "(toto pole)"

#: includes/admin/admin-field-group.php:163 assets/build/js/acf-input.js:918
#: assets/build/js/acf-input.js:943 assets/build/js/acf-input.js:1002
#: assets/build/js/acf-input.js:1030
msgid "Checked"
msgstr "Zaškrtnuto"

#: includes/admin/admin-field-group.php:160
#: assets/build/js/acf-field-group.js:1249
#: assets/build/js/acf-field-group.js:1408
msgid "Move Custom Field"
msgstr "Přesunout vlastní pole"

#: includes/admin/admin-field-group.php:159
#: assets/build/js/acf-field-group.js:370
#: assets/build/js/acf-field-group.js:415
msgid "No toggle fields available"
msgstr "Žádné zapínatelné pole není k dispozici"

#: includes/admin/admin-field-group.php:157
msgid "Field group title is required"
msgstr "Vyžadován nadpis pro skupinu polí"

#: includes/admin/admin-field-group.php:156
#: assets/build/js/acf-field-group.js:1238
#: assets/build/js/acf-field-group.js:1394
msgid "This field cannot be moved until its changes have been saved"
msgstr "Toto pole nelze přesunout, dokud nebudou uloženy jeho změny"

#: includes/admin/admin-field-group.php:155
#: assets/build/js/acf-field-group.js:1049
#: assets/build/js/acf-field-group.js:1192
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Řetězec \"pole_\" nesmí být použit na začátku názvu pole"

#: includes/admin/admin-field-group.php:82
msgid "Field group draft updated."
msgstr "Koncept skupiny polí aktualizován."

#: includes/admin/admin-field-group.php:81
msgid "Field group scheduled for."
msgstr "Skupina polí naplánována."

#: includes/admin/admin-field-group.php:80
msgid "Field group submitted."
msgstr "Skupina polí odeslána."

#: includes/admin/admin-field-group.php:79
msgid "Field group saved."
msgstr "Skupina polí uložena."

#: includes/admin/admin-field-group.php:78
msgid "Field group published."
msgstr "Skupina polí publikována."

#: includes/admin/admin-field-group.php:75
msgid "Field group deleted."
msgstr "Skupina polí smazána."

#: includes/admin/admin-field-group.php:73
#: includes/admin/admin-field-group.php:74
#: includes/admin/admin-field-group.php:76
msgid "Field group updated."
msgstr "Skupina polí aktualizována."

#: includes/admin/admin-tools.php:119
#: includes/admin/views/html-admin-navigation.php:109
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Nástroje"

#: includes/locations/abstract-acf-location.php:106
msgid "is not equal to"
msgstr "není rovno"

#: includes/locations/abstract-acf-location.php:105
msgid "is equal to"
msgstr "je rovno"

#: includes/locations.php:102
msgid "Forms"
msgstr "Formuláře"

#: includes/locations.php:100 includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Stránka"

#: includes/locations.php:99 includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Příspěvek"

#: includes/fields.php:358
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:357
msgid "Relational"
msgstr "Relační"

#: includes/fields.php:356
msgid "Choice"
msgstr "Volba"

#: includes/fields.php:354
msgid "Basic"
msgstr "Základní"

#: includes/fields.php:313
msgid "Unknown"
msgstr "Neznámý"

#: includes/fields.php:313
msgid "Field type does not exist"
msgstr "Typ pole neexistuje"

#: includes/forms/form-front.php:236
msgid "Spam Detected"
msgstr "Zjištěn spam"

#: includes/forms/form-front.php:107
msgid "Post updated"
msgstr "Příspěvek aktualizován"

#: includes/forms/form-front.php:106
msgid "Update"
msgstr "Aktualizace"

#: includes/forms/form-front.php:57
msgid "Validate Email"
msgstr "Ověřit e-mail"

#: includes/fields.php:355 includes/forms/form-front.php:49
msgid "Content"
msgstr "Obsah"

#: includes/forms/form-front.php:40
msgid "Title"
msgstr "Název"

#: includes/assets.php:371 includes/forms/form-comment.php:160
#: assets/build/js/acf-input.js:7310 assets/build/js/acf-input.js:7876
msgid "Edit field group"
msgstr "Editovat skupinu polí"

#: includes/admin/admin-field-group.php:181 assets/build/js/acf-input.js:1125
#: assets/build/js/acf-input.js:1230
msgid "Selection is less than"
msgstr "Výběr je menší než"

#: includes/admin/admin-field-group.php:180 assets/build/js/acf-input.js:1106
#: assets/build/js/acf-input.js:1202
msgid "Selection is greater than"
msgstr "Výběr je větší než"

#: includes/admin/admin-field-group.php:179 assets/build/js/acf-input.js:1075
#: assets/build/js/acf-input.js:1170
msgid "Value is less than"
msgstr "Hodnota je menší než"

#: includes/admin/admin-field-group.php:178 assets/build/js/acf-input.js:1045
#: assets/build/js/acf-input.js:1139
msgid "Value is greater than"
msgstr "Hodnota je větší než"

#: includes/admin/admin-field-group.php:177 assets/build/js/acf-input.js:888
#: assets/build/js/acf-input.js:960
msgid "Value contains"
msgstr "Hodnota obsahuje"

#: includes/admin/admin-field-group.php:176 assets/build/js/acf-input.js:862
#: assets/build/js/acf-input.js:926
msgid "Value matches pattern"
msgstr "Hodnota odpovídá masce"

#: includes/admin/admin-field-group.php:175 assets/build/js/acf-input.js:840
#: assets/build/js/acf-input.js:1023 assets/build/js/acf-input.js:903
#: assets/build/js/acf-input.js:1116
msgid "Value is not equal to"
msgstr "Hodnota není rovna"

#: includes/admin/admin-field-group.php:174 assets/build/js/acf-input.js:810
#: assets/build/js/acf-input.js:964 assets/build/js/acf-input.js:864
#: assets/build/js/acf-input.js:1053
msgid "Value is equal to"
msgstr "Hodnota je rovna"

#: includes/admin/admin-field-group.php:173 assets/build/js/acf-input.js:788
#: assets/build/js/acf-input.js:841
msgid "Has no value"
msgstr "Nemá hodnotu"

#: includes/admin/admin-field-group.php:172 assets/build/js/acf-input.js:758
#: assets/build/js/acf-input.js:783
msgid "Has any value"
msgstr "Má libovolnou hodnotu"

#: includes/assets.php:352 assets/build/js/acf.js:1524
#: assets/build/js/acf.js:1604
msgid "Cancel"
msgstr "Zrušit"

#: includes/assets.php:348 assets/build/js/acf.js:1698
#: assets/build/js/acf.js:1801
msgid "Are you sure?"
msgstr "Jste si jistí?"

#: includes/assets.php:368 assets/build/js/acf-input.js:9343
#: assets/build/js/acf-input.js:10172
msgid "%d fields require attention"
msgstr "Několik polí vyžaduje pozornost (%d)"

#: includes/assets.php:367 assets/build/js/acf-input.js:9341
#: assets/build/js/acf-input.js:10168
msgid "1 field requires attention"
msgstr "1 pole vyžaduje pozornost"

#: includes/assets.php:366 includes/validation.php:287
#: includes/validation.php:297 assets/build/js/acf-input.js:9336
#: assets/build/js/acf-input.js:10163
msgid "Validation failed"
msgstr "Ověření selhalo"

#: includes/assets.php:365 assets/build/js/acf-input.js:9499
#: assets/build/js/acf-input.js:10346
msgid "Validation successful"
msgstr "Ověření úspěšné"

#: includes/media.php:54 assets/build/js/acf-input.js:7138
#: assets/build/js/acf-input.js:7680
msgid "Restricted"
msgstr "Omezeno"

#: includes/media.php:53 assets/build/js/acf-input.js:6953
#: assets/build/js/acf-input.js:7444
msgid "Collapse Details"
msgstr "Sbalit podrobnosti"

#: includes/media.php:52 assets/build/js/acf-input.js:6953
#: assets/build/js/acf-input.js:7441
msgid "Expand Details"
msgstr "Rozbalit podrobnosti"

#: includes/media.php:51 assets/build/js/acf-input.js:6820
#: assets/build/js/acf-input.js:7289
msgid "Uploaded to this post"
msgstr "Nahrán k tomuto příspěvku"

#: includes/media.php:50 assets/build/js/acf-input.js:6859
#: assets/build/js/acf-input.js:7328
msgctxt "verb"
msgid "Update"
msgstr "Aktualizace"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Upravit"

#: includes/assets.php:362 assets/build/js/acf-input.js:9113
#: assets/build/js/acf-input.js:9934
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Pokud opustíte tuto stránku, změny, které jste provedli, budou ztraceny"

#: includes/api/api-helpers.php:3395
msgid "File type must be %s."
msgstr "Typ souboru musí být %s."

#: includes/admin/admin-field-group.php:167
#: includes/admin/views/field-group-field-conditional-logic.php:59
#: includes/admin/views/field-group-field-conditional-logic.php:169
#: includes/admin/views/field-group-locations.php:36
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3391 assets/build/js/acf-field-group.js:492
#: assets/build/js/acf-field-group.js:1973
#: assets/build/js/acf-field-group.js:544
#: assets/build/js/acf-field-group.js:2210
msgid "or"
msgstr "nebo"

#: includes/api/api-helpers.php:3364
msgid "File size must not exceed %s."
msgstr ""

#: includes/api/api-helpers.php:3359
msgid "File size must be at least %s."
msgstr "Velikost souboru musí být alespoň %s."

#: includes/api/api-helpers.php:3344
msgid "Image height must not exceed %dpx."
msgstr "Výška obrázku nesmí přesáhnout %dpx."

#: includes/api/api-helpers.php:3339
msgid "Image height must be at least %dpx."
msgstr "Výška obrázku musí být alespoň %dpx."

#: includes/api/api-helpers.php:3325
msgid "Image width must not exceed %dpx."
msgstr "Šířka obrázku nesmí přesáhnout %dpx."

#: includes/api/api-helpers.php:3320
msgid "Image width must be at least %dpx."
msgstr "Šířka obrázku musí být alespoň %dpx."

#: includes/api/api-helpers.php:1566 includes/api/api-term.php:147
msgid "(no title)"
msgstr "(bez názvu)"

#: includes/api/api-helpers.php:861
msgid "Full Size"
msgstr "Plná velikost"

#: includes/api/api-helpers.php:820
msgid "Large"
msgstr "Velký"

#: includes/api/api-helpers.php:819
msgid "Medium"
msgstr "Střední"

#: includes/api/api-helpers.php:818
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/acf-field-functions.php:846
#: includes/admin/admin-field-group.php:164
#: assets/build/js/acf-field-group.js:789
#: assets/build/js/acf-field-group.js:859
msgid "(no label)"
msgstr "(bez štítku)"

#: includes/fields/class-acf-field-textarea.php:142
msgid "Sets the textarea height"
msgstr "Nastavuje výšku textového pole"

#: includes/fields/class-acf-field-textarea.php:141
msgid "Rows"
msgstr "Řádky"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Textové pole"

#: includes/fields/class-acf-field-checkbox.php:447
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Přidat zaškrtávátko navíc pro přepnutí všech možností"

#: includes/fields/class-acf-field-checkbox.php:409
msgid "Save 'custom' values to the field's choices"
msgstr "Uložit 'vlastní' hodnoty do voleb polí"

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Allow 'custom' values to be added"
msgstr "Povolit přidání 'vlastních' hodnot"

#: includes/fields/class-acf-field-checkbox.php:233
msgid "Add new choice"
msgstr "Přidat novou volbu"

#: includes/fields/class-acf-field-checkbox.php:170
msgid "Toggle All"
msgstr "Přepnout vše"

#: includes/fields/class-acf-field-page_link.php:477
msgid "Allow Archives URLs"
msgstr "Umožnit URL adresy archivu"

#: includes/fields/class-acf-field-page_link.php:165
msgid "Archives"
msgstr "Archivy"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Odkaz stránky"

#: includes/fields/class-acf-field-taxonomy.php:945
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Přidat"

#: includes/admin/views/field-group-fields.php:42
#: includes/fields/class-acf-field-taxonomy.php:910
msgid "Name"
msgstr "Jméno"

#: includes/fields/class-acf-field-taxonomy.php:894
msgid "%s added"
msgstr "%s přidán"

#: includes/fields/class-acf-field-taxonomy.php:858
msgid "%s already exists"
msgstr "%s již existuje"

#: includes/fields/class-acf-field-taxonomy.php:846
msgid "User unable to add new %s"
msgstr "Uživatel není schopen přidat nové %s"

#: includes/fields/class-acf-field-taxonomy.php:756
msgid "Term ID"
msgstr "ID pojmu"

#: includes/fields/class-acf-field-taxonomy.php:755
msgid "Term Object"
msgstr "Objekt pojmu"

#: includes/fields/class-acf-field-taxonomy.php:740
msgid "Load value from posts terms"
msgstr "Nahrát pojmy z příspěvků"

#: includes/fields/class-acf-field-taxonomy.php:739
msgid "Load Terms"
msgstr "Nahrát pojmy"

#: includes/fields/class-acf-field-taxonomy.php:729
msgid "Connect selected terms to the post"
msgstr "Připojte vybrané pojmy k příspěvku"

#: includes/fields/class-acf-field-taxonomy.php:728
msgid "Save Terms"
msgstr "Uložit pojmy"

#: includes/fields/class-acf-field-taxonomy.php:718
msgid "Allow new terms to be created whilst editing"
msgstr "Povolit vytvoření nových pojmů během editace"

#: includes/fields/class-acf-field-taxonomy.php:717
msgid "Create Terms"
msgstr "Vytvořit pojmy"

#: includes/fields/class-acf-field-taxonomy.php:776
msgid "Radio Buttons"
msgstr "Radio přepínače"

#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Single Value"
msgstr "Jednotlivá hodnota"

#: includes/fields/class-acf-field-taxonomy.php:773
msgid "Multi Select"
msgstr "Vícenásobný výběr"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Checkbox"
msgstr "Zaškrtávátko"

#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Multiple Values"
msgstr "Více hodnot"

#: includes/fields/class-acf-field-taxonomy.php:766
msgid "Select the appearance of this field"
msgstr "Vyberte vzhled tohoto pole"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Appearance"
msgstr "Vzhled"

#: includes/fields/class-acf-field-taxonomy.php:707
msgid "Select the taxonomy to be displayed"
msgstr "Zvolit zobrazovanou taxonomii"

#: includes/fields/class-acf-field-taxonomy.php:668
msgctxt "No Terms"
msgid "No %s"
msgstr "Nic pro %s"

#: includes/fields/class-acf-field-number.php:263
msgid "Value must be equal to or lower than %d"
msgstr "Hodnota musí být rovna nebo menší než %d"

#: includes/fields/class-acf-field-number.php:256
msgid "Value must be equal to or higher than %d"
msgstr "Hodnota musí být rovna nebo větší než %d"

#: includes/fields/class-acf-field-number.php:241
msgid "Value must be a number"
msgstr "Hodnota musí být číslo"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Číslo"

#: includes/fields/class-acf-field-radio.php:261
msgid "Save 'other' values to the field's choices"
msgstr "Uložit 'jiné' hodnoty do voleb polí"

#: includes/fields/class-acf-field-radio.php:250
msgid "Add 'other' choice to allow for custom values"
msgstr "Přidat volbu 'jiné', která umožňuje vlastní hodnoty"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Přepínač"

#: includes/fields/class-acf-field-accordion.php:104
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Definujte koncový bod pro předchozí akordeon. Tento akordeon nebude "
"viditelný."

#: includes/fields/class-acf-field-accordion.php:93
msgid "Allow this accordion to open without closing others."
msgstr "Povolit otevření tohoto akordeonu bez zavření ostatních."

#: includes/fields/class-acf-field-accordion.php:92
msgid "Multi-expand"
msgstr "Vícenásobné rozbalení"

#: includes/fields/class-acf-field-accordion.php:82
msgid "Display this accordion as open on page load."
msgstr "Zobrazit tento akordeon jako otevřený při načtení stránky."

#: includes/fields/class-acf-field-accordion.php:81
msgid "Open"
msgstr "Otevřít"

#: includes/fields/class-acf-field-accordion.php:25
msgid "Accordion"
msgstr "Akordeon"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-file.php:276
msgid "Restrict which files can be uploaded"
msgstr "Omezte, které typy souborů lze nahrát"

#: includes/fields/class-acf-field-file.php:217
msgid "File ID"
msgstr "ID souboru"

#: includes/fields/class-acf-field-file.php:216
msgid "File URL"
msgstr "Adresa souboru"

#: includes/fields/class-acf-field-file.php:215
msgid "File Array"
msgstr "Pole souboru"

#: includes/fields/class-acf-field-file.php:183
msgid "Add File"
msgstr "Přidat soubor"

#: includes/admin/tools/class-acf-admin-tool-import.php:94
#: includes/fields/class-acf-field-file.php:183
msgid "No file selected"
msgstr "Dokument nevybrán"

#: includes/fields/class-acf-field-file.php:147
msgid "File name"
msgstr "Jméno souboru"

#: includes/fields/class-acf-field-file.php:60
#: assets/build/js/acf-input.js:2466 assets/build/js/acf-input.js:2612
msgid "Update File"
msgstr "Aktualizovat soubor"

#: includes/fields/class-acf-field-file.php:59
#: assets/build/js/acf-input.js:2465 assets/build/js/acf-input.js:2611
msgid "Edit File"
msgstr "Upravit soubor"

#: includes/admin/tools/class-acf-admin-tool-import.php:59
#: includes/fields/class-acf-field-file.php:58
#: assets/build/js/acf-input.js:2439 assets/build/js/acf-input.js:2584
msgid "Select File"
msgstr "Vybrat soubor"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Soubor"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Heslo"

#: includes/fields/class-acf-field-select.php:391
msgid "Specify the value returned"
msgstr "Zadat konkrétní návratovou hodnotu"

#: includes/fields/class-acf-field-select.php:460
msgid "Use AJAX to lazy load choices?"
msgstr "K načtení volby použít AJAX lazy load?"

#: includes/fields/class-acf-field-checkbox.php:358
#: includes/fields/class-acf-field-select.php:380
msgid "Enter each default value on a new line"
msgstr "Zadejte každou výchozí hodnotu na nový řádek"

#: includes/fields/class-acf-field-select.php:255 includes/media.php:48
#: assets/build/js/acf-input.js:6718 assets/build/js/acf-input.js:7174
msgctxt "verb"
msgid "Select"
msgstr "Vybrat"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Načítání selhalo"

#: includes/fields/class-acf-field-select.php:117
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Vyhledávání&hellip;"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Načítání dalších výsledků&hellip;"

#: includes/fields/class-acf-field-select.php:115
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Můžete vybrat pouze %d položek"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Můžete vybrat pouze 1 položku"

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Prosím odstraňte %d znaků"

#: includes/fields/class-acf-field-select.php:112
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Prosím odstraňte 1 znak"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Prosím zadejte %d nebo více znaků"

#: includes/fields/class-acf-field-select.php:110
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Prosím zadejte 1 nebo více znaků"

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Nebyly nalezeny žádné výsledky"

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d výsledků je k dispozici, použijte šipky nahoru a dolů pro navigaci."

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr ""
"Jeden výsledek je k dispozici, stiskněte klávesu enter pro jeho vybrání."

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:777
msgctxt "noun"
msgid "Select"
msgstr "Vybrat"

#: includes/fields/class-acf-field-user.php:74
msgid "User ID"
msgstr "ID uživatele"

#: includes/fields/class-acf-field-user.php:73
msgid "User Object"
msgstr "Objekt uživatele"

#: includes/fields/class-acf-field-user.php:72
msgid "User Array"
msgstr "Pole uživatelů"

#: includes/fields/class-acf-field-user.php:60
msgid "All user roles"
msgstr "Všechny uživatelské role"

#: includes/fields/class-acf-field-user.php:52
msgid "Filter by role"
msgstr "Filtrovat podle role"

#: includes/fields/class-acf-field-user.php:20 includes/locations.php:101
msgid "User"
msgstr "Uživatel"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Oddělovač"

#: includes/fields/class-acf-field-color_picker.php:73
msgid "Select Color"
msgstr "Výběr barvy"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Default"
msgstr "Výchozí nastavení"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Clear"
msgstr "Vymazat"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Výběr barvy"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "do"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "odp"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "od"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "dop"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Vybrat"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Hotovo"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Nyní"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Časové pásmo"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosekunda"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milisekunda"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Vteřina"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuta"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Hodina"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Čas"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Zvolit čas"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Výběr data a času"

#: includes/fields/class-acf-field-accordion.php:103
msgid "Endpoint"
msgstr "Koncový bod"

#: includes/admin/views/field-group-options.php:95
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Zarovnat zleva"

#: includes/admin/views/field-group-options.php:94
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Zarovnat shora"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Umístění"

#: includes/fields/class-acf-field-tab.php:26
msgid "Tab"
msgstr "Záložka"

#: includes/fields/class-acf-field-url.php:159
msgid "Value must be a valid URL"
msgstr "Hodnota musí být validní adresa URL"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Adresa URL"

#: includes/fields/class-acf-field-link.php:174
msgid "Link URL"
msgstr "URL adresa odkazu"

#: includes/fields/class-acf-field-link.php:173
msgid "Link Array"
msgstr "Pole odkazů"

#: includes/fields/class-acf-field-link.php:142
msgid "Opens in a new window/tab"
msgstr "Otevřít v novém okně/záložce"

#: includes/fields/class-acf-field-link.php:137
msgid "Select Link"
msgstr "Vybrat odkaz"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Odkaz"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "E-mail"

#: includes/fields/class-acf-field-number.php:185
#: includes/fields/class-acf-field-range.php:214
msgid "Step Size"
msgstr "Velikost kroku"

#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-range.php:192
msgid "Maximum Value"
msgstr "Maximální hodnota"

#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-range.php:181
msgid "Minimum Value"
msgstr "Minimální hodnota"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Rozmezí"

#: includes/fields/class-acf-field-button-group.php:172
#: includes/fields/class-acf-field-checkbox.php:375
#: includes/fields/class-acf-field-radio.php:217
#: includes/fields/class-acf-field-select.php:398
msgid "Both (Array)"
msgstr "Obě (pole)"

#: includes/admin/views/field-group-fields.php:41
#: includes/fields/class-acf-field-button-group.php:171
#: includes/fields/class-acf-field-checkbox.php:374
#: includes/fields/class-acf-field-radio.php:216
#: includes/fields/class-acf-field-select.php:397
msgid "Label"
msgstr "Štítek"

#: includes/fields/class-acf-field-button-group.php:170
#: includes/fields/class-acf-field-checkbox.php:373
#: includes/fields/class-acf-field-radio.php:215
#: includes/fields/class-acf-field-select.php:396
msgid "Value"
msgstr "Hodnota"

#: includes/fields/class-acf-field-button-group.php:219
#: includes/fields/class-acf-field-checkbox.php:437
#: includes/fields/class-acf-field-radio.php:289
msgid "Vertical"
msgstr "Vertikální"

#: includes/fields/class-acf-field-button-group.php:218
#: includes/fields/class-acf-field-checkbox.php:438
#: includes/fields/class-acf-field-radio.php:290
msgid "Horizontal"
msgstr "Horizontální"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "red : Red"
msgstr "cervena : Červená"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "For more control, you may specify both a value and label like this:"
msgstr "Pro větší kontrolu můžete zadat jak hodnotu, tak štítek:"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "Enter each choice on a new line."
msgstr "Zadejte každou volbu na nový řádek."

#: includes/fields/class-acf-field-button-group.php:144
#: includes/fields/class-acf-field-checkbox.php:347
#: includes/fields/class-acf-field-radio.php:189
#: includes/fields/class-acf-field-select.php:368
msgid "Choices"
msgstr "Možnosti"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Skupina tlačítek"

#: includes/fields/class-acf-field-page_link.php:488
#: includes/fields/class-acf-field-post_object.php:404
#: includes/fields/class-acf-field-select.php:406
#: includes/fields/class-acf-field-user.php:83
msgid "Select multiple values?"
msgstr "Vybrat více hodnot?"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-page_link.php:509
#: includes/fields/class-acf-field-post_object.php:426
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:428
#: includes/fields/class-acf-field-taxonomy.php:786
#: includes/fields/class-acf-field-user.php:104
msgid "Allow Null?"
msgstr "Povolit prázdné?"

#: includes/fields/class-acf-field-page_link.php:249
#: includes/fields/class-acf-field-post_object.php:250
#: includes/fields/class-acf-field-taxonomy.php:932
msgid "Parent"
msgstr "Rodič"

#: includes/fields/class-acf-field-wysiwyg.php:394
msgid "TinyMCE will not be initialized until field is clicked"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:393
msgid "Delay initialization?"
msgstr "Zpoždění inicializace?"

#: includes/fields/class-acf-field-wysiwyg.php:382
msgid "Show Media Upload Buttons?"
msgstr "Zobrazit tlačítka nahrávání médií?"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Toolbar"
msgstr "Lišta nástrojů"

#: includes/fields/class-acf-field-wysiwyg.php:358
msgid "Text Only"
msgstr "Pouze text"

#: includes/fields/class-acf-field-wysiwyg.php:357
msgid "Visual Only"
msgstr "Pouze grafika"

#: includes/fields/class-acf-field-wysiwyg.php:356
msgid "Visual & Text"
msgstr "Grafika a text"

#: includes/fields/class-acf-field-wysiwyg.php:351
msgid "Tabs"
msgstr "Záložky"

#: includes/fields/class-acf-field-wysiwyg.php:289
msgid "Click to initialize TinyMCE"
msgstr "Klikněte pro inicializaci TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:283
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-wysiwyg.php:282
msgid "Visual"
msgstr "Grafika"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Wysiwyg Editor"

#: includes/fields/class-acf-field-text.php:180
#: includes/fields/class-acf-field-textarea.php:233
msgid "Value must not exceed %d characters"
msgstr ""

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Nechte prázdné pro nastavení bez omezení"

#: includes/fields/class-acf-field-text.php:114
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Limit znaků"

#: includes/fields/class-acf-field-email.php:155
#: includes/fields/class-acf-field-number.php:206
#: includes/fields/class-acf-field-password.php:102
#: includes/fields/class-acf-field-range.php:236
#: includes/fields/class-acf-field-text.php:155
msgid "Appears after the input"
msgstr "Zobrazí se za inputem"

#: includes/fields/class-acf-field-email.php:154
#: includes/fields/class-acf-field-number.php:205
#: includes/fields/class-acf-field-password.php:101
#: includes/fields/class-acf-field-range.php:235
#: includes/fields/class-acf-field-text.php:154
msgid "Append"
msgstr "Zobrazit po"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:196
#: includes/fields/class-acf-field-password.php:92
#: includes/fields/class-acf-field-range.php:226
#: includes/fields/class-acf-field-text.php:145
msgid "Appears before the input"
msgstr "Zobrazí se před inputem"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:195
#: includes/fields/class-acf-field-password.php:91
#: includes/fields/class-acf-field-range.php:225
#: includes/fields/class-acf-field-text.php:144
msgid "Prepend"
msgstr "Zobrazit před"

#: includes/fields/class-acf-field-email.php:135
#: includes/fields/class-acf-field-number.php:176
#: includes/fields/class-acf-field-password.php:82
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:153
#: includes/fields/class-acf-field-url.php:119
msgid "Appears within the input"
msgstr "Zobrazí se v inputu"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:175
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-text.php:134
#: includes/fields/class-acf-field-textarea.php:152
#: includes/fields/class-acf-field-url.php:118
msgid "Placeholder Text"
msgstr "Zástupný text"

#: includes/fields/class-acf-field-button-group.php:155
#: includes/fields/class-acf-field-email.php:115
#: includes/fields/class-acf-field-number.php:126
#: includes/fields/class-acf-field-radio.php:200
#: includes/fields/class-acf-field-range.php:162
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:101
#: includes/fields/class-acf-field-url.php:99
#: includes/fields/class-acf-field-wysiwyg.php:316
msgid "Appears when creating a new post"
msgstr "Objeví se při vytváření nového příspěvku"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-relationship.php:760
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-post_object.php:395
#: includes/fields/class-acf-field-relationship.php:622
msgid "Post ID"
msgstr "ID příspěvku"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:394
#: includes/fields/class-acf-field-relationship.php:621
msgid "Post Object"
msgstr "Objekt příspěvku"

#: includes/fields/class-acf-field-relationship.php:654
msgid "Maximum posts"
msgstr "Maximum příspěvků"

#: includes/fields/class-acf-field-relationship.php:644
msgid "Minimum posts"
msgstr "Minimum příspěvků"

#: includes/admin/views/field-group-options.php:148
#: includes/fields/class-acf-field-relationship.php:679
msgid "Featured Image"
msgstr "Uživatelský obrázek"

#: includes/fields/class-acf-field-relationship.php:675
msgid "Selected elements will be displayed in each result"
msgstr "Vybrané prvky se zobrazí v každém výsledku"

#: includes/fields/class-acf-field-relationship.php:674
msgid "Elements"
msgstr "Prvky"

#: includes/fields/class-acf-field-relationship.php:608
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:706
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taxonomie"

#: includes/fields/class-acf-field-relationship.php:607
#: includes/locations/class-acf-location-post-type.php:22
msgid "Post Type"
msgstr "Typ příspěvku"

#: includes/fields/class-acf-field-relationship.php:601
msgid "Filters"
msgstr "Filtry"

#: includes/fields/class-acf-field-page_link.php:470
#: includes/fields/class-acf-field-post_object.php:382
#: includes/fields/class-acf-field-relationship.php:594
msgid "All taxonomies"
msgstr "Všechny taxonomie"

#: includes/fields/class-acf-field-page_link.php:462
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:586
msgid "Filter by Taxonomy"
msgstr "Filtrovat dle taxonomie"

#: includes/fields/class-acf-field-page_link.php:455
#: includes/fields/class-acf-field-post_object.php:367
#: includes/fields/class-acf-field-relationship.php:579
msgid "All post types"
msgstr "Všechny typy příspěvků"

#: includes/fields/class-acf-field-page_link.php:447
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:571
msgid "Filter by Post Type"
msgstr "Filtrovat dle typu příspěvku"

#: includes/fields/class-acf-field-relationship.php:469
msgid "Search..."
msgstr "Hledat..."

#: includes/fields/class-acf-field-relationship.php:399
msgid "Select taxonomy"
msgstr "Zvolit taxonomii"

#: includes/fields/class-acf-field-relationship.php:390
msgid "Select post type"
msgstr "Zvolit typ příspěvku"

#: includes/fields/class-acf-field-relationship.php:65
#: assets/build/js/acf-input.js:3917 assets/build/js/acf-input.js:4195
msgid "No matches found"
msgstr "Nebyly nalezeny žádné výsledky"

#: includes/fields/class-acf-field-relationship.php:64
#: assets/build/js/acf-input.js:3900 assets/build/js/acf-input.js:4174
msgid "Loading"
msgstr "Načítání"

#: includes/fields/class-acf-field-relationship.php:63
#: assets/build/js/acf-input.js:3809 assets/build/js/acf-input.js:4070
msgid "Maximum values reached ( {max} values )"
msgstr "Dosaženo maximálního množství hodnot ( {max} hodnot )"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Vztah"

#: includes/fields/class-acf-field-file.php:288
#: includes/fields/class-acf-field-image.php:314
msgid "Comma separated list. Leave blank for all types"
msgstr "Seznam oddělený čárkami. Nechte prázdné pro povolení všech typů"

#: includes/fields/class-acf-field-file.php:287
#: includes/fields/class-acf-field-image.php:313
msgid "Allowed file types"
msgstr "Povolené typy souborů"

#: includes/fields/class-acf-field-file.php:275
#: includes/fields/class-acf-field-image.php:277
msgid "Maximum"
msgstr "Maximum"

#: includes/fields/class-acf-field-file.php:151
#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-file.php:279
#: includes/fields/class-acf-field-image.php:268
#: includes/fields/class-acf-field-image.php:304
msgid "File size"
msgstr "Velikost souboru"

#: includes/fields/class-acf-field-image.php:242
#: includes/fields/class-acf-field-image.php:278
msgid "Restrict which images can be uploaded"
msgstr "Omezte, které typy obrázků je možné nahrát"

#: includes/fields/class-acf-field-file.php:263
#: includes/fields/class-acf-field-image.php:241
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:232
#: includes/fields/class-acf-field-image.php:207
msgid "Uploaded to post"
msgstr "Nahráno k příspěvku"

#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:206
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Vše"

#: includes/fields/class-acf-field-file.php:226
#: includes/fields/class-acf-field-image.php:201
msgid "Limit the media library choice"
msgstr "Omezit výběr knihovny médií"

#: includes/fields/class-acf-field-file.php:225
#: includes/fields/class-acf-field-image.php:200
msgid "Library"
msgstr "Knihovna"

#: includes/fields/class-acf-field-image.php:333
msgid "Preview Size"
msgstr "Velikost náhledu"

#: includes/fields/class-acf-field-image.php:192
msgid "Image ID"
msgstr "ID obrázku"

#: includes/fields/class-acf-field-image.php:191
msgid "Image URL"
msgstr "Adresa obrázku"

#: includes/fields/class-acf-field-image.php:190
msgid "Image Array"
msgstr "Pole obrázku"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:368
#: includes/fields/class-acf-field-file.php:210
#: includes/fields/class-acf-field-link.php:168
#: includes/fields/class-acf-field-radio.php:210
msgid "Specify the returned value on front end"
msgstr "Zadat konkrétní návratovou hodnotu na frontendu"

#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:367
#: includes/fields/class-acf-field-file.php:209
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-taxonomy.php:750
msgid "Return Value"
msgstr "Vrátit hodnotu"

#: includes/fields/class-acf-field-image.php:159
msgid "Add Image"
msgstr "Přidat obrázek"

#: includes/fields/class-acf-field-image.php:159
msgid "No image selected"
msgstr "Není vybrán žádný obrázek"

#: includes/assets.php:351 includes/fields/class-acf-field-file.php:159
#: includes/fields/class-acf-field-image.php:139
#: includes/fields/class-acf-field-link.php:142 assets/build/js/acf.js:1523
#: assets/build/js/acf.js:1603
msgid "Remove"
msgstr "Odstranit"

#: includes/admin/views/field-group-field.php:65
#: includes/fields/class-acf-field-file.php:157
#: includes/fields/class-acf-field-image.php:137
#: includes/fields/class-acf-field-link.php:142
msgid "Edit"
msgstr "Upravit"

#: includes/fields/class-acf-field-image.php:67 includes/media.php:55
#: assets/build/js/acf-input.js:6765 assets/build/js/acf-input.js:7228
msgid "All images"
msgstr "Všechny obrázky"

#: includes/fields/class-acf-field-image.php:66
#: assets/build/js/acf-input.js:3173 assets/build/js/acf-input.js:3386
msgid "Update Image"
msgstr "Aktualizovat obrázek"

#: includes/fields/class-acf-field-image.php:65
#: assets/build/js/acf-input.js:3172 assets/build/js/acf-input.js:3385
msgid "Edit Image"
msgstr "Upravit obrázek"

#: includes/fields/class-acf-field-image.php:64
#: assets/build/js/acf-input.js:3148 assets/build/js/acf-input.js:3360
msgid "Select Image"
msgstr "Vybrat obrázek"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Obrázek"

#: includes/fields/class-acf-field-message.php:123
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Nevykreslovat efekt, ale zobrazit značky HTML jako prostý text"

#: includes/fields/class-acf-field-message.php:122
msgid "Escape HTML"
msgstr "Escapovat HTML"

#: includes/fields/class-acf-field-message.php:114
#: includes/fields/class-acf-field-textarea.php:169
msgid "No Formatting"
msgstr "Žádné formátování"

#: includes/fields/class-acf-field-message.php:113
#: includes/fields/class-acf-field-textarea.php:168
msgid "Automatically add &lt;br&gt;"
msgstr "Automaticky přidávat &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:112
#: includes/fields/class-acf-field-textarea.php:167
msgid "Automatically add paragraphs"
msgstr "Automaticky přidávat odstavce"

#: includes/fields/class-acf-field-message.php:108
#: includes/fields/class-acf-field-textarea.php:163
msgid "Controls how new lines are rendered"
msgstr "Řídí, jak se vykreslují nové řádky"

#: includes/fields/class-acf-field-message.php:107
#: includes/fields/class-acf-field-textarea.php:162
msgid "New Lines"
msgstr "Nové řádky"

#: includes/fields/class-acf-field-date_picker.php:229
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "Týden začíná"

#: includes/fields/class-acf-field-date_picker.php:198
msgid "The format used when saving a value"
msgstr "Formát použitý při ukládání hodnoty"

#: includes/fields/class-acf-field-date_picker.php:197
msgid "Save Format"
msgstr "Uložit formát"

#: includes/fields/class-acf-field-date_picker.php:64
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Týden"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Předchozí"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Následující"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Dnes"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Hotovo"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Výběr data"

#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:281
#: includes/fields/class-acf-field-oembed.php:265
msgid "Width"
msgstr "Šířka"

#: includes/fields/class-acf-field-oembed.php:262
#: includes/fields/class-acf-field-oembed.php:274
msgid "Embed Size"
msgstr "Velikost pro Embed"

#: includes/fields/class-acf-field-oembed.php:219
msgid "Enter URL"
msgstr "Vložte URL"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:181
msgid "Text shown when inactive"
msgstr "Text zobrazený při neaktivním poli"

#: includes/fields/class-acf-field-true_false.php:180
msgid "Off Text"
msgstr "Text (neaktivní)"

#: includes/fields/class-acf-field-true_false.php:165
msgid "Text shown when active"
msgstr "Text zobrazený při aktivním poli"

#: includes/fields/class-acf-field-true_false.php:164
msgid "On Text"
msgstr "Text (aktivní)"

#: includes/fields/class-acf-field-select.php:449
#: includes/fields/class-acf-field-true_false.php:196
msgid "Stylized UI"
msgstr "Stylizované uživatelské rozhraní"

#: includes/fields/class-acf-field-button-group.php:154
#: includes/fields/class-acf-field-checkbox.php:357
#: includes/fields/class-acf-field-color_picker.php:155
#: includes/fields/class-acf-field-email.php:114
#: includes/fields/class-acf-field-number.php:125
#: includes/fields/class-acf-field-radio.php:199
#: includes/fields/class-acf-field-range.php:161
#: includes/fields/class-acf-field-select.php:379
#: includes/fields/class-acf-field-text.php:94
#: includes/fields/class-acf-field-textarea.php:100
#: includes/fields/class-acf-field-true_false.php:144
#: includes/fields/class-acf-field-url.php:98
#: includes/fields/class-acf-field-wysiwyg.php:315
msgid "Default Value"
msgstr "Výchozí hodnota"

#: includes/fields/class-acf-field-true_false.php:135
msgid "Displays text alongside the checkbox"
msgstr "Zobrazí text vedle zaškrtávacího políčka"

#: includes/fields/class-acf-field-message.php:26
#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-true_false.php:134
msgid "Message"
msgstr "Zpráva"

#: includes/assets.php:350 includes/fields/class-acf-field-true_false.php:83
#: includes/fields/class-acf-field-true_false.php:184
#: assets/build/js/acf.js:1700 assets/build/js/acf.js:1803
msgid "No"
msgstr "Ne"

#: includes/assets.php:349 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:168
#: assets/build/js/acf.js:1699 assets/build/js/acf.js:1802
msgid "Yes"
msgstr "Ano"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Pravda / Nepravda"

#: includes/fields/class-acf-field-group.php:471
msgid "Row"
msgstr "Řádek"

#: includes/fields/class-acf-field-group.php:470
msgid "Table"
msgstr "Tabulka"

#: includes/fields/class-acf-field-group.php:469
msgid "Block"
msgstr "Blok"

#: includes/fields/class-acf-field-group.php:464
msgid "Specify the style used to render the selected fields"
msgstr "Určení stylu použitého pro vykreslení vybraných polí"

#: includes/fields.php:359 includes/fields/class-acf-field-button-group.php:212
#: includes/fields/class-acf-field-checkbox.php:431
#: includes/fields/class-acf-field-group.php:463
#: includes/fields/class-acf-field-radio.php:283
msgid "Layout"
msgstr "Typ zobrazení"

#: includes/fields/class-acf-field-group.php:447
msgid "Sub Fields"
msgstr "Podřazená pole"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Skupina"

#: includes/fields/class-acf-field-google-map.php:232
msgid "Customize the map height"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:231
#: includes/fields/class-acf-field-image.php:256
#: includes/fields/class-acf-field-image.php:292
#: includes/fields/class-acf-field-oembed.php:277
msgid "Height"
msgstr "Výška"

#: includes/fields/class-acf-field-google-map.php:220
msgid "Set the initial zoom level"
msgstr "Nastavit počáteční úroveň přiblížení"

#: includes/fields/class-acf-field-google-map.php:219
msgid "Zoom"
msgstr "Přiblížení"

#: includes/fields/class-acf-field-google-map.php:193
#: includes/fields/class-acf-field-google-map.php:206
msgid "Center the initial map"
msgstr "Vycentrovat počáteční zobrazení mapy"

#: includes/fields/class-acf-field-google-map.php:192
#: includes/fields/class-acf-field-google-map.php:205
msgid "Center"
msgstr "Vycentrovat"

#: includes/fields/class-acf-field-google-map.php:160
msgid "Search for address..."
msgstr "Vyhledat adresu..."

#: includes/fields/class-acf-field-google-map.php:157
msgid "Find current location"
msgstr "Najít aktuální umístění"

#: includes/fields/class-acf-field-google-map.php:156
msgid "Clear location"
msgstr "Vymazat polohu"

#: includes/fields/class-acf-field-google-map.php:155
#: includes/fields/class-acf-field-relationship.php:606
msgid "Search"
msgstr "Hledat"

#: includes/fields/class-acf-field-google-map.php:60
#: assets/build/js/acf-input.js:2832 assets/build/js/acf-input.js:3013
msgid "Sorry, this browser does not support geolocation"
msgstr "Je nám líto, ale tento prohlížeč nepodporuje geolokaci"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Mapa Google"

#: includes/fields/class-acf-field-date_picker.php:209
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:129
msgid "The format returned via template functions"
msgstr "Formát vrácen pomocí funkcí šablony"

#: includes/fields/class-acf-field-color_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:197
#: includes/fields/class-acf-field-image.php:184
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:616
#: includes/fields/class-acf-field-select.php:390
#: includes/fields/class-acf-field-time_picker.php:128
#: includes/fields/class-acf-field-user.php:67
msgid "Return Format"
msgstr "Formát návratové hodnoty"

#: includes/fields/class-acf-field-date_picker.php:187
#: includes/fields/class-acf-field-date_picker.php:218
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-date_time_picker.php:207
#: includes/fields/class-acf-field-time_picker.php:120
#: includes/fields/class-acf-field-time_picker.php:136
msgid "Custom:"
msgstr "Vlastní:"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-time_picker.php:113
msgid "The format displayed when editing a post"
msgstr "Formát zobrazený při úpravě příspěvku"

#: includes/fields/class-acf-field-date_picker.php:178
#: includes/fields/class-acf-field-date_time_picker.php:179
#: includes/fields/class-acf-field-time_picker.php:112
msgid "Display Format"
msgstr "Formát zobrazení"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Výběr času"

#: acf.php:470
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Neaktivní <span class=\"count\">(%s)</span>"
msgstr[1] "Neaktivní <span class=\"count\">(%s)</span>"
msgstr[2] "Neaktivních <span class=\"count\">(%s)</span>"

#: acf.php:413
msgid "No Fields found in Trash"
msgstr "V koši nenalezeno žádné pole"

#: acf.php:412
msgid "No Fields found"
msgstr "Nenalezeno žádné pole"

#: acf.php:411
msgid "Search Fields"
msgstr "Vyhledat pole"

#: acf.php:410
msgid "View Field"
msgstr "Zobrazit pole"

#: acf.php:409 includes/admin/views/field-group-fields.php:104
msgid "New Field"
msgstr "Nové pole"

#: acf.php:408
msgid "Edit Field"
msgstr "Upravit pole"

#: acf.php:407
msgid "Add New Field"
msgstr "Přidat nové pole"

#: acf.php:405
msgid "Field"
msgstr "Pole"

#: acf.php:404 includes/admin/admin-field-group.php:220
#: includes/admin/admin-field-groups.php:287
#: includes/admin/views/field-group-fields.php:21
msgid "Fields"
msgstr "Pole"

#: acf.php:379
msgid "No Field Groups found in Trash"
msgstr "V koši nebyly nalezeny žádné skupiny polí"

#: acf.php:378
msgid "No Field Groups found"
msgstr "Nebyly nalezeny žádné skupiny polí"

#: acf.php:377
msgid "Search Field Groups"
msgstr "Hledat skupiny polí"

#: acf.php:376
msgid "View Field Group"
msgstr "Prohlížet skupinu polí"

#: acf.php:375
msgid "New Field Group"
msgstr "Nová skupina polí"

#: acf.php:374
msgid "Edit Field Group"
msgstr "Upravit skupinu polí"

#: acf.php:373
msgid "Add New Field Group"
msgstr "Přidat novou skupinu polí"

#: acf.php:372 acf.php:406 includes/admin/admin.php:51
msgid "Add New"
msgstr "Přidat nové"

#: acf.php:371
msgid "Field Group"
msgstr "Skupina polí"

#: acf.php:370 includes/admin/admin.php:50
msgid "Field Groups"
msgstr "Skupiny polí"

#. Description of the plugin
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php:91
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:465 includes/admin/admin-field-group.php:384
#: includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "Neaktivní"

#: includes/admin/admin-field-group.php:156
msgid "Move to trash. Are you sure?"
msgstr "Přesunout do koše. Jste si jistí?"

#: includes/admin/admin-field-group.php:747
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "Pole %s lze nyní najít ve skupině polí %s"

#: includes/admin/admin-field-group.php:748
msgid "Close Window"
msgstr "Zavřít okno"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "Skupina polí duplikována. %s"

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s skupina polí duplikována."
msgstr[1] "%s skupiny polí duplikovány."
msgstr[2] "%s skupin polí duplikováno."

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr "Skupina polí synchronizována. %s"

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s skupina polí synchronizována."
msgstr[1] "%s skupiny polí synchronizovány."
msgstr[2] "%s skupin polí synchronizováno."

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "Stav"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr ""
"Upravte si WordPress pomocí výkonných, profesionálních a intuitivně "
"použitelných polí."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Seznam změn"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Podívejte se, co je nového ve <a href=\"%s\">verzi %s</a>."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "Zdroje"

#: includes/admin/admin-field-groups.php:619
msgid "Website"
msgstr "Webová stránka"

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "Dokumentace"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "Podpora"

#: includes/admin/admin-field-groups.php:623
#: includes/admin/views/settings-info.php:84
msgid "Pro"
msgstr "Pro"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Děkujeme, že používáte <a href=\"%s\">ACF</a>."

#: includes/admin/admin-field-groups.php:767
msgid "Synchronise field group"
msgstr "Synchronizujte skupinu polí"

#: includes/admin/admin-field-groups.php:779
msgid "Apply"
msgstr "Použít"

#: includes/admin/admin-field-groups.php:797
msgid "Bulk Actions"
msgstr "Hromadné akce"

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Doplňky"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>Chyba</b>. Nelze načíst seznam doplňků"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Informace"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Co je nového"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Vyberte skupiny polí, které chcete exportovat, a vyberte způsob exportu. "
"Použijte tlačítko pro stažení pro exportování do souboru .json, který pak "
"můžete importovat do jiné instalace ACF. Pomocí tlačítka generovat můžete "
"exportovat do kódu PHP, který můžete umístit do vašeho tématu."

#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr "Exportovat soubor"

#: includes/admin/tools/class-acf-admin-tool-import.php:61
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Vyberte Advanced Custom Fields JSON soubor, který chcete importovat. Po "
"klepnutí na tlačítko importu níže bude ACF importovat skupiny polí."

#: includes/admin/tools/class-acf-admin-tool-import.php:76
msgid "Import File"
msgstr "Importovat soubor"

#: includes/admin/views/field-group-field.php:106
msgid "Required?"
msgstr "Požadováno?"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Žádná pole. Klikněte na tlačítko<strong>+ Přidat pole</strong> pro vytvoření "
"prvního pole."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Přidat pole"

#: includes/admin/views/html-admin-page-upgrade-network.php:74
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Stránky vyžadují aktualizaci databáze z %s na %s"

#: includes/admin/views/html-notice-upgrade.php:22
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Děkujeme vám za aktualizaci na %s v%s!"

#: includes/admin/views/html-notice-upgrade.php:24
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""
"Zkontrolujte také, zda jsou všechny prémiové doplňky ( %s) nejprve "
"aktualizovány na nejnovější verzi."

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Stáhnout a instalovat"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Instalováno"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Vítejte v Advanced Custom Fields"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Děkujeme za aktualizaci! ACF %s je větší a lepší než kdykoli předtím. "
"Doufáme, že se vám bude líbit."

#: includes/admin/views/settings-info.php:15
msgid "A Smoother Experience"
msgstr "Plynulejší zážitek"

#: includes/admin/views/settings-info.php:19
msgid "Improved Usability"
msgstr "Vylepšená použitelnost"

#: includes/admin/views/settings-info.php:20
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Zahrnutí oblíbené knihovny Select2 zlepšilo jak použitelnost, tak i rychlost "
"v různých typech polí, včetně objektu příspěvku, odkazu na stránku, "
"taxonomie a možnosti výběru."

#: includes/admin/views/settings-info.php:24
msgid "Improved Design"
msgstr "Zlepšený design"

#: includes/admin/views/settings-info.php:25
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Mnoho polí podstoupilo osvěžení grafiky, aby ACF vypadalo lépe než kdy "
"jindy! Znatelné změny jsou vidět na polích galerie, vztahů a oEmbed "
"(novinka)!"

#: includes/admin/views/settings-info.php:29
msgid "Improved Data"
msgstr "Vylepšené údaje"

#: includes/admin/views/settings-info.php:30
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"Přepracování datové architektury umožnilo, aby podřazená pole žila nezávisle "
"na rodičích. To umožňuje jejich přetahování mezi rodičovskými poli!"

#: includes/admin/views/settings-info.php:38
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Sbohem doplňkům. Pozdrav verzi PRO"

#: includes/admin/views/settings-info.php:41
msgid "Introducing ACF PRO"
msgstr "Představujeme ACF PRO"

#: includes/admin/views/settings-info.php:42
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr "Měníme způsob poskytování prémiových funkcí vzrušujícím způsobem!"

#: includes/admin/views/settings-info.php:43
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Všechny 4 prémiové doplňky byly spojeny do nové verze <a href=\"%s\">Pro pro "
"ACF</a>. Se svými osobními i vývojovými licencemi je prémiová funkčnost "
"cenově dostupná a přístupnější než kdykoli předtím!"

#: includes/admin/views/settings-info.php:47
msgid "Powerful Features"
msgstr "Výkonné funkce"

#: includes/admin/views/settings-info.php:48
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF PRO obsahuje výkonné funkce, jako jsou opakovatelná data, flexibilní "
"rozložení obsahu, krásné pole galerie a možnost vytvářet další stránky "
"administrátorských voleb!"

#: includes/admin/views/settings-info.php:49
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Přečtěte si další informace o funkcích <a href=\"%s\">ACF PRO</a>."

#: includes/admin/views/settings-info.php:53
msgid "Easy Upgrading"
msgstr "Snadná aktualizace"

#: includes/admin/views/settings-info.php:54
msgid ""
"Upgrading to ACF PRO is easy. Simply purchase a license online and download "
"the plugin!"
msgstr ""
"Upgrade na ACF PRO je snadný. Stačí online zakoupit licenci a stáhnout "
"plugin!"

#: includes/admin/views/settings-info.php:55
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>."
msgstr ""
"Také jsme napsali <a href=\"%s\">průvodce aktualizací</a> na zodpovězení "
"jakýchkoliv dotazů, ale pokud i přes to nějaký máte, kontaktujte prosím náš "
"tým podpory prostřednictvím <a href=\"%s\">Help Desku</a>."

#: includes/admin/views/settings-info.php:64
msgid "New Features"
msgstr "Nové funkce"

#: includes/admin/views/settings-info.php:69
msgid "Link Field"
msgstr "Odkaz pole"

#: includes/admin/views/settings-info.php:70
msgid ""
"The Link field provides a simple way to select or define a link (url, title, "
"target)."
msgstr ""
"Pole odkazu poskytuje jednoduchý způsob, jak vybrat nebo definovat odkaz "
"(URL, název, cíl)."

#: includes/admin/views/settings-info.php:74
msgid "Group Field"
msgstr "Skupinové pole"

#: includes/admin/views/settings-info.php:75
msgid "The Group field provides a simple way to create a group of fields."
msgstr "Skupina polí poskytuje jednoduchý způsob vytvoření skupiny polí."

#: includes/admin/views/settings-info.php:79
msgid "oEmbed Field"
msgstr "oEmbed pole"

#: includes/admin/views/settings-info.php:80
msgid ""
"The oEmbed field allows an easy way to embed videos, images, tweets, audio, "
"and other content."
msgstr ""
"oEmbed pole umožňuje snadno vkládat videa, obrázky, tweety, audio a další "
"obsah."

#: includes/admin/views/settings-info.php:85
msgid "The clone field allows you to select and display existing fields."
msgstr "Klonované pole umožňuje vybrat a zobrazit existující pole."

#: includes/admin/views/settings-info.php:89
msgid "More AJAX"
msgstr "Více AJAXu"

#: includes/admin/views/settings-info.php:90
msgid "More fields use AJAX powered search to speed up page loading."
msgstr "Více polí využívá vyhledávání pomocí AJAX pro rychlé načítání stránky."

#: includes/admin/views/settings-info.php:95
msgid ""
"New auto export to JSON feature improves speed and allows for syncronisation."
msgstr ""
"Nová funkce automatického exportu do JSONu zvyšuje rychlost a umožňuje "
"synchronizaci."

#: includes/admin/views/settings-info.php:99
msgid "Easy Import / Export"
msgstr "Snadný import/export"

#: includes/admin/views/settings-info.php:100
msgid "Both import and export can easily be done through a new tools page."
msgstr "Import i export lze snadno provést pomocí nové stránky nástroje."

#: includes/admin/views/settings-info.php:104
msgid "New Form Locations"
msgstr "Umístění nového formuláře"

#: includes/admin/views/settings-info.php:105
msgid ""
"Fields can now be mapped to menus, menu items, comments, widgets and all "
"user forms!"
msgstr ""
"Pole lze nyní mapovat na nabídky, položky nabídky, komentáře, widgety a "
"všechny uživatelské formuláře!"

#: includes/admin/views/settings-info.php:109
msgid "More Customization"
msgstr "Další úpravy"

#: includes/admin/views/settings-info.php:110
msgid ""
"New PHP (and JS) actions and filters have been added to allow for more "
"customization."
msgstr ""
"Byly přidány nové akce a filtry PHP (a JS), které umožňují další úpravy."

#: includes/admin/views/settings-info.php:114
msgid "Fresh UI"
msgstr "Svěží uživatelské rozhraní"

#: includes/admin/views/settings-info.php:115
msgid ""
"The entire plugin has had a design refresh including new field types, "
"settings and design!"
msgstr "Celý plugin je redesignován včetně nových typů polí a nastavení!"

#: includes/admin/views/settings-info.php:119
msgid "New Settings"
msgstr "Nová nastavení"

#: includes/admin/views/settings-info.php:120
msgid ""
"Field group settings have been added for Active, Label Placement, "
"Instructions Placement and Description."
msgstr ""
"Bylo přidáno nastavení skupiny polí bylo přidáno pro aktivní, umístění "
"štítků, umístění instrukcí a popis."

#: includes/admin/views/settings-info.php:124
msgid "Better Front End Forms"
msgstr "Lepší vizuální stránka formulářů"

#: includes/admin/views/settings-info.php:125
msgid ""
"acf_form() can now create a new post on submission with lots of new settings."
msgstr ""
"acf_form() může nyní vytvořit nový příspěvek po odeslání se spoustou nových "
"možností."

#: includes/admin/views/settings-info.php:129
msgid "Better Validation"
msgstr "Lepší validace"

#: includes/admin/views/settings-info.php:130
msgid "Form validation is now done via PHP + AJAX in favour of only JS."
msgstr ""
"Validace formuláře nyní probíhá prostřednictvím PHP + AJAX a to ve prospěch "
"pouze JS."

#: includes/admin/views/settings-info.php:134
msgid "Moving Fields"
msgstr "Pohyblivá pole"

#: includes/admin/views/settings-info.php:135
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents."
msgstr ""
"Nová funkčnost skupiny polí umožňuje přesouvání pole mezi skupinami a rodiči."

#: includes/admin/views/settings-info.php:146
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Myslíme si, že změny v %s si zamilujete."

#: includes/api/api-helpers.php:4043
#, php-format
msgid "File size must must not exceed %s."
msgstr "Velikost souboru nesmí přesáhnout %s."

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "Povolit vlastní"

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "Uložit vlastní"

#: includes/fields/class-acf-field-checkbox.php:403
msgid "Toggle"
msgstr "Přepnout"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Aktuální barva"

#: includes/fields/class-acf-field-google-map.php:233
msgid "Customise the map height"
msgstr "Upravit výšku mapy"

#: includes/fields/class-acf-field-image.php:221
msgid "Shown when entering data"
msgstr "Zobrazit při zadávání dat"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Jiné"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "Uložit Jiné"

#: includes/fields/class-acf-field-relationship.php:808
#: pro/fields/class-acf-field-gallery.php:815
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s vyžaduje alespoň %s volbu"
msgstr[1] "%s vyžaduje alespoň %s volby"
msgstr[2] "%s vyžaduje alespoň %s voleb"

#: includes/fields/class-acf-field-select.php:397
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "Stylizované uživatelské rozhraní"

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""
"Definujte koncový bod pro předchozí záložky. Tím se začne nová skupina "
"záložek."

#: includes/fields/class-acf-field-taxonomy.php:714
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "Nic pro %s"

#: includes/fields/class-acf-field-wysiwyg.php:430
msgid "TinyMCE will not be initalized until field is clicked"
msgstr "TinyMCE nebude inicializován, dokud nekliknete na pole"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:200
msgid "Publish"
msgstr "Publikovat"

#: pro/admin/admin-options-page.php:206
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Nebyly nalezeny žádné vlastní skupiny polí. <a href=\"%s\">Vytvořit vlastní "
"skupinu polí</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Chyba</b>. Nelze se připojit k serveru a aktualizovat"

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Deaktivujte licenci"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Aktivujte licenci"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Informace o licenci"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Chcete-li povolit aktualizace, zadejte prosím licenční klíč. Pokud nemáte "
"licenční klíč, přečtěte si <a href=\"%s\">podrobnosti a ceny</a>."

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Licenční klíč"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Aktualizovat informace"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Současná verze"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Nejnovější verze"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Aktualizace je dostupná"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Aktualizovat plugin"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr "Pro odemčení aktualizací zadejte prosím výše svůj licenční klíč"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Zkontrolujte znovu"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "Upozornění na aktualizaci"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Klonovat"

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr "Vyberte jedno nebo více polí, které chcete klonovat"

#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr "Zobrazovat"

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr "Určení stylu použitého pro vykreslení klonovaných polí"

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr "Skupina (zobrazuje vybrané pole ve skupině v tomto poli)"

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr "Bezešvé (nahradí toto pole vybranými poli)"

#: pro/fields/class-acf-field-clone.php:857
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Štítky budou zobrazeny jako %s"

#: pro/fields/class-acf-field-clone.php:860
msgid "Prefix Field Labels"
msgstr "Prefix štítku pole"

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr "Hodnoty budou uloženy jako %s"

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Names"
msgstr "Prefix jména pole"

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr "Neznámé pole"

#: pro/fields/class-acf-field-clone.php:1031
msgid "Unknown field group"
msgstr "Skupina neznámých polí"

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr "Všechna pole z skupiny polí %s"

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:193
#: pro/fields/class-acf-field-repeater.php:463
msgid "Add Row"
msgstr "Přidat řádek"

#: pro/fields/class-acf-field-flexible-content.php:73
#: pro/fields/class-acf-field-flexible-content.php:938
#: pro/fields/class-acf-field-flexible-content.php:1020
msgid "layout"
msgid_plural "layouts"
msgstr[0] "typ zobrazení"
msgstr[1] "typ zobrazení"
msgstr[2] "typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:74
msgid "layouts"
msgstr "typy zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:77
#: pro/fields/class-acf-field-flexible-content.php:937
#: pro/fields/class-acf-field-flexible-content.php:1019
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Toto pole vyžaduje alespoň {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:78
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Toto pole má limit {max}{label}  {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:81
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} dostupný (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} povinný (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "Flexible Content requires at least 1 layout"
msgstr "Flexibilní obsah vyžaduje minimálně jedno rozložení obsahu"

#: pro/fields/class-acf-field-flexible-content.php:302
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""
"Klikněte na tlačítko \"%s\" níže pro vytvoření vlastního typu zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:427
msgid "Add layout"
msgstr "Přidat typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:428
msgid "Remove layout"
msgstr "Odstranit typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:429
#: pro/fields/class-acf-field-repeater.php:296
msgid "Click to toggle"
msgstr "Klikněte pro přepnutí"

#: pro/fields/class-acf-field-flexible-content.php:569
msgid "Reorder Layout"
msgstr "Změnit pořadí typu zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:569
msgid "Reorder"
msgstr "Změnit pořadí"

#: pro/fields/class-acf-field-flexible-content.php:570
msgid "Delete Layout"
msgstr "Smazat typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Duplicate Layout"
msgstr "Duplikovat typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Add New Layout"
msgstr "Přidat nový typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:643
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:656
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:683
#: pro/fields/class-acf-field-repeater.php:459
msgid "Button Label"
msgstr "Nápis tlačítka"

#: pro/fields/class-acf-field-flexible-content.php:692
msgid "Minimum Layouts"
msgstr "Minimální rozložení"

#: pro/fields/class-acf-field-flexible-content.php:701
msgid "Maximum Layouts"
msgstr "Maximální rozložení"

#: pro/fields/class-acf-field-gallery.php:71
msgid "Add Image to Gallery"
msgstr "Přidat obrázek do galerie"

#: pro/fields/class-acf-field-gallery.php:72
msgid "Maximum selection reached"
msgstr "Maximální výběr dosažen"

#: pro/fields/class-acf-field-gallery.php:336
msgid "Length"
msgstr "Délka"

#: pro/fields/class-acf-field-gallery.php:379
msgid "Caption"
msgstr "Popisek"

#: pro/fields/class-acf-field-gallery.php:388
msgid "Alt Text"
msgstr "Alternativní text"

#: pro/fields/class-acf-field-gallery.php:559
msgid "Add to gallery"
msgstr "Přidat do galerie"

#: pro/fields/class-acf-field-gallery.php:563
msgid "Bulk actions"
msgstr "Hromadné akce"

#: pro/fields/class-acf-field-gallery.php:564
msgid "Sort by date uploaded"
msgstr "Řadit dle data nahrání"

#: pro/fields/class-acf-field-gallery.php:565
msgid "Sort by date modified"
msgstr "Řadit dle data změny"

#: pro/fields/class-acf-field-gallery.php:566
msgid "Sort by title"
msgstr "Řadit dle názvu"

#: pro/fields/class-acf-field-gallery.php:567
msgid "Reverse current order"
msgstr "Převrátit aktuální pořadí"

#: pro/fields/class-acf-field-gallery.php:585
msgid "Close"
msgstr "Zavřít"

#: pro/fields/class-acf-field-gallery.php:639
msgid "Minimum Selection"
msgstr "Minimální výběr"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Maximum Selection"
msgstr "Maximální výběr"

#: pro/fields/class-acf-field-gallery.php:657
msgid "Insert"
msgstr "Vložit"

#: pro/fields/class-acf-field-gallery.php:658
msgid "Specify where new attachments are added"
msgstr "Určete, kde budou přidány nové přílohy"

#: pro/fields/class-acf-field-gallery.php:662
msgid "Append to the end"
msgstr "Přidat na konec"

#: pro/fields/class-acf-field-gallery.php:663
msgid "Prepend to the beginning"
msgstr "Přidat na začátek"

#: pro/fields/class-acf-field-repeater.php:65
#: pro/fields/class-acf-field-repeater.php:656
msgid "Minimum rows reached ({min} rows)"
msgstr "Minimální počet řádků dosažen ({min} řádků)"

#: pro/fields/class-acf-field-repeater.php:66
msgid "Maximum rows reached ({max} rows)"
msgstr "Maximální počet řádků dosažen ({max} řádků)"

#: pro/fields/class-acf-field-repeater.php:333
msgid "Add row"
msgstr "Přidat řádek"

#: pro/fields/class-acf-field-repeater.php:334
msgid "Remove row"
msgstr "Odebrat řádek"

#: pro/fields/class-acf-field-repeater.php:412
msgid "Collapsed"
msgstr "Sbaleno"

#: pro/fields/class-acf-field-repeater.php:413
msgid "Select a sub field to show when row is collapsed"
msgstr "Zvolte dílčí pole, které se zobrazí při sbalení řádku"

#: pro/fields/class-acf-field-repeater.php:423
msgid "Minimum Rows"
msgstr "Minimum řádků"

#: pro/fields/class-acf-field-repeater.php:433
msgid "Maximum Rows"
msgstr "Maximum řádků"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "Neexistuje stránka nastavení"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Nastavení aktualizováno"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Chcete-li povolit aktualizace, zadejte prosím licenční klíč na stránce <a "
"href=\"%s\">Aktualizace</a>. Pokud nemáte licenční klíč, přečtěte si <a href="
"\"%s\">podrobnosti a ceny</a>."

#. Plugin URI of the plugin/theme
#| msgid "http://www.advancedcustomfields.com/"
msgid "https://www.advancedcustomfields.com/"
msgstr "https://www.advancedcustomfields.com/"

#. Author of the plugin/theme
#| msgid "elliot condon"
msgid "Elliot Condon"
msgstr "Elliot Condon"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#~ msgid "Gallery Field"
#~ msgstr "Pole galerie"

#~ msgid "Flexible Content Field"
#~ msgstr "Pole flexibilního obsahu"

#~ msgid "Repeater Field"
#~ msgstr "Opakovací pole"

#~ msgid "Deactivate"
#~ msgstr "Deaktivovat"

#~ msgid "Activate"
#~ msgstr "Aktivovat"

#~ msgid "Disabled"
#~ msgstr "Zakázáno"

#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "<span class=\"count\">(%s)</span> zakázán"
#~ msgstr[1] "<span class=\"count\">(%s)</span> zakázány"
#~ msgstr[2] "<span class=\"count\">(%s)</span> zakázáno"

#~ msgid "Parent fields"
#~ msgstr "Rodičovské pole"

#~ msgid "Sibling fields"
#~ msgstr "Sesterské pole"

#~ msgid "See what's new in"
#~ msgstr "Co je nového v"

#~ msgid "version"
#~ msgstr "verze"

#~ msgid "Getting Started"
#~ msgstr "Začínáme"

#~ msgid "Field Types"
#~ msgstr "Typy polí"

#~ msgid "Functions"
#~ msgstr "Funkce"

#~ msgid "Actions"
#~ msgstr "Akce"

#~ msgid "'How to' guides"
#~ msgstr "Průvodce \"jak na to\""

#~ msgid "Tutorials"
#~ msgstr "Tutoriál"

#~ msgid "Created by"
#~ msgstr "Vytvořil/a"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr "<b>Úspěch</b>. Nástroj pro import přidal %s skupin polí: %s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Upozornění</b>. Nástroj pro import rozpoznal %s již existujících "
#~ "skupin polí a ty byly ignorovány: %s"

#~ msgid "Upgrade ACF"
#~ msgstr "Aktualizovat ACF"

#~ msgid "Upgrade"
#~ msgstr "Aktualizovat"

#~ msgid "Error"
#~ msgstr "Chyba"

#~ msgid "Error."
#~ msgstr "Chyba."

#~ msgid "Drag and drop to reorder"
#~ msgstr "Chytněte a táhněte pro změnu pořadí"

#~ msgid "Taxonomy Term"
#~ msgstr "Taxonomie"

#~ msgid ""
#~ "To help make upgrading easy, <a href=\"%s\">login to your store account</"
#~ "a> and claim a free copy of ACF PRO!"
#~ msgstr ""
#~ "Pro usnadnění aktualizace se <a href=\"%s\">přihlaste do svého obchodu</"
#~ "a> a požádejte o bezplatnou kopii ACF PRO!"

#~ msgid "Under the Hood"
#~ msgstr "Pod kapotou"

#~ msgid "Smarter field settings"
#~ msgstr "Chytřejší nastavení pole"

#~ msgid "ACF now saves its field settings as individual post objects"
#~ msgstr "ACF nyní ukládá nastavení polí jako individuální objekty"

#~ msgid "Better version control"
#~ msgstr "Lepší verzování"

#~ msgid ""
#~ "New auto export to JSON feature allows field settings to be version "
#~ "controlled"
#~ msgstr ""
#~ "Nový automatický export do formátu JSON umožňuje, aby nastavení polí bylo "
#~ "verzovatelné"

#~ msgid "Swapped XML for JSON"
#~ msgstr "XML vyměněno za JSON"

#~ msgid "Import / Export now uses JSON in favour of XML"
#~ msgstr "Import / Export nyní používá JSON místo XML"

#~ msgid "A new field for embedding content has been added"
#~ msgstr "Bylo přidáno nové pole pro vkládání obsahu"

#~ msgid "New Gallery"
#~ msgstr "Nová galerie"

#~ msgid "The gallery field has undergone a much needed facelift"
#~ msgstr "Pole pro galerii prošlo potřebovaným vylepšením vzhledu"

#~ msgid "Relationship Field"
#~ msgstr "Vztahová pole"

#~ msgid ""
#~ "New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
#~ msgstr ""
#~ "Nastavení nových polí pro \"Filtry\" (vyhledávání, typ příspěvku, "
#~ "taxonomie)"

#~ msgid "New archives group in page_link field selection"
#~ msgstr "Nová skupina archivů v poli pro výběr page_link"

#~ msgid "Better Options Pages"
#~ msgstr "Vylepšená stránka nastavení"

#~ msgid ""
#~ "New functions for options page allow creation of both parent and child "
#~ "menu pages"
#~ msgstr ""
#~ "Nové funkce pro stránku nastavení umožňují vytvoření stránek obou "
#~ "rodičovských i podřízených menu"

#~ msgid "Export Field Groups to PHP"
#~ msgstr "Exportujte skupiny polí do PHP"

#~ msgid "Download export file"
#~ msgstr "Stáhnout soubor s exportem"

#~ msgid "Generate export code"
#~ msgstr "Generovat kód pro exportu"

#~ msgid "Advanced Custom Fields Database Upgrade"
#~ msgstr "Aktualizace databáze Advanced Custom Fields"

#~ msgid "Upgrading data to"
#~ msgstr "Aktualizace dat na"

#~ msgid ""
#~ "Before you start using the new awesome features, please update your "
#~ "database to the newest version."
#~ msgstr ""
#~ "Než začnete používat nové úžasné funkce, aktualizujte databázi na "
#~ "nejnovější verzi."

#~ msgid "See what's new"
#~ msgstr "Podívejte se, co je nového"

#~ msgid "Show a different month"
#~ msgstr "Zobrazit jiný měsíc"

#~ msgid "Return format"
#~ msgstr "Formát návratu"

#~ msgid "uploaded to this post"
#~ msgstr "nahrán k tomuto příspěvku"

#~ msgid "File Size"
#~ msgstr "Velikost souboru"

#~ msgid "No File selected"
#~ msgstr "Nebyl vybrán žádný soubor"

#~ msgid "Locating"
#~ msgstr "Určování polohy"

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr ""
#~ "Berte prosím na vědomí, že veškerý text musí projít přes funkce "
#~ "wordpressu "

#~ msgid "No embed found for the given URL."
#~ msgstr "Pro danou adresu URL nebyl nalezen žádný embed."

#~ msgid "Minimum values reached ( {min} values )"
#~ msgstr "Dosaženo minimálního množství hodnot ( {min} hodnot )"

#~ msgid "Warning"
#~ msgstr "Varování"

#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "Pole záložky se zobrazí nesprávně, pokud je přidáno do opakovače v "
#~ "tabulkovém stylu nebo do flexibilního pole"

#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr ""
#~ "Chcete-li lépe uspořádat obrazovku úprav, použijte seskupování polí "
#~ "pomocí Záložek."

#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "Všechna pole následující po této záložce (až po další záložku nebo konec "
#~ "výpisu) budou seskupena a jako nadpis bude použit štítek záložky."

#~ msgid "Add new %s "
#~ msgstr "Přidat novou %s "

#~ msgid "None"
#~ msgstr "Žádný"

#~ msgid "eg. Show extra content"
#~ msgstr "např. Zobrazit dodatečný obsah"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Chyba připojení</b>. Omlouváme se, zkuste to znovu"

#~ msgid "Save Options"
#~ msgstr "Uložit nastavení"

#~ msgid "License"
#~ msgstr "Licence"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Pro odemčení aktualizací prosím zadejte níže svůj licenční klíč. Pokud "
#~ "nemáte licenční klíč, prosím navštivte"

#~ msgid "details & pricing"
#~ msgstr "detaily a ceny"

#~ msgid "remove {layout}?"
#~ msgstr "odstranit {layout}?"

#~ msgid "This field requires at least {min} {identifier}"
#~ msgstr "Toto pole vyžaduje alespoň {min} {identifier}"

#~ msgid "Maximum {label} limit reached ({max} {identifier})"
#~ msgstr "Maximální {label} limit dosažen ({max} {identifier})"

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "Advanced Custom Fields Pro"

#~ msgid "Custom field updated."
#~ msgstr "Vlastní pole aktualizováno."

#~ msgid "Custom field deleted."
#~ msgstr "Vlastní pole smazáno."

#~ msgid "Field group restored to revision from %s"
#~ msgstr "Skupina polí obnovena z revize %s"

#~ msgid "Error: Field Type does not exist!"
#~ msgstr "Chyba: Typ pole neexistuje!"

#~ msgid "Full"
#~ msgstr "Plný"

#~ msgid "No ACF groups selected"
#~ msgstr "Nejsou vybrány žádné ACF skupiny"

#~ msgid "Add Fields to Edit Screens"
#~ msgstr "Přidat pole na obrazovky úprav"

#~ msgid "Customise the edit page"
#~ msgstr "Přizpůsobit stránku úprav"

#~ msgid "Parent Page"
#~ msgstr "Rodičovská stránka"

#~ msgid "Child Page"
#~ msgstr "Podstránka"

#~ msgid "Normal"
#~ msgstr "Normální"

#~ msgid "Standard Metabox"
#~ msgstr "Standardní metabox"

#~ msgid "No Metabox"
#~ msgstr "Žádný metabox"

#~ msgid ""
#~ "Read documentation, learn the functions and find some tips &amp; tricks "
#~ "for your next web project."
#~ msgstr ""
#~ "Přečtěte si dokumentaci, naučte se funkce a objevte zajímavé tipy &amp; "
#~ "triky pro váš další webový projekt."

#~ msgid "Visit the ACF website"
#~ msgstr "Navštívit web ACF"

#~ msgid "Vote"
#~ msgstr "Hlasujte"

#~ msgid "Follow"
#~ msgstr "Následujte"

#~ msgid "Validation Failed. One or more fields below are required."
#~ msgstr "Ověřování selhalo. Jedno nebo více polí níže je povinné."

#~ msgid "Add File to Field"
#~ msgstr "+ Přidat soubor do pole"

#~ msgid "Add Image to Field"
#~ msgstr "Přidat obrázek do pole"

#~ msgid "Attachment updated"
#~ msgstr "Příloha aktualizována."

#~ msgid "No Custom Field Group found for the options page"
#~ msgstr "Žádná vlastní skupina polí nebyla pro stránku konfigurace nalezena"

#~ msgid "Repeater field deactivated"
#~ msgstr "Opakovací pole deaktivováno"

#~ msgid "Options page deactivated"
#~ msgstr "Stránka konfigurace deaktivována"

#~ msgid "Flexible Content field deactivated"
#~ msgstr "Pole flexibilního pole deaktivováno"

#~ msgid "Gallery field deactivated"
#~ msgstr "Pole galerie deaktivováno"

#~ msgid "Repeater field activated"
#~ msgstr "Opakovací pole aktivováno"

#~ msgid "Options page activated"
#~ msgstr "Stránka konfigurace aktivována"

#~ msgid "Flexible Content field activated"
#~ msgstr "Pole flexibilního obsahu aktivováno"

#~ msgid "Gallery field activated"
#~ msgstr "Pole galerie aktivováno"

#~ msgid "License key unrecognised"
#~ msgstr "Licenční klíč nebyl rozpoznán"

#~ msgid "Activate Add-ons."
#~ msgstr "Aktivovat přídavky."

#~ msgid ""
#~ "Add-ons can be unlocked by purchasing a license key. Each key can be used "
#~ "on multiple sites."
#~ msgstr ""
#~ "Přídavky mohou být odemčeny zakoupením licenčního klíče. Každý klíč může "
#~ "být použit na více webech."

#~ msgid "Find Add-ons"
#~ msgstr "Hledat přídavky"

#~ msgid "Activation Code"
#~ msgstr "Aktivační kód"

#~ msgid "Export Field Groups to XML"
#~ msgstr "Exportovat skupiny polí do XML"

#~ msgid ""
#~ "ACF will create a .xml export file which is compatible with the native WP "
#~ "import plugin."
#~ msgstr ""
#~ "ACF vytvoří soubor .xml exportu, který je kompatibilní s originálním "
#~ "importním pluginem WP."

#~ msgid ""
#~ "Imported field groups <b>will</b> appear in the list of editable field "
#~ "groups. This is useful for migrating fields groups between Wp websites."
#~ msgstr ""
#~ "Importované skupiny polí <b>budou</b> zobrazeny v seznamu upravitelných "
#~ "skupin polí. Toto je užitečné pro přesouvání skupin polí mezi WP weby."

#~ msgid "Select field group(s) from the list and click \"Export XML\""
#~ msgstr "Vyberte skupinu(y) polí ze seznamu a klikněte na \"Export XML\""

#~ msgid "Save the .xml file when prompted"
#~ msgstr "Uložte .xml soubor při požádání"

#~ msgid "Navigate to Tools &raquo; Import and select WordPress"
#~ msgstr "Otevřete Nástroje &raquo; Import a vyberte WordPress"

#~ msgid "Install WP import plugin if prompted"
#~ msgstr "Nainstalujte importní WP plugin, pokud jste o to požádáni"

#~ msgid "Upload and import your exported .xml file"
#~ msgstr "Nahrajte a importujte váš exportovaný .xml soubor"

#~ msgid "Select your user and ignore Import Attachments"
#~ msgstr "Vyberte vašeho uživatele a ignorujte možnost Importovat přílohy"

#~ msgid "That's it! Happy WordPressing"
#~ msgstr "To je vše! Veselé WordPressování!"

#~ msgid "Export XML"
#~ msgstr "Exportovat XML"

#~ msgid "ACF will create the PHP code to include in your theme."
#~ msgstr "ACF vytvoří PHP kód pro vložení do vaší šablony."

#~ msgid "Register Field Groups"
#~ msgstr "Registrovat skupiny polí"

#~ msgid ""
#~ "Registered field groups <b>will not</b> appear in the list of editable "
#~ "field groups. This is useful for including fields in themes."
#~ msgstr ""
#~ "Registrované skupiny polí <b>nebudou</b> zobrazeny v seznamu "
#~ "upravitelných skupin polí. Toto je užitečné při používání polí v "
#~ "šablonách."

#~ msgid ""
#~ "Please note that if you export and register field groups within the same "
#~ "WP, you will see duplicate fields on your edit screens. To fix this, "
#~ "please move the original field group to the trash or remove the code from "
#~ "your functions.php file."
#~ msgstr ""
#~ "Mějte prosím na paměti, že pokud exportujete a registrujete skupiny polí "
#~ "v rámci stejného WordPressu, uvidíte na obrazovkách úprav duplikovaná "
#~ "pole. Pro nápravu prosím přesuňte původní skupinu polí do koše nebo "
#~ "odstraňte kód ze souboru functions.php."

#~ msgid "Select field group(s) from the list and click \"Create PHP\""
#~ msgstr "Vyberte skupinu(y) polí ze seznamu a klikněte na \"Vytvořit  PHP\""

#~ msgid "Copy the PHP code generated"
#~ msgstr "Zkopírujte vygenerovaný PHP kód"

#~ msgid "Paste into your functions.php file"
#~ msgstr "Vložte jej do vašeho souboru functions.php"

#~ msgid ""
#~ "To activate any Add-ons, edit and use the code in the first few lines."
#~ msgstr ""
#~ "K aktivací kteréhokoli přídavku upravte a použijte kód na prvních "
#~ "několika řádcích."

#~ msgid "Back to settings"
#~ msgstr "Zpět na nastavení"

#~ msgid ""
#~ "/**\n"
#~ " * Activate Add-ons\n"
#~ " * Here you can enter your activation codes to unlock Add-ons to use in "
#~ "your theme. \n"
#~ " * Since all activation codes are multi-site licenses, you are allowed to "
#~ "include your key in premium themes. \n"
#~ " * Use the commented out code to update the database with your activation "
#~ "code. \n"
#~ " * You may place this code inside an IF statement that only runs on theme "
#~ "activation.\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " * Aktivovat přídavky\n"
#~ " * Zde můžete vložit váš aktivační kód pro odemčení přídavků k použití ve "
#~ "vaší šabloně. \n"
#~ " * Jelikož jsou všechny aktivační kódy licencovány pro použití na více "
#~ "webech, můžete je použít ve vaší premium šabloně. \n"
#~ " * Použijte zakomentovaný kód pro aktualizaci databáze s vaším aktivačním "
#~ "kódem. \n"
#~ " * Tento kód můžete vložit dovnitř IF konstrukce, která proběhne pouze po "
#~ "aktivaci šablony.\n"
#~ " */"

#~ msgid ""
#~ "/**\n"
#~ " * Register field groups\n"
#~ " * The register_field_group function accepts 1 array which holds the "
#~ "relevant data to register a field group\n"
#~ " * You may edit the array as you see fit. However, this may result in "
#~ "errors if the array is not compatible with ACF\n"
#~ " * This code must run every time the functions.php file is read\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " * Registrace skupiny polí\n"
#~ " * Funkce register_field_group akceptuje pole, které obsahuje relevatní "
#~ "data k registraci skupiny polí\n"
#~ " * Pole můžete upravit podle potřeb. Může to ovšem vyústit v pole "
#~ "nekompatibilní s ACF\n"
#~ " * Tento kód musí proběhnout při každém čtení souboru functions.php\n"
#~ " */"

#~ msgid "No field groups were selected"
#~ msgstr "Nebyly vybrány žádné skupiny polí"

#~ msgid "Advanced Custom Fields Settings"
#~ msgstr "Nastavení Pokročilých vlastních polí"

#~ msgid "requires a database upgrade"
#~ msgstr "vyžaduje aktualizaci databáze"

#~ msgid "why?"
#~ msgstr "proč?"

#~ msgid "Please"
#~ msgstr "Prosím"

#~ msgid "backup your database"
#~ msgstr "zálohujte svou databázi"

#~ msgid "then click"
#~ msgstr "a pak klikněte"

#~ msgid "Modifying field group options 'show on page'"
#~ msgstr "Úprava možnosti skupiny polí 'zobrazit na stránce'"

#~ msgid "Modifying field option 'taxonomy'"
#~ msgstr "Úprava možností pole 'taxonomie'"

#~ msgid "No choices to choose from"
#~ msgstr "Žádné možnosti, z nichž by bylo možné vybírat"

#~ msgid "Enter your choices one per line"
#~ msgstr "Vložte vaše možnosti po jedné na řádek"

#~ msgid "Red"
#~ msgstr "Červená"

#~ msgid "Blue"
#~ msgstr "Modrá"

#~ msgid "blue : Blue"
#~ msgstr "modra: Modrá"

#~ msgid "eg. dd/mm/yy. read more about"
#~ msgstr "např. dd/mm/yy. přečtěte si více"

#~ msgid "File Updated."
#~ msgstr "Soubor aktualizován."

#~ msgid "No File Selected"
#~ msgstr "Nebyl vybrán žádný soubor"

#~ msgid "Attachment ID"
#~ msgstr "ID přílohy"

#~ msgid "Media attachment updated."
#~ msgstr "Příloha aktualizována."

#~ msgid "No files selected"
#~ msgstr "Nebyly vybrány žádné soubory."

#~ msgid "Add Selected Files"
#~ msgstr "Přidat vybrané soubory"

#~ msgid "+ Add Row"
#~ msgstr "+ Přidat řádek"

#~ msgid "Field Order"
#~ msgstr "Pořadí pole"

#~ msgid ""
#~ "No fields. Click the \"+ Add Sub Field button\" to create your first "
#~ "field."
#~ msgstr ""
#~ "Žádná pole. Klikněte na tlačítko \"+ Přidat podpole\" pro vytvoření "
#~ "prvního pole."

#~ msgid "Edit this Field"
#~ msgstr "Upravit toto pole"

#~ msgid "Read documentation for this field"
#~ msgstr "Přečtěte si dokumentaci pro toto pole"

#~ msgid "Docs"
#~ msgstr "Dokumenty"

#~ msgid "Duplicate this Field"
#~ msgstr "Duplikovat toto pole"

#~ msgid "Delete this Field"
#~ msgstr "Smazat toto pole"

#~ msgid "Save Field"
#~ msgstr "Uložit pole"

#~ msgid "Close Sub Field"
#~ msgstr "Zavřít podpole"

#~ msgid "+ Add Sub Field"
#~ msgstr "+ Přidat podpole"

#~ msgid "Thumbnail is advised"
#~ msgstr "Je doporučen náhled"

#~ msgid "Image Updated"
#~ msgstr "Obrázek aktualizován"

#~ msgid "Grid"
#~ msgstr "Mřížka"

#~ msgid "List"
#~ msgstr "Seznam"

#~ msgid "No images selected"
#~ msgstr "Není vybrán žádný obrázek"

#~ msgid "1 image selected"
#~ msgstr "1 vybraný obrázek"

#~ msgid "{count} images selected"
#~ msgstr "{count} vybraných obrázků"

#~ msgid "Image already exists in gallery"
#~ msgstr "Obrázek v galerii už existuje"

#~ msgid "Image Added"
#~ msgstr "Obrázek přidán"

#~ msgid "Image Updated."
#~ msgstr "Obrázek aktualizován."

#~ msgid "Image Object"
#~ msgstr "Objekt obrázku"

#~ msgid "Add selected Images"
#~ msgstr "Přidat vybrané obrázky"

#~ msgid "Filter from Taxonomy"
#~ msgstr "Filtrovat z taxonomie"

#~ msgid "Repeater Fields"
#~ msgstr "Opakovací pole"

#~ msgid "Table (default)"
#~ msgstr "Tabulka (výchozí)"

#~ msgid "Formatting"
#~ msgstr "Formátování"

#~ msgid "Define how to render html tags"
#~ msgstr "Definujte způsob vypisování HTML tagů"

#~ msgid "HTML"
#~ msgstr "HTML"

#~ msgid "Define how to render html tags / new lines"
#~ msgstr "Definujte způsob výpisu HTML tagů / nových řádků"

#~ msgid "auto &lt;br /&gt;"
#~ msgstr "auto &lt;br /&gt;"

#~ msgid "new_field"
#~ msgstr "nove_pole"

#~ msgid "Field Instructions"
#~ msgstr "Instrukce pole"

#~ msgid "Logged in User Type"
#~ msgstr "Typ přihlášeného uživatele"

#~ msgid "Page Specific"
#~ msgstr "Specifická stránka"

#~ msgid "Post Specific"
#~ msgstr "Specifický příspěvek"

#~ msgid "Taxonomy (Add / Edit)"
#~ msgstr "Taxonomie (přidat / upravit)"

#~ msgid "User (Add / Edit)"
#~ msgstr "Uživatel (přidat / upravit)"

#~ msgid "Media (Edit)"
#~ msgstr "Media (upravit)"

#~ msgid "match"
#~ msgstr "souhlasí"

#~ msgid "all"
#~ msgstr "vše"

#~ msgid "any"
#~ msgstr "libovolné"

#~ msgid "of the above"
#~ msgstr "z uvedeného"

#~ msgid "Unlock options add-on with an activation code"
#~ msgstr "Odemkněte přídavek konfigurace s aktivačním kódem"

#~ msgid "Field groups are created in order <br />from lowest to highest."
#~ msgstr ""
#~ "Skupiny polí jsou vytvořeny v pořadí <br /> od nejnižšího k nejvyššímu."

#~ msgid "<b>Select</b> items to <b>hide</b> them from the edit screen"
#~ msgstr "<b>Vybrat</b> položky pro <b>skrytí</b> z obrazovky úprav"

#~ msgid ""
#~ "If multiple field groups appear on an edit screen, the first field "
#~ "group's options will be used. (the one with the lowest order number)"
#~ msgstr ""
#~ "Pokud se na obrazovce úprav objeví několik skupin polí, bude použito "
#~ "nastavení první skupiny. (s nejnižším pořadovým číslem)"

#~ msgid "Everything Fields deactivated"
#~ msgstr "Všechna pole deaktivována"

#~ msgid "Everything Fields activated"
#~ msgstr "Všechna pole aktivována"

#~ msgid "Navigate to the"
#~ msgstr "Běžte na"

#~ msgid "and select WordPress"
#~ msgstr "a vyberte WordPress"

#~ msgid ""
#~ "Filter posts by selecting a post type<br />\n"
#~ "\t\t\t\tTip: deselect all post types to show all post type's posts"
#~ msgstr ""
#~ "Filtrovat příspěvky výběrem typu příspěvku<br />\n"
#~ "\t\t\t\tTip: zrušte výběr všech typů příspěvku pro zobrazení příspěvků "
#~ "všech typů příspěvků"

#~ msgid "Set to -1 for infinite"
#~ msgstr "Nastavte na -1 pro nekonečno"

#~ msgid "Row Limit"
#~ msgstr "Limit řádků"
