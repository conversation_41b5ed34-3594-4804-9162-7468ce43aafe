# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2022-12-13T15:07:38+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: it_IT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/api/api-template.php:867
msgid "[ACF shortcode value disabled for preview]"
msgstr ""

#: includes/admin/admin-field-group.php:672
msgid "Close Modal"
msgstr ""

#: includes/admin/admin-field-group.php:162
#: assets/build/js/acf-field-group.js:1304
#: assets/build/js/acf-field-group.js:1468
msgid "Field moved to other group"
msgstr ""

#: includes/admin/admin-field-group.php:161 assets/build/js/acf.js:1397
#: assets/build/js/acf.js:1463
msgid "Close modal"
msgstr ""

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr ""

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr ""

#: includes/fields/class-acf-field-select.php:450
#: includes/fields/class-acf-field-true_false.php:197
msgid "Use a stylized checkbox using select2"
msgstr ""

#: includes/fields/class-acf-field-radio.php:257
msgid "Save Other Choice"
msgstr ""

#: includes/fields/class-acf-field-radio.php:246
msgid "Allow Other Choice"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:446
msgid "Add Toggle All"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:405
msgid "Save Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:394
msgid "Allow Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:144
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""

#: pro/admin/admin-updates.php:122,
#: pro/admin/views/html-settings-updates.php:12
msgid "Updates"
msgstr "Aggiornamenti"

#: includes/admin/views/html-admin-navigation.php:79
msgid "Advanced Custom Fields logo"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:22
msgid "Save Changes"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:16
msgid "Field Group Title"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:3
msgid "Add title"
msgstr ""

#. translators: %s url to getting started guide
#: includes/admin/views/field-groups-empty.php:20
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""

#: includes/admin/views/field-groups-empty.php:15
msgid "Add Field Group"
msgstr ""

#. translators: %s url to creating a field group page
#: includes/admin/views/field-groups-empty.php:10
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""

#: includes/admin/views/field-groups-empty.php:5
msgid "Add Your First Field Group"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:16
msgid "Upgrade Now"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:11
msgid "Options Pages"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:10
msgid "ACF Blocks"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:8
msgid "Gallery Field"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:7
msgid "Flexible Content Field"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:6
msgid "Repeater Field"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:4
#: includes/admin/views/html-admin-navigation.php:97
msgid "Unlock Extra Features with ACF PRO"
msgstr ""

#: includes/admin/views/field-group-options.php:242
msgid "Delete Field Group"
msgstr ""

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/field-group-options.php:236
msgid "Created on %1$s at %2$s"
msgstr ""

#: includes/admin/views/field-group-options.php:179
msgid "Group Settings"
msgstr ""

#: includes/admin/views/field-group-options.php:28
msgid "Location Rules"
msgstr ""

#. translators: %s url to field types list
#: includes/admin/views/field-group-fields.php:61
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""

#: includes/admin/views/field-group-fields.php:54
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""

#: includes/admin/views/field-group-fields.php:53
msgid "Add Your First Field"
msgstr ""

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/field-group-fields.php:32
msgid "#"
msgstr ""

#: includes/admin/views/field-group-fields.php:22
#: includes/admin/views/field-group-fields.php:56
#: includes/admin/views/field-group-fields.php:92
#: includes/admin/views/html-admin-form-top.php:21
msgid "Add Field"
msgstr ""

#: includes/admin/views/field-group-field.php:191
#: includes/admin/views/field-group-options.php:40
msgid "Presentation"
msgstr ""

#: includes/admin/views/field-group-field.php:159
msgid "Validation"
msgstr ""

#: includes/admin/views/field-group-field.php:93
msgid "General"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:70
msgid "Import JSON"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:361
msgid "Export Field Groups - Generate PHP"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid "Export As JSON"
msgstr ""

#: includes/admin/admin-field-groups.php:638
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/admin-field-groups.php:585
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/admin-field-groups.php:537
#: includes/admin/admin-field-groups.php:558
msgid "Deactivate"
msgstr ""

#: includes/admin/admin-field-groups.php:537
msgid "Deactivate this item"
msgstr ""

#: includes/admin/admin-field-groups.php:533
#: includes/admin/admin-field-groups.php:557
msgid "Activate"
msgstr ""

#: includes/admin/admin-field-groups.php:533
msgid "Activate this item"
msgstr ""

#: includes/admin/admin-field-group.php:158
#: assets/build/js/acf-field-group.js:2366
#: assets/build/js/acf-field-group.js:2635
msgid "Move field group to trash?"
msgstr ""

#: acf.php:448 includes/admin/admin-field-group.php:353
#: includes/admin/admin-field-groups.php:232
msgctxt "post status"
msgid "Inactive"
msgstr ""

#. Author of the plugin
msgid "WP Engine"
msgstr ""

#: acf.php:506
msgid ""
"Advanced Custom Fields and Advanced Custom Fields should not be active at "
"the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""

#: acf.php:504
msgid ""
"Advanced Custom Fields and Advanced Custom Fields should not be active at "
"the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""

#: includes/acf-value-functions.php:374
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" target=\"_blank"
"\">Learn how to fix this</a>."
msgstr ""
"<strong>%1$s</strong> - Sono state rilevate una o più chiamate per "
"recuperare valori di campi ACF prima che ACF fosse inizializzato. Questo non "
"è supportato e può causare dati non corretti o mancanti. <a href=\"%2$s\" "
"target=\"_blank\">Scopri come correggere questo problema</a>."

#: includes/fields/class-acf-field-user.php:537
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s deve aver un utente con il ruolo %2$s."
msgstr[1] "%1$s deve aver un utente con uno dei seguenti ruoli: %2$s"

#: includes/fields/class-acf-field-user.php:528
msgid "%1$s must have a valid user ID."
msgstr "%1$s deve avere un ID utente valido."

#: includes/fields/class-acf-field-user.php:366
msgid "Invalid request."
msgstr "Richiesta non valida."

#: includes/fields/class-acf-field-select.php:683
msgid "%1$s is not one of %2$s"
msgstr "%1$s non è uno di %2$s"

#: includes/fields/class-acf-field-post_object.php:669
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s deve avere il termine %2$s."
msgstr[1] "%1$s deve avere uno dei seguenti termini: %2$s"

#: includes/fields/class-acf-field-post_object.php:653
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s deve essere di tipo %2$s."
msgstr[1] "%1$s deve essere di uno dei seguenti tipi: %2$s"

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must have a valid post ID."
msgstr "%1$s deve avere un ID articolo valido."

#: includes/fields/class-acf-field-file.php:472
msgid "%s requires a valid attachment ID."
msgstr "%s richiede un ID allegato valido."

#: includes/admin/views/field-group-options.php:205
msgid "Show in REST API"
msgstr "Mostra in API REST"

#: includes/fields/class-acf-field-color_picker.php:167
msgid "Enable Transparency"
msgstr "Abilita trasparenza"

#: includes/fields/class-acf-field-color_picker.php:186
msgid "RGBA Array"
msgstr "Array RGBA"

#: includes/fields/class-acf-field-color_picker.php:96
msgid "RGBA String"
msgstr "Stringa RGBA"

#: includes/fields/class-acf-field-color_picker.php:95
#: includes/fields/class-acf-field-color_picker.php:185
msgid "Hex String"
msgstr "Stringa esadecimale"

#: includes/admin/admin-field-group.php:187
#: assets/build/js/acf-field-group.js:837
#: assets/build/js/acf-field-group.js:933
msgid "Gallery (Pro only)"
msgstr "Galleria (solo versione Pro)"

#: includes/admin/admin-field-group.php:186
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:923
msgid "Clone (Pro only)"
msgstr "Clone (solo versione Pro)"

#: includes/admin/admin-field-group.php:185
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:920
msgid "Flexible Content (Pro only)"
msgstr "Contenuto flessibile (solo versione Pro)"

#: includes/admin/admin-field-group.php:184
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:917
msgid "Repeater (Pro only)"
msgstr "Ripetitore (solo versione Pro)"

#: includes/admin/admin-field-group.php:353
msgctxt "post status"
msgid "Active"
msgstr "Attivo"

#: includes/fields/class-acf-field-email.php:178
msgid "'%s' is not a valid email address"
msgstr "'%s' non è un indirizzo email valido"

#: includes/fields/class-acf-field-color_picker.php:74
msgid "Color value"
msgstr "Valore del colore"

#: includes/fields/class-acf-field-color_picker.php:72
msgid "Select default color"
msgstr "Seleziona il colore predefinito"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Clear color"
msgstr "Rimuovi colore"

#: includes/acf-wp-functions.php:87
msgid "Blocks"
msgstr "Blocchi"

#: includes/acf-wp-functions.php:83
msgid "Options"
msgstr "Opzioni"

#: includes/acf-wp-functions.php:79
msgid "Users"
msgstr "Utenti"

#: includes/acf-wp-functions.php:75
msgid "Menu items"
msgstr "Elementi del menu"

#: includes/acf-wp-functions.php:67
msgid "Widgets"
msgstr "Widget"

#: includes/acf-wp-functions.php:59
msgid "Attachments"
msgstr "Allegati"

#: includes/acf-wp-functions.php:54
msgid "Taxonomies"
msgstr "Tassonomie"

#: includes/acf-wp-functions.php:41
msgid "Posts"
msgstr "Articoli"

#: includes/ajax/class-acf-ajax-local-json-diff.php:68
msgid "JSON field group (newer)"
msgstr "Gruppo di campo JSON (più recente)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:64
msgid "Original field group"
msgstr "Gruppo di campi originale"

#: includes/ajax/class-acf-ajax-local-json-diff.php:59
msgid "Last updated: %s"
msgstr "Ultimo aggiornamento: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:53
msgid "Sorry, this field group is unavailable for diff comparison."
msgstr ""
"Questo gruppo di campi non è disponibile per un confronto di differenze."

#: includes/ajax/class-acf-ajax-local-json-diff.php:43
msgid "Invalid field group ID."
msgstr "ID del gruppo di campi non valido."

#: includes/ajax/class-acf-ajax-local-json-diff.php:36
msgid "Invalid field group parameter(s)."
msgstr "Parametri del gruppo di campi non validi."

#: includes/admin/admin-field-groups.php:506
msgid "Awaiting save"
msgstr "In attesa del salvataggio"

#: includes/admin/admin-field-groups.php:503
msgid "Saved"
msgstr "Salvato"

#: includes/admin/admin-field-groups.php:499
msgid "Import"
msgstr "Importa"

#: includes/admin/admin-field-groups.php:495
msgid "Review changes"
msgstr "Rivedi le modifiche"

#: includes/admin/admin-field-groups.php:471
msgid "Located in: %s"
msgstr "Situato in: %s"

#: includes/admin/admin-field-groups.php:467
msgid "Located in plugin: %s"
msgstr "Situato in plugin: %s"

#: includes/admin/admin-field-groups.php:463
msgid "Located in theme: %s"
msgstr "Situato in tema: %s"

#: includes/admin/admin-field-groups.php:441
msgid "Various"
msgstr "Varie"

#: includes/admin/admin-field-groups.php:200
#: includes/admin/admin-field-groups.php:565
msgid "Sync changes"
msgstr "Sincronizza modifiche"

#: includes/admin/admin-field-groups.php:199
msgid "Loading diff"
msgstr "Caricamento differenze"

#: includes/admin/admin-field-groups.php:198
msgid "Review local JSON changes"
msgstr "Verifica modifiche a JSON locale"

#: includes/admin/admin.php:172
msgid "Visit website"
msgstr "Visita il sito"

#: includes/admin/admin.php:171
msgid "View details"
msgstr "Visualizza i dettagli"

#: includes/admin/admin.php:170
msgid "Version %s"
msgstr "Versione %s"

#: includes/admin/admin.php:169
msgid "Information"
msgstr "Informazioni"

#: includes/admin/admin.php:160
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. I professionisti del nostro "
"Help Desk ti assisteranno per problematiche tecniche."

#: includes/admin/admin.php:156
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the ‘how-tos’ of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Discussioni</a>. Abbiamo una community "
"attiva ed accogliente nei nostri Community Forums che potrebbe aiutarti a "
"raggiungere i risultati da te auspicati tramite l'utilizzo di ACF."

#: includes/admin/admin.php:152
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Documentazione</a>. La nostra estesa "
"documentazione contiene riferimenti e guide per la maggior parte delle "
"situazioni che potresti incontrare."

#: includes/admin/admin.php:149
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Siamo fissati con il supporto, e vogliamo che tu ottenga il meglio dal tuo "
"sito web con ACF. Se incontri difficoltà, ci sono vari posti in cui puoi "
"trovare aiuto:"

#: includes/admin/admin.php:146 includes/admin/admin.php:148
msgid "Help & Support"
msgstr "Aiuto e supporto"

#: includes/admin/admin.php:137
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Utilizza la scheda \"Aiuto e supporto\" per contattarci in caso di necessità "
"di assistenza."

#: includes/admin/admin.php:134
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Prima di creare il tuo primo gruppo di campi, ti raccomandiamo di leggere la "
"nostra guida <a href=\"%s\" target=\"_blank\">Getting started</a> per "
"familiarizzare con la filosofia del plugin e le buone pratiche da adottare."

#: includes/admin/admin.php:132
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Il plugin Advanced Custom Fields fornisce un costruttore visuale di moduli "
"per personalizzare le schermate di modifica di WordPress con campi "
"aggiuntivi, ed un'intuitiva API per la visualizzazione dei campi "
"personalizzati in qualunque file di template di un tema."

#: includes/admin/admin.php:129 includes/admin/admin.php:131
msgid "Overview"
msgstr "Panoramica"

#: includes/locations.php:36
msgid "Location type \"%s\" is already registered."
msgstr "Il tipo di posizione \"%s\" è già registrato."

#: includes/locations.php:25
msgid "Class \"%s\" does not exist."
msgstr "La classe \"%s\" non esiste."

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Nonce non valido."

#: includes/fields/class-acf-field-user.php:361
msgid "Error loading field."
msgstr "Errore nel caricamento del campo."

#: assets/build/js/acf-input.js:2742 assets/build/js/acf-input.js:2811
#: assets/build/js/acf-input.js:2913 assets/build/js/acf-input.js:2987
msgid "Location not found: %s"
msgstr "Posizione non trovata: %s"

#: includes/forms/form-user.php:353
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Errore</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Ruolo utente"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Commento"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Formato articolo"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Elemento menu"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Stato articolo"

#: includes/acf-wp-functions.php:71
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menu"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Posizioni menu"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Tassonomia articolo"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Pagina figlia (ha un genitore)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Pagina genitore (ha figli)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Pagina di primo livello (senza genitore)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Pagina articoli"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Home page"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Tipo di pagina"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Visualizzazione back-end"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Visualizzazione front-end"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Connesso"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Utente corrente"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Template pagina"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Registra"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Aggiungi / Modifica"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Modulo utente"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Genitore pagina"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super amministratore"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Ruolo dell'utente corrente"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Template predefinito"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Template articolo"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Categoria articolo"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Tutti i formati %s"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Allegato"

#: includes/validation.php:365
msgid "%s value is required"
msgstr "Il valore %s è obbligatorio"

#: includes/admin/views/field-group-field-conditional-logic.php:59
msgid "Show this field if"
msgstr "Mostra questo campo se"

#: includes/admin/views/field-group-field-conditional-logic.php:26
#: includes/admin/views/field-group-field.php:279
msgid "Conditional Logic"
msgstr "Condizione logica"

#: includes/admin/admin.php:207
#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/html-location-rule.php:92
msgid "and"
msgstr "e"

#: includes/admin/admin-field-groups.php:290
msgid "Local JSON"
msgstr "JSON locale"

#: includes/admin/views/field-group-pro-features.php:9
msgid "Clone Field"
msgstr "Campo clone"

#: includes/admin/views/html-notice-upgrade.php:30
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Controlla anche che tutti gli add-on premium (%s) siano aggiornati "
"all'ultima versione."

#: includes/admin/views/html-notice-upgrade.php:28
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Questa versione contiene miglioramenti al tuo database e richiede un "
"aggiornamento."

#: includes/admin/views/html-notice-upgrade.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Grazie per aver aggiornato a %1$s v%2$s!"

#: includes/admin/views/html-notice-upgrade.php:27
msgid "Database Upgrade Required"
msgstr "È richiesto un aggiornamento del database"

#: includes/admin/views/html-notice-upgrade.php:18
msgid "Options Page"
msgstr "Pagina opzioni"

#: includes/admin/views/html-notice-upgrade.php:15
msgid "Gallery"
msgstr "Galleria"

#: includes/admin/views/html-notice-upgrade.php:12
msgid "Flexible Content"
msgstr "Contenuto flessibile"

#: includes/admin/views/html-notice-upgrade.php:9
msgid "Repeater"
msgstr "Ripetitore"

#: includes/admin/views/html-admin-tools.php:24
msgid "Back to all tools"
msgstr "Torna a tutti gli strumenti"

#: includes/admin/views/field-group-options.php:160
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Se più gruppi di campi appaiono su una schermata di modifica, verranno usate "
"le opzioni del primo gruppo di campi usato (quello con il numero d'ordine "
"più basso)"

#: includes/admin/views/field-group-options.php:160
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Seleziona</b> gli elementi per <b>nasconderli</b> dalla schermata di "
"modifica."

#: includes/admin/views/field-group-options.php:159
msgid "Hide on screen"
msgstr "Nascondi nella schermata"

#: includes/admin/views/field-group-options.php:151
msgid "Send Trackbacks"
msgstr "Invia trackback"

#: includes/admin/views/field-group-options.php:150
msgid "Tags"
msgstr "Tag"

#: includes/admin/views/field-group-options.php:149
msgid "Categories"
msgstr "Categorie"

#: includes/admin/views/field-group-options.php:147
msgid "Page Attributes"
msgstr "Attributi della pagina"

#: includes/admin/views/field-group-options.php:146
msgid "Format"
msgstr "Formato"

#: includes/admin/views/field-group-options.php:145
msgid "Author"
msgstr "Autore"

#: includes/admin/views/field-group-options.php:144
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/field-group-options.php:143
msgid "Revisions"
msgstr "Revisioni"

#: includes/acf-wp-functions.php:63
#: includes/admin/views/field-group-options.php:142
msgid "Comments"
msgstr "Commenti"

#: includes/admin/views/field-group-options.php:141
msgid "Discussion"
msgstr "Discussione"

#: includes/admin/views/field-group-options.php:139
msgid "Excerpt"
msgstr "Riassunto"

#: includes/admin/views/field-group-options.php:138
msgid "Content Editor"
msgstr "Editor contenuto"

#: includes/admin/views/field-group-options.php:137
msgid "Permalink"
msgstr "Permalink"

#: includes/admin/views/field-group-options.php:222
msgid "Shown in field group list"
msgstr "Mostrato nell'elenco dei gruppi di campi"

#: includes/admin/views/field-group-options.php:122
msgid "Field groups with a lower order will appear first"
msgstr "I gruppi di campi con un valore inferiore appariranno per primi"

#: includes/admin/views/field-group-options.php:121
msgid "Order No."
msgstr "N. ordine"

#: includes/admin/views/field-group-options.php:112
msgid "Below fields"
msgstr "Sotto ai campi"

#: includes/admin/views/field-group-options.php:111
msgid "Below labels"
msgstr "Sotto alle etichette"

#: includes/admin/views/field-group-options.php:104
msgid "Instruction placement"
msgstr "Posizione istruzioni"

#: includes/admin/views/field-group-options.php:87
msgid "Label placement"
msgstr "Posizione etichetta"

#: includes/admin/views/field-group-options.php:77
msgid "Side"
msgstr "Laterale"

#: includes/admin/views/field-group-options.php:76
msgid "Normal (after content)"
msgstr "Normale (dopo il contenuto)"

#: includes/admin/views/field-group-options.php:75
msgid "High (after title)"
msgstr "Alta (dopo il titolo)"

#: includes/admin/views/field-group-options.php:68
msgid "Position"
msgstr "Posizione"

#: includes/admin/views/field-group-options.php:59
msgid "Seamless (no metabox)"
msgstr "Senza soluzione di continuità (senza metabox)"

#: includes/admin/views/field-group-options.php:58
msgid "Standard (WP metabox)"
msgstr "Standard (metabox WP)"

#: includes/admin/views/field-group-options.php:51
msgid "Style"
msgstr "Stile"

#: includes/admin/views/field-group-fields.php:44
msgid "Type"
msgstr "Tipo"

#: includes/admin/admin-field-groups.php:285
#: includes/admin/views/field-group-fields.php:43
msgid "Key"
msgstr "Chiave"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/field-group-fields.php:37
msgid "Order"
msgstr "Ordine"

#: includes/admin/views/field-group-field.php:294
msgid "Close Field"
msgstr "Chiudi campo"

#: includes/admin/views/field-group-field.php:235
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:219
msgid "class"
msgstr "classe"

#: includes/admin/views/field-group-field.php:256
msgid "width"
msgstr "larghezza"

#: includes/admin/views/field-group-field.php:250
msgid "Wrapper Attributes"
msgstr "Attributi contenitore"

#: includes/admin/views/field-group-field.php:171
msgid "Required"
msgstr ""

#: includes/admin/views/field-group-field.php:203
msgid "Instructions for authors. Shown when submitting data"
msgstr "Istruzioni per gli autori. Mostrato in fase di invio dei dati"

#: includes/admin/views/field-group-field.php:202
msgid "Instructions"
msgstr "Istruzioni"

#: includes/admin/views/field-group-field.php:106
msgid "Field Type"
msgstr "Tipo di campo"

#: includes/admin/views/field-group-field.php:134
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Singola parola, nessun spazio. Sottolineatura e trattini consentiti"

#: includes/admin/views/field-group-field.php:133
msgid "Field Name"
msgstr "Nome campo"

#: includes/admin/views/field-group-field.php:121
msgid "This is the name which will appear on the EDIT page"
msgstr "Questo è il nome che apparirà sulla pagina di modifica"

#: includes/admin/views/field-group-field.php:120
msgid "Field Label"
msgstr "Etichetta campo"

#: includes/admin/views/field-group-field.php:70
msgid "Delete"
msgstr "Elimina"

#: includes/admin/views/field-group-field.php:70
msgid "Delete field"
msgstr "Elimina campo"

#: includes/admin/views/field-group-field.php:68
msgid "Move"
msgstr "Sposta"

#: includes/admin/views/field-group-field.php:68
msgid "Move field to another group"
msgstr "Sposta campo in un altro gruppo"

#: includes/admin/views/field-group-field.php:66
msgid "Duplicate field"
msgstr "Duplica campo"

#: includes/admin/views/field-group-field.php:62
#: includes/admin/views/field-group-field.php:65
msgid "Edit field"
msgstr "Modifica campo"

#: includes/admin/views/field-group-field.php:58
msgid "Drag to reorder"
msgstr "Trascina per riordinare"

#: includes/admin/admin-field-group.php:168
#: includes/admin/views/html-location-group.php:3
#: assets/build/js/acf-field-group.js:1935
#: assets/build/js/acf-field-group.js:2166
msgid "Show this field group if"
msgstr "Mostra questo gruppo di campo se"

#: includes/admin/views/html-admin-page-upgrade.php:94
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Nessun aggiornamento disponibile."

#: includes/admin/views/html-admin-page-upgrade.php:33
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Aggiornamento del database completato. <a href=\"%s\">Leggi le novità</a>"

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Lettura attività di aggiornamento..."

#: includes/admin/views/html-admin-page-upgrade-network.php:165
#: includes/admin/views/html-admin-page-upgrade.php:65
msgid "Upgrade failed."
msgstr "Aggiornamento fallito."

#: includes/admin/views/html-admin-page-upgrade-network.php:162
msgid "Upgrade complete."
msgstr "Aggiornamento completato."

#: includes/admin/views/html-admin-page-upgrade-network.php:148
#: includes/admin/views/html-admin-page-upgrade.php:31
msgid "Upgrading data to version %s"
msgstr "Aggiornamento dati alla versione %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:121
#: includes/admin/views/html-notice-upgrade.php:44
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Si raccomanda vivamente di eseguire il backup del database prima di "
"procedere. Sei sicuro di voler eseguire il programma di aggiornamento adesso?"

#: includes/admin/views/html-admin-page-upgrade-network.php:117
msgid "Please select at least one site to upgrade."
msgstr "Seleziona almeno un sito da aggiornare."

#: includes/admin/views/html-admin-page-upgrade-network.php:97
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"L'aggiornamento del database è stato completato. <a href=\"%s\">Ritorna alla "
"bacheca del network</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:80
msgid "Site is up to date"
msgstr "Il sito è aggiornato"

#: includes/admin/views/html-admin-page-upgrade-network.php:78
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr ""
"Il sito necessita di un aggiornamento del database dalla versione %1$s alla "
"%2$s"

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Sito"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:96
msgid "Upgrade Sites"
msgstr "Aggiorna siti"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"I seguenti siti hanno necessità di un aggiornamento del DB. Controlla quelli "
"che vuoi aggiornare e fai clic su %s."

#: includes/admin/views/field-group-field-conditional-logic.php:171
#: includes/admin/views/field-group-locations.php:38
msgid "Add rule group"
msgstr "Aggiungi gruppo di regole"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Crea un insieme di regole per determinare in quali schermate di modifica "
"saranno usati i campi personalizzati avanzati"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Regole"

#: includes/admin/tools/class-acf-admin-tool-export.php:478
msgid "Copied"
msgstr "Copiato"

#: includes/admin/tools/class-acf-admin-tool-export.php:441
msgid "Copy to clipboard"
msgstr "Copia negli appunti"

#: includes/admin/tools/class-acf-admin-tool-export.php:362
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Il codice seguente può essere utilizzato per registrare una versione locale "
"dei gruppi di campi selezionato. Un gruppo di campi locale può fornire "
"numerosi vantaggi come ad esempio tempi di caricamento più veloci, controllo "
"di versione e il controllo dinamico di campi e impostazioni. Semplicemente "
"copia e incolla il seguente codice nel file functions.php del tuo tema o "
"includilo tramite un file esterno."

#: includes/admin/tools/class-acf-admin-tool-export.php:329
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:233
#: includes/admin/tools/class-acf-admin-tool-export.php:262
msgid "Select Field Groups"
msgstr "Seleziona gruppi di campi"

#: includes/admin/tools/class-acf-admin-tool-export.php:167
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "Esportato 1 gruppo di campi."
msgstr[1] "Esportati %s gruppi di campi."

#: includes/admin/tools/class-acf-admin-tool-export.php:96
#: includes/admin/tools/class-acf-admin-tool-export.php:131
msgid "No field groups selected"
msgstr "Nessun gruppo di campi selezionato"

#: includes/admin/tools/class-acf-admin-tool-export.php:39
#: includes/admin/tools/class-acf-admin-tool-export.php:337
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "Genera PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:35
msgid "Export Field Groups"
msgstr "Esporta gruppi di campi"

#: includes/admin/tools/class-acf-admin-tool-import.php:146
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Importato 1 gruppo di campi"
msgstr[1] "Importati %s gruppi di campi"

#: includes/admin/tools/class-acf-admin-tool-import.php:115
msgid "Import file empty"
msgstr "File da importare vuoto"

#: includes/admin/tools/class-acf-admin-tool-import.php:106
msgid "Incorrect file type"
msgstr "Tipo file non corretto"

#: includes/admin/tools/class-acf-admin-tool-import.php:101
msgid "Error uploading file. Please try again"
msgstr "Errore durante il caricamento del file. Prova di nuovo"

#: includes/admin/tools/class-acf-admin-tool-import.php:51
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:28
#: includes/admin/tools/class-acf-admin-tool-import.php:50
msgid "Import Field Groups"
msgstr "Importa gruppi di campi"

#: includes/admin/admin-field-groups.php:494
msgid "Sync"
msgstr "Sincronizza"

#: includes/admin/admin-field-groups.php:942
msgid "Select %s"
msgstr "Seleziona %s"

#: includes/admin/admin-field-groups.php:527
#: includes/admin/admin-field-groups.php:556
#: includes/admin/views/field-group-field.php:66
msgid "Duplicate"
msgstr "Duplica"

#: includes/admin/admin-field-groups.php:527
msgid "Duplicate this item"
msgstr "Duplica questo elemento"

#: includes/admin/admin-field-groups.php:284
#: includes/admin/views/field-group-options.php:221
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
msgid "Description"
msgstr "Descrizione"

#: includes/admin/admin-field-groups.php:491
#: includes/admin/admin-field-groups.php:829
msgid "Sync available"
msgstr "Sincronizzazione disponibile"

#: includes/admin/admin-field-groups.php:754
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "Gruppo di campi sincronizzato."
msgstr[1] "%s gruppi di campi sincronizzati."

#: includes/admin/admin-field-groups.php:696
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Gruppo di campi duplicato."
msgstr[1] "%s gruppi di campi duplicati."

#: includes/admin/admin-field-groups.php:118
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Attivo <span class=\"count\">(%s)</span>"
msgstr[1] "Attivi <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:237
msgid "Review sites & upgrade"
msgstr "Verifica i siti ed effettua l'aggiornamento"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:93
#: includes/admin/admin-upgrade.php:94 includes/admin/admin-upgrade.php:213
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Aggiorna database"

#: includes/admin/admin.php:49 includes/admin/views/field-group-options.php:140
msgid "Custom Fields"
msgstr "Campi personalizzati"

#: includes/admin/admin-field-group.php:718
msgid "Move Field"
msgstr "Sposta campo"

#: includes/admin/admin-field-group.php:707
#: includes/admin/admin-field-group.php:711
msgid "Please select the destination for this field"
msgstr "Seleziona la destinazione per questo campo"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/admin-field-group.php:668
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "Il campo %1$s può essere trovato nel gruppo di campi %2$s"

#: includes/admin/admin-field-group.php:665
msgid "Move Complete."
msgstr "Spostamento completato."

#: includes/admin/views/field-group-field.php:273
#: includes/admin/views/field-group-options.php:189
msgid "Active"
msgstr "Attivo"

#: includes/admin/admin-field-group.php:325
msgid "Field Keys"
msgstr "Chiavi campo"

#: includes/admin/admin-field-group.php:224
#: includes/admin/tools/class-acf-admin-tool-export.php:286
msgid "Settings"
msgstr "Impostazioni"

#: includes/admin/admin-field-groups.php:286
msgid "Location"
msgstr "Posizione"

#: includes/admin/admin-field-group.php:169 assets/build/js/acf-input.js:983
#: assets/build/js/acf-input.js:1075
msgid "Null"
msgstr "Null"

#: includes/acf-field-group-functions.php:846
#: includes/admin/admin-field-group.php:166
#: assets/build/js/acf-field-group.js:1158
#: assets/build/js/acf-field-group.js:1310
msgid "copy"
msgstr "copia"

#: includes/admin/admin-field-group.php:165
#: assets/build/js/acf-field-group.js:344
#: assets/build/js/acf-field-group.js:389
msgid "(this field)"
msgstr "(questo campo)"

#: includes/admin/admin-field-group.php:163 assets/build/js/acf-input.js:918
#: assets/build/js/acf-input.js:943 assets/build/js/acf-input.js:1002
#: assets/build/js/acf-input.js:1030
msgid "Checked"
msgstr "Selezionato"

#: includes/admin/admin-field-group.php:160
#: assets/build/js/acf-field-group.js:1249
#: assets/build/js/acf-field-group.js:1408
msgid "Move Custom Field"
msgstr "Sposta campo personalizzato"

#: includes/admin/admin-field-group.php:159
#: assets/build/js/acf-field-group.js:370
#: assets/build/js/acf-field-group.js:415
msgid "No toggle fields available"
msgstr "Nessun campo attiva/disattiva disponibile"

#: includes/admin/admin-field-group.php:157
msgid "Field group title is required"
msgstr "Il titolo del gruppo di campi è necessario"

#: includes/admin/admin-field-group.php:156
#: assets/build/js/acf-field-group.js:1238
#: assets/build/js/acf-field-group.js:1394
msgid "This field cannot be moved until its changes have been saved"
msgstr ""
"Questo campo non può essere spostato fino a quando non saranno state salvate "
"le modifiche"

#: includes/admin/admin-field-group.php:155
#: assets/build/js/acf-field-group.js:1049
#: assets/build/js/acf-field-group.js:1192
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr ""
"La stringa \"field_\" non può essere usata come inizio nel nome di un campo"

#: includes/admin/admin-field-group.php:82
msgid "Field group draft updated."
msgstr "Bozza del gruppo di campi aggiornata."

#: includes/admin/admin-field-group.php:81
msgid "Field group scheduled for."
msgstr "Gruppo di campi programmato."

#: includes/admin/admin-field-group.php:80
msgid "Field group submitted."
msgstr "Gruppo di campi inviato."

#: includes/admin/admin-field-group.php:79
msgid "Field group saved."
msgstr "Gruppo di campi salvato."

#: includes/admin/admin-field-group.php:78
msgid "Field group published."
msgstr "Gruppo di campi pubblicato."

#: includes/admin/admin-field-group.php:75
msgid "Field group deleted."
msgstr "Gruppo di campi eliminato."

#: includes/admin/admin-field-group.php:73
#: includes/admin/admin-field-group.php:74
#: includes/admin/admin-field-group.php:76
msgid "Field group updated."
msgstr "Gruppo di campi aggiornato."

#: includes/admin/admin-tools.php:119
#: includes/admin/views/html-admin-navigation.php:109
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Strumenti"

#: includes/locations/abstract-acf-location.php:106
msgid "is not equal to"
msgstr "non è uguale a"

#: includes/locations/abstract-acf-location.php:105
msgid "is equal to"
msgstr "è uguale a"

#: includes/locations.php:102
msgid "Forms"
msgstr "Moduli"

#: includes/locations.php:100 includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Pagina"

#: includes/locations.php:99 includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Articolo"

#: includes/fields.php:358
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:357
msgid "Relational"
msgstr "Relazionale"

#: includes/fields.php:356
msgid "Choice"
msgstr "Scelta"

#: includes/fields.php:354
msgid "Basic"
msgstr "Base"

#: includes/fields.php:313
msgid "Unknown"
msgstr "Sconosciuto"

#: includes/fields.php:313
msgid "Field type does not exist"
msgstr "Il tipo di campo non esiste"

#: includes/forms/form-front.php:236
msgid "Spam Detected"
msgstr "Spam rilevato"

#: includes/forms/form-front.php:107
msgid "Post updated"
msgstr "Articolo aggiornato"

#: includes/forms/form-front.php:106
msgid "Update"
msgstr "Aggiorna"

#: includes/forms/form-front.php:57
msgid "Validate Email"
msgstr "Valida email"

#: includes/fields.php:355 includes/forms/form-front.php:49
msgid "Content"
msgstr "Contenuto"

#: includes/forms/form-front.php:40
msgid "Title"
msgstr "Titolo"

#: includes/assets.php:371 includes/forms/form-comment.php:160
#: assets/build/js/acf-input.js:7310 assets/build/js/acf-input.js:7876
msgid "Edit field group"
msgstr "Modifica gruppo di campi"

#: includes/admin/admin-field-group.php:181 assets/build/js/acf-input.js:1125
#: assets/build/js/acf-input.js:1230
msgid "Selection is less than"
msgstr "La selezione è minore di"

#: includes/admin/admin-field-group.php:180 assets/build/js/acf-input.js:1106
#: assets/build/js/acf-input.js:1202
msgid "Selection is greater than"
msgstr "La selezione è maggiore di"

#: includes/admin/admin-field-group.php:179 assets/build/js/acf-input.js:1075
#: assets/build/js/acf-input.js:1170
msgid "Value is less than"
msgstr "Il valore è minore di"

#: includes/admin/admin-field-group.php:178 assets/build/js/acf-input.js:1045
#: assets/build/js/acf-input.js:1139
msgid "Value is greater than"
msgstr "Il valore è maggiore di"

#: includes/admin/admin-field-group.php:177 assets/build/js/acf-input.js:888
#: assets/build/js/acf-input.js:960
msgid "Value contains"
msgstr "Il valore contiene"

#: includes/admin/admin-field-group.php:176 assets/build/js/acf-input.js:862
#: assets/build/js/acf-input.js:926
msgid "Value matches pattern"
msgstr "Il valore ha corrispondenza con il pattern"

#: includes/admin/admin-field-group.php:175 assets/build/js/acf-input.js:840
#: assets/build/js/acf-input.js:1023 assets/build/js/acf-input.js:903
#: assets/build/js/acf-input.js:1116
msgid "Value is not equal to"
msgstr "Il valore non è uguale a"

#: includes/admin/admin-field-group.php:174 assets/build/js/acf-input.js:810
#: assets/build/js/acf-input.js:964 assets/build/js/acf-input.js:864
#: assets/build/js/acf-input.js:1053
msgid "Value is equal to"
msgstr "Il valore è uguale a"

#: includes/admin/admin-field-group.php:173 assets/build/js/acf-input.js:788
#: assets/build/js/acf-input.js:841
msgid "Has no value"
msgstr "Non ha un valore"

#: includes/admin/admin-field-group.php:172 assets/build/js/acf-input.js:758
#: assets/build/js/acf-input.js:783
msgid "Has any value"
msgstr "Ha qualunque valore"

#: includes/assets.php:352 assets/build/js/acf.js:1524
#: assets/build/js/acf.js:1604
msgid "Cancel"
msgstr "Annulla"

#: includes/assets.php:348 assets/build/js/acf.js:1698
#: assets/build/js/acf.js:1801
msgid "Are you sure?"
msgstr "Sei sicuro?"

#: includes/assets.php:368 assets/build/js/acf-input.js:9343
#: assets/build/js/acf-input.js:10172
msgid "%d fields require attention"
msgstr "%d campi necessitano attenzione"

#: includes/assets.php:367 assets/build/js/acf-input.js:9341
#: assets/build/js/acf-input.js:10168
msgid "1 field requires attention"
msgstr "1 campo richiede attenzione"

#: includes/assets.php:366 includes/validation.php:287
#: includes/validation.php:297 assets/build/js/acf-input.js:9336
#: assets/build/js/acf-input.js:10163
msgid "Validation failed"
msgstr "Validazione fallita"

#: includes/assets.php:365 assets/build/js/acf-input.js:9499
#: assets/build/js/acf-input.js:10346
msgid "Validation successful"
msgstr "Validazione avvenuta con successo"

#: includes/media.php:54 assets/build/js/acf-input.js:7138
#: assets/build/js/acf-input.js:7680
msgid "Restricted"
msgstr "Limitato"

#: includes/media.php:53 assets/build/js/acf-input.js:6953
#: assets/build/js/acf-input.js:7444
msgid "Collapse Details"
msgstr "Comprimi dettagli"

#: includes/media.php:52 assets/build/js/acf-input.js:6953
#: assets/build/js/acf-input.js:7441
msgid "Expand Details"
msgstr "Espandi dettagli"

#: includes/media.php:51 assets/build/js/acf-input.js:6820
#: assets/build/js/acf-input.js:7289
msgid "Uploaded to this post"
msgstr "Caricato in questo articolo"

#: includes/media.php:50 assets/build/js/acf-input.js:6859
#: assets/build/js/acf-input.js:7328
msgctxt "verb"
msgid "Update"
msgstr "Aggiorna"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Modifica"

#: includes/assets.php:362 assets/build/js/acf-input.js:9113
#: assets/build/js/acf-input.js:9934
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Le modifiche effettuate verranno cancellate se esci da questa pagina"

#: includes/api/api-helpers.php:3395
msgid "File type must be %s."
msgstr "La tipologia del file deve essere %s."

#: includes/admin/admin-field-group.php:167
#: includes/admin/views/field-group-field-conditional-logic.php:59
#: includes/admin/views/field-group-field-conditional-logic.php:169
#: includes/admin/views/field-group-locations.php:36
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3391 assets/build/js/acf-field-group.js:492
#: assets/build/js/acf-field-group.js:1973
#: assets/build/js/acf-field-group.js:544
#: assets/build/js/acf-field-group.js:2210
msgid "or"
msgstr "oppure"

#: includes/api/api-helpers.php:3364
msgid "File size must not exceed %s."
msgstr "La dimensione del file non deve superare %s."

#: includes/api/api-helpers.php:3359
msgid "File size must be at least %s."
msgstr "La dimensione del file deve essere di almeno %s."

#: includes/api/api-helpers.php:3344
msgid "Image height must not exceed %dpx."
msgstr "L'altezza dell'immagine non deve superare i %dpx."

#: includes/api/api-helpers.php:3339
msgid "Image height must be at least %dpx."
msgstr "L'altezza dell'immagine deve essere di almeno %dpx."

#: includes/api/api-helpers.php:3325
msgid "Image width must not exceed %dpx."
msgstr "La larghezza dell'immagine non deve superare i %dpx."

#: includes/api/api-helpers.php:3320
msgid "Image width must be at least %dpx."
msgstr "La larghezza dell'immagine deve essere di almeno %dpx."

#: includes/api/api-helpers.php:1566 includes/api/api-term.php:147
msgid "(no title)"
msgstr "(nessun titolo)"

#: includes/api/api-helpers.php:861
msgid "Full Size"
msgstr "Dimensione originale"

#: includes/api/api-helpers.php:820
msgid "Large"
msgstr "Grande"

#: includes/api/api-helpers.php:819
msgid "Medium"
msgstr "Media"

#: includes/api/api-helpers.php:818
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/acf-field-functions.php:846
#: includes/admin/admin-field-group.php:164
#: assets/build/js/acf-field-group.js:789
#: assets/build/js/acf-field-group.js:859
msgid "(no label)"
msgstr "(nessuna etichetta)"

#: includes/fields/class-acf-field-textarea.php:142
msgid "Sets the textarea height"
msgstr "Imposta l'altezza dell'area di testo"

#: includes/fields/class-acf-field-textarea.php:141
msgid "Rows"
msgstr "Righe"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Area di testo"

#: includes/fields/class-acf-field-checkbox.php:447
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"Anteponi un checkbox aggiuntivo per poter selezionare/deselzionare tutte le "
"opzioni"

#: includes/fields/class-acf-field-checkbox.php:409
msgid "Save 'custom' values to the field's choices"
msgstr "Salva i valori 'personalizzati' per le scelte del campo"

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Allow 'custom' values to be added"
msgstr "Consenti l'aggiunta di valori 'personalizzati'"

#: includes/fields/class-acf-field-checkbox.php:233
msgid "Add new choice"
msgstr "Aggiungi nuova scelta"

#: includes/fields/class-acf-field-checkbox.php:170
msgid "Toggle All"
msgstr "Commuta tutti"

#: includes/fields/class-acf-field-page_link.php:477
msgid "Allow Archives URLs"
msgstr "Consenti URL degli archivi"

#: includes/fields/class-acf-field-page_link.php:165
msgid "Archives"
msgstr "Archivi"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Link pagina"

#: includes/fields/class-acf-field-taxonomy.php:945
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Aggiungi"

#: includes/admin/views/field-group-fields.php:42
#: includes/fields/class-acf-field-taxonomy.php:910
msgid "Name"
msgstr "Nome"

#: includes/fields/class-acf-field-taxonomy.php:894
msgid "%s added"
msgstr "%s aggiunto"

#: includes/fields/class-acf-field-taxonomy.php:858
msgid "%s already exists"
msgstr "%s esiste già"

#: includes/fields/class-acf-field-taxonomy.php:846
msgid "User unable to add new %s"
msgstr "Utente non abilitato ad aggiungere %s"

#: includes/fields/class-acf-field-taxonomy.php:756
msgid "Term ID"
msgstr "ID termine"

#: includes/fields/class-acf-field-taxonomy.php:755
msgid "Term Object"
msgstr "Oggetto termine"

#: includes/fields/class-acf-field-taxonomy.php:740
msgid "Load value from posts terms"
msgstr "Carica valori dai termini dell'articolo"

#: includes/fields/class-acf-field-taxonomy.php:739
msgid "Load Terms"
msgstr "Carica termini"

#: includes/fields/class-acf-field-taxonomy.php:729
msgid "Connect selected terms to the post"
msgstr "Collega i termini selezionati all'articolo"

#: includes/fields/class-acf-field-taxonomy.php:728
msgid "Save Terms"
msgstr "Salva termini"

#: includes/fields/class-acf-field-taxonomy.php:718
msgid "Allow new terms to be created whilst editing"
msgstr "Abilita la creazione di nuovi termini in fase di modifica"

#: includes/fields/class-acf-field-taxonomy.php:717
msgid "Create Terms"
msgstr "Crea termini"

#: includes/fields/class-acf-field-taxonomy.php:776
msgid "Radio Buttons"
msgstr "Pulsanti radio"

#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Single Value"
msgstr "Valore singolo"

#: includes/fields/class-acf-field-taxonomy.php:773
msgid "Multi Select"
msgstr "Selezione multipla"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Checkbox"
msgstr "Checkbox"

#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Multiple Values"
msgstr "Valori multipli"

#: includes/fields/class-acf-field-taxonomy.php:766
msgid "Select the appearance of this field"
msgstr "Seleziona l'aspetto di questo campo"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Appearance"
msgstr "Aspetto"

#: includes/fields/class-acf-field-taxonomy.php:707
msgid "Select the taxonomy to be displayed"
msgstr "Seleziona la tassonomia da mostrare"

#: includes/fields/class-acf-field-taxonomy.php:668
msgctxt "No Terms"
msgid "No %s"
msgstr ""

#: includes/fields/class-acf-field-number.php:263
msgid "Value must be equal to or lower than %d"
msgstr "Il valore deve essere uguale o inferiore a %d"

#: includes/fields/class-acf-field-number.php:256
msgid "Value must be equal to or higher than %d"
msgstr "Il valore deve essere uguale o superiore a %d"

#: includes/fields/class-acf-field-number.php:241
msgid "Value must be a number"
msgstr "Il valore deve essere un numero"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Numero"

#: includes/fields/class-acf-field-radio.php:261
msgid "Save 'other' values to the field's choices"
msgstr "Salvare gli 'altri' valori nelle scelte del campo"

#: includes/fields/class-acf-field-radio.php:250
msgid "Add 'other' choice to allow for custom values"
msgstr "Aggiungi scelta 'altro' per consentire valori personalizzati"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Radio button"

#: includes/fields/class-acf-field-accordion.php:104
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Definisce il punto di chiusura del precedente accordion. Questo accordion "
"non sarà visibile."

#: includes/fields/class-acf-field-accordion.php:93
msgid "Allow this accordion to open without closing others."
msgstr "Consenti a questo accordion di essere aperto senza chiudere gli altri."

#: includes/fields/class-acf-field-accordion.php:92
msgid "Multi-expand"
msgstr "Espansione multipla"

#: includes/fields/class-acf-field-accordion.php:82
msgid "Display this accordion as open on page load."
msgstr "Mostra questo accordion aperto l caricamento della pagina."

#: includes/fields/class-acf-field-accordion.php:81
msgid "Open"
msgstr "Aperto"

#: includes/fields/class-acf-field-accordion.php:25
msgid "Accordion"
msgstr "Fisarmonica"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-file.php:276
msgid "Restrict which files can be uploaded"
msgstr "Limita i tipi di file che possono essere caricati"

#: includes/fields/class-acf-field-file.php:217
msgid "File ID"
msgstr "ID file"

#: includes/fields/class-acf-field-file.php:216
msgid "File URL"
msgstr "URL file"

#: includes/fields/class-acf-field-file.php:215
msgid "File Array"
msgstr "Array di file"

#: includes/fields/class-acf-field-file.php:183
msgid "Add File"
msgstr "Aggiungi file"

#: includes/admin/tools/class-acf-admin-tool-import.php:94
#: includes/fields/class-acf-field-file.php:183
msgid "No file selected"
msgstr "Nessun file selezionato"

#: includes/fields/class-acf-field-file.php:147
msgid "File name"
msgstr "Nome file"

#: includes/fields/class-acf-field-file.php:60
#: assets/build/js/acf-input.js:2466 assets/build/js/acf-input.js:2612
msgid "Update File"
msgstr "Aggiorna file"

#: includes/fields/class-acf-field-file.php:59
#: assets/build/js/acf-input.js:2465 assets/build/js/acf-input.js:2611
msgid "Edit File"
msgstr "Modifica file"

#: includes/admin/tools/class-acf-admin-tool-import.php:59
#: includes/fields/class-acf-field-file.php:58
#: assets/build/js/acf-input.js:2439 assets/build/js/acf-input.js:2584
msgid "Select File"
msgstr "Seleziona file"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "File"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Password"

#: includes/fields/class-acf-field-select.php:391
msgid "Specify the value returned"
msgstr "Specifica il valore restituito"

#: includes/fields/class-acf-field-select.php:460
msgid "Use AJAX to lazy load choices?"
msgstr "Usa AJAX per il caricamento differito delle opzioni?"

#: includes/fields/class-acf-field-checkbox.php:358
#: includes/fields/class-acf-field-select.php:380
msgid "Enter each default value on a new line"
msgstr "Inserire ogni valore predefinito su una nuova linea"

#: includes/fields/class-acf-field-select.php:255 includes/media.php:48
#: assets/build/js/acf-input.js:6718 assets/build/js/acf-input.js:7174
msgctxt "verb"
msgid "Select"
msgstr "Seleziona"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Caricamento fallito"

#: includes/fields/class-acf-field-select.php:117
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Ricerca &hellip;"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Caricamento di altri risultati&hellip;"

#: includes/fields/class-acf-field-select.php:115
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Puoi selezionare solo %d elementi"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Puoi selezionare solo 1 elemento"

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Elimina %d caratteri"

#: includes/fields/class-acf-field-select.php:112
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Elimina 1 carattere"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "﻿Inserisci %d o più caratteri"

#: includes/fields/class-acf-field-select.php:110
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Inserisci 1 o più caratteri"

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Nessun riscontro trovato"

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d risultati disponibili, usa i tasti freccia su e giù per scorrere."

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Un risultato disponibile, premi invio per selezionarlo."

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:777
msgctxt "noun"
msgid "Select"
msgstr "Seleziona"

#: includes/fields/class-acf-field-user.php:74
msgid "User ID"
msgstr "ID utente"

#: includes/fields/class-acf-field-user.php:73
msgid "User Object"
msgstr "Oggetto utente"

#: includes/fields/class-acf-field-user.php:72
msgid "User Array"
msgstr "Array di utenti"

#: includes/fields/class-acf-field-user.php:60
msgid "All user roles"
msgstr "Tutti i ruoli utente"

#: includes/fields/class-acf-field-user.php:52
msgid "Filter by role"
msgstr "Filtra per ruolo"

#: includes/fields/class-acf-field-user.php:20 includes/locations.php:101
msgid "User"
msgstr "Utente"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Separatore"

#: includes/fields/class-acf-field-color_picker.php:73
msgid "Select Color"
msgstr "Seleziona colore"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Default"
msgstr "Predefinito"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Clear"
msgstr "Rimuovi"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Selettore colore"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Seleziona"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Fatto"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Ora"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Fuso orario"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microsecondo"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Millisecondo"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Secondo"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuto"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Ora"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Orario"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Scegli orario"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Selettore data/ora"

#: includes/fields/class-acf-field-accordion.php:103
msgid "Endpoint"
msgstr "Endpoint"

#: includes/admin/views/field-group-options.php:95
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Allineamento a sinistra"

#: includes/admin/views/field-group-options.php:94
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Allineamento in alto"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Posizione"

#: includes/fields/class-acf-field-tab.php:26
msgid "Tab"
msgstr "Scheda"

#: includes/fields/class-acf-field-url.php:159
msgid "Value must be a valid URL"
msgstr "Il valore deve essere un URL valido"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Url"

#: includes/fields/class-acf-field-link.php:174
msgid "Link URL"
msgstr "Link URL"

#: includes/fields/class-acf-field-link.php:173
msgid "Link Array"
msgstr "Array di link"

#: includes/fields/class-acf-field-link.php:142
msgid "Opens in a new window/tab"
msgstr "Apri in una nuova scheda/finestra"

#: includes/fields/class-acf-field-link.php:137
msgid "Select Link"
msgstr "Seleziona link"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Link"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-number.php:185
#: includes/fields/class-acf-field-range.php:214
msgid "Step Size"
msgstr "Dimensione step"

#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-range.php:192
msgid "Maximum Value"
msgstr "Valore massimo"

#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-range.php:181
msgid "Minimum Value"
msgstr "Valore minimo"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Intervallo"

#: includes/fields/class-acf-field-button-group.php:172
#: includes/fields/class-acf-field-checkbox.php:375
#: includes/fields/class-acf-field-radio.php:217
#: includes/fields/class-acf-field-select.php:398
msgid "Both (Array)"
msgstr "Entrambi (Array)"

#: includes/admin/views/field-group-fields.php:41
#: includes/fields/class-acf-field-button-group.php:171
#: includes/fields/class-acf-field-checkbox.php:374
#: includes/fields/class-acf-field-radio.php:216
#: includes/fields/class-acf-field-select.php:397
msgid "Label"
msgstr "Etichetta"

#: includes/fields/class-acf-field-button-group.php:170
#: includes/fields/class-acf-field-checkbox.php:373
#: includes/fields/class-acf-field-radio.php:215
#: includes/fields/class-acf-field-select.php:396
msgid "Value"
msgstr "Valore"

#: includes/fields/class-acf-field-button-group.php:219
#: includes/fields/class-acf-field-checkbox.php:437
#: includes/fields/class-acf-field-radio.php:289
msgid "Vertical"
msgstr "Verticale"

#: includes/fields/class-acf-field-button-group.php:218
#: includes/fields/class-acf-field-checkbox.php:438
#: includes/fields/class-acf-field-radio.php:290
msgid "Horizontal"
msgstr "Orizzontale"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "red : Red"
msgstr "rosso : Rosso"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Per un maggiore controllo, puoi specificare sia un valore che un'etichetta "
"in questo modo:"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "Enter each choice on a new line."
msgstr "Inserisci ogni scelta su una nuova linea."

#: includes/fields/class-acf-field-button-group.php:144
#: includes/fields/class-acf-field-checkbox.php:347
#: includes/fields/class-acf-field-radio.php:189
#: includes/fields/class-acf-field-select.php:368
msgid "Choices"
msgstr "Scelte"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Gruppo di pulsanti"

#: includes/fields/class-acf-field-page_link.php:488
#: includes/fields/class-acf-field-post_object.php:404
#: includes/fields/class-acf-field-select.php:406
#: includes/fields/class-acf-field-user.php:83
msgid "Select multiple values?"
msgstr "Selezionare più valori?"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-page_link.php:509
#: includes/fields/class-acf-field-post_object.php:426
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:428
#: includes/fields/class-acf-field-taxonomy.php:786
#: includes/fields/class-acf-field-user.php:104
msgid "Allow Null?"
msgstr "Consenti valore nullo?"

#: includes/fields/class-acf-field-page_link.php:249
#: includes/fields/class-acf-field-post_object.php:250
#: includes/fields/class-acf-field-taxonomy.php:932
msgid "Parent"
msgstr "Genitore"

#: includes/fields/class-acf-field-wysiwyg.php:394
msgid "TinyMCE will not be initialized until field is clicked"
msgstr ""
"TinyMCE non sarà inizializzato fino a quando non verrà fatto clic sul campo"

#: includes/fields/class-acf-field-wysiwyg.php:393
msgid "Delay initialization?"
msgstr "Ritarda inizializzazione?"

#: includes/fields/class-acf-field-wysiwyg.php:382
msgid "Show Media Upload Buttons?"
msgstr "Mostra pulsanti per il caricamento di media?"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Toolbar"
msgstr "Barra degli strumenti"

#: includes/fields/class-acf-field-wysiwyg.php:358
msgid "Text Only"
msgstr "Solo testo"

#: includes/fields/class-acf-field-wysiwyg.php:357
msgid "Visual Only"
msgstr "Solo visuale"

#: includes/fields/class-acf-field-wysiwyg.php:356
msgid "Visual & Text"
msgstr "Visuale e testo"

#: includes/fields/class-acf-field-wysiwyg.php:351
msgid "Tabs"
msgstr "Schede"

#: includes/fields/class-acf-field-wysiwyg.php:289
msgid "Click to initialize TinyMCE"
msgstr "Fare clic per inizializzare TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:283
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Testo"

#: includes/fields/class-acf-field-wysiwyg.php:282
msgid "Visual"
msgstr "Visuale"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Editor Wysiwyg"

#: includes/fields/class-acf-field-text.php:180
#: includes/fields/class-acf-field-textarea.php:233
msgid "Value must not exceed %d characters"
msgstr "Il valore non può superare %d caratteri"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Lasciare vuoto per nessun limite"

#: includes/fields/class-acf-field-text.php:114
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Limite caratteri"

#: includes/fields/class-acf-field-email.php:155
#: includes/fields/class-acf-field-number.php:206
#: includes/fields/class-acf-field-password.php:102
#: includes/fields/class-acf-field-range.php:236
#: includes/fields/class-acf-field-text.php:155
msgid "Appears after the input"
msgstr "Appare dopo il campo di input"

#: includes/fields/class-acf-field-email.php:154
#: includes/fields/class-acf-field-number.php:205
#: includes/fields/class-acf-field-password.php:101
#: includes/fields/class-acf-field-range.php:235
#: includes/fields/class-acf-field-text.php:154
msgid "Append"
msgstr "Postponi"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:196
#: includes/fields/class-acf-field-password.php:92
#: includes/fields/class-acf-field-range.php:226
#: includes/fields/class-acf-field-text.php:145
msgid "Appears before the input"
msgstr "Appare prima del campo di input"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:195
#: includes/fields/class-acf-field-password.php:91
#: includes/fields/class-acf-field-range.php:225
#: includes/fields/class-acf-field-text.php:144
msgid "Prepend"
msgstr "Anteponi"

#: includes/fields/class-acf-field-email.php:135
#: includes/fields/class-acf-field-number.php:176
#: includes/fields/class-acf-field-password.php:82
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:153
#: includes/fields/class-acf-field-url.php:119
msgid "Appears within the input"
msgstr "Appare all'interno del campo di input"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:175
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-text.php:134
#: includes/fields/class-acf-field-textarea.php:152
#: includes/fields/class-acf-field-url.php:118
msgid "Placeholder Text"
msgstr "Testo segnaposto"

#: includes/fields/class-acf-field-button-group.php:155
#: includes/fields/class-acf-field-email.php:115
#: includes/fields/class-acf-field-number.php:126
#: includes/fields/class-acf-field-radio.php:200
#: includes/fields/class-acf-field-range.php:162
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:101
#: includes/fields/class-acf-field-url.php:99
#: includes/fields/class-acf-field-wysiwyg.php:316
msgid "Appears when creating a new post"
msgstr "Appare quando si crea un nuovo articolo"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Testo"

#: includes/fields/class-acf-field-relationship.php:760
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s richiede la selezione di almeno %2$s elemento"
msgstr[1] "%1$s richiede la selezione di almeno %2$s elementi"

#: includes/fields/class-acf-field-post_object.php:395
#: includes/fields/class-acf-field-relationship.php:622
msgid "Post ID"
msgstr "ID articolo"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:394
#: includes/fields/class-acf-field-relationship.php:621
msgid "Post Object"
msgstr "Oggetto articolo"

#: includes/fields/class-acf-field-relationship.php:654
msgid "Maximum posts"
msgstr "Numero massimo di articoli"

#: includes/fields/class-acf-field-relationship.php:644
msgid "Minimum posts"
msgstr "Numero minimo di articoli"

#: includes/admin/views/field-group-options.php:148
#: includes/fields/class-acf-field-relationship.php:679
msgid "Featured Image"
msgstr "Immagine in evidenza"

#: includes/fields/class-acf-field-relationship.php:675
msgid "Selected elements will be displayed in each result"
msgstr "Gli elementi selezionati verranno visualizzati in ogni risultato"

#: includes/fields/class-acf-field-relationship.php:674
msgid "Elements"
msgstr "Elementi"

#: includes/fields/class-acf-field-relationship.php:608
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:706
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Tassonomia"

#: includes/fields/class-acf-field-relationship.php:607
#: includes/locations/class-acf-location-post-type.php:22
msgid "Post Type"
msgstr "Tipo di contenuto"

#: includes/fields/class-acf-field-relationship.php:601
msgid "Filters"
msgstr "Filtri"

#: includes/fields/class-acf-field-page_link.php:470
#: includes/fields/class-acf-field-post_object.php:382
#: includes/fields/class-acf-field-relationship.php:594
msgid "All taxonomies"
msgstr "Tutte le tassonomie"

#: includes/fields/class-acf-field-page_link.php:462
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:586
msgid "Filter by Taxonomy"
msgstr "Fitra per tassonomia"

#: includes/fields/class-acf-field-page_link.php:455
#: includes/fields/class-acf-field-post_object.php:367
#: includes/fields/class-acf-field-relationship.php:579
msgid "All post types"
msgstr "Tutti i tipi di articolo"

#: includes/fields/class-acf-field-page_link.php:447
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:571
msgid "Filter by Post Type"
msgstr "Filtra per tipo di articolo"

#: includes/fields/class-acf-field-relationship.php:469
msgid "Search..."
msgstr "Cerca..."

#: includes/fields/class-acf-field-relationship.php:399
msgid "Select taxonomy"
msgstr "Seleziona tassonomia"

#: includes/fields/class-acf-field-relationship.php:390
msgid "Select post type"
msgstr "Seleziona tipo di articolo"

#: includes/fields/class-acf-field-relationship.php:65
#: assets/build/js/acf-input.js:3917 assets/build/js/acf-input.js:4195
msgid "No matches found"
msgstr "Nessuna corrispondenza trovata"

#: includes/fields/class-acf-field-relationship.php:64
#: assets/build/js/acf-input.js:3900 assets/build/js/acf-input.js:4174
msgid "Loading"
msgstr "Caricamento in corso"

#: includes/fields/class-acf-field-relationship.php:63
#: assets/build/js/acf-input.js:3809 assets/build/js/acf-input.js:4070
msgid "Maximum values reached ( {max} values )"
msgstr "Numero massimo di valori raggiunto ( {max} valori )"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Relazione"

#: includes/fields/class-acf-field-file.php:288
#: includes/fields/class-acf-field-image.php:314
msgid "Comma separated list. Leave blank for all types"
msgstr "Lista con valori separati da virgole. Lascia vuoto per tutti i tipi"

#: includes/fields/class-acf-field-file.php:287
#: includes/fields/class-acf-field-image.php:313
msgid "Allowed file types"
msgstr "Tipi di file consentiti"

#: includes/fields/class-acf-field-file.php:275
#: includes/fields/class-acf-field-image.php:277
msgid "Maximum"
msgstr "Massimo"

#: includes/fields/class-acf-field-file.php:151
#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-file.php:279
#: includes/fields/class-acf-field-image.php:268
#: includes/fields/class-acf-field-image.php:304
msgid "File size"
msgstr "Dimensione file"

#: includes/fields/class-acf-field-image.php:242
#: includes/fields/class-acf-field-image.php:278
msgid "Restrict which images can be uploaded"
msgstr "Limita i tipi di immagine che possono essere caricati"

#: includes/fields/class-acf-field-file.php:263
#: includes/fields/class-acf-field-image.php:241
msgid "Minimum"
msgstr "Minimo"

#: includes/fields/class-acf-field-file.php:232
#: includes/fields/class-acf-field-image.php:207
msgid "Uploaded to post"
msgstr "Caricato nell'articolo"

#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:206
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Tutti"

#: includes/fields/class-acf-field-file.php:226
#: includes/fields/class-acf-field-image.php:201
msgid "Limit the media library choice"
msgstr "Limitare la scelta dalla libreria media"

#: includes/fields/class-acf-field-file.php:225
#: includes/fields/class-acf-field-image.php:200
msgid "Library"
msgstr "Libreria"

#: includes/fields/class-acf-field-image.php:333
msgid "Preview Size"
msgstr "Dimensione anteprima"

#: includes/fields/class-acf-field-image.php:192
msgid "Image ID"
msgstr "ID immagine"

#: includes/fields/class-acf-field-image.php:191
msgid "Image URL"
msgstr "URL Immagine"

#: includes/fields/class-acf-field-image.php:190
msgid "Image Array"
msgstr "Array di immagini"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:368
#: includes/fields/class-acf-field-file.php:210
#: includes/fields/class-acf-field-link.php:168
#: includes/fields/class-acf-field-radio.php:210
msgid "Specify the returned value on front end"
msgstr "Specificare il valore restituito sul front-end"

#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:367
#: includes/fields/class-acf-field-file.php:209
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-taxonomy.php:750
msgid "Return Value"
msgstr "Valore di ritorno"

#: includes/fields/class-acf-field-image.php:159
msgid "Add Image"
msgstr "Aggiungi immagine"

#: includes/fields/class-acf-field-image.php:159
msgid "No image selected"
msgstr "Nessuna immagine selezionata"

#: includes/assets.php:351 includes/fields/class-acf-field-file.php:159
#: includes/fields/class-acf-field-image.php:139
#: includes/fields/class-acf-field-link.php:142 assets/build/js/acf.js:1523
#: assets/build/js/acf.js:1603
msgid "Remove"
msgstr "Rimuovi"

#: includes/admin/views/field-group-field.php:65
#: includes/fields/class-acf-field-file.php:157
#: includes/fields/class-acf-field-image.php:137
#: includes/fields/class-acf-field-link.php:142
msgid "Edit"
msgstr "Modifica"

#: includes/fields/class-acf-field-image.php:67 includes/media.php:55
#: assets/build/js/acf-input.js:6765 assets/build/js/acf-input.js:7228
msgid "All images"
msgstr "Tutte le immagini"

#: includes/fields/class-acf-field-image.php:66
#: assets/build/js/acf-input.js:3173 assets/build/js/acf-input.js:3386
msgid "Update Image"
msgstr "Aggiorna immagine"

#: includes/fields/class-acf-field-image.php:65
#: assets/build/js/acf-input.js:3172 assets/build/js/acf-input.js:3385
msgid "Edit Image"
msgstr "Modifica immagine"

#: includes/fields/class-acf-field-image.php:64
#: assets/build/js/acf-input.js:3148 assets/build/js/acf-input.js:3360
msgid "Select Image"
msgstr "Selezionare immagine"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Immagine"

#: includes/fields/class-acf-field-message.php:123
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Consenti al markup HTML di essere visualizzato come testo visibile anziché "
"essere processato"

#: includes/fields/class-acf-field-message.php:122
msgid "Escape HTML"
msgstr "Effettua escape HTML"

#: includes/fields/class-acf-field-message.php:114
#: includes/fields/class-acf-field-textarea.php:169
msgid "No Formatting"
msgstr "Nessuna formattazione"

#: includes/fields/class-acf-field-message.php:113
#: includes/fields/class-acf-field-textarea.php:168
msgid "Automatically add &lt;br&gt;"
msgstr "Aggiungi automaticamente &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:112
#: includes/fields/class-acf-field-textarea.php:167
msgid "Automatically add paragraphs"
msgstr "Aggiungi automaticamente paragrafi"

#: includes/fields/class-acf-field-message.php:108
#: includes/fields/class-acf-field-textarea.php:163
msgid "Controls how new lines are rendered"
msgstr "Controlla come le nuove linee sono renderizzate"

#: includes/fields/class-acf-field-message.php:107
#: includes/fields/class-acf-field-textarea.php:162
msgid "New Lines"
msgstr "Nuove linee"

#: includes/fields/class-acf-field-date_picker.php:229
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "La settimana inizia"

#: includes/fields/class-acf-field-date_picker.php:198
msgid "The format used when saving a value"
msgstr "Il formato utilizzato durante il salvataggio di un valore"

#: includes/fields/class-acf-field-date_picker.php:197
msgid "Save Format"
msgstr "Salva formato"

#: includes/fields/class-acf-field-date_picker.php:64
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Sett"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Prec"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Succ"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Oggi"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Fatto"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Selettore data"

#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:281
#: includes/fields/class-acf-field-oembed.php:265
msgid "Width"
msgstr "Larghezza"

#: includes/fields/class-acf-field-oembed.php:262
#: includes/fields/class-acf-field-oembed.php:274
msgid "Embed Size"
msgstr "Dimensione oggetto incorporato"

#: includes/fields/class-acf-field-oembed.php:219
msgid "Enter URL"
msgstr "Inserisci URL"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:181
msgid "Text shown when inactive"
msgstr "Testo mostrato quando inattivo"

#: includes/fields/class-acf-field-true_false.php:180
msgid "Off Text"
msgstr "Testo Off"

#: includes/fields/class-acf-field-true_false.php:165
msgid "Text shown when active"
msgstr "Testo visualizzato quando è attivo"

#: includes/fields/class-acf-field-true_false.php:164
msgid "On Text"
msgstr "Testo On"

#: includes/fields/class-acf-field-select.php:449
#: includes/fields/class-acf-field-true_false.php:196
msgid "Stylized UI"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:154
#: includes/fields/class-acf-field-checkbox.php:357
#: includes/fields/class-acf-field-color_picker.php:155
#: includes/fields/class-acf-field-email.php:114
#: includes/fields/class-acf-field-number.php:125
#: includes/fields/class-acf-field-radio.php:199
#: includes/fields/class-acf-field-range.php:161
#: includes/fields/class-acf-field-select.php:379
#: includes/fields/class-acf-field-text.php:94
#: includes/fields/class-acf-field-textarea.php:100
#: includes/fields/class-acf-field-true_false.php:144
#: includes/fields/class-acf-field-url.php:98
#: includes/fields/class-acf-field-wysiwyg.php:315
msgid "Default Value"
msgstr "Valore predefinito"

#: includes/fields/class-acf-field-true_false.php:135
msgid "Displays text alongside the checkbox"
msgstr "Visualizza il testo a fianco alla casella di controllo"

#: includes/fields/class-acf-field-message.php:26
#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-true_false.php:134
msgid "Message"
msgstr "Messaggio"

#: includes/assets.php:350 includes/fields/class-acf-field-true_false.php:83
#: includes/fields/class-acf-field-true_false.php:184
#: assets/build/js/acf.js:1700 assets/build/js/acf.js:1803
msgid "No"
msgstr "No"

#: includes/assets.php:349 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:168
#: assets/build/js/acf.js:1699 assets/build/js/acf.js:1802
msgid "Yes"
msgstr "Sì"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Vero / Falso"

#: includes/fields/class-acf-field-group.php:471
msgid "Row"
msgstr "Riga"

#: includes/fields/class-acf-field-group.php:470
msgid "Table"
msgstr "Tabella"

#: includes/fields/class-acf-field-group.php:469
msgid "Block"
msgstr "Blocco"

#: includes/fields/class-acf-field-group.php:464
msgid "Specify the style used to render the selected fields"
msgstr ""
"Specifica lo stile utilizzato per la visualizzazione dei campi selezionati"

#: includes/fields.php:359 includes/fields/class-acf-field-button-group.php:212
#: includes/fields/class-acf-field-checkbox.php:431
#: includes/fields/class-acf-field-group.php:463
#: includes/fields/class-acf-field-radio.php:283
msgid "Layout"
msgstr "Layout"

#: includes/fields/class-acf-field-group.php:447
msgid "Sub Fields"
msgstr "Sottocampi"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Gruppo"

#: includes/fields/class-acf-field-google-map.php:232
msgid "Customize the map height"
msgstr "Personalizza l'altezza della mappa"

#: includes/fields/class-acf-field-google-map.php:231
#: includes/fields/class-acf-field-image.php:256
#: includes/fields/class-acf-field-image.php:292
#: includes/fields/class-acf-field-oembed.php:277
msgid "Height"
msgstr "Altezza"

#: includes/fields/class-acf-field-google-map.php:220
msgid "Set the initial zoom level"
msgstr "Imposta il livello di zoom iniziale"

#: includes/fields/class-acf-field-google-map.php:219
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:193
#: includes/fields/class-acf-field-google-map.php:206
msgid "Center the initial map"
msgstr "Centra la mappa iniziale"

#: includes/fields/class-acf-field-google-map.php:192
#: includes/fields/class-acf-field-google-map.php:205
msgid "Center"
msgstr "Centro"

#: includes/fields/class-acf-field-google-map.php:160
msgid "Search for address..."
msgstr "Cerca per indirizzo..."

#: includes/fields/class-acf-field-google-map.php:157
msgid "Find current location"
msgstr "Trova posizione corrente"

#: includes/fields/class-acf-field-google-map.php:156
msgid "Clear location"
msgstr "Rimuovi posizione"

#: includes/fields/class-acf-field-google-map.php:155
#: includes/fields/class-acf-field-relationship.php:606
msgid "Search"
msgstr "Cerca"

#: includes/fields/class-acf-field-google-map.php:60
#: assets/build/js/acf-input.js:2832 assets/build/js/acf-input.js:3013
msgid "Sorry, this browser does not support geolocation"
msgstr "Questo browser non supporta la geolocalizzazione"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google Map"

#: includes/fields/class-acf-field-date_picker.php:209
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:129
msgid "The format returned via template functions"
msgstr "Il formato restituito tramite funzioni template"

#: includes/fields/class-acf-field-color_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:197
#: includes/fields/class-acf-field-image.php:184
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:616
#: includes/fields/class-acf-field-select.php:390
#: includes/fields/class-acf-field-time_picker.php:128
#: includes/fields/class-acf-field-user.php:67
msgid "Return Format"
msgstr "Formato di ritorno"

#: includes/fields/class-acf-field-date_picker.php:187
#: includes/fields/class-acf-field-date_picker.php:218
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-date_time_picker.php:207
#: includes/fields/class-acf-field-time_picker.php:120
#: includes/fields/class-acf-field-time_picker.php:136
msgid "Custom:"
msgstr "Personalizzato:"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-time_picker.php:113
msgid "The format displayed when editing a post"
msgstr "Il formato visualizzato durante la modifica di un articolo"

#: includes/fields/class-acf-field-date_picker.php:178
#: includes/fields/class-acf-field-date_time_picker.php:179
#: includes/fields/class-acf-field-time_picker.php:112
msgid "Display Format"
msgstr "Formato di visualizzazione"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Selettore orario"

#: acf.php:413
msgid "No Fields found in Trash"
msgstr "Nessun campo trovato nel cestino"

#: acf.php:412
msgid "No Fields found"
msgstr "Nessun campo trovato"

#: acf.php:411
msgid "Search Fields"
msgstr "Cerca campi"

#: acf.php:410
msgid "View Field"
msgstr "Visualizza campo"

#: acf.php:409 includes/admin/views/field-group-fields.php:104
msgid "New Field"
msgstr "Nuovo campo"

#: acf.php:408
msgid "Edit Field"
msgstr "Modifica campo"

#: acf.php:407
msgid "Add New Field"
msgstr "Aggiungi nuovo campo"

#: acf.php:405
msgid "Field"
msgstr "Campo"

#: acf.php:404 includes/admin/admin-field-group.php:220
#: includes/admin/admin-field-groups.php:287
#: includes/admin/views/field-group-fields.php:21
msgid "Fields"
msgstr "Campi"

#: acf.php:379
msgid "No Field Groups found in Trash"
msgstr "Nessun gruppo di campi trovato nel cestino"

#: acf.php:378
msgid "No Field Groups found"
msgstr "Nessun gruppo di campi trovato"

#: acf.php:377
msgid "Search Field Groups"
msgstr "Cerca gruppi di campi"

#: acf.php:376
msgid "View Field Group"
msgstr "Visualizza gruppo di campi"

#: acf.php:375
msgid "New Field Group"
msgstr "Nuovo gruppo di campi"

#: acf.php:374
msgid "Edit Field Group"
msgstr "Modifica gruppo di campi"

#: acf.php:373
msgid "Add New Field Group"
msgstr "Aggiungi nuovo gruppo di campi"

#: acf.php:372 acf.php:406 includes/admin/admin.php:51
msgid "Add New"
msgstr "Aggiungi nuovo"

#: acf.php:371
msgid "Field Group"
msgstr "Gruppo di campi"

#: acf.php:370 includes/admin/admin.php:50
msgid "Field Groups"
msgstr "Gruppi di campi"

#. Description of the plugin
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "Personalizza WordPress con campi potenti, professionali e intuitivi."

#. Plugin URI of the plugin
#. Author URI of the plugin
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php:91
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:166
msgid "Block type name is required."
msgstr ""

#: pro/blocks.php:173
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:731
msgid "Switch to Edit"
msgstr ""

#: pro/blocks.php:732
msgid "Switch to Preview"
msgstr ""

#: pro/blocks.php:733
msgid "Change content alignment"
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:736
msgid "%s settings"
msgstr ""

#: pro/blocks.php:949
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:955
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Opzioni Aggiornate"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a href=\"%1$s"
"\">Updates</a> page. If you don't have a licence key, please see <a href="
"\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""

#: pro/updates.php:279, pro/admin/views/html-settings-updates.php:117
msgid "Check Again"
msgstr "Ricontrollare"

#: pro/updates.php:561
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Pubblica"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Nessun Field Group personalizzato trovato in questa Pagina Opzioni. <a href="
"\"%s\">Crea un Field Group personalizzato</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Errore</b>.Impossibile connettersi al server di aggiornamento"

#: pro/admin/admin-updates.php:209
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""

#: pro/admin/admin-updates.php:196
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Clona"

#: pro/fields/class-acf-field-clone.php:815
msgid "Select one or more fields you wish to clone"
msgstr "Selezionare uno o più campi che si desidera clonare"

#: pro/fields/class-acf-field-clone.php:834
msgid "Display"
msgstr "Visualizza"

#: pro/fields/class-acf-field-clone.php:835
msgid "Specify the style used to render the clone field"
msgstr "Specificare lo stile utilizzato per il rendering del campo clona"

#: pro/fields/class-acf-field-clone.php:840
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Gruppo (Visualizza campi selezionati in un gruppo all'interno di questo "
"campo)"

#: pro/fields/class-acf-field-clone.php:841
msgid "Seamless (replaces this field with selected fields)"
msgstr "Senza interruzione (sostituisce questo campo con i campi selezionati)"

#: pro/fields/class-acf-field-clone.php:864
msgid "Labels will be displayed as %s"
msgstr "Etichette verranno visualizzate come %s"

#: pro/fields/class-acf-field-clone.php:869
msgid "Prefix Field Labels"
msgstr "Prefisso Etichetta Campo"

#: pro/fields/class-acf-field-clone.php:880
msgid "Values will be saved as %s"
msgstr "I valori verranno salvati come %s"

#: pro/fields/class-acf-field-clone.php:885
msgid "Prefix Field Names"
msgstr "Prefisso Nomi Campo"

#: pro/fields/class-acf-field-clone.php:1001
msgid "Unknown field"
msgstr "Campo sconosciuto"

#: pro/fields/class-acf-field-clone.php:1038
msgid "Unknown field group"
msgstr "Field Group sconosciuto"

#: pro/fields/class-acf-field-clone.php:1042
msgid "All fields from %s field group"
msgstr "Tutti i campi dal %s field group"

#: pro/fields/class-acf-field-flexible-content.php:31,
#: pro/fields/class-acf-field-repeater.php:79,
#: pro/fields/class-acf-field-repeater.php:263
msgid "Add Row"
msgstr "Aggiungi Riga"

#: pro/fields/class-acf-field-flexible-content.php:71,
#: pro/fields/class-acf-field-flexible-content.php:917,
#: pro/fields/class-acf-field-flexible-content.php:996
#, fuzzy
#| msgid "layout"
msgid "layout"
msgid_plural "layouts"
msgstr[0] "layout"
msgstr[1] "layout"

#: pro/fields/class-acf-field-flexible-content.php:72
msgid "layouts"
msgstr "layout"

#: pro/fields/class-acf-field-flexible-content.php:75,
#: pro/fields/class-acf-field-flexible-content.php:916,
#: pro/fields/class-acf-field-flexible-content.php:995
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Questo campo richiede almeno {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:76
msgid "This field has a limit of {max} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:79
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} disponibile (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:80
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} richiesto (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:83
msgid "Flexible Content requires at least 1 layout"
msgstr "Flexible Content richiede almeno 1 layout"

#: pro/fields/class-acf-field-flexible-content.php:276
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Clicca il bottone \"%s\" qui sotto per iniziare a creare il layout"

#: pro/fields/class-acf-field-flexible-content.php:413
msgid "Add layout"
msgstr "Aggiungi Layout"

#: pro/fields/class-acf-field-flexible-content.php:414
msgid "Duplicate layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:415
msgid "Remove layout"
msgstr "Rimuovi Layout"

#: pro/fields/class-acf-field-flexible-content.php:416,
#: pro/fields/class-acf-repeater-table.php:369
msgid "Click to toggle"
msgstr "Clicca per alternare"

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder Layout"
msgstr "Riordina Layout"

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder"
msgstr "Riordina"

#: pro/fields/class-acf-field-flexible-content.php:552
msgid "Delete Layout"
msgstr "Cancella Layout"

#: pro/fields/class-acf-field-flexible-content.php:553
msgid "Duplicate Layout"
msgstr "Duplica Layout"

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Add New Layout"
msgstr "Aggiungi Nuovo Layout"

#: pro/fields/class-acf-field-flexible-content.php:635
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:650
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:679,
#: pro/fields/class-acf-field-repeater.php:259
msgid "Button Label"
msgstr "Etichetta Bottone"

#: pro/fields/class-acf-field-flexible-content.php:690
msgid "Minimum Layouts"
msgstr "Layout Minimi"

#: pro/fields/class-acf-field-flexible-content.php:701
msgid "Maximum Layouts"
msgstr "Layout Massimi"

#: pro/fields/class-acf-field-flexible-content.php:1704,
#: pro/fields/class-acf-field-repeater.php:861
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1715
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:1731
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-gallery.php:73
msgid "Add Image to Gallery"
msgstr "Aggiungi Immagine alla Galleria"

#: pro/fields/class-acf-field-gallery.php:74
msgid "Maximum selection reached"
msgstr "Selezione massima raggiunta"

#: pro/fields/class-acf-field-gallery.php:320
msgid "Length"
msgstr "Lunghezza"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr "Didascalia"

#: pro/fields/class-acf-field-gallery.php:376
msgid "Alt Text"
msgstr "Testo Alt"

#: pro/fields/class-acf-field-gallery.php:500
msgid "Add to gallery"
msgstr "Aggiungi a Galleria"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Bulk actions"
msgstr "Azioni in blocco"

#: pro/fields/class-acf-field-gallery.php:505
msgid "Sort by date uploaded"
msgstr "Ordina per aggiornamento data"

#: pro/fields/class-acf-field-gallery.php:506
msgid "Sort by date modified"
msgstr "Ordina per data modifica"

#: pro/fields/class-acf-field-gallery.php:507
msgid "Sort by title"
msgstr "Ordina per titolo"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Reverse current order"
msgstr "Ordine corrente inversa"

#: pro/fields/class-acf-field-gallery.php:520
msgid "Close"
msgstr "Chiudi"

#: pro/fields/class-acf-field-gallery.php:602
msgid "Insert"
msgstr "Inserisci"

#: pro/fields/class-acf-field-gallery.php:603
msgid "Specify where new attachments are added"
msgstr "Specificare dove vengono aggiunti nuovi allegati"

#: pro/fields/class-acf-field-gallery.php:607
msgid "Append to the end"
msgstr "Aggiungere alla fine"

#: pro/fields/class-acf-field-gallery.php:608
msgid "Prepend to the beginning"
msgstr "Anteporre all'inizio"

#: pro/fields/class-acf-field-gallery.php:633
msgid "Minimum Selection"
msgstr "Seleziona Minima"

#: pro/fields/class-acf-field-gallery.php:644
msgid "Maximum Selection"
msgstr "Seleziona Massima"

#: pro/fields/class-acf-field-repeater.php:53,
#: pro/fields/class-acf-field-repeater.php:423
msgid "Minimum rows reached ({min} rows)"
msgstr "Righe minime raggiunte ({min} righe)"

#: pro/fields/class-acf-field-repeater.php:54
msgid "Maximum rows reached ({max} rows)"
msgstr "Righe massime raggiunte ({max} righe)"

#: pro/fields/class-acf-field-repeater.php:55
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:174
msgid "Collapsed"
msgstr "Collassata"

#: pro/fields/class-acf-field-repeater.php:175
msgid "Select a sub field to show when row is collapsed"
msgstr ""
"Selezionare un campo secondario da visualizzare quando la riga è collassata"

#: pro/fields/class-acf-field-repeater.php:187
msgid "Minimum Rows"
msgstr "Righe Minime"

#: pro/fields/class-acf-field-repeater.php:199
msgid "Maximum Rows"
msgstr "Righe Massime"

#: pro/fields/class-acf-field-repeater.php:228
msgid "Pagination"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:229
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:240
msgid "Rows Per Page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:241
msgid "Set the number of rows to be displayed on a page."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:959
msgid "Invalid field key."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:968
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:389
msgid "Add row"
msgstr "Aggiungi riga"

#: pro/fields/class-acf-repeater-table.php:390
msgid "Duplicate row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:391
msgid "Remove row"
msgstr "Rimuovi riga"

#: pro/fields/class-acf-repeater-table.php:435,
#: pro/fields/class-acf-repeater-table.php:452
msgid "Current Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:444
msgid "First page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:448
msgid "Previous page"
msgstr ""

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:457
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:465
msgid "Next page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:469
msgid "Last page"
msgstr ""

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr ""

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Nessuna Pagina Opzioni esistente"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Disattivare Licenza"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Attiva Licenza"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Informazioni Licenza"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Per sbloccare gli aggiornamenti, si prega di inserire la chiave di licenza "
"qui sotto. Se non hai una chiave di licenza, si prega di vedere <a href=\"%s"
"\" target=\"_blank\">Dettagli e prezzi</a>."

#: pro/admin/views/html-settings-updates.php:41
msgid "License Key"
msgstr "Chiave di licenza"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr ""

#: pro/admin/views/html-settings-updates.php:76
msgid "Update Information"
msgstr "Informazioni di aggiornamento"

#: pro/admin/views/html-settings-updates.php:83
msgid "Current Version"
msgstr "Versione corrente"

#: pro/admin/views/html-settings-updates.php:91
msgid "Latest Version"
msgstr "Ultima versione"

#: pro/admin/views/html-settings-updates.php:99
msgid "Update Available"
msgstr "Aggiornamento Disponibile"

#: pro/admin/views/html-settings-updates.php:111
msgid "Please enter your license key above to unlock updates"
msgstr "Inserisci il tuo codice di licenza per sbloccare gli aggiornamenti"

#: pro/admin/views/html-settings-updates.php:109
msgid "Update Plugin"
msgstr "Aggiorna Plugin"

#: pro/admin/views/html-settings-updates.php:107
msgid "Please reactivate your license to unlock updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:124
msgid "Changelog"
msgstr "Novità"

#: pro/admin/views/html-settings-updates.php:134
msgid "Upgrade Notice"
msgstr "Avviso di Aggiornamento"

#, php-format
#~ msgid "Inactive <span class=\"count\">(%s)</span>"
#~ msgid_plural "Inactive <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Inattivo <span class=\"count\">(%s)</span>"
#~ msgstr[1] "Inattivo <span class=\"count\">(%s)</span>"

#~ msgid "Inactive"
#~ msgstr "Inattivo"

#~ msgid "Move to trash. Are you sure?"
#~ msgstr "Sposta nel cestino. Sei sicuro?"

#~ msgid "checked"
#~ msgstr "selezionato"

#, php-format
#~ msgid "The %s field can now be found in the %s field group"
#~ msgstr ""
#~ "Il Campo %s può essere trovato nel \n"
#~ "Field Group\n"
#~ " %s"

#~ msgid "Close Window"
#~ msgstr "Chiudi Finestra"

#, php-format
#~ msgid "Field group duplicated. %s"
#~ msgstr ""
#~ "Field Group\n"
#~ " duplicato. %s"

#, php-format
#~ msgid "%s field group duplicated."
#~ msgid_plural "%s field groups duplicated."
#~ msgstr[0] "%s Field Group duplicato."
#~ msgstr[1] "%s Field Group duplicati."

#, php-format
#~ msgid "Field group synchronised. %s"
#~ msgstr ""
#~ "Field Group\n"
#~ " sincronizzato. %s"

#, php-format
#~ msgid "%s field group synchronised."
#~ msgid_plural "%s field groups synchronised."
#~ msgstr[0] "%s Field Group sincronizzato."
#~ msgstr[1] "%s Field Group sincronizzati."

#~ msgid "Status"
#~ msgstr "Stato"

#~ msgid ""
#~ "Customise WordPress with powerful, professional and intuitive fields."
#~ msgstr ""
#~ "Personalizza WordPress con campi potenti, professionali e intuitivi."

#, php-format
#~ msgid "See what's new in <a href=\"%s\">version %s</a>."
#~ msgstr "Guarda cosa c'è di nuovo nella <a href=\"%s\">versione %s</a>."

#~ msgid "Resources"
#~ msgstr "Risorse"

#~ msgid "Website"
#~ msgstr "Sito Web"

#~ msgid "Documentation"
#~ msgstr "Documentazione"

#~ msgid "Support"
#~ msgstr "Supporto"

#~ msgid "Pro"
#~ msgstr "PRO"

#, php-format
#~ msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
#~ msgstr "Grazie per aver creato con <a href=\"%s\">ACF</a>."

#~ msgid "Synchronise field group"
#~ msgstr ""
#~ "Sincronizza \n"
#~ "Field Group"

#~ msgid "Apply"
#~ msgstr "Applica"

#~ msgid "Bulk Actions"
#~ msgstr "Azioni di massa"

#~ msgid "Error validating request"
#~ msgstr "Errore di convalida richiesta"

#~ msgid "Add-ons"
#~ msgstr "Add-ons"

#~ msgid "<b>Error</b>. Could not load add-ons list"
#~ msgstr "<b>Errore</b>. Impossibile caricare l'elenco Add-ons"

#~ msgid "Info"
#~ msgstr "Informazioni"

#~ msgid "What's New"
#~ msgstr "Cosa c'è di nuovo"

#~ msgid ""
#~ "Select the field groups you would like to export and then select your "
#~ "export method. Use the download button to export to a .json file which "
#~ "you can then import to another ACF installation. Use the generate button "
#~ "to export to PHP code which you can place in your theme."
#~ msgstr ""
#~ "Selezionare i \n"
#~ "Field Group\n"
#~ " che si desidera esportare e quindi selezionare il metodo di "
#~ "esportazione. Utilizzare il pulsante di download per esportare in un "
#~ "file .json che sarà poi possibile importare in un'altra installazione "
#~ "ACF. Utilizzare il pulsante generare per esportare il codice PHP che è "
#~ "possibile inserire nel vostro tema."

#~ msgid "Export File"
#~ msgstr "Esporta file"

#~ msgid ""
#~ "Select the Advanced Custom Fields JSON file you would like to import. "
#~ "When you click the import button below, ACF will import the field groups."
#~ msgstr ""
#~ "Selezionare il file JSON di Advanced Custom Fields che si desidera "
#~ "importare. Quando si fa clic sul pulsante di importazione di seguito, ACF "
#~ "importerà i \n"
#~ "Field Group\n"
#~ "."

#~ msgid "Import File"
#~ msgstr "Importa file"

#~ msgid "Required?"
#~ msgstr "Richiesto?"

#~ msgid ""
#~ "No fields. Click the <strong>+ Add Field</strong> button to create your "
#~ "first field."
#~ msgstr ""
#~ "Nessun Campo. Clicca il bottone <strong>+ Aggiungi Campo</strong> per "
#~ "creare il primo campo."

#~ msgid "+ Add Field"
#~ msgstr "+ Aggiungi Campo"

#~ msgid "Advanced Custom Fields Database Upgrade"
#~ msgstr ""
#~ "Aggiornamento Database \n"
#~ "Advanced Custom Fields"

#, php-format
#~ msgid "Site requires database upgrade from %s to %s"
#~ msgstr "Il sito necessita di un aggiornamento Database da %s a %s"

#~ msgid "Upgrade complete"
#~ msgstr "Aggiornamento completato"

#, php-format
#~ msgid "Thank you for updating to %s v%s!"
#~ msgstr "Grazie per aver aggiornato a %s v%s!"

#~ msgid ""
#~ "Before you start using the new awesome features, please update your "
#~ "database to the newest version."
#~ msgstr ""
#~ "Prima di iniziare ad utilizzare queste nuove fantastiche funzionalità, "
#~ "aggiorna il tuo Database alla versione più attuale."

#, php-format
#~ msgid ""
#~ "Please also ensure any premium add-ons (%s) have first been updated to "
#~ "the latest version."
#~ msgstr ""
#~ "Si prega di assicurarsi che anche i componenti premium (%s) siano prima "
#~ "stati aggiornati all'ultima versione."

#, php-format
#~ msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
#~ msgstr ""
#~ "Aggiornamento del database completato. <a href=\"%s\">Guarda le novità</a>"

#~ msgid "Download & Install"
#~ msgstr "Scarica & Installa"

#~ msgid "Installed"
#~ msgstr "Installato"

#~ msgid "Welcome to Advanced Custom Fields"
#~ msgstr "Benvenuto in Advanced Custom Fields"

#, php-format
#~ msgid ""
#~ "Thank you for updating! ACF %s is bigger and better than ever before. We "
#~ "hope you like it."
#~ msgstr ""
#~ "Grazie per l'aggiornamento! ACF %s è più grande e migliore che mai. "
#~ "Speriamo che vi piaccia."

#~ msgid "A smoother custom field experience"
#~ msgstr "Campi Personalizzati come non li avete mai visti"

#~ msgid "Improved Usability"
#~ msgstr "Migliorata Usabilità"

#~ msgid ""
#~ "Including the popular Select2 library has improved both usability and "
#~ "speed across a number of field types including post object, page link, "
#~ "taxonomy and select."
#~ msgstr ""
#~ "Inclusa la famosa biblioteca Select2, che ha migliorato sia l'usabilità, "
#~ "che la velocità di Campi come Post, Link, Tassonomie e Select."

#~ msgid "Improved Design"
#~ msgstr "Miglioramento del Design"

#~ msgid ""
#~ "Many fields have undergone a visual refresh to make ACF look better than "
#~ "ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
#~ "(new) fields!"
#~ msgstr ""
#~ "Molti Campi hanno subito un aggiornamento visivo per rendere ACF un "
#~ "aspetto migliore che mai! Notevoli cambiamenti li trovate nei Campi "
#~ "Gallery, Relazioni e oEmbed (nuovo)!"

#~ msgid "Improved Data"
#~ msgstr "Miglioramento dei dati"

#~ msgid ""
#~ "Redesigning the data architecture has allowed sub fields to live "
#~ "independently from their parents. This allows you to drag and drop fields "
#~ "in and out of parent fields!"
#~ msgstr ""
#~ "Ridisegnare l'architettura dei dati ha permesso ai Sotto-Campi di vivere "
#~ "in modo indipendente dai loro Genitori. Ciò consente di trascinare e "
#~ "rilasciare i Campi dentro e fuori i Campi Genitore!"

#~ msgid "Goodbye Add-ons. Hello PRO"
#~ msgstr "Ciao, ciao Add-ons. Benvenuto PRO"

#~ msgid "Introducing ACF PRO"
#~ msgstr "Introduzione ACF PRO"

#~ msgid ""
#~ "We're changing the way premium functionality is delivered in an exciting "
#~ "way!"
#~ msgstr "Stiamo cambiando in modo eccitante le funzionalità Premium!"

#, php-format
#~ msgid ""
#~ "All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
#~ "version of ACF</a>. With both personal and developer licenses available, "
#~ "premium functionality is more affordable and accessible than ever before!"
#~ msgstr ""
#~ "Parallelamente ACF5 è la versione tutta nuova di <a href=\"%s\">ACF5 PRO</"
#~ "a>! Questa versione PRO include tutti e 4 i componenti aggiuntivi premium "
#~ "(Repeater, Gallery, Flexible Content e Pagina Opzioni) e con le licenze "
#~ "personali e di sviluppo disponibili, funzionalità premium è più "
#~ "conveniente che mai!"

#~ msgid "Powerful Features"
#~ msgstr "Potenti funzionalità"

#~ msgid ""
#~ "ACF PRO contains powerful features such as repeatable data, flexible "
#~ "content layouts, a beautiful gallery field and the ability to create "
#~ "extra admin options pages!"
#~ msgstr ""
#~ "ACF PRO contiene caratteristiche impressionanti come i Campi Repeater, "
#~ "Flexible Layout, Gallery e la possibilità di creare Options Page (pagine "
#~ "opzioni di amministrazione) personalizzabili!"

#, php-format
#~ msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
#~ msgstr "Scopri di più sulle <a href=\"%s\">funzionalità di ACF PRO</a>."

#~ msgid "Easy Upgrading"
#~ msgstr "Aggiornamento facile"

#, php-format
#~ msgid ""
#~ "To help make upgrading easy, <a href=\"%s\">login to your store account</"
#~ "a> and claim a free copy of ACF PRO!"
#~ msgstr ""
#~ "Per rendere più semplice gli aggiornamenti, \n"
#~ "<a href=\"%s\">accedi al tuo account</a> e richiedi una copia gratuita di "
#~ "ACF PRO!"

#, php-format
#~ msgid ""
#~ "We also wrote an <a href=\"%s\">upgrade guide</a> to answer any "
#~ "questions, but if you do have one, please contact our support team via "
#~ "the <a href=\"%s\">help desk</a>"
#~ msgstr ""
#~ "Abbiamo inoltre scritto una <a href=\"%s\">guida all'aggiornamento</a> "
#~ "per rispondere alle vostre richieste, ma se ne avete di nuove, contattate "
#~ "il nostro <a href=\"%s\">help desk</a>"

#~ msgid "Under the Hood"
#~ msgstr "Sotto il cofano"

#~ msgid "Smarter field settings"
#~ msgstr "Impostazioni dei Campi più intelligenti"

#~ msgid "ACF now saves its field settings as individual post objects"
#~ msgstr ""
#~ "ACF ora salva le impostazioni dei Campi come oggetti Post individuali"

#~ msgid "More AJAX"
#~ msgstr "Più AJAX"

#~ msgid "More fields use AJAX powered search to speed up page loading"
#~ msgstr ""
#~ "Altri campi utilizzano la ricerca di AJAX per velocizzare il caricamento "
#~ "della pagina"

#~ msgid "New auto export to JSON feature improves speed"
#~ msgstr ""
#~ "Nuovo esportazione automatica di funzionalità JSON migliora la velocità"

#~ msgid "Better version control"
#~ msgstr "Migliore versione di controllo"

#~ msgid ""
#~ "New auto export to JSON feature allows field settings to be version "
#~ "controlled"
#~ msgstr ""
#~ "Nuova esportazione automatica di funzione JSON consente impostazioni dei "
#~ "campi da versione controllati"

#~ msgid "Swapped XML for JSON"
#~ msgstr "XML scambiato per JSON"

#~ msgid "Import / Export now uses JSON in favour of XML"
#~ msgstr "Importa / Esporta ora utilizza JSON a favore di XML"

#~ msgid "New Forms"
#~ msgstr "Nuovi Forme"

#~ msgid "Fields can now be mapped to comments, widgets and all user forms!"
#~ msgstr ""
#~ "I campi possono essere mappati con i commenti, widget e tutte le forme "
#~ "degli utenti!"

#~ msgid "A new field for embedding content has been added"
#~ msgstr "È stato aggiunto un nuovo campo per incorporare contenuti"

#~ msgid "New Gallery"
#~ msgstr "Nuova Galleria"

#~ msgid "The gallery field has undergone a much needed facelift"
#~ msgstr "Il campo galleria ha subito un lifting tanto necessario"

#~ msgid "New Settings"
#~ msgstr "Nuove Impostazioni"

#~ msgid ""
#~ "Field group settings have been added for label placement and instruction "
#~ "placement"
#~ msgstr ""
#~ "Sono state aggiunte impostazioni di gruppo sul Campo per l'inserimento "
#~ "dell'etichetta e il posizionamento di istruzioni"

#~ msgid "Better Front End Forms"
#~ msgstr "Forme Anteriori migliori"

#~ msgid "acf_form() can now create a new post on submission"
#~ msgstr "acf_form() può ora creare un nuovo post di presentazione"

#~ msgid "Better Validation"
#~ msgstr "Validazione Migliore"

#~ msgid "Form validation is now done via PHP + AJAX in favour of only JS"
#~ msgstr ""
#~ "Validazione del form ora avviene tramite PHP + AJAX in favore del solo JS"

#~ msgid "Relationship Field"
#~ msgstr "Campo Relazione"

#~ msgid ""
#~ "New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
#~ msgstr ""
#~ "Nuove Impostazione Campo Relazione per i 'Filtri' (Ricerca, Tipo di Post, "
#~ "Tassonomia)"

#~ msgid "Moving Fields"
#~ msgstr "Spostamento Campi"

#~ msgid ""
#~ "New field group functionality allows you to move a field between groups & "
#~ "parents"
#~ msgstr ""
#~ "La nuova funzionalità di Field Group consente di spostare un campo tra i "
#~ "gruppi e genitori"

#~ msgid "New archives group in page_link field selection"
#~ msgstr "Nuovo gruppo archivi in materia di selezione page_link"

#~ msgid "Better Options Pages"
#~ msgstr "Migliori Pagine Opzioni"

#~ msgid ""
#~ "New functions for options page allow creation of both parent and child "
#~ "menu pages"
#~ msgstr ""
#~ "Nuove funzioni per la Pagina Opzioni consentono la creazione di pagine "
#~ "menu genitore e figlio"

#, php-format
#~ msgid "We think you'll love the changes in %s."
#~ msgstr "Pensiamo che amerete i cambiamenti in %s."

#, php-format
#~ msgid "File size must must not exceed %s."
#~ msgstr "La dimensione massima non deve superare i %s."

#~ msgid "Allow Custom"
#~ msgstr "Consenti Personalizzato"

#~ msgid "Save Custom"
#~ msgstr "Salva Personalizzato"

#~ msgid "Toggle"
#~ msgstr "Toggle"

#~ msgid "Current Color"
#~ msgstr "Colore Corrente"

#~ msgid "Customise the map height"
#~ msgstr "Personalizza l'altezza della mappa iniziale"

#~ msgid "Shown when entering data"
#~ msgstr "Mostrato durante l'immissione dei dati"

#~ msgid "Other"
#~ msgstr "Altro"

#~ msgid "Save Other"
#~ msgstr "Salva Altro"

#, php-format
#~ msgid "%s requires at least %s selection"
#~ msgid_plural "%s requires at least %s selections"
#~ msgstr[0] "%s richiede la selezione di almeno %s"
#~ msgstr[1] "%s richiede le selezioni di almeno %s"

#~ msgid "Stylised UI"
#~ msgstr "UI stilizzata"

#~ msgid ""
#~ "Define an endpoint for the previous tabs to stop. This will start a new "
#~ "group of tabs."
#~ msgstr ""
#~ "Definire un endpoint per le schede precedenti da interrompere. Questo "
#~ "avvierà un nuovo gruppo di schede."

#, php-format
#~ msgctxt "No terms"
#~ msgid "No %s"
#~ msgstr "Nessun %s"

#~ msgid "None"
#~ msgstr "Nessuno"

#~ msgid "Error."
#~ msgstr "Errore."

#~ msgid "TinyMCE will not be initalized until field is clicked"
#~ msgstr ""
#~ "TinyMCE non sarà inizializzato fino a quando il campo non viene cliccato"

#~ msgid "remove {layout}?"
#~ msgstr "rimuovi {layout}?"

#~ msgid "This field requires at least {min} {identifier}"
#~ msgstr "Questo campoQuesto campo richiede almeno {min} {identifier}"

#~ msgid "This field has a limit of {max} {identifier}"
#~ msgstr "Questo campo ha un limite di {max} {identifier}"

#~ msgid "Maximum {label} limit reached ({max} {identifier})"
#~ msgstr "Massimo {label} limite raggiunto ({max} {identifier})"

#, php-format
#~ msgid ""
#~ "To enable updates, please enter your license key on the <a href=\"%s"
#~ "\">Updates</a> page. If you don't have a licence key, please see <a href="
#~ "\"%s\">details & pricing</a>."
#~ msgstr ""
#~ "Per attivare gli aggiornamenti, per favore inserisci la tua chiave di "
#~ "licenza nella pagina <a href=\"%s\">Aggiornamenti</a>. Se non hai una "
#~ "chiave di licenza, per favore vedi <a href=\"%s\">dettagli e prezzi</a>."

#~ msgid "https://www.advancedcustomfields.com/"
#~ msgstr "https://www.advancedcustomfields.com/"

#~ msgid "Elliot Condon"
#~ msgstr "Elliot Condon"

#~ msgid "http://www.elliotcondon.com/"
#~ msgstr "http://www.elliotcondon.com/"

#~ msgid "No conditional fields available"
#~ msgstr "Non ci sono campi condizionali disponibili"

#~ msgid "Parent fields"
#~ msgstr "Campi genitore"

#~ msgid "Sibling fields"
#~ msgstr "Campi di pari livello"

#~ msgid "Left Aligned"
#~ msgstr "Allineamento a sinistra"

#~ msgid "Locating"
#~ msgstr "Localizzazione"

#~ msgid "Minimum values reached ( {min} values )"
#~ msgstr "Valori minimi raggiunti ( valori {min} )"

#~ msgid "Taxonomy Term"
#~ msgstr "Termine Tassonomia"

#~ msgid "Export Field Groups to PHP"
#~ msgstr ""
#~ "Esporta \n"
#~ "Field Group\n"
#~ " di PHP"

#~ msgid "Download export file"
#~ msgstr "Scarica file di esportazione"

#~ msgid "Generate export code"
#~ msgstr "Generare codice di esportazione"

#~ msgid "No embed found for the given URL."
#~ msgstr "Nessun embed trovato per l'URL specificato."

#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "Il campo scheda visualizzerà correttamente quando aggiunto a un campo "
#~ "ripetitore stile di tabella o disposizione flessibile in campo dei "
#~ "contenuti"

#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr ""
#~ "Usa \"Campi Scheda\" per organizzare al meglio la vostra schermata di "
#~ "modifica raggruppando i campi insieme."

#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "Tutti i campi che seguono questo \"campo scheda\" (o finché un altro "
#~ "\"campo tab \" viene definito) verranno raggruppati utilizzando "
#~ "l'etichetta di questo campo come intestazione scheda."

#~ msgid "End-point"
#~ msgstr "Punto finale"

#~ msgid "Use this field as an end-point and start a new group of tabs"
#~ msgstr ""
#~ "Utilizzare questo campo come un punto finale e iniziare un nuovo gruppo "
#~ "di schede"

#~ msgid "Getting Started"
#~ msgstr "Guida introduttiva"

#~ msgid "Field Types"
#~ msgstr "Tipi di Field"

#~ msgid "Functions"
#~ msgstr "Funzioni"

#~ msgid "Actions"
#~ msgstr "Azioni"

#~ msgid "Features"
#~ msgstr "Caratteristiche"

#~ msgid "How to"
#~ msgstr "Come fare"

#~ msgid "Tutorials"
#~ msgstr "Tutorial"

#~ msgid "FAQ"
#~ msgstr "FAQ"

#~ msgid "Term meta upgrade not possible (termmeta table does not exist)"
#~ msgstr ""
#~ "Non è possibile l'aggiornamento del meta termine (la tabella termmeta non "
#~ "esiste)"

#~ msgid "Error"
#~ msgstr "Errore"

#~ msgid "1 field requires attention."
#~ msgid_plural "%d fields require attention."
#~ msgstr[0] "1 campo richiede attenzione."
#~ msgstr[1] "%d campi richiedono attenzione."

#~ msgid ""
#~ "Error validating ACF PRO license URL (website does not match). Please re-"
#~ "activate your license"
#~ msgstr ""
#~ "Errore durante la convalida dell'URL della licenza di ACF PRO (sito web "
#~ "non corrisponde). Si prega di riattivare la licenza"

#~ msgid "See what's new"
#~ msgstr "Guarda cosa c'è di nuovo"

#~ msgid "Disabled"
#~ msgstr "Disabilitato"

#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Disabilitato <span class=\"count\">(%s)</span>"
#~ msgstr[1] "Disabilitato <span class=\"count\">(%s)</span>"

#~ msgid "'How to' guides"
#~ msgstr "Guide del 'come si fa'"

#~ msgid "Created by"
#~ msgstr "Creato da"

#~ msgid "Text shown when not active"
#~ msgstr "Testo visualizzato quando non è attivo"

#~ msgid ""
#~ "Error validating license URL (website does not match). Please re-activate "
#~ "your license"
#~ msgstr ""
#~ "Errore nella convalida licenza URL (sito Web non corrisponde). Si prega "
#~ "di ri-attivare la licenza"

#~ msgid "Error loading update"
#~ msgstr "Errore durante il caricamento."

#~ msgid "eg. Show extra content"
#~ msgstr "es. Mostra contenuti extra"

#~ msgid "Select"
#~ msgstr "Seleziona"

#~ msgctxt "Field label"
#~ msgid "Clone"
#~ msgstr "Clona"

#~ msgctxt "Field instruction"
#~ msgid "Clone"
#~ msgstr "Clona"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Errore di connessione</b>. Spiacenti, per favore riprova"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr ""
#~ "<b>Successo</b>. Lo strumento di importazione ha aggiunto %s Field Group: "
#~ "%s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Attenzione</b>. Lo strumento di importazione ha trovato %s \n"
#~ "Field Group\n"
#~ " già esistenti e sono stati ignorati: %s"

#~ msgid "Upgrade ACF"
#~ msgstr "Aggiorna ACF"

#~ msgid "Upgrade"
#~ msgstr "Aggiornamento"

#~ msgid ""
#~ "The following sites require a DB upgrade. Check the ones you want to "
#~ "update and then click “Upgrade Database”."
#~ msgstr ""
#~ "I seguenti siti necessitano di un aggiornamento Database. Seleziona "
#~ "quelli da aggiornare e clicca \"Aggiorna Database\""

#~ msgid "Done"
#~ msgstr "Fatto"

#~ msgid "Today"
#~ msgstr "Oggi"

#~ msgid "Show a different month"
#~ msgstr "Mostra un altro mese"

#~ msgid "See what's new in"
#~ msgstr "Guarda cosa c'è di nuovo"

#~ msgid "version"
#~ msgstr "versione"

#~ msgid "Upgrading data to"
#~ msgstr "Aggiornare i dati a"

#~ msgid "Return format"
#~ msgstr "Formato ritorno"

#~ msgid "uploaded to this post"
#~ msgstr "caricare a questo post"

#~ msgid "File Name"
#~ msgstr "Nome file"

#~ msgid "File Size"
#~ msgstr "Dimensione file"

#~ msgid "No File selected"
#~ msgstr "Nessun file selezionato"

#~ msgid "Save Options"
#~ msgstr "Salva Opzioni"

#~ msgid "License"
#~ msgstr "Licenza"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Per sbloccare gli aggiornamenti, inserisci il tuo codice di licenza di "
#~ "seguito. Se non si dispone di una chiave di licenza, si prega di "
#~ "consultare"

#~ msgid "details & pricing"
#~ msgstr "dettagli & prezzi"

#~ msgid ""
#~ "To enable updates, please enter your license key on the <a href=\"%s"
#~ "\">Updates</a> page. If you don't have a licence key, please see <a href="
#~ "\"%s\">details & pricing</a>"
#~ msgstr ""
#~ "Per attivare gli aggiornamenti, inserisci il tuo codice di licenza sulla "
#~ "pagina <a href=\"%s\">Aggiornamenti</a>. Se non si dispone di una chiave "
#~ "di licenza, si prega di consultare <a href=\"%s\">dettagli & prezzi</a>"

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "Advanced Custom Fields Pro"

#~ msgid "http://www.advancedcustomfields.com/"
#~ msgstr "http://www.advancedcustomfields.com/"

#~ msgid "elliot condon"
#~ msgstr "elliot condon"

#~ msgid "Drag and drop to reorder"
#~ msgstr "Trascina per riordinare"

#~ msgid "Add new %s "
#~ msgstr "Aggiungi %s "

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr ""
#~ "Si prega di notare che tutto il testo viene prima passato attraverso la "
#~ "funzione wp"

#~ msgid "Warning"
#~ msgstr "Attenzione"

#~ msgid "Import / Export"
#~ msgstr "Importa / Esporta"

#~ msgid "Field groups are created in order from lowest to highest"
#~ msgstr "I Field Group sono creati in ordine dal più basso al più alto"
