# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2022-12-13T15:07:38+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: ca\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/api/api-template.php:867
msgid "[ACF shortcode value disabled for preview]"
msgstr ""

#: includes/admin/admin-field-group.php:672
msgid "Close Modal"
msgstr ""

#: includes/admin/admin-field-group.php:162
#: assets/build/js/acf-field-group.js:1304
#: assets/build/js/acf-field-group.js:1468
msgid "Field moved to other group"
msgstr ""

#: includes/admin/admin-field-group.php:161 assets/build/js/acf.js:1397
#: assets/build/js/acf.js:1463
msgid "Close modal"
msgstr ""

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr "Comença un nou grup de pestanyes en aquesta pestanya."

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr "Nou grup de pestanyes"

#: includes/fields/class-acf-field-select.php:450
#: includes/fields/class-acf-field-true_false.php:197
msgid "Use a stylized checkbox using select2"
msgstr ""

#: includes/fields/class-acf-field-radio.php:257
msgid "Save Other Choice"
msgstr ""

#: includes/fields/class-acf-field-radio.php:246
msgid "Allow Other Choice"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:446
msgid "Add Toggle All"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:405
msgid "Save Custom Values"
msgstr "Desa els valors personalitzats"

#: includes/fields/class-acf-field-checkbox.php:394
msgid "Allow Custom Values"
msgstr "Permet valors personalitzats"

#: includes/fields/class-acf-field-checkbox.php:144
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""
"Els valors personalitzats de les caselles de selecció no poden estar buits. "
"Desmarqueu els valors buits."

#: includes/admin/views/html-admin-navigation.php:111
msgid "Updates"
msgstr "Actualitzacions"

#: includes/admin/views/html-admin-navigation.php:79
msgid "Advanced Custom Fields logo"
msgstr "Logotip de l'Advanced Custom Fields"

#: includes/admin/views/html-admin-form-top.php:22
msgid "Save Changes"
msgstr "Desa els canvis"

#: includes/admin/views/html-admin-form-top.php:16
msgid "Field Group Title"
msgstr "Títol del grup de camps"

#: includes/admin/views/html-admin-form-top.php:3
msgid "Add title"
msgstr "Afegeix un títol"

#. translators: %s url to getting started guide
#: includes/admin/views/field-groups-empty.php:20
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"Sou nou a l'ACF? Feu una ullada a la nostra <a href=\"%s\" target=\"_blank"
"\">guia d'inici</a>."

#: includes/admin/views/field-groups-empty.php:15
msgid "Add Field Group"
msgstr "Afegeix un grup de camps"

#. translators: %s url to creating a field group page
#: includes/admin/views/field-groups-empty.php:10
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"L'ACF utilitza <a href=\"%s\" target=\"_blank\">grups de camps</a> per "
"agrupar camps personalitzats i, a continuació, adjuntar aquests camps per "
"editar pantalles."

#: includes/admin/views/field-groups-empty.php:5
msgid "Add Your First Field Group"
msgstr "Afegiu el vostre primer grup de camps"

#: includes/admin/views/field-group-pro-features.php:16
msgid "Upgrade Now"
msgstr "Actualitza ara"

#: includes/admin/views/field-group-pro-features.php:11
msgid "Options Pages"
msgstr "Pàgines d'opcions"

#: includes/admin/views/field-group-pro-features.php:10
msgid "ACF Blocks"
msgstr "Blocs ACF"

#: includes/admin/views/field-group-pro-features.php:8
msgid "Gallery Field"
msgstr "Camp de galeria"

#: includes/admin/views/field-group-pro-features.php:7
msgid "Flexible Content Field"
msgstr "Camp de contingut flexible"

#: includes/admin/views/field-group-pro-features.php:6
msgid "Repeater Field"
msgstr "Camp repetible"

#: includes/admin/views/field-group-pro-features.php:4
#: includes/admin/views/html-admin-navigation.php:97
msgid "Unlock Extra Features with ACF PRO"
msgstr "Desbloquegeu característiques addicionals amb l'ACF PRO"

#: includes/admin/views/field-group-options.php:242
msgid "Delete Field Group"
msgstr "Suprimeix el grup de camps"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/field-group-options.php:236
msgid "Created on %1$s at %2$s"
msgstr "Creat el %1$s a les %2$s"

#: includes/admin/views/field-group-options.php:179
msgid "Group Settings"
msgstr ""

#: includes/admin/views/field-group-options.php:28
msgid "Location Rules"
msgstr "Regles d'ubicació"

#. translators: %s url to field types list
#: includes/admin/views/field-group-fields.php:61
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"Trieu entre més de 30 tipus de camps. <a href=\"%s\" target=\"_blank\">Més "
"informació</a>."

#: includes/admin/views/field-group-fields.php:54
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Comenceu a crear nous camps personalitzats per a les vostres entrades, "
"pàgines, tipus de contingut personalitzats i altres continguts del WordPress."

#: includes/admin/views/field-group-fields.php:53
msgid "Add Your First Field"
msgstr "Afegiu el vostre primer camp"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/field-group-fields.php:32
msgid "#"
msgstr "#"

#: includes/admin/views/field-group-fields.php:22
#: includes/admin/views/field-group-fields.php:56
#: includes/admin/views/field-group-fields.php:92
#: includes/admin/views/html-admin-form-top.php:21
msgid "Add Field"
msgstr "Afegeix un camp"

#: includes/admin/views/field-group-field.php:191
#: includes/admin/views/field-group-options.php:40
msgid "Presentation"
msgstr "Presentació"

#: includes/admin/views/field-group-field.php:159
msgid "Validation"
msgstr "Validació"

#: includes/admin/views/field-group-field.php:93
msgid "General"
msgstr "General"

#: includes/admin/tools/class-acf-admin-tool-import.php:70
msgid "Import JSON"
msgstr "Importa JSON"

#: includes/admin/tools/class-acf-admin-tool-export.php:361
msgid "Export Field Groups - Generate PHP"
msgstr "Exporta grups de camps - Genera PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid "Export As JSON"
msgstr "Exporta com a JSON"

#: includes/admin/admin-field-groups.php:638
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "S'ha desactivat el grup de camps."
msgstr[1] "S'han desactivat %s grups de camps."

#: includes/admin/admin-field-groups.php:585
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "S'ha activat el grup de camps."
msgstr[1] "S'han activat %s grups de camps."

#: includes/admin/admin-field-groups.php:537
#: includes/admin/admin-field-groups.php:558
msgid "Deactivate"
msgstr "Desactiva"

#: includes/admin/admin-field-groups.php:537
msgid "Deactivate this item"
msgstr "Desactiva aquest element"

#: includes/admin/admin-field-groups.php:533
#: includes/admin/admin-field-groups.php:557
msgid "Activate"
msgstr "Activa"

#: includes/admin/admin-field-groups.php:533
msgid "Activate this item"
msgstr "Activa aquest element"

#: includes/admin/admin-field-group.php:158
#: assets/build/js/acf-field-group.js:2366
#: assets/build/js/acf-field-group.js:2635
msgid "Move field group to trash?"
msgstr "Voleu moure el grup de camps a la paperera?"

#: acf.php:448 includes/admin/admin-field-group.php:353
#: includes/admin/admin-field-groups.php:232
msgctxt "post status"
msgid "Inactive"
msgstr "Inactiva"

#. Author of the plugin
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:506
msgid ""
"Advanced Custom Fields and Advanced Custom Fields should not be active at "
"the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""

#: acf.php:504
msgid ""
"Advanced Custom Fields and Advanced Custom Fields should not be active at "
"the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""

#: includes/acf-value-functions.php:374
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" target=\"_blank"
"\">Learn how to fix this</a>."
msgstr ""
"<strong>%1$s</strong> - hem detectat una o més crides per recuperar valors "
"de camps ACF abans que ACF s'hagi inicialitzat. Això no s'admet i pot donar "
"lloc a dades malformades o que faltin. <a href=\"%2$s\" target=\"_blank"
"\">Obteniu informació sobre com solucionar-ho</a>."

#: includes/fields/class-acf-field-user.php:537
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s ha de tenir un usuari amb el rol %2$s."
msgstr[1] "%1$s ha de tenir un usuari amb un dels següents rols: %2$s"

#: includes/fields/class-acf-field-user.php:528
msgid "%1$s must have a valid user ID."
msgstr "%1$s ha de tenir un identificador d'usuari vàlid."

#: includes/fields/class-acf-field-user.php:366
msgid "Invalid request."
msgstr "Sol·licitud no vàlida."

#: includes/fields/class-acf-field-select.php:683
msgid "%1$s is not one of %2$s"
msgstr "%1$s no és un dels %2$s"

#: includes/fields/class-acf-field-post_object.php:669
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s ha de tenir el terme %2$s."
msgstr[1] "%1$s ha de tenir un dels següents termes: %2$s"

#: includes/fields/class-acf-field-post_object.php:653
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s ha de ser del tipus de contingut %2$s."
msgstr[1] "%1$s ha de ser d'un dels següents tipus de contingut: %2$s"

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must have a valid post ID."
msgstr "%1$s ha de tenir un identificador d'entrada vàlid."

#: includes/fields/class-acf-field-file.php:472
msgid "%s requires a valid attachment ID."
msgstr "%s requereix un identificador d'adjunt vàlid."

#: includes/admin/views/field-group-options.php:205
msgid "Show in REST API"
msgstr "Mostra a l'API REST"

#: includes/fields/class-acf-field-color_picker.php:167
msgid "Enable Transparency"
msgstr "Activa la transparència"

#: includes/fields/class-acf-field-color_picker.php:186
msgid "RGBA Array"
msgstr "Matriu RGBA"

#: includes/fields/class-acf-field-color_picker.php:96
msgid "RGBA String"
msgstr "Cadena RGBA"

#: includes/fields/class-acf-field-color_picker.php:95
#: includes/fields/class-acf-field-color_picker.php:185
msgid "Hex String"
msgstr "Cadena hexadecimal"

#: includes/admin/admin-field-group.php:187
#: assets/build/js/acf-field-group.js:837
#: assets/build/js/acf-field-group.js:933
msgid "Gallery (Pro only)"
msgstr "Galeria (només a la versió pro)"

#: includes/admin/admin-field-group.php:186
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:923
msgid "Clone (Pro only)"
msgstr "Clona (només a la versió pro)"

#: includes/admin/admin-field-group.php:185
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:920
msgid "Flexible Content (Pro only)"
msgstr "Contingut flexible (només a la versió pro)"

#: includes/admin/admin-field-group.php:184
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:917
msgid "Repeater (Pro only)"
msgstr "Repetible (només a la versió pro)"

#: includes/admin/admin-field-group.php:353
msgctxt "post status"
msgid "Active"
msgstr "Activa"

#: includes/fields/class-acf-field-email.php:178
msgid "'%s' is not a valid email address"
msgstr "«%s» no és una adreça electrònica vàlida"

#: includes/fields/class-acf-field-color_picker.php:74
msgid "Color value"
msgstr "Valor de color"

#: includes/fields/class-acf-field-color_picker.php:72
msgid "Select default color"
msgstr "Seleccioneu el color per defecte"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Clear color"
msgstr "Neteja el color"

#: includes/acf-wp-functions.php:87
msgid "Blocks"
msgstr "Blocs"

#: includes/acf-wp-functions.php:83
msgid "Options"
msgstr "Opcions"

#: includes/acf-wp-functions.php:79
msgid "Users"
msgstr "Usuaris"

#: includes/acf-wp-functions.php:75
msgid "Menu items"
msgstr "Elements del menú"

#: includes/acf-wp-functions.php:67
msgid "Widgets"
msgstr "Ginys"

#: includes/acf-wp-functions.php:59
msgid "Attachments"
msgstr "Adjunts"

#: includes/acf-wp-functions.php:54
msgid "Taxonomies"
msgstr "Taxonomies"

#: includes/acf-wp-functions.php:41
msgid "Posts"
msgstr "Entrades"

#: includes/ajax/class-acf-ajax-local-json-diff.php:68
msgid "JSON field group (newer)"
msgstr "Grup de camps JSON (nou)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:64
msgid "Original field group"
msgstr "Grup de camps original"

#: includes/ajax/class-acf-ajax-local-json-diff.php:59
msgid "Last updated: %s"
msgstr "Darrera actualització: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:53
msgid "Sorry, this field group is unavailable for diff comparison."
msgstr ""
"Aquest grup de camps no està disponible per a la comparació de diferències."

#: includes/ajax/class-acf-ajax-local-json-diff.php:43
msgid "Invalid field group ID."
msgstr "ID de grup de camps invàlid."

#: includes/ajax/class-acf-ajax-local-json-diff.php:36
msgid "Invalid field group parameter(s)."
msgstr "Paràmetre/s del grup de camps no vàlids."

#: includes/admin/admin-field-groups.php:506
msgid "Awaiting save"
msgstr "Esperant desar"

#: includes/admin/admin-field-groups.php:503
msgid "Saved"
msgstr "S'ha desat"

#: includes/admin/admin-field-groups.php:499
msgid "Import"
msgstr "Importa"

#: includes/admin/admin-field-groups.php:495
msgid "Review changes"
msgstr "Revisa els canvis"

#: includes/admin/admin-field-groups.php:471
msgid "Located in: %s"
msgstr "Ubicat a: %s"

#: includes/admin/admin-field-groups.php:467
msgid "Located in plugin: %s"
msgstr "Ubicat a l'extensió: %s"

#: includes/admin/admin-field-groups.php:463
msgid "Located in theme: %s"
msgstr "Ubicat al tema: %s"

#: includes/admin/admin-field-groups.php:441
msgid "Various"
msgstr "Diversos"

#: includes/admin/admin-field-groups.php:200
#: includes/admin/admin-field-groups.php:565
msgid "Sync changes"
msgstr "Sincronitza els canvis"

#: includes/admin/admin-field-groups.php:199
msgid "Loading diff"
msgstr "S'està carregant el diff"

#: includes/admin/admin-field-groups.php:198
msgid "Review local JSON changes"
msgstr "Revisa els canvis JSON locals"

#: includes/admin/admin.php:172
msgid "Visit website"
msgstr "Visiteu el lloc web"

#: includes/admin/admin.php:171
msgid "View details"
msgstr "Visualitza els detalls"

#: includes/admin/admin.php:170
msgid "Version %s"
msgstr "Versió %s"

#: includes/admin/admin.php:169
msgid "Information"
msgstr "Informació"

#: includes/admin/admin.php:160
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Servei d'ajuda</a>. Els professionals de "
"suport al servei d'ajuda us ajudaran amb els problemes tècnics més profunds."

#: includes/admin/admin.php:156
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the ‘how-tos’ of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Debats</a>. Tenim una comunitat activa i "
"amistosa als nostres fòrums comunitaris que pot ajudar-vos a descobrir com "
"es fan les coses al món de l'ACF."

#: includes/admin/admin.php:152
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Documentació</a>. La nostra extensa "
"documentació conté referències i guies per a la majoria de situacions que "
"podeu trobar."

#: includes/admin/admin.php:149
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Som fanàtics del suport i volem que tragueu el màxim profit del vostre lloc "
"web amb l'ACF. Si trobeu alguna dificultat, hi ha diversos llocs on podeu "
"trobar ajuda:"

#: includes/admin/admin.php:146 includes/admin/admin.php:148
msgid "Help & Support"
msgstr "Ajuda i suport"

#: includes/admin/admin.php:137
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Utilitzeu la pestanya d'ajuda i suport per posar-vos en contacte amb "
"nosaltres si necessiteu ajuda."

#: includes/admin/admin.php:134
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Abans de crear el vostre primer grup de camps recomanem llegir abans la "
"nostra guia <a href=\"%s\" target=\"_blank\">Primers passos</a> per "
"familiaritzar-vos amb la filosofia i les millors pràctiques de l'extensió."

#: includes/admin/admin.php:132
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"L'extensió Advanced Custom Fields proporciona un maquetador de formularis "
"visual per personalitzar les pantalles d'edició del WordPress amb camps "
"addicionals i una API intuïtiva per mostrar els valors dels camps "
"personalitzats en qualsevol fitxer de plantilla de tema."

#: includes/admin/admin.php:129 includes/admin/admin.php:131
msgid "Overview"
msgstr "Resum"

#: includes/locations.php:36
msgid "Location type \"%s\" is already registered."
msgstr "El tipus d'ubicació «%s» ja està registrat."

#: includes/locations.php:25
msgid "Class \"%s\" does not exist."
msgstr "La classe «%s» no existeix."

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "El nonce no és vàlid."

#: includes/fields/class-acf-field-user.php:361
msgid "Error loading field."
msgstr "Error en carregar el camp."

#: assets/build/js/acf-input.js:2742 assets/build/js/acf-input.js:2811
#: assets/build/js/acf-input.js:2913 assets/build/js/acf-input.js:2987
msgid "Location not found: %s"
msgstr "No s'ha trobat la ubicació: %s"

#: includes/forms/form-user.php:353
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Error</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Giny"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Rol de l'usuari"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Comentari"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Format de l'entrada"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Element del menú"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Estat de l'entrada"

#: includes/acf-wp-functions.php:71
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menús"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Ubicacions dels menús"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menú"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Taxonomia de l'entrada"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Pàgina filla (té mare)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Pàgina mare (té filles)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Pàgina de primer nivell (no té mare)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Pàgina de les entrades"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Portada"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Tipus de pàgina"

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr "Veient l’administració"

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr "Veient la part frontal"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Connectat"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Usuari actual"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Plantilla de la pàgina"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Registra"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Afegeix / Edita"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Formulari d'usuari"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Pàgina mare"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Superadministrador"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Rol de l'usuari actual"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Plantilla per defecte"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Plantilla de l'entrada"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Categoria de l'entrada"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Tots els formats de %s"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Adjunt"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "Cal introduir un valor a %s"

#: includes/admin/views/field-group-field-conditional-logic.php:59
msgid "Show this field if"
msgstr "Mostra aquest camp si"

#: includes/admin/views/field-group-field-conditional-logic.php:26
#: includes/admin/views/field-group-field.php:279
msgid "Conditional Logic"
msgstr "Lògica condicional"

#: includes/admin/admin.php:207
#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/html-location-rule.php:92
msgid "and"
msgstr "i"

#: includes/admin/admin-field-groups.php:290
msgid "Local JSON"
msgstr "JSON local"

#: includes/admin/views/field-group-pro-features.php:9
msgid "Clone Field"
msgstr "Clona el camp"

#: includes/admin/views/html-notice-upgrade.php:30
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Comproveu que tots els complements prèmium (%s) estan actualitzats a la "
"darrera versió."

#: includes/admin/views/html-notice-upgrade.php:28
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Aquesta versió inclou millores a la base de dades i necessita una "
"actualització."

#: includes/admin/views/html-notice-upgrade.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Gràcies per actualitzar a %1$s v%2$s!"

#: includes/admin/views/html-notice-upgrade.php:27
msgid "Database Upgrade Required"
msgstr "Cal actualitzar la base de dades"

#: includes/admin/views/html-notice-upgrade.php:18
msgid "Options Page"
msgstr "Pàgina d'opcions"

#: includes/admin/views/html-notice-upgrade.php:15
msgid "Gallery"
msgstr "Galeria"

#: includes/admin/views/html-notice-upgrade.php:12
msgid "Flexible Content"
msgstr "Contingut flexible"

#: includes/admin/views/html-notice-upgrade.php:9
msgid "Repeater"
msgstr "Repetible"

#: includes/admin/views/html-admin-tools.php:24
msgid "Back to all tools"
msgstr "Torna a totes les eines"

#: includes/admin/views/field-group-options.php:160
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Si hi ha diversos grups de camps a la pantalla d'edició, s'utilitzaran les "
"opcions del primer grup de camps (el que tingui el nombre d'ordre més baix)"

#: includes/admin/views/field-group-options.php:160
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Seleccioneu</b> els elements a <b>amagar</b>de la pantalla d'edició."

#: includes/admin/views/field-group-options.php:159
msgid "Hide on screen"
msgstr "Amaga a la pantalla"

#: includes/admin/views/field-group-options.php:151
msgid "Send Trackbacks"
msgstr "Envia retroenllaços"

#: includes/admin/views/field-group-options.php:150
msgid "Tags"
msgstr "Etiquetes"

#: includes/admin/views/field-group-options.php:149
msgid "Categories"
msgstr "Categories"

#: includes/admin/views/field-group-options.php:147
msgid "Page Attributes"
msgstr "Atributs de la pàgina"

#: includes/admin/views/field-group-options.php:146
msgid "Format"
msgstr "Format"

#: includes/admin/views/field-group-options.php:145
msgid "Author"
msgstr "Autor"

#: includes/admin/views/field-group-options.php:144
msgid "Slug"
msgstr "Àlies"

#: includes/admin/views/field-group-options.php:143
msgid "Revisions"
msgstr "Revisions"

#: includes/acf-wp-functions.php:63
#: includes/admin/views/field-group-options.php:142
msgid "Comments"
msgstr "Comentaris"

#: includes/admin/views/field-group-options.php:141
msgid "Discussion"
msgstr "Debats"

#: includes/admin/views/field-group-options.php:139
msgid "Excerpt"
msgstr "Extracte"

#: includes/admin/views/field-group-options.php:138
msgid "Content Editor"
msgstr "Editor de contingut"

#: includes/admin/views/field-group-options.php:137
msgid "Permalink"
msgstr "Enllaç permanent"

#: includes/admin/views/field-group-options.php:222
msgid "Shown in field group list"
msgstr "Es mostra a la llista de grups de camps"

#: includes/admin/views/field-group-options.php:122
msgid "Field groups with a lower order will appear first"
msgstr "Els grups de camps amb un ordre més baix apareixeran primer"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Núm. d’ordre"

#: includes/admin/views/field-group-options.php:112
msgid "Below fields"
msgstr "Sota els camps"

#: includes/admin/views/field-group-options.php:111
msgid "Below labels"
msgstr "Sota les etiquetes"

#: includes/admin/views/field-group-options.php:104
msgid "Instruction placement"
msgstr "Posició de les instruccions"

#: includes/admin/views/field-group-options.php:87
msgid "Label placement"
msgstr "Posició de les etiquetes"

#: includes/admin/views/field-group-options.php:77
msgid "Side"
msgstr "Lateral"

#: includes/admin/views/field-group-options.php:76
msgid "Normal (after content)"
msgstr "Normal (després del contingut)"

#: includes/admin/views/field-group-options.php:75
msgid "High (after title)"
msgstr "Alta (després del títol)"

#: includes/admin/views/field-group-options.php:68
msgid "Position"
msgstr "Posició"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Fluid (sense la caixa meta)"

#: includes/admin/views/field-group-options.php:58
msgid "Standard (WP metabox)"
msgstr "Estàndard (en una caixa meta de WP)"

#: includes/admin/views/field-group-options.php:51
msgid "Style"
msgstr "Estil"

#: includes/admin/views/field-group-fields.php:44
msgid "Type"
msgstr "Tipus"

#: includes/admin/admin-field-groups.php:285
#: includes/admin/views/field-group-fields.php:43
msgid "Key"
msgstr "Clau"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/field-group-fields.php:37
msgid "Order"
msgstr "Ordre"

#: includes/admin/views/field-group-field.php:294
msgid "Close Field"
msgstr "Tanca el camp"

#: includes/admin/views/field-group-field.php:163
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:219
msgid "class"
msgstr "classe"

#: includes/admin/views/field-group-field.php:256
msgid "width"
msgstr "amplada"

#: includes/admin/views/field-group-field.php:129
msgid "Wrapper Attributes"
msgstr "Atributs del contenidor"

#: includes/admin/views/field-group-field.php:171
msgid "Required"
msgstr "Obligatori"

#: includes/admin/views/field-group-field.php:203
msgid "Instructions for authors. Shown when submitting data"
msgstr "Instruccions per als autors. Es mostren en enviar les dades"

#: includes/admin/views/field-group-field.php:202
msgid "Instructions"
msgstr "Instruccions"

#: includes/admin/views/field-group-field.php:106
msgid "Field Type"
msgstr "Tipus de camp"

#: includes/admin/views/field-group-field.php:76
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Una sola paraula, sense espais. S’admeten barres baixes i guions"

#: includes/admin/views/field-group-field.php:133
msgid "Field Name"
msgstr "Nom del camp"

#: includes/admin/views/field-group-field.php:121
msgid "This is the name which will appear on the EDIT page"
msgstr "Aquest és el nom que apareixerà a la pàgina d'edició"

#: includes/admin/views/field-group-field.php:120
msgid "Field Label"
msgstr "Etiqueta del camp"

#: includes/admin/views/field-group-field.php:70
msgid "Delete"
msgstr "Suprimeix"

#: includes/admin/views/field-group-field.php:70
msgid "Delete field"
msgstr "Suprimeix el camp"

#: includes/admin/views/field-group-field.php:68
msgid "Move"
msgstr "Mou"

#: includes/admin/views/field-group-field.php:68
msgid "Move field to another group"
msgstr "Mou el camp a un altre grup"

#: includes/admin/views/field-group-field.php:66
msgid "Duplicate field"
msgstr "Duplica el camp"

#: includes/admin/views/field-group-field.php:62
#: includes/admin/views/field-group-field.php:65
msgid "Edit field"
msgstr "Edita el camp"

#: includes/admin/views/field-group-field.php:58
msgid "Drag to reorder"
msgstr "Arrossegueu per reordenar"

#: includes/admin/admin-field-group.php:168
#: includes/admin/views/html-location-group.php:3
#: assets/build/js/acf-field-group.js:1935
#: assets/build/js/acf-field-group.js:2166
msgid "Show this field group if"
msgstr "Mostra aquest grup de camps si"

#: includes/admin/views/html-admin-page-upgrade.php:94
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "No hi ha actualitzacions disponibles."

#: includes/admin/views/html-admin-page-upgrade.php:33
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"S'ha completat l'actualització de la base de dades. <a href=\"%s\">Feu una "
"ullada a les novetats</a>"

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "S'estan llegint les tasques d'actualització…"

#: includes/admin/views/html-admin-page-upgrade-network.php:165
#: includes/admin/views/html-admin-page-upgrade.php:65
msgid "Upgrade failed."
msgstr "L'actualització ha fallat."

#: includes/admin/views/html-admin-page-upgrade-network.php:162
msgid "Upgrade complete."
msgstr "S'ha completat l'actualització."

#: includes/admin/views/html-admin-page-upgrade-network.php:148
#: includes/admin/views/html-admin-page-upgrade.php:31
msgid "Upgrading data to version %s"
msgstr "S'estan actualitzant les dades a la versió %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:121
#: includes/admin/views/html-notice-upgrade.php:44
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"És recomanable que feu una còpia de seguretat de la base de dades abans de "
"continuar. Segur que voleu executar l'actualitzador ara?"

#: includes/admin/views/html-admin-page-upgrade-network.php:117
msgid "Please select at least one site to upgrade."
msgstr "Seleccioneu almenys un lloc web per actualitzar."

#: includes/admin/views/html-admin-page-upgrade-network.php:97
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"S'ha completat l'actualització de la base de dades. <a href=\"%s\">Torna al "
"tauler de la xarxa</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:80
msgid "Site is up to date"
msgstr "El lloc web està actualitzat"

#: includes/admin/views/html-admin-page-upgrade-network.php:78
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr ""
"El lloc web requereix una actualització de la base de dades de %1$s a %2$s"

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Lloc"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:92
msgid "Upgrade Sites"
msgstr "Actualitza els llocs"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Els següents llocs web necessiten una actualització de la base de dades. "
"Marqueu els que voleu actualitzar i feu clic a %s."

#: includes/admin/views/field-group-field-conditional-logic.php:171
#: includes/admin/views/field-group-locations.php:38
msgid "Add rule group"
msgstr "Afegeix un grup de regles"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Crea un grup de regles que determinaran quines pantalles d’edició mostraran "
"aquests camps personalitzats"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Regles"

#: includes/admin/tools/class-acf-admin-tool-export.php:478
msgid "Copied"
msgstr "Copiat"

#: includes/admin/tools/class-acf-admin-tool-export.php:441
msgid "Copy to clipboard"
msgstr "Copia-ho al porta-retalls"

#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"El següent codi es pot usar per a registrar una versió local del(s) grup(s) "
"de camps escollit(s). Un grup de camps local pot aportar diversos avantatges "
"com ara temps de càrrega més ràpids, control de versions, i opcions i camps "
"dinàmics. Simplement copieu i enganxeu el següent codi al fitxer functions."
"php del vostre tema, o incloeu-lo en un fitxer extern."

#: includes/admin/tools/class-acf-admin-tool-export.php:329
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Seleccioneu els grups de camps que voleu exportar i, a continuació, "
"seleccioneu el mètode d'exportació. Exporteu com a JSON per exportar a un "
"fitxer .json que després podeu importar a una altra instal·lació d'ACF. "
"Genereu PHP per exportar a codi PHP que podeu col·locar al vostre tema."

#: includes/admin/tools/class-acf-admin-tool-export.php:233
#: includes/admin/tools/class-acf-admin-tool-export.php:262
msgid "Select Field Groups"
msgstr "Seleccioneu grups de camps"

#: includes/admin/tools/class-acf-admin-tool-export.php:167
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "S'ha exportat 1 grup de camps."
msgstr[1] "S'han exportat %s grups de camps."

#: includes/admin/tools/class-acf-admin-tool-export.php:96
#: includes/admin/tools/class-acf-admin-tool-export.php:131
msgid "No field groups selected"
msgstr "No s'ha seleccionat cap grup de camps"

#: includes/admin/tools/class-acf-admin-tool-export.php:39
#: includes/admin/tools/class-acf-admin-tool-export.php:337
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "Genera PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:35
msgid "Export Field Groups"
msgstr "Exporta els grups de camps"

#: includes/admin/tools/class-acf-admin-tool-import.php:146
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "S'ha importat un grup de camps."
msgstr[1] "S'han importat %s grups de camps."

#: includes/admin/tools/class-acf-admin-tool-import.php:115
msgid "Import file empty"
msgstr "El fitxer d'importació és buit"

#: includes/admin/tools/class-acf-admin-tool-import.php:106
msgid "Incorrect file type"
msgstr "Tipus de fitxer incorrecte"

#: includes/admin/tools/class-acf-admin-tool-import.php:101
msgid "Error uploading file. Please try again"
msgstr "S'ha produït un error en penjar el fitxer. Torneu-ho a provar"

#: includes/admin/tools/class-acf-admin-tool-import.php:51
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups"
msgstr ""
"Seleccioneu el fitxer JSON de l'Advanced Custom Fields que voleu importar. "
"En fer clic al botó d'importació, l'ACF importarà els grups de camps."

#: includes/admin/tools/class-acf-admin-tool-import.php:28
#: includes/admin/tools/class-acf-admin-tool-import.php:50
msgid "Import Field Groups"
msgstr "Importa grups de camps"

#: includes/admin/admin-field-groups.php:494
msgid "Sync"
msgstr "Sincronitza"

#: includes/admin/admin-field-groups.php:942
msgid "Select %s"
msgstr "Selecciona %s"

#: includes/admin/admin-field-groups.php:527
#: includes/admin/admin-field-groups.php:556
#: includes/admin/views/field-group-field.php:66
msgid "Duplicate"
msgstr "Duplica"

#: includes/admin/admin-field-groups.php:527
msgid "Duplicate this item"
msgstr "Duplica aquest element"

#: includes/admin/admin-field-groups.php:284
#: includes/admin/views/field-group-options.php:221
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
msgid "Description"
msgstr "Descripció"

#: includes/admin/admin-field-groups.php:491
#: includes/admin/admin-field-groups.php:829
msgid "Sync available"
msgstr "Sincronització disponible"

#: includes/admin/admin-field-groups.php:754
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "S'ha sincronitzat el grup de camps."
msgstr[1] "S'han sincronitzat %s grups de camps."

#: includes/admin/admin-field-groups.php:696
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "S'ha duplicat el grup de camps."
msgstr[1] "S'han duplicat %s grups de camps."

#: includes/admin/admin-field-groups.php:118
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Actiu <span class=\"count\">(%s)</span>"
msgstr[1] "Actius <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:237
msgid "Review sites & upgrade"
msgstr "Revisa els llocs web i actualitza"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:93
#: includes/admin/admin-upgrade.php:94 includes/admin/admin-upgrade.php:213
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Actualitza la base de dades"

#: includes/admin/admin.php:49 includes/admin/views/field-group-options.php:140
msgid "Custom Fields"
msgstr "Camps personalitzats"

#: includes/admin/admin-field-group.php:718
msgid "Move Field"
msgstr "Mou el camp"

#: includes/admin/admin-field-group.php:707
#: includes/admin/admin-field-group.php:711
msgid "Please select the destination for this field"
msgstr "Seleccioneu la destinació d'aquest camp"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/admin-field-group.php:668
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "El camp %1$s ara es pot trobar al grup de camps %2$s"

#: includes/admin/admin-field-group.php:665
msgid "Move Complete."
msgstr "S’ha completat el moviment."

#: includes/admin/views/field-group-field.php:273
#: includes/admin/views/field-group-options.php:189
msgid "Active"
msgstr "Actiu"

#: includes/admin/admin-field-group.php:325
msgid "Field Keys"
msgstr "Claus dels camps"

#: includes/admin/admin-field-group.php:224
#: includes/admin/tools/class-acf-admin-tool-export.php:286
msgid "Settings"
msgstr "Configuració"

#: includes/admin/admin-field-groups.php:286
msgid "Location"
msgstr "Ubicació"

#: includes/admin/admin-field-group.php:169 assets/build/js/acf-input.js:983
#: assets/build/js/acf-input.js:1075
msgid "Null"
msgstr "Nul"

#: includes/acf-field-group-functions.php:846
#: includes/admin/admin-field-group.php:166
#: assets/build/js/acf-field-group.js:1158
#: assets/build/js/acf-field-group.js:1310
msgid "copy"
msgstr "copia"

#: includes/admin/admin-field-group.php:165
#: assets/build/js/acf-field-group.js:344
#: assets/build/js/acf-field-group.js:389
msgid "(this field)"
msgstr "(aquest camp)"

#: includes/admin/admin-field-group.php:163 assets/build/js/acf-input.js:918
#: assets/build/js/acf-input.js:943 assets/build/js/acf-input.js:1002
#: assets/build/js/acf-input.js:1030
msgid "Checked"
msgstr "Marcat"

#: includes/admin/admin-field-group.php:160
#: assets/build/js/acf-field-group.js:1249
#: assets/build/js/acf-field-group.js:1408
msgid "Move Custom Field"
msgstr "Mou el grup de camps"

#: includes/admin/admin-field-group.php:159
#: assets/build/js/acf-field-group.js:370
#: assets/build/js/acf-field-group.js:415
msgid "No toggle fields available"
msgstr "No hi ha camps commutables disponibles"

#: includes/admin/admin-field-group.php:157
msgid "Field group title is required"
msgstr "El títol del grup de camps és obligatori"

#: includes/admin/admin-field-group.php:172
msgid "This field cannot be moved until its changes have been saved"
msgstr "Aquest camp no es pot moure fins que no se n’hagin desat els canvis"

#: includes/admin/admin-field-group.php:155
#: assets/build/js/acf-field-group.js:1049
#: assets/build/js/acf-field-group.js:1192
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "La cadena «field_» no es pot utilitzar al principi del nom d'un camp"

#: includes/admin/admin-field-group.php:82
msgid "Field group draft updated."
msgstr "S'ha actualitzat l’esborrany del grup de camps."

#: includes/admin/admin-field-group.php:81
msgid "Field group scheduled for."
msgstr "S'ha programat el grup de camps."

#: includes/admin/admin-field-group.php:93
msgid "Field group submitted."
msgstr "S’ha tramès el grup de camps."

#: includes/admin/admin-field-group.php:79
msgid "Field group saved."
msgstr "S'ha desat el grup de camps."

#: includes/admin/admin-field-group.php:78
msgid "Field group published."
msgstr "S'ha publicat el grup de camps."

#: includes/admin/admin-field-group.php:75
msgid "Field group deleted."
msgstr "S'ha suprimit el grup de camps."

#: includes/admin/admin-field-group.php:73
#: includes/admin/admin-field-group.php:74
#: includes/admin/admin-field-group.php:76
msgid "Field group updated."
msgstr "S'ha actualitzat el grup de camps."

#: includes/admin/admin-tools.php:119
#: includes/admin/views/html-admin-navigation.php:109
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Eines"

#: includes/locations/abstract-acf-location.php:106
msgid "is not equal to"
msgstr "no és igual a"

#: includes/locations/abstract-acf-location.php:105
msgid "is equal to"
msgstr "és igual a"

#: includes/locations.php:102
msgid "Forms"
msgstr "Formularis"

#: includes/locations.php:100 includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Pàgina"

#: includes/locations.php:99 includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Entrada"

#: includes/fields.php:358
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:357
msgid "Relational"
msgstr "Relacional"

#: includes/fields.php:351
msgid "Choice"
msgstr "Elecció"

#: includes/fields.php:354
msgid "Basic"
msgstr "Bàsic"

#: includes/fields.php:313
msgid "Unknown"
msgstr "Desconegut"

#: includes/fields.php:313
msgid "Field type does not exist"
msgstr "El tipus de camp no existeix"

#: includes/forms/form-front.php:236
msgid "Spam Detected"
msgstr "S'ha detectat brossa"

#: includes/forms/form-front.php:107
msgid "Post updated"
msgstr "S'ha actualitzat l'entrada"

#: includes/forms/form-front.php:106
msgid "Update"
msgstr "Actualitza"

#: includes/forms/form-front.php:57
msgid "Validate Email"
msgstr "Valida el correu electrònic"

#: includes/fields.php:355 includes/forms/form-front.php:49
msgid "Content"
msgstr "Contingut"

#: includes/forms/form-front.php:40
msgid "Title"
msgstr "Títol"

#: includes/assets.php:371 includes/forms/form-comment.php:160
#: assets/build/js/acf-input.js:7310 assets/build/js/acf-input.js:7876
msgid "Edit field group"
msgstr "Edita el grup de camps"

#: includes/admin/admin-field-group.php:181 assets/build/js/acf-input.js:1125
#: assets/build/js/acf-input.js:1230
msgid "Selection is less than"
msgstr "La selecció és inferior a"

#: includes/admin/admin-field-group.php:180 assets/build/js/acf-input.js:1106
#: assets/build/js/acf-input.js:1202
msgid "Selection is greater than"
msgstr "La selecció és superior a"

#: includes/admin/admin-field-group.php:179 assets/build/js/acf-input.js:1075
#: assets/build/js/acf-input.js:1170
msgid "Value is less than"
msgstr "El valor és inferior a"

#: includes/admin/admin-field-group.php:178 assets/build/js/acf-input.js:1045
#: assets/build/js/acf-input.js:1139
msgid "Value is greater than"
msgstr "El valor és superior a"

#: includes/admin/admin-field-group.php:177 assets/build/js/acf-input.js:888
#: assets/build/js/acf-input.js:960
msgid "Value contains"
msgstr "El valor conté"

#: includes/admin/admin-field-group.php:176 assets/build/js/acf-input.js:862
#: assets/build/js/acf-input.js:926
msgid "Value matches pattern"
msgstr "El valor coincideix amb el patró"

#: includes/admin/admin-field-group.php:175 assets/build/js/acf-input.js:840
#: assets/build/js/acf-input.js:1023 assets/build/js/acf-input.js:903
#: assets/build/js/acf-input.js:1116
msgid "Value is not equal to"
msgstr "El valor no és igual a"

#: includes/admin/admin-field-group.php:174 assets/build/js/acf-input.js:810
#: assets/build/js/acf-input.js:964 assets/build/js/acf-input.js:864
#: assets/build/js/acf-input.js:1053
msgid "Value is equal to"
msgstr "El valor és igual a"

#: includes/admin/admin-field-group.php:173 assets/build/js/acf-input.js:788
#: assets/build/js/acf-input.js:841
msgid "Has no value"
msgstr "No té cap valor"

#: includes/admin/admin-field-group.php:172 assets/build/js/acf-input.js:758
#: assets/build/js/acf-input.js:783
msgid "Has any value"
msgstr "Té algun valor"

#: includes/assets.php:352 assets/build/js/acf.js:1524
#: assets/build/js/acf.js:1604
msgid "Cancel"
msgstr "Cancel·la"

#: includes/assets.php:348 assets/build/js/acf.js:1698
#: assets/build/js/acf.js:1801
msgid "Are you sure?"
msgstr "N'esteu segur?"

#: includes/assets.php:368 assets/build/js/acf-input.js:9343
#: assets/build/js/acf-input.js:10172
msgid "%d fields require attention"
msgstr "Cal revisar %d camps"

#: includes/assets.php:367 assets/build/js/acf-input.js:9341
#: assets/build/js/acf-input.js:10168
msgid "1 field requires attention"
msgstr "Cal revisar un camp"

#: includes/assets.php:366 includes/validation.php:287
#: includes/validation.php:297 assets/build/js/acf-input.js:9336
#: assets/build/js/acf-input.js:10163
msgid "Validation failed"
msgstr "La validació ha fallat"

#: includes/assets.php:365 assets/build/js/acf-input.js:9499
#: assets/build/js/acf-input.js:10346
msgid "Validation successful"
msgstr "Validació correcta"

#: includes/media.php:54 assets/build/js/acf-input.js:7138
#: assets/build/js/acf-input.js:7680
msgid "Restricted"
msgstr "Restringit"

#: includes/assets.php:176
msgid "Collapse Details"
msgstr "Amaga els detalls"

#: includes/media.php:52 assets/build/js/acf-input.js:6953
#: assets/build/js/acf-input.js:7441
msgid "Expand Details"
msgstr "Expandeix els detalls"

#: includes/media.php:51 assets/build/js/acf-input.js:6820
#: assets/build/js/acf-input.js:7289
msgid "Uploaded to this post"
msgstr "S'ha penjat a aquesta entrada"

#: includes/media.php:50 assets/build/js/acf-input.js:6859
#: assets/build/js/acf-input.js:7328
msgctxt "verb"
msgid "Update"
msgstr "Actualitza"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Edita"

#: includes/assets.php:362 assets/build/js/acf-input.js:9113
#: assets/build/js/acf-input.js:9934
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Perdreu els canvis que heu fet si abandoneu aquesta pàgina"

#: includes/api/api-helpers.php:3395
msgid "File type must be %s."
msgstr "El tipus de fitxer ha de ser %s."

#: includes/admin/admin-field-group.php:167
#: includes/admin/views/field-group-field-conditional-logic.php:59
#: includes/admin/views/field-group-field-conditional-logic.php:169
#: includes/admin/views/field-group-locations.php:36
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3391 assets/build/js/acf-field-group.js:492
#: assets/build/js/acf-field-group.js:1973
#: assets/build/js/acf-field-group.js:544
#: assets/build/js/acf-field-group.js:2210
msgid "or"
msgstr "o"

#: includes/api/api-helpers.php:3364
msgid "File size must not exceed %s."
msgstr "La mida del fitxer no ha de superar %s."

#: includes/api/api-helpers.php:3359
msgid "File size must be at least %s."
msgstr "La mida del fitxer ha de ser almenys %s."

#: includes/api/api-helpers.php:3344
msgid "Image height must not exceed %dpx."
msgstr "L'alçada de la imatge no pot ser superior a %dpx."

#: includes/api/api-helpers.php:3339
msgid "Image height must be at least %dpx."
msgstr "L'alçada de la imatge ha de ser almenys de %dpx."

#: includes/api/api-helpers.php:3325
msgid "Image width must not exceed %dpx."
msgstr "L'amplada de la imatge no pot ser superior a %dpx."

#: includes/api/api-helpers.php:3320
msgid "Image width must be at least %dpx."
msgstr "L'amplada de la imatge ha de ser almenys de %dpx."

#: includes/api/api-helpers.php:1566 includes/api/api-term.php:147
msgid "(no title)"
msgstr "(sense títol)"

#: includes/api/api-helpers.php:861
msgid "Full Size"
msgstr "Mida completa"

#: includes/api/api-helpers.php:820
msgid "Large"
msgstr "Gran"

#: includes/api/api-helpers.php:819
msgid "Medium"
msgstr "Mitjana"

#: includes/api/api-helpers.php:818
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/acf-field-functions.php:846
#: includes/admin/admin-field-group.php:164
#: assets/build/js/acf-field-group.js:789
#: assets/build/js/acf-field-group.js:859
msgid "(no label)"
msgstr "(sense etiqueta)"

#: includes/fields/class-acf-field-textarea.php:142
msgid "Sets the textarea height"
msgstr "Estableix l'alçada de l'àrea de text"

#: includes/fields/class-acf-field-textarea.php:141
msgid "Rows"
msgstr "Files"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Àrea de text"

#: includes/fields/class-acf-field-checkbox.php:447
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"Afegeix una casella de selecció addicional per commutar totes les opcions"

#: includes/fields/class-acf-field-checkbox.php:409
msgid "Save 'custom' values to the field's choices"
msgstr "Desa els valors personalitzats a les opcions del camp"

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Allow 'custom' values to be added"
msgstr "Permet afegir-hi valors personalitzats"

#: includes/fields/class-acf-field-checkbox.php:233
msgid "Add new choice"
msgstr "Afegeix una nova opció"

#: includes/fields/class-acf-field-checkbox.php:170
msgid "Toggle All"
msgstr "Commuta'ls tots"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr "Permet les URLs dels arxius"

#: includes/fields/class-acf-field-page_link.php:165
msgid "Archives"
msgstr "Arxius"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Enllaç de la pàgina"

#: includes/fields/class-acf-field-taxonomy.php:945
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Afegeix"

#: includes/admin/views/field-group-fields.php:42
#: includes/fields/class-acf-field-taxonomy.php:910
msgid "Name"
msgstr "Nom"

#: includes/fields/class-acf-field-taxonomy.php:894
msgid "%s added"
msgstr "%s afegit"

#: includes/fields/class-acf-field-taxonomy.php:858
msgid "%s already exists"
msgstr "%s ja existeix"

#: includes/fields/class-acf-field-taxonomy.php:846
msgid "User unable to add new %s"
msgstr "L'usuari no pot afegir nous %s"

#: includes/fields/class-acf-field-taxonomy.php:756
msgid "Term ID"
msgstr "Identificador de terme"

#: includes/fields/class-acf-field-taxonomy.php:755
msgid "Term Object"
msgstr "Objecte de terme"

#: includes/fields/class-acf-field-taxonomy.php:820
msgid "Load value from posts terms"
msgstr "Carrega el valor dels termes de l’entrada"

#: includes/fields/class-acf-field-taxonomy.php:739
msgid "Load Terms"
msgstr "Carrega els termes"

#: includes/fields/class-acf-field-taxonomy.php:729
msgid "Connect selected terms to the post"
msgstr "Connecta els termes seleccionats a l'entrada"

#: includes/fields/class-acf-field-taxonomy.php:728
msgid "Save Terms"
msgstr "Desa els termes"

#: includes/fields/class-acf-field-taxonomy.php:800
msgid "Allow new terms to be created whilst editing"
msgstr "Permet crear nous termes mentre s’està editant"

#: includes/fields/class-acf-field-taxonomy.php:717
msgid "Create Terms"
msgstr "Crea els termes"

#: includes/fields/class-acf-field-taxonomy.php:776
msgid "Radio Buttons"
msgstr "Botons d'opció"

#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Single Value"
msgstr "Un sol valor"

#: includes/fields/class-acf-field-taxonomy.php:773
msgid "Multi Select"
msgstr "Selecció múltiple"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Checkbox"
msgstr "Casella de selecció"

#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Multiple Values"
msgstr "Múltiples valors"

#: includes/fields/class-acf-field-taxonomy.php:766
msgid "Select the appearance of this field"
msgstr "Seleccioneu l'aparença d'aquest camp"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Appearance"
msgstr "Aparença"

#: includes/fields/class-acf-field-taxonomy.php:707
msgid "Select the taxonomy to be displayed"
msgstr "Seleccioneu la taxonomia a mostrar"

#: includes/fields/class-acf-field-taxonomy.php:668
msgctxt "No Terms"
msgid "No %s"
msgstr "No hi ha %s"

#: includes/fields/class-acf-field-number.php:263
msgid "Value must be equal to or lower than %d"
msgstr "El valor ha de ser igual o inferior a %d"

#: includes/fields/class-acf-field-number.php:256
msgid "Value must be equal to or higher than %d"
msgstr "El valor ha de ser igual o superior a %d"

#: includes/fields/class-acf-field-number.php:241
msgid "Value must be a number"
msgstr "El valor ha de ser un nombre"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Número"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "Desa els valors d’’Altres’ a les opcions del camp"

#: includes/fields/class-acf-field-radio.php:250
msgid "Add 'other' choice to allow for custom values"
msgstr "Afegeix l'opció «altres» per permetre valors personalitzats"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Botó d'opció"

#: includes/fields/class-acf-field-accordion.php:120
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Definiu un punt final per a aturar l’acordió previ. Aquest acordió no serà "
"visible."

#: includes/fields/class-acf-field-accordion.php:93
msgid "Allow this accordion to open without closing others."
msgstr "Permet que aquest acordió s'obri sense tancar els altres."

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr "Expansió múltiple"

#: includes/fields/class-acf-field-accordion.php:82
msgid "Display this accordion as open on page load."
msgstr "Mostra aquest acordió obert en carregar la pàgina."

#: includes/fields/class-acf-field-accordion.php:81
msgid "Open"
msgstr "Obert"

#: includes/fields/class-acf-field-accordion.php:25
msgid "Accordion"
msgstr "Acordió"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-file.php:276
msgid "Restrict which files can be uploaded"
msgstr "Restringeix quins fitxers es poden penjar"

#: includes/fields/class-acf-field-file.php:217
msgid "File ID"
msgstr "Identificador de fitxer"

#: includes/fields/class-acf-field-file.php:216
msgid "File URL"
msgstr "URL del fitxer"

#: includes/fields/class-acf-field-file.php:215
msgid "File Array"
msgstr "Matriu de fitxer"

#: includes/fields/class-acf-field-file.php:183
msgid "Add File"
msgstr "Afegeix un fitxer"

#: includes/admin/tools/class-acf-admin-tool-import.php:94
#: includes/fields/class-acf-field-file.php:183
msgid "No file selected"
msgstr "No s'ha seleccionat cap fitxer"

#: includes/fields/class-acf-field-file.php:147
msgid "File name"
msgstr "Nom del fitxer"

#: includes/fields/class-acf-field-file.php:60
#: assets/build/js/acf-input.js:2466 assets/build/js/acf-input.js:2612
msgid "Update File"
msgstr "Actualitza el fitxer"

#: includes/fields/class-acf-field-file.php:59
#: assets/build/js/acf-input.js:2465 assets/build/js/acf-input.js:2611
msgid "Edit File"
msgstr "Edita el fitxer"

#: includes/admin/tools/class-acf-admin-tool-import.php:52
#: includes/fields/class-acf-field-file.php:57
msgid "Select File"
msgstr "Escull el fitxer"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Fitxer"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Contrasenya"

#: includes/fields/class-acf-field-select.php:391
msgid "Specify the value returned"
msgstr "Especifiqueu el valor a retornar"

#: includes/fields/class-acf-field-select.php:412
msgid "Use AJAX to lazy load choices?"
msgstr "Usa AJAX per a carregar opcions de manera relaxada?"

#: includes/fields/class-acf-field-checkbox.php:358
#: includes/fields/class-acf-field-select.php:380
msgid "Enter each default value on a new line"
msgstr "Introduïu cada valor per defecte en una línia nova"

#: includes/assets.php:171 includes/fields/class-acf-field-select.php:259
msgctxt "verb"
msgid "Select"
msgstr "Selecciona"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "No s'ha pogut carregar"

#: includes/fields/class-acf-field-select.php:117
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "S'està cercant&hellip;"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "S'estan carregant més resultats&hellip;"

#: includes/fields/class-acf-field-select.php:115
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Només podeu seleccionar %d elements"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Només podeu seleccionar un element"

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Suprimiu %d caràcters"

#: includes/fields/class-acf-field-select.php:112
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Suprimiu un caràcter"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Introduïu %d o més caràcters"

#: includes/fields/class-acf-field-select.php:110
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Introduïu un o més caràcters"

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "No hi ha coincidències"

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"Hi ha %d resultats disponibles, utilitzeu les fletxes amunt i avall per "
"navegar-hi."

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Hi ha un resultat disponible, premeu retorn per seleccionar-lo."

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:777
msgctxt "noun"
msgid "Select"
msgstr "Selecció"

#: includes/fields/class-acf-field-user.php:74
msgid "User ID"
msgstr "Identificador de l'usuari"

#: includes/fields/class-acf-field-user.php:73
msgid "User Object"
msgstr "Objecte d'usuari"

#: includes/fields/class-acf-field-user.php:417
msgid "User Array"
msgstr "Matriu d’usuari"

#: includes/fields/class-acf-field-user.php:60
msgid "All user roles"
msgstr "Tots els rols d'usuari"

#: includes/fields/class-acf-field-user.php:52
msgid "Filter by role"
msgstr "Filtra per rol"

#: includes/fields/class-acf-field-user.php:20 includes/locations.php:101
msgid "User"
msgstr "Usuari"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Separador"

#: includes/fields/class-acf-field-color_picker.php:73
msgid "Select Color"
msgstr "Selecciona un color"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Default"
msgstr "Predeterminat"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Clear"
msgstr "Neteja"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Selector de color"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Selecciona"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Fet"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Ara"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Fus horari"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microsegon"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Mil·lisegon"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Segon"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minut"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Trieu l'hora"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Selector de data i hora"

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr "Punt final"

#: includes/admin/views/field-group-options.php:95
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Alineat a l'esquerra"

#: includes/admin/views/field-group-options.php:94
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Alineat a la part superior"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Ubicació"

#: includes/fields/class-acf-field-tab.php:26
msgid "Tab"
msgstr "Pestanya"

#: includes/fields/class-acf-field-url.php:159
msgid "Value must be a valid URL"
msgstr "El valor ha de ser un URL vàlid"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "URL"

#: includes/fields/class-acf-field-link.php:174
msgid "Link URL"
msgstr "URL de l'enllaç"

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "Matriu d’enllaç"

#: includes/fields/class-acf-field-link.php:142
msgid "Opens in a new window/tab"
msgstr "S'obre en una nova finestra/pestanya"

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "Escolliu l’enllaç"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Enllaç"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Correu electrònic"

#: includes/fields/class-acf-field-number.php:185
#: includes/fields/class-acf-field-range.php:214
msgid "Step Size"
msgstr "Mida del pas"

#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-range.php:192
msgid "Maximum Value"
msgstr "Valor màxim"

#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-range.php:181
msgid "Minimum Value"
msgstr "Valor mínim"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Interval"

#: includes/fields/class-acf-field-button-group.php:172
#: includes/fields/class-acf-field-checkbox.php:375
#: includes/fields/class-acf-field-radio.php:217
#: includes/fields/class-acf-field-select.php:398
msgid "Both (Array)"
msgstr "Ambdós (matriu)"

#: includes/admin/views/field-group-fields.php:41
#: includes/fields/class-acf-field-button-group.php:171
#: includes/fields/class-acf-field-checkbox.php:374
#: includes/fields/class-acf-field-radio.php:216
#: includes/fields/class-acf-field-select.php:397
msgid "Label"
msgstr "Etiqueta"

#: includes/fields/class-acf-field-button-group.php:170
#: includes/fields/class-acf-field-checkbox.php:373
#: includes/fields/class-acf-field-radio.php:215
#: includes/fields/class-acf-field-select.php:396
msgid "Value"
msgstr "Valor"

#: includes/fields/class-acf-field-button-group.php:219
#: includes/fields/class-acf-field-checkbox.php:437
#: includes/fields/class-acf-field-radio.php:289
msgid "Vertical"
msgstr "Vertical"

#: includes/fields/class-acf-field-button-group.php:218
#: includes/fields/class-acf-field-checkbox.php:438
#: includes/fields/class-acf-field-radio.php:290
msgid "Horizontal"
msgstr "Horitzontal"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "red : Red"
msgstr "vermell : Vermell"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Per a més control, podeu especificar tant un valor com una etiqueta "
"d'aquesta manera:"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "Enter each choice on a new line."
msgstr "Introduïu cada opció en una línia nova."

#: includes/fields/class-acf-field-button-group.php:144
#: includes/fields/class-acf-field-checkbox.php:347
#: includes/fields/class-acf-field-radio.php:189
#: includes/fields/class-acf-field-select.php:368
msgid "Choices"
msgstr "Opcions"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Grup de botons"

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-select.php:392
#: includes/fields/class-acf-field-user.php:403
msgid "Select multiple values?"
msgstr "Escollir múltiples valors?"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:382
#: includes/fields/class-acf-field-taxonomy.php:784
#: includes/fields/class-acf-field-user.php:393
msgid "Allow Null?"
msgstr "Permet nul?"

#: includes/fields/class-acf-field-page_link.php:249
#: includes/fields/class-acf-field-post_object.php:250
#: includes/fields/class-acf-field-taxonomy.php:932
msgid "Parent"
msgstr "Pare"

#: includes/fields/class-acf-field-wysiwyg.php:394
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "El TinyMCE no s'inicialitzarà fins que no es faci clic al camp"

#: includes/fields/class-acf-field-wysiwyg.php:429
msgid "Delay initialization?"
msgstr "Endarrereix la inicialització?"

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Show Media Upload Buttons?"
msgstr "Mostra els botons de penjar mèdia?"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Toolbar"
msgstr "Barra d'eines"

#: includes/fields/class-acf-field-wysiwyg.php:358
msgid "Text Only"
msgstr "Només Text"

#: includes/fields/class-acf-field-wysiwyg.php:357
msgid "Visual Only"
msgstr "Només visual"

#: includes/fields/class-acf-field-wysiwyg.php:356
msgid "Visual & Text"
msgstr "Visual i text"

#: includes/fields/class-acf-field-wysiwyg.php:351
msgid "Tabs"
msgstr "Pestanyes"

#: includes/fields/class-acf-field-wysiwyg.php:289
msgid "Click to initialize TinyMCE"
msgstr "Feu clic per inicialitzar el TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:283
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-wysiwyg.php:282
msgid "Visual"
msgstr "Visual"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Editor Wysiwyg"

#: includes/fields/class-acf-field-text.php:180
#: includes/fields/class-acf-field-textarea.php:233
msgid "Value must not exceed %d characters"
msgstr "El valor no ha de superar els %d caràcters"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Deixeu-lo en blanc per no establir cap límit"

#: includes/fields/class-acf-field-text.php:114
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Límit de caràcters"

#: includes/fields/class-acf-field-email.php:155
#: includes/fields/class-acf-field-number.php:206
#: includes/fields/class-acf-field-password.php:102
#: includes/fields/class-acf-field-range.php:236
#: includes/fields/class-acf-field-text.php:155
msgid "Appears after the input"
msgstr "Apareix després del camp"

#: includes/fields/class-acf-field-email.php:154
#: includes/fields/class-acf-field-number.php:205
#: includes/fields/class-acf-field-password.php:101
#: includes/fields/class-acf-field-range.php:235
#: includes/fields/class-acf-field-text.php:154
msgid "Append"
msgstr "Afegeix al final"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:196
#: includes/fields/class-acf-field-password.php:92
#: includes/fields/class-acf-field-range.php:226
#: includes/fields/class-acf-field-text.php:145
msgid "Appears before the input"
msgstr "Apareix abans del camp"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:195
#: includes/fields/class-acf-field-password.php:91
#: includes/fields/class-acf-field-range.php:225
#: includes/fields/class-acf-field-text.php:144
msgid "Prepend"
msgstr "Afegeix al principi"

#: includes/fields/class-acf-field-email.php:135
#: includes/fields/class-acf-field-number.php:176
#: includes/fields/class-acf-field-password.php:82
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:153
#: includes/fields/class-acf-field-url.php:119
msgid "Appears within the input"
msgstr "Apareix a dins del camp"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:175
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-text.php:134
#: includes/fields/class-acf-field-textarea.php:152
#: includes/fields/class-acf-field-url.php:118
msgid "Placeholder Text"
msgstr "Text a l’espai reservat"

#: includes/fields/class-acf-field-button-group.php:155
#: includes/fields/class-acf-field-email.php:115
#: includes/fields/class-acf-field-number.php:126
#: includes/fields/class-acf-field-radio.php:200
#: includes/fields/class-acf-field-range.php:162
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:101
#: includes/fields/class-acf-field-url.php:99
#: includes/fields/class-acf-field-wysiwyg.php:316
msgid "Appears when creating a new post"
msgstr "Apareix quan es crea una nova entrada"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-relationship.php:760
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s requereix com a mínim %2$s selecció"
msgstr[1] "%1$s requereix com a mínim %2$s seleccions"

#: includes/fields/class-acf-field-post_object.php:395
#: includes/fields/class-acf-field-relationship.php:622
msgid "Post ID"
msgstr "ID de l'entrada"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:394
#: includes/fields/class-acf-field-relationship.php:621
msgid "Post Object"
msgstr "Objecte de l'entrada"

#: includes/fields/class-acf-field-relationship.php:654
msgid "Maximum posts"
msgstr "Màxim d'entrades"

#: includes/fields/class-acf-field-relationship.php:644
msgid "Minimum posts"
msgstr "Mínim d'entrades"

#: includes/admin/views/field-group-options.php:148
#: includes/fields/class-acf-field-relationship.php:679
msgid "Featured Image"
msgstr "Imatge destacada"

#: includes/fields/class-acf-field-relationship.php:675
msgid "Selected elements will be displayed in each result"
msgstr "Els elements seleccionats es mostraran a cada resultat"

#: includes/fields/class-acf-field-relationship.php:674
msgid "Elements"
msgstr "Elements"

#: includes/fields/class-acf-field-relationship.php:608
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:706
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taxonomia"

#: includes/fields/class-acf-field-relationship.php:607
#: includes/locations/class-acf-location-post-type.php:22
msgid "Post Type"
msgstr "Tipus de contingut"

#: includes/fields/class-acf-field-relationship.php:601
msgid "Filters"
msgstr "Filtres"

#: includes/fields/class-acf-field-page_link.php:470
#: includes/fields/class-acf-field-post_object.php:382
#: includes/fields/class-acf-field-relationship.php:594
msgid "All taxonomies"
msgstr "Totes les taxonomies"

#: includes/fields/class-acf-field-page_link.php:462
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:586
msgid "Filter by Taxonomy"
msgstr "Filtra per taxonomia"

#: includes/fields/class-acf-field-page_link.php:455
#: includes/fields/class-acf-field-post_object.php:367
#: includes/fields/class-acf-field-relationship.php:579
msgid "All post types"
msgstr "Tots els tipus de contingut"

#: includes/fields/class-acf-field-page_link.php:447
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:571
msgid "Filter by Post Type"
msgstr "Filtra per tipus de contingut"

#: includes/fields/class-acf-field-relationship.php:469
msgid "Search..."
msgstr "Cerca…"

#: includes/fields/class-acf-field-relationship.php:399
msgid "Select taxonomy"
msgstr "Seleccioneu una taxonomia"

#: includes/fields/class-acf-field-relationship.php:390
msgid "Select post type"
msgstr "Seleccioneu el tipus de contingut"

#: includes/fields/class-acf-field-relationship.php:65
#: assets/build/js/acf-input.js:3917 assets/build/js/acf-input.js:4195
msgid "No matches found"
msgstr "No s'ha trobat cap coincidència"

#: includes/fields/class-acf-field-relationship.php:64
#: assets/build/js/acf-input.js:3900 assets/build/js/acf-input.js:4174
msgid "Loading"
msgstr "S'està carregant"

#: includes/fields/class-acf-field-relationship.php:63
#: assets/build/js/acf-input.js:3809 assets/build/js/acf-input.js:4070
msgid "Maximum values reached ( {max} values )"
msgstr "S'han assolit els valors màxims ({max} valors)"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Relació"

#: includes/fields/class-acf-field-file.php:288
#: includes/fields/class-acf-field-image.php:314
msgid "Comma separated list. Leave blank for all types"
msgstr "Llista separada per comes. Deixeu-ho en blanc per a tots els tipus"

#: includes/fields/class-acf-field-file.php:287
#: includes/fields/class-acf-field-image.php:313
msgid "Allowed file types"
msgstr "Tipus de fitxers permesos"

#: includes/fields/class-acf-field-file.php:275
#: includes/fields/class-acf-field-image.php:277
msgid "Maximum"
msgstr "Màxim"

#: includes/fields/class-acf-field-file.php:151
#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-file.php:279
#: includes/fields/class-acf-field-image.php:268
#: includes/fields/class-acf-field-image.php:304
msgid "File size"
msgstr "Mida del fitxer"

#: includes/fields/class-acf-field-image.php:242
#: includes/fields/class-acf-field-image.php:278
msgid "Restrict which images can be uploaded"
msgstr "Restringeix quines imatges es poden penjar"

#: includes/fields/class-acf-field-file.php:263
#: includes/fields/class-acf-field-image.php:241
msgid "Minimum"
msgstr "Mínim"

#: includes/fields/class-acf-field-file.php:232
#: includes/fields/class-acf-field-image.php:207
msgid "Uploaded to post"
msgstr "S'ha penjat a l'entrada"

#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:206
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Tots"

#: includes/fields/class-acf-field-file.php:226
#: includes/fields/class-acf-field-image.php:201
msgid "Limit the media library choice"
msgstr "Limita l'elecció d'elements de la mediateca"

#: includes/fields/class-acf-field-file.php:225
#: includes/fields/class-acf-field-image.php:200
msgid "Library"
msgstr "Mediateca"

#: includes/fields/class-acf-field-image.php:333
msgid "Preview Size"
msgstr "Mida de la vista prèvia"

#: includes/fields/class-acf-field-image.php:192
msgid "Image ID"
msgstr "ID de la imatge"

#: includes/fields/class-acf-field-image.php:191
msgid "Image URL"
msgstr "URL de la imatge"

#: includes/fields/class-acf-field-image.php:190
msgid "Image Array"
msgstr "Matriu d'imatges"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:368
#: includes/fields/class-acf-field-file.php:210
#: includes/fields/class-acf-field-link.php:168
#: includes/fields/class-acf-field-radio.php:210
msgid "Specify the returned value on front end"
msgstr "Especifiqueu el valor retornat al davant"

#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:367
#: includes/fields/class-acf-field-file.php:209
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-taxonomy.php:750
msgid "Return Value"
msgstr "Valor de retorn"

#: includes/fields/class-acf-field-image.php:159
msgid "Add Image"
msgstr "Afegeix imatge"

#: includes/fields/class-acf-field-image.php:159
msgid "No image selected"
msgstr "No s'ha seleccionat cap imatge"

#: includes/assets.php:351 includes/fields/class-acf-field-file.php:159
#: includes/fields/class-acf-field-image.php:139
#: includes/fields/class-acf-field-link.php:142 assets/build/js/acf.js:1523
#: assets/build/js/acf.js:1603
msgid "Remove"
msgstr "Suprimeix"

#: includes/admin/views/field-group-field.php:65
#: includes/fields/class-acf-field-file.php:157
#: includes/fields/class-acf-field-image.php:137
#: includes/fields/class-acf-field-link.php:142
msgid "Edit"
msgstr "Edita"

#: includes/fields/class-acf-field-image.php:67 includes/media.php:55
#: assets/build/js/acf-input.js:6765 assets/build/js/acf-input.js:7228
msgid "All images"
msgstr "Totes les imatges"

#: includes/fields/class-acf-field-image.php:66
#: assets/build/js/acf-input.js:3173 assets/build/js/acf-input.js:3386
msgid "Update Image"
msgstr "Actualitza la imatge"

#: includes/fields/class-acf-field-image.php:65
#: assets/build/js/acf-input.js:3172 assets/build/js/acf-input.js:3385
msgid "Edit Image"
msgstr "Edita la imatge"

#: includes/fields/class-acf-field-image.php:64
#: assets/build/js/acf-input.js:3148 assets/build/js/acf-input.js:3360
msgid "Select Image"
msgstr "Seleccioneu una imatge"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Imatge"

#: includes/fields/class-acf-field-message.php:123
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Permet que el marcatge HTML es mostri com a text visible en lloc de "
"renderitzar"

#: includes/fields/class-acf-field-message.php:122
msgid "Escape HTML"
msgstr "Escapa l’HTML"

#: includes/fields/class-acf-field-message.php:114
#: includes/fields/class-acf-field-textarea.php:169
msgid "No Formatting"
msgstr "Sense format"

#: includes/fields/class-acf-field-message.php:113
#: includes/fields/class-acf-field-textarea.php:168
msgid "Automatically add &lt;br&gt;"
msgstr "Afegeix &lt;br&gt; automàticament"

#: includes/fields/class-acf-field-message.php:112
#: includes/fields/class-acf-field-textarea.php:167
msgid "Automatically add paragraphs"
msgstr "Afegeix paràgrafs automàticament"

#: includes/fields/class-acf-field-message.php:108
#: includes/fields/class-acf-field-textarea.php:163
msgid "Controls how new lines are rendered"
msgstr "Controla com es renderitzaran les línies noves"

#: includes/fields/class-acf-field-message.php:107
#: includes/fields/class-acf-field-textarea.php:162
msgid "New Lines"
msgstr "Noves línies"

#: includes/fields/class-acf-field-date_picker.php:229
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "La setmana comença el"

#: includes/fields/class-acf-field-date_picker.php:198
msgid "The format used when saving a value"
msgstr "El format utilitzat en desar un valor"

#: includes/fields/class-acf-field-date_picker.php:197
msgid "Save Format"
msgstr "Format de desat"

#: includes/fields/class-acf-field-date_picker.php:64
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Stm"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Anterior"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Següent"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Avui"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Fet"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Selector de data"

#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:281
#: includes/fields/class-acf-field-oembed.php:265
msgid "Width"
msgstr "Amplada"

#: includes/fields/class-acf-field-oembed.php:262
#: includes/fields/class-acf-field-oembed.php:274
msgid "Embed Size"
msgstr "Mida de la incrustació"

#: includes/fields/class-acf-field-oembed.php:219
msgid "Enter URL"
msgstr "Introduïu la URL"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:181
msgid "Text shown when inactive"
msgstr "Text que es mostra quan està inactiu"

#: includes/fields/class-acf-field-true_false.php:180
msgid "Off Text"
msgstr "Text desactivat"

#: includes/fields/class-acf-field-true_false.php:165
msgid "Text shown when active"
msgstr "Text que es mostra quan està actiu"

#: includes/fields/class-acf-field-true_false.php:164
msgid "On Text"
msgstr "Text activat"

#: includes/fields/class-acf-field-select.php:449
#: includes/fields/class-acf-field-true_false.php:196
msgid "Stylized UI"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:154
#: includes/fields/class-acf-field-checkbox.php:357
#: includes/fields/class-acf-field-color_picker.php:155
#: includes/fields/class-acf-field-email.php:114
#: includes/fields/class-acf-field-number.php:125
#: includes/fields/class-acf-field-radio.php:199
#: includes/fields/class-acf-field-range.php:161
#: includes/fields/class-acf-field-select.php:379
#: includes/fields/class-acf-field-text.php:94
#: includes/fields/class-acf-field-textarea.php:100
#: includes/fields/class-acf-field-true_false.php:144
#: includes/fields/class-acf-field-url.php:98
#: includes/fields/class-acf-field-wysiwyg.php:315
msgid "Default Value"
msgstr "Valor per defecte"

#: includes/fields/class-acf-field-true_false.php:135
msgid "Displays text alongside the checkbox"
msgstr "Mostra el text al costat de la casella de selecció"

#: includes/fields/class-acf-field-message.php:26
#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-true_false.php:134
msgid "Message"
msgstr "Missatge"

#: includes/assets.php:350 includes/fields/class-acf-field-true_false.php:83
#: includes/fields/class-acf-field-true_false.php:184
#: assets/build/js/acf.js:1700 assets/build/js/acf.js:1803
msgid "No"
msgstr "No"

#: includes/assets.php:349 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:168
#: assets/build/js/acf.js:1699 assets/build/js/acf.js:1802
msgid "Yes"
msgstr "Sí"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Cert / Fals"

#: includes/fields/class-acf-field-group.php:471
msgid "Row"
msgstr "Fila"

#: includes/fields/class-acf-field-group.php:470
msgid "Table"
msgstr "Taula"

#: includes/fields/class-acf-field-group.php:469
msgid "Block"
msgstr "Bloc"

#: includes/fields/class-acf-field-group.php:464
msgid "Specify the style used to render the selected fields"
msgstr "Especifiqueu l'estil utilitzat per renderitzar els camps seleccionats"

#: includes/fields.php:359 includes/fields/class-acf-field-button-group.php:212
#: includes/fields/class-acf-field-checkbox.php:431
#: includes/fields/class-acf-field-group.php:463
#: includes/fields/class-acf-field-radio.php:283
msgid "Layout"
msgstr "Format"

#: includes/fields/class-acf-field-group.php:447
msgid "Sub Fields"
msgstr "Sub camps"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Grup"

#: includes/fields/class-acf-field-google-map.php:232
msgid "Customize the map height"
msgstr "Personalitzeu l'alçada del mapa"

#: includes/fields/class-acf-field-google-map.php:231
#: includes/fields/class-acf-field-image.php:256
#: includes/fields/class-acf-field-image.php:292
#: includes/fields/class-acf-field-oembed.php:277
msgid "Height"
msgstr "Alçada"

#: includes/fields/class-acf-field-google-map.php:220
msgid "Set the initial zoom level"
msgstr "Estableix el nivell inicial de zoom"

#: includes/fields/class-acf-field-google-map.php:219
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:193
#: includes/fields/class-acf-field-google-map.php:206
msgid "Center the initial map"
msgstr "Centra el mapa inicial"

#: includes/fields/class-acf-field-google-map.php:192
#: includes/fields/class-acf-field-google-map.php:205
msgid "Center"
msgstr "Centra"

#: includes/fields/class-acf-field-google-map.php:160
msgid "Search for address..."
msgstr "Cerca l'adreça…"

#: includes/fields/class-acf-field-google-map.php:157
msgid "Find current location"
msgstr "Cerca la ubicació actual"

#: includes/fields/class-acf-field-google-map.php:156
msgid "Clear location"
msgstr "Neteja la ubicació"

#: includes/fields/class-acf-field-google-map.php:155
#: includes/fields/class-acf-field-relationship.php:606
msgid "Search"
msgstr "Cerca"

#: includes/fields/class-acf-field-google-map.php:60
#: assets/build/js/acf-input.js:2832 assets/build/js/acf-input.js:3013
msgid "Sorry, this browser does not support geolocation"
msgstr "Aquest navegador no és compatible amb la geolocalització"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Mapa de Google"

#: includes/fields/class-acf-field-date_picker.php:209
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:129
msgid "The format returned via template functions"
msgstr "El format retornat mitjançant funcions de plantilla"

#: includes/fields/class-acf-field-color_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:197
#: includes/fields/class-acf-field-image.php:184
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:616
#: includes/fields/class-acf-field-select.php:390
#: includes/fields/class-acf-field-time_picker.php:128
#: includes/fields/class-acf-field-user.php:67
msgid "Return Format"
msgstr "Format de retorn"

#: includes/fields/class-acf-field-date_picker.php:187
#: includes/fields/class-acf-field-date_picker.php:218
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-date_time_picker.php:207
#: includes/fields/class-acf-field-time_picker.php:120
#: includes/fields/class-acf-field-time_picker.php:136
msgid "Custom:"
msgstr "Personalitzat:"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-time_picker.php:113
msgid "The format displayed when editing a post"
msgstr "El format que es mostra en editar una publicació"

#: includes/fields/class-acf-field-date_picker.php:178
#: includes/fields/class-acf-field-date_time_picker.php:179
#: includes/fields/class-acf-field-time_picker.php:112
msgid "Display Format"
msgstr "Format de visualització"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Selector d'hora"

#. translators: counts for inactive field groups
#: acf.php:454
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inactiu <span class=\"count\">(%s)</span>"
msgstr[1] "Inactius <span class=\"count\">(%s)</span>"

#: acf.php:413
msgid "No Fields found in Trash"
msgstr "No s'ha trobat cap camp a la paperera"

#: acf.php:412
msgid "No Fields found"
msgstr "No s'ha trobat cap camp"

#: acf.php:411
msgid "Search Fields"
msgstr "Cerca camps"

#: acf.php:410
msgid "View Field"
msgstr "Visualitza el camp"

#: acf.php:409 includes/admin/views/field-group-fields.php:104
msgid "New Field"
msgstr "Nou camp"

#: acf.php:408
msgid "Edit Field"
msgstr "Edita el camp"

#: acf.php:407
msgid "Add New Field"
msgstr "Afegeix un nou camp"

#: acf.php:405
msgid "Field"
msgstr "Camp"

#: acf.php:404 includes/admin/admin-field-group.php:220
#: includes/admin/admin-field-groups.php:287
#: includes/admin/views/field-group-fields.php:21
msgid "Fields"
msgstr "Camps"

#: acf.php:379
msgid "No Field Groups found in Trash"
msgstr "No s'ha trobat cap grup de camps a la paperera"

#: acf.php:378
msgid "No Field Groups found"
msgstr "No s'ha trobat cap grup de camps"

#: acf.php:377
msgid "Search Field Groups"
msgstr "Cerca grups de camps"

#: acf.php:376
msgid "View Field Group"
msgstr "Visualitza el grup de camps"

#: acf.php:375
msgid "New Field Group"
msgstr "Nou grup de camps"

#: acf.php:374
msgid "Edit Field Group"
msgstr "Edita el grup de camps"

#: acf.php:373
msgid "Add New Field Group"
msgstr "Afegeix un nou grup de camps"

#: acf.php:372 acf.php:406 includes/admin/admin.php:51
msgid "Add New"
msgstr "Afegeix nou"

#: acf.php:371
msgid "Field Group"
msgstr "Grup de camps"

#: acf.php:370 includes/admin/admin.php:50
msgid "Field Groups"
msgstr "Grups de camps"

#. Description of the plugin
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Personalitzeu el WordPress amb camps potents, professionals i intuïtius."

#. Plugin URI of the plugin
#. Author URI of the plugin
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php:91
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:417 includes/admin/admin-field-group.php:402
#: includes/admin/admin-field-groups.php:587
msgid "Inactive"
msgstr "Inactiu"

#: includes/admin/admin-field-group.php:174
msgid "Move to trash. Are you sure?"
msgstr "Segur que ho voleu moure a la paperera?"

#: includes/admin/admin-field-group.php:768
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "El camp %s es pot trobar ara al grup de camps %s"

#: includes/admin/admin-field-group.php:769
msgid "Close Window"
msgstr "Tanca la finestra"

#: includes/admin/admin-field-groups.php:529
msgid "Status"
msgstr "Estat"

#: includes/admin/admin-field-groups.php:628
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Registre de canvis"

#: includes/admin/admin-field-groups.php:633
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Mira què hi ha de nou a la <a href=“%s”>versió %s</a>."

#: includes/admin/admin-field-groups.php:636
msgid "Resources"
msgstr "Recursos"

#: includes/admin/admin-field-groups.php:638
msgid "Website"
msgstr "Lloc web"

#: includes/admin/admin-field-groups.php:639
msgid "Documentation"
msgstr "Documentació"

#: includes/admin/admin-field-groups.php:640
msgid "Support"
msgstr "Suport"

#: includes/admin/admin-field-groups.php:642
#: includes/admin/views/settings-info.php:81
msgid "Pro"
msgstr "Pro"

#: includes/admin/admin-field-groups.php:647
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Gràcies per crear amb  <a href=“%s”>ACF</a>."

#: includes/admin/admin-field-groups.php:786
msgid "Synchronise field group"
msgstr "Sincronitza el grup de camps"

#: includes/admin/admin-field-groups.php:798
msgid "Apply"
msgstr "Aplica"

#: includes/admin/admin-field-groups.php:816
msgid "Bulk Actions"
msgstr "Accions massives"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Informació"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Novetats"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Escolliu els grups de camps que voleu exportar i després escolliu el mètode "
"d’exportació. Useu el botó de descàrrega per a exportar-ho a un fitxer .json "
"que després podreu importar a una altra instal·lació d’ACF. Useu el botó de "
"generació per a exportar codi PHP que podreu usar al vostre tema."

#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr "Exporta el fitxer"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Escolliu el fitxer JSON de l’Advanced Custom Fields que voleu importar. En "
"fer clic al botó d’importació, l’ACF importarà els grups de camps."

#: includes/admin/tools/class-acf-admin-tool-import.php:62
msgid "Import File"
msgstr "Importa el fitxer"

#: includes/admin/views/field-group-field.php:106
msgid "Required?"
msgstr "Obligatori?"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"No hi ha camps. Feu clic al botó <strong>+ Afegeix un camp</strong> per a "
"crear el vostre primer camp."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Afegeix un camp"

#: includes/admin/views/html-admin-page-upgrade-network.php:74
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Cal actualitzar la base de dades del lloc de %s a %s"

#: includes/admin/views/html-notice-upgrade.php:22
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Gràcies per actualitzar a %s v%s!"

#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Complements"

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Descarrega i instal·la"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Instal·lats"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Benvingut/da a Advanced Custom Fields"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Gràcies per actualitzar! L’ACF %s és més gran i millor que mai. Esperem que "
"us agradi."

#: includes/admin/views/settings-info.php:15
msgid "A Smoother Experience"
msgstr "Una millor experiència"

#: includes/admin/views/settings-info.php:18
msgid "Improved Usability"
msgstr "Usabilitat millorada"

#: includes/admin/views/settings-info.php:19
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"En incloure la popular llibreria Select2 hem millorat tant la usabilitat com "
"la velocitat en un munt de tipus de camps, incloent objecte post, enllaç de "
"pàgina, taxonomia i selecció."

#: includes/admin/views/settings-info.php:22
msgid "Improved Design"
msgstr "Disseny millorat"

#: includes/admin/views/settings-info.php:23
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Hem actualitzat l’aspecte de molts camps perquè l’ACF llueixi més que mai! "
"Es poden veure canvis a les galeries, relacions, i al nou camp d’oEmbed!"

#: includes/admin/views/settings-info.php:26
msgid "Improved Data"
msgstr "Dades millorades"

#: includes/admin/views/settings-info.php:27
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"El redisseny de l’arquitectura de dades ha permès que els subcamps siguin "
"independents dels seus pares. Això permet arrossegar camps des de i cap a "
"camps pares!"

#: includes/admin/views/settings-info.php:35
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Adeu, complements. Hola, PRO"

#: includes/admin/views/settings-info.php:38
msgid "Introducing ACF PRO"
msgstr "Presentem l’ACF PRO"

#: includes/admin/views/settings-info.php:39
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr "Estem canviant la manera en què presentem les funcionalitats prèmium!"

#: includes/admin/views/settings-info.php:40
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Els quatre complements prèmium s’han combinat a la nova <a href=\"%s"
"\">versió PRO de l’ACF</a>. Amb llicències personals i per a desenvolupadors "
"disponibles, les funcionalitats prèmium són més assequibles i accessibles "
"que mai!"

#: includes/admin/views/settings-info.php:44
msgid "Powerful Features"
msgstr "Característiques potents"

#: includes/admin/views/settings-info.php:45
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"L’ACF PRO conté característiques potents com ara camps repetibles, "
"disposicions amb contingut flexible, un bonic camp de galeria i la "
"possibilitat de crear noves pàgines d’opcions a l’administració!"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr ""
"Més informació sobre <a href=\"%s\">les característiques de l’ACF PRO</a>."

#: includes/admin/views/settings-info.php:50
msgid "Easy Upgrading"
msgstr "Fàcil actualització"

#: includes/admin/views/settings-info.php:51
msgid ""
"Upgrading to ACF PRO is easy. Simply purchase a license online and download "
"the plugin!"
msgstr ""
"L’actualització a l’ACF PRO és senzilla. Només cal que compreu una llicència "
"en línia i descarregueu l’extensió!"

#: includes/admin/views/settings-info.php:52
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>."
msgstr ""
"També hem escrit una <a href=\"%s\">guia d’actualització</a> per a respondre "
"qualsevol pregunta, però si en teniu cap, contacteu amb el nostre equip de "
"suport al <a href=\"%s\">tauler d’ajuda</a>."

#: includes/admin/views/settings-info.php:61
msgid "New Features"
msgstr "Noves característiques"

#: includes/admin/views/settings-info.php:66
msgid "Link Field"
msgstr "Camp d'enllaç"

#: includes/admin/views/settings-info.php:67
msgid ""
"The Link field provides a simple way to select or define a link (url, title, "
"target)."
msgstr ""
"El camp d’enllaç ofereix una manera senzilla d’escollir o definir un enllaç "
"(url, títol, destí)."

#: includes/admin/views/settings-info.php:71
msgid "Group Field"
msgstr "Camp de grup"

#: includes/admin/views/settings-info.php:72
msgid "The Group field provides a simple way to create a group of fields."
msgstr "El camp de grup facilita la creació d’un grup de camps."

#: includes/admin/views/settings-info.php:76
msgid "oEmbed Field"
msgstr "Camp d’oEmbed"

#: includes/admin/views/settings-info.php:77
msgid ""
"The oEmbed field allows an easy way to embed videos, images, tweets, audio, "
"and other content."
msgstr ""
"El camp d’oEmbed permet incrustar fàcilment vídeos, imatges, tuits, àudio i "
"altres continguts."

#: includes/admin/views/settings-info.php:82
msgid "The clone field allows you to select and display existing fields."
msgstr "El camp de clon permet escollir i mostrar camps existents."

#: includes/admin/views/settings-info.php:86
msgid "More AJAX"
msgstr "Més AJAX"

#: includes/admin/views/settings-info.php:87
msgid "More fields use AJAX powered search to speed up page loading."
msgstr ""
"Més camps usen una cerca que funciona amb AJAX per a accelerar la càrrega de "
"la pàgina."

#: includes/admin/views/settings-info.php:92
msgid ""
"New auto export to JSON feature improves speed and allows for syncronisation."
msgstr ""
"La nova funció d’auto exportació a JSON millora la velocitat i permet la "
"sincronització."

#: includes/admin/views/settings-info.php:96
msgid "Easy Import / Export"
msgstr "Importació i exportació senzilla"

#: includes/admin/views/settings-info.php:97
msgid "Both import and export can easily be done through a new tools page."
msgstr ""
"Tant la importació com l’exportació es poden realitzar fàcilment des de la "
"nova pàgina d’eines."

#: includes/admin/views/settings-info.php:101
msgid "New Form Locations"
msgstr "Noves ubicacions per als formularis"

#: includes/admin/views/settings-info.php:102
msgid ""
"Fields can now be mapped to menus, menu items, comments, widgets and all "
"user forms!"
msgstr ""
"Els camps es poden assignar a menús, elements del menú, comentaris, ginys i "
"formularis d’usuari!"

#: includes/admin/views/settings-info.php:106
msgid "More Customization"
msgstr "Més personalització"

#: includes/admin/views/settings-info.php:107
msgid ""
"New PHP (and JS) actions and filters have been added to allow for more "
"customization."
msgstr ""
"S’han afegit nous filtres i accions de PHP (i JS) per a permetre més "
"personalització."

#: includes/admin/views/settings-info.php:111
msgid "Fresh UI"
msgstr "Interfície estilitzada"

#: includes/admin/views/settings-info.php:112
msgid ""
"The entire plugin has had a design refresh including new field types, "
"settings and design!"
msgstr ""
"S’ha redissenyat tota l’extensió, incloent nous tipus de camps, opcions i "
"disseny!"

#: includes/admin/views/settings-info.php:116
msgid "New Settings"
msgstr "Noves opcions"

#: includes/admin/views/settings-info.php:117
msgid ""
"Field group settings have been added for Active, Label Placement, "
"Instructions Placement and Description."
msgstr ""
"S’han afegit les següents opcions als grups de camps: actiu, posició de "
"l’etiqueta, posició de les instruccions, i descripció."

#: includes/admin/views/settings-info.php:121
msgid "Better Front End Forms"
msgstr "Millors formularis a la interfície frontal"

#: includes/admin/views/settings-info.php:122
msgid ""
"acf_form() can now create a new post on submission with lots of new settings."
msgstr ""
"acf_form() ara pot crear una nova entrada en ser enviat amb un munt de noves "
"opcions."

#: includes/admin/views/settings-info.php:126
msgid "Better Validation"
msgstr "Validació millorada"

#: includes/admin/views/settings-info.php:127
msgid "Form validation is now done via PHP + AJAX in favour of only JS."
msgstr ""
"La validació del formulari ara es fa amb PHP + AJAX en lloc de només JS."

#: includes/admin/views/settings-info.php:131
msgid "Moving Fields"
msgstr "Moure els camps"

#: includes/admin/views/settings-info.php:132
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents."
msgstr ""
"Una nova funcionalitat als grups de camps permet moure un camp entre grups i "
"pares."

#: includes/admin/views/settings-info.php:143
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Creiem que us encantaran els canvis a %s."

#: includes/api/api-helpers.php:3619
#, php-format
msgid "File size must must not exceed %s."
msgstr "La mida del fitxer no pot ser superior a %s."

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "Permet personalitzats"

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "Desa personalitzats"

#: includes/fields/class-acf-field-checkbox.php:403
msgid "Toggle"
msgstr "Commuta"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Color actual"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Altres"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "Desa Altres"

#: includes/fields/class-acf-field-relationship.php:727
#: pro/fields/class-acf-field-gallery.php:779
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s necessita almenys %s selecció"
msgstr[1] "%s necessita almenys %s seleccions"

#: includes/fields/class-acf-field-select.php:402
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "Interfície estilitzada"

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""
"Definiu un punt de final per a aturar les pestanyes anteriors. Això generarà "
"un nou grup de pestanyes."

#: includes/fields/class-acf-field-taxonomy.php:714
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "No hi ha %s"

#: includes/forms/form-user.php:336
#, php-format
msgid "<strong>ERROR</strong>: %s"
msgstr "<strong>ERROR</strong>: %s"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:198
msgid "Publish"
msgstr "Publica"

#: pro/admin/admin-options-page.php:204
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"No s’han trobat grups de camps personalitzats per a aquesta pàgina "
"d’opcions. <a href=\"%s\">Creeu un grup de camps nou</a>"

#: pro/admin/admin-updates.php:49
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Error</b>. No s’ha pogut connectar al servidor d’actualitzacions"

#: pro/admin/admin-updates.php:191
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Error</b>. No s’ha pogut verificar el paquet d’actualització. Torneu-ho a "
"intentar o desactiveu i torneu a activar la vostra llicència de l’ACF PRO."

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Desactiva la llicència"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Activa la llicència"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Informació de la llicència"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Per a desbloquejar les actualitzacions, introduïu la clau de llicència a "
"continuació. Si no teniu cap clau de llicència, vegeu els <a href=\"%s"
"\">detalls i preu</a>."

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Clau de llicència"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Informació de l'actualització"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Versió actual"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Darrera versió"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Actualització disponible"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Actualitza l’extensió"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr ""
"Introduïu la clau de llicència al damunt per a desbloquejar les "
"actualitzacions"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Torneu-ho a comprovar"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "Avís d’actualització"

#: pro/blocks.php:371
msgid "Switch to Edit"
msgstr "Canvia a edició"

#: pro/blocks.php:372
msgid "Switch to Preview"
msgstr "Canvia a previsualització"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Clon"

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr "Escolliu un o més camps a clonar"

#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr "Mostra"

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr "Indiqueu l’estil que s’usarà per a mostrar el camp clonat"

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr "Grup (mostra els camps escollits en un grup dins d’aquest camp)"

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr "Fluid (reemplaça aquest camp amb els camps escollits)"

#: pro/fields/class-acf-field-clone.php:857
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Les etiquetes es mostraran com a %s"

#: pro/fields/class-acf-field-clone.php:860
msgid "Prefix Field Labels"
msgstr "Prefixa les etiquetes dels camps"

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr "Els valors es desaran com a %s"

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Names"
msgstr "Prefixa els noms dels camps"

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr "Camp desconegut"

#: pro/fields/class-acf-field-clone.php:1031
msgid "Unknown field group"
msgstr "Grup de camps desconegut"

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr "Tots els camps del grup de camps %s"

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:193
#: pro/fields/class-acf-field-repeater.php:468
msgid "Add Row"
msgstr "Afegeix una fila"

#: pro/fields/class-acf-field-flexible-content.php:73
#: pro/fields/class-acf-field-flexible-content.php:924
#: pro/fields/class-acf-field-flexible-content.php:1006
msgid "layout"
msgid_plural "layouts"
msgstr[0] "disposició"
msgstr[1] "disposicions"

#: pro/fields/class-acf-field-flexible-content.php:74
msgid "layouts"
msgstr "disposicions"

#: pro/fields/class-acf-field-flexible-content.php:77
#: pro/fields/class-acf-field-flexible-content.php:923
#: pro/fields/class-acf-field-flexible-content.php:1005
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Aquest camp requereix almenys {min} {label} de {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:78
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Aquest camp té un límit de {max} {label} de {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:81
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} de {identifier} disponible (màx {max})"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} de {identifier} necessari (mín {min})"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "Flexible Content requires at least 1 layout"
msgstr "El contingut flexible necessita almenys una disposició"

#: pro/fields/class-acf-field-flexible-content.php:287
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Feu clic al botó “%s” de sota per a començar a crear el vostre disseny"

#: pro/fields/class-acf-field-flexible-content.php:413
msgid "Add layout"
msgstr "Afegeix una disposició"

#: pro/fields/class-acf-field-flexible-content.php:414
msgid "Remove layout"
msgstr "Esborra la disposició"

#: pro/fields/class-acf-field-flexible-content.php:415
#: pro/fields/class-acf-field-repeater.php:301
msgid "Click to toggle"
msgstr "Feu clic per alternar"

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Reorder Layout"
msgstr "Reordena la disposició"

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Reorder"
msgstr "Reordena"

#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Delete Layout"
msgstr "Esborra la disposició"

#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Duplicate Layout"
msgstr "Duplica la disposició"

#: pro/fields/class-acf-field-flexible-content.php:558
msgid "Add New Layout"
msgstr "Afegeix una disposició"

#: pro/fields/class-acf-field-flexible-content.php:629
msgid "Min"
msgstr "Mín"

#: pro/fields/class-acf-field-flexible-content.php:642
msgid "Max"
msgstr "Màx"

#: pro/fields/class-acf-field-flexible-content.php:669
#: pro/fields/class-acf-field-repeater.php:464
msgid "Button Label"
msgstr "Etiqueta del botó"

#: pro/fields/class-acf-field-flexible-content.php:678
msgid "Minimum Layouts"
msgstr "Mínim de disposicions"

#: pro/fields/class-acf-field-flexible-content.php:687
msgid "Maximum Layouts"
msgstr "Màxim de disposicions"

#: pro/fields/class-acf-field-gallery.php:73
msgid "Add Image to Gallery"
msgstr "Afegeix una imatge a la galeria"

#: pro/fields/class-acf-field-gallery.php:74
msgid "Maximum selection reached"
msgstr "S’ha arribat al màxim d’elements seleccionats"

#: pro/fields/class-acf-field-gallery.php:322
msgid "Length"
msgstr "Llargada"

#: pro/fields/class-acf-field-gallery.php:362
msgid "Caption"
msgstr "Llegenda"

#: pro/fields/class-acf-field-gallery.php:371
msgid "Alt Text"
msgstr "Text alternatiu"

#: pro/fields/class-acf-field-gallery.php:487
msgid "Add to gallery"
msgstr "Afegeix a la galeria"

#: pro/fields/class-acf-field-gallery.php:491
msgid "Bulk actions"
msgstr "Accions massives"

#: pro/fields/class-acf-field-gallery.php:492
msgid "Sort by date uploaded"
msgstr "Ordena per la data de càrrega"

#: pro/fields/class-acf-field-gallery.php:493
msgid "Sort by date modified"
msgstr "Ordena per la data de modificació"

#: pro/fields/class-acf-field-gallery.php:494
msgid "Sort by title"
msgstr "Ordena pel títol"

#: pro/fields/class-acf-field-gallery.php:495
msgid "Reverse current order"
msgstr "Inverteix l’ordre actual"

#: pro/fields/class-acf-field-gallery.php:507
msgid "Close"
msgstr "Tanca"

#: pro/fields/class-acf-field-gallery.php:580
msgid "Insert"
msgstr "Insereix"

#: pro/fields/class-acf-field-gallery.php:581
msgid "Specify where new attachments are added"
msgstr "Especifiqueu on s’afegeixen els nous fitxers adjunts"

#: pro/fields/class-acf-field-gallery.php:585
msgid "Append to the end"
msgstr "Afegeix-los al final"

#: pro/fields/class-acf-field-gallery.php:586
msgid "Prepend to the beginning"
msgstr "Afegeix-los al principi"

#: pro/fields/class-acf-field-gallery.php:605
msgid "Minimum Selection"
msgstr "Selecció mínima"

#: pro/fields/class-acf-field-gallery.php:613
msgid "Maximum Selection"
msgstr "Selecció màxima"

#: pro/fields/class-acf-field-repeater.php:65
#: pro/fields/class-acf-field-repeater.php:661
msgid "Minimum rows reached ({min} rows)"
msgstr "No s’ha arribat al mínim de files ({min} files)"

#: pro/fields/class-acf-field-repeater.php:66
msgid "Maximum rows reached ({max} rows)"
msgstr "S’ha superat el màxim de files ({max} files)"

#: pro/fields/class-acf-field-repeater.php:338
msgid "Add row"
msgstr "Afegeix una fila"

#: pro/fields/class-acf-field-repeater.php:339
msgid "Remove row"
msgstr "Esborra la fila"

#: pro/fields/class-acf-field-repeater.php:417
msgid "Collapsed"
msgstr "Replegat"

#: pro/fields/class-acf-field-repeater.php:418
msgid "Select a sub field to show when row is collapsed"
msgstr "Escull un subcamp per a mostrar quan la fila estigui replegada"

#: pro/fields/class-acf-field-repeater.php:428
msgid "Minimum Rows"
msgstr "Mínim de files"

#: pro/fields/class-acf-field-repeater.php:438
msgid "Maximum Rows"
msgstr "Màxim de files"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "No hi ha pàgines d’opcions"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "S’han actualitzat les opcions"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Per a activar les actualitzacions, introduïu la clau de llicència a la "
"pàgina d’<a href=\"%s\">Actualitzacions</a>. Si no teniu cap clau de "
"llicència, vegeu-ne els<a href=\"%s\">detalls i preu</a>."

#: tests/basic/test-blocks.php:30
msgid "Normal"
msgstr "Normal"

#: tests/basic/test-blocks.php:31
msgid "Fancy"
msgstr "Sofisticat"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"
